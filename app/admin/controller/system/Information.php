<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2020-12-02 17:54:29
 * @Last Modified time: 2020-12-02 17:55:05
 */
/**
 * @Author: <PERSON><PERSON>
 * @Date:   2020-10-16 09:45:22
 * @Last Modified time: 2020-10-20 17:31:38
 */
namespace app\admin\controller\system;

use app\admin\controller\AuthController;
use crmeb\services\{
    CacheService,
    FormBuilder as Form,
    UtilService as Util,
    JsonService as Json
};
use think\facade\Route as Url;
use app\admin\model\chat\{
    StoreBuddyLog as BuddyLogModel
};

/**
 * 会话管理
 * Class StoreService
 * @package app\admin\controller\store
 */
class Information extends AuthController
{
    /**
     * 显示资源列表
     *
     * @return \think\Response
     */
    public function index()
    {
        $this->assign([
            'year' => get_month(),
            'status' => $this->request->param('status', '')
        ]);
        return $this->fetch();
    }


    /**
     * 获取会话列表
     * return json
     */
    public function list()
    {
        $where = Util::getMore([
            ['status', ''],
            ['data', ''],
            ['keywords', $this->request->param('keywords', '')], //关键字
            ['page', 1],
            ['limit', 20],
        ]);
        return Json::successlayui(BuddyLogModel::CommentList($where));
    }

    /**
     * 删除指定资源
     *
     * @param int $id
     * @return \think\Response
     */
    public function delete($id)
    {
        if (!BuddyLogModel::del($id))
            return Json::fail(BuddyLogModel::getErrorInfo('删除失败,请稍候再试!'));
        else
            return Json::successful('删除成功!');
    }

    /**
     * 显示资源列表
     *
     * @return \think\Response
     */
    public function chat_user($id)
    {
        $now_service = BuddyLogModel::get($id);
        if (!$now_service) return Json::fail('数据不存在!');
        $list = BuddyLogModel::getChatUser($now_service->toArray(), 0);
        $this->assign(compact('list', 'now_service'));
        return $this->fetch();
    }

    /**
     * @param int $uid
     * @param int $to_uid
     * @return string
     */
    public function chat_list()
    {
        $where = Util::getMore([['uid', 0], ['to_uid', 0], ['id', 0]]);
        if ($where['uid'])
            CacheService::set('admin_chat_list' . $this->adminId, $where);
        $where = CacheService::get('admin_chat_list' . $this->adminId);
        $this->assign(StoreServiceLog::getChatList($where, 0));
        $this->assign('where', $where);
        return $this->fetch();
    }
}
