<?php

namespace app\admin\controller\system;

use app\admin\controller\AuthController;
use crmeb\services\{
    FormBuilder as Form, UtilService, JsonService as Json
};
use app\admin\model\user\UserPollster as UserPollsterModel;
use app\admin\model\system\SystemPollster as SystemPollsterModel;

/**
 * 调查问卷  控制器
 * Class SystemPollster
 * @package app\admin\controller\system
 */
class SystemPollster extends AuthController
{
    protected $bindModel = SystemPollsterModel::class;

    /**
     * 显示资源列表
     *
     * @return \think\Response
     */
    public function index()
    {
        return $this->fetch();
    }

    /**
     * 异步获取砍价数据
     */
    public function get_pollster_list()
    {
        $where = UtilService::getMore([
            ['page', 1],
            ['limit', 20],
            ['status', ''],
            ['store_name', '']
        ]);
        $seckillList = SystemPollsterModel::systemPage($where);
        if (is_object($seckillList['list'])) $seckillList['list'] = $seckillList['list']->toArray();
        $data = $seckillList['list']['data'];
        return Json::successlayui(['count' => $seckillList['list']['total'], 'data' => $data]);
    }

    /**
     * 添加调查问卷课程
     * @return form-builder
     */
    public function create($id = 0)
    {
        if ($id) {
            $pollster_info = SystemPollsterModel::where('id',$id)->find();
            if (!$pollster_info) {
                return Json::fail('修改的调查问卷不存在');
            }
        }
        $this->assign('id', (int)$id);
        return $this->fetch();
    }

    /**
     * 保存调查问卷课程
     * @param int $id
     */
    public function save($id = 0)
    {
        $data = UtilService::postMore([
            ['type', 1],
            'title',
            'guided_speech',
            'content',
            ['status', 1],
        ]);
        if (!$data['title']) return Json::fail('请输入调查问卷：标题');  
        if (!$data['guided_speech']) return Json::fail('请输入调查问卷：填写提示语');  
        if (!$data['content']) return Json::fail('请输入调查问卷：填写内容');  
        if ($id) {
            SystemPollsterModel::edit($data, $id);
            return Json::successful('编辑成功!');
        } else {
            if(SystemPollsterModel::be(['title'=>$data['title'],'status'=>1,'is_del'=>0])) return Json::fail('添加失败，存在相同的调查问卷');
            $data['add_time'] = time();
            $res = SystemPollsterModel::create($data);
            if ($res)
                return Json::successful('添加成功');
            else
                return Json::fail('添加失败');
        }
    }


    /**
     * 获取调查问卷详细信息
     * @param int $id
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function get_pollster_info($id = 0)
    {
        $data = [];
        if ($id) {
            $pollster_info = SystemPollsterModel::where('id',$id)->find();
            if (!$pollster_info) {
                return Json::fail('修改的调查问卷不存在');
            }
            $data['pollsterInfo'] = $pollster_info;
         }
        return Json::successful($data);
    }



    /**
     * 设置资源上架|下架
     * @param string $is_show
     * @param string $id
     */
    public function set_status($status = '', $id = '')
    {
        ($status == '' || $id == '') && Json::fail('缺少参数');
        if (SystemPollsterModel::setPollsterStatus($id, (int)$status)) {
            return Json::successful($status == 1 ? '开启成功' : '关闭成功');
        } else {
            return Json::fail(SystemPollsterModel::getErrorInfo($status == 1 ? '开启失败' : '关闭失败'));
        }
    }


    /**
     * 调查问卷属性选择页面
     * @param $id
     * @return string|void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function roster($id)
    {
        $this->assign('id', $id);
        return $this->fetch();
    }


    /*
     * 异步获取参与调查问卷参与记录列表
     * @param int $vip_id 会员id
     * @param int $page 分页
     * @param int $limit 显示条数
     * @return json
     * */
    public function get_roster_list($id = 0)
    {

        $where['id'] = $id;
        list($page, $limit,$keywords) = UtilService::getMore([
            ['page', 1],
            ['limit', 10],
            ['keywords', '']
        ], $this->request, true);
        $where['keywords'] = $keywords;
        $where['page'] = $page;
        $where['limit'] = $limit;
        return Json::successlayui(UserPollsterModel::getWriteRecordList($where, (int)$page, (int)$limit));
    }
    
    /**
     * 删除指定资源
     *
     * @param int $id
     * @return \think\Response
     */
    public function delete($id)
    {
        if (!$id) return $this->failed('数据不存在');
        $product = SystemPollsterModel::get($id);
        if (!$product) return Json::fail('数据不存在!');
        if ($product['is_del']) return Json::fail('已删除!');
        $data['is_del'] = 1;
        if (!SystemPollsterModel::edit($data, $id))
            return Json::fail(SystemPollsterModel::getErrorInfo('删除失败,请稍候再试!'));
        else
            return Json::successful('删除成功!');
    }
}
