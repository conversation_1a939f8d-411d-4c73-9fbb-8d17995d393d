<?php

namespace app\admin\controller\sms;

use app\admin\controller\AuthController;
use crmeb\services\JsonService;
use app\admin\model\sms\SmsRecord as SmsRecordModel;
use crmeb\services\sms\Sms;
use crmeb\services\UtilService;

/**
 * 短息发送日志
 * Class SmsLog
 * @package app\admin\controller\sms
 */
class SmsRecord extends AuthController
{
    /**
     * @var Sms
     */
    protected function initialize()
    {
        parent::initialize(); // TODO: Change the autogenerated stub
    }

    /**
     * 短信记录页面
     * @return string
     */
    public function index()
    {
        return $this->fetch();
    }

    /**
     * 获取短信记录列表
     */
    public function recordList()
    {
        $where = UtilService::getMore([
            ['page', 1],
            ['limit', 20],
            ['type', ''],
            ['uid', ''],
            ['phone', ''],
        ]);
        return JsonService::successlayui(SmsRecordModel::getRecordList($where));
    }
}