<?php

namespace app\admin\controller\sms;

use app\admin\controller\AuthController;
use think\facade\Route;
use app\admin\model\system\SystemConfig as ConfigModel;
use crmeb\services\{FormBuilder, sms\Sms, SystemConfigService, UtilService, CacheService};

/**
 * 短信配置
 * Class SmsConfig
 * @package app\admin\controller\sms
 */
class SmsConfig extends AuthController
{
    /**
     * @var Sms
     */
    protected $smsHandle;

    protected function initialize()
    {
        parent::initialize(); // TODO: Change the autogenerated stub
    }

    /**
     * 展示配置
     * @return string
     * @throws \FormBuilder\exception\FormBuilderException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function index()
    {
        [$type, $tab_id] = UtilService::getMore([
            ['type', 0],
            ['tab_id', 0]
        ], null, true);

        if (!$tab_id) $tab_id = 1;
        $this->assign('tab_id', $tab_id);
        $list = ConfigModel::getAll($tab_id);
        if ($type == 3) {//其它分类
            $config_tab = null;
        } else {
            $config_tab = ConfigModel::getConfigTabAll($type);
            foreach ($config_tab as $kk => $vv) {
                $arr = ConfigModel::getAll($vv['value'])->toArray();
                if (empty($arr)) {
                    unset($config_tab[$kk]);
                }
            }
        }
        $formBuilder = ConfigModel::builder_config_from_data($list);
        $form = FormBuilder::make_post_form('编辑配置', $formBuilder, Route::buildUrl('save_basics'));
        $this->assign(compact('form'));
        $this->assign('config_tab', $config_tab);
        $this->assign('list', $list);
        return $this->fetch();
    }

    /**
     * 保存配置
     */
    public function save_basics()
    {
        $request = app('request');
        if ($request->isPost()) {
            CacheService::clear();
            $post = $request->post();
            foreach ($post as $k => $v) {
                if (is_array($v)) {
                    $res = ConfigModel::where('menu_name', $k)->column('upload_type', 'type');
                    foreach ($res as $kk => $vv) {
                        if ($kk == 'upload') {
                            if ($vv == 1 || $vv == 3) {
                                $post[$k] = $v[0];
                            }
                        }
                    }
                }
            }
            foreach ($post as $k => $v) {
                ConfigModel::edit(['value' => json_encode($v)], $k, 'menu_name');
            }
            return $this->successful('修改成功');
        }
    }

    /**
     * 退出
     * @return mixed
     * @throws \Psr\SimpleCache\InvalidArgumentException
     */
    public function logout()
    {
        $post = [
            'sms_account' => '',
            'sms_token' => ''
        ];
        foreach ($post as $k => $v) {
            if (is_array($v)) {
                $res = ConfigModel::where('menu_name', $k)->column('upload_type', 'type');
                foreach ($res as $kk => $vv) {
                    if ($kk == 'upload') {
                        if ($vv == 1 || $vv == 3) {
                            $post[$k] = $v[0];
                        }
                    }
                }
            }
        }
        foreach ($post as $k => $v) {
            ConfigModel::edit(['value' => json_encode($v)], $k, 'menu_name');
        }
        CacheService::clear();
        return redirect(url('sms.smsConfig/index') . '?type=4&tab_id=18');
    }
}