<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2021-11-08 17:20:33
 * @Last Modified time: 2022-03-21 19:44:45
 */
namespace app\admin\controller\special;

use app\admin\controller\AuthController;
use crmeb\services\{
    JsonService,
    FormBuilder as Form,
    UtilService as Util,
    JsonService as Json
};
use think\Request;
use think\facade\Route as Url;
use crmeb\services\upload\storage\OssHelper;
use app\admin\model\special\Special as SpecialModel;
use app\admin\model\special\SpecialTask as SpecialTaskModel;
use app\admin\model\special\SpecialHomeRecommen as SpecialHomeRecommenModel;

/**
 * 首页配置-最新课程推荐处理
 * Class Home
 * @package app\admin\controller\evaluation
 */
class SpecialHomeRecommen extends AuthController
{
    public function index()
    {
        return $this->fetch();
    }

    /**
     * 获取最新课程推荐列表
     * return json
     */
    public function list()
    {
        $where = Util::getMore([
            ['page', 1],
            ['limit', 20],
        ]);
        return Json::successlayui(SpecialHomeRecommenModel::List($where));
    }

    /**
     * 获取user表
     *
     * @return json
     */
    public function userList($id = 0)
    {
        $this->assign('id', (int)$id);
        return $this->fetch();
    }

    
    /**
     * 显示创建资源表单页.
     *
     * @return \think\Response
     */
    public function create($id = 0)
    {
        $this->assign('id', (int)$id);
        return $this->fetch();
    }

    /**
     * 获取最新课程推荐详细信息
     * @param int $id
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function get_special_home_recommen_info($id = 0)
    {
        $data['recommenInfo'] = [];
        if ($id) {
            $recommenInfo = SpecialHomeRecommenModel::get($id);
            if (!$recommenInfo) {
                return Json::fail('修改的活动不存在');
            }
            //根据类型查找课程专题、或者素材
            if ($recommenInfo['type'] == 1) {
                $recommenInfo['source_image'] = SpecialTaskModel::where('id',$recommenInfo['source_id'])->value('image') ?: '';
                if (!filter_var( $recommenInfo['source_image'], FILTER_VALIDATE_URL ) !== false ) {
                    $recommenInfo['source_image'] =  OssHelper::normalVideo($recommenInfo['source_image']) ?: '';
                }
            }else{
                $recommenInfo['special_image'] = SpecialModel::where('id',$recommenInfo['special_id'])->value('image') ?: '';
            }
            $recommenInfo['type'] =  (int) $recommenInfo['type'];
            $data['recommenInfo'] = $recommenInfo;
        }
        return JsonService::successful($data);
    }


    /**
     * 保存新建的资源
     *
     * @param \think\Request $request
     * @return \think\Response
     */
    public function save($id)
    {
        $data = Util::postMore([
            'type',
            'special_id',
            'source_id',
            'url',
            'sort',
            'is_show',
        ]);
        if ($data['type'] == 0 && !$data['special_id']) return Json::fail('请关联课程专题');
        if ($data['type'] == 1 && !$data['source_id']) return Json::fail('请关联课程专题素材');
        if ($id) {
            SpecialHomeRecommenModel::edit($data, $id);
            return Json::successful('修改最新课程推荐成功!');
         }else{
            $data['status'] = 1;
            $data['add_time'] = time();
            SpecialHomeRecommenModel::create($data);
            return Json::successful('添加最新课程推荐成功!');
         }
    }


    /**
     * 删除指定资源
     *
     * @param int $id
     * @return \think\Response
     */
    public function destroy($id)
    {
        if (!$id) return $this->failed('数据不存在');
        if (!SpecialHomeRecommenModel::be(['id' => $id])) return $this->failed('最新课程推荐数据不存在');
        if (SpecialHomeRecommenModel::be(['id' => $id, 'is_del' => 1])) {
            return Json::successful('最新课程推荐数据已被删除!');
        } else {
            $data['is_del'] = 1;
            $data['del_time'] = time();
            if (!SpecialHomeRecommenModel::edit($data, $id)){
                return Json::fail('删除失败,请稍候再试!');
            }else{
                return Json::successful('成功删除最新课程推荐!');
            }
        }
    }

    /**
     * 快速编辑
     * @param string $field
     * @param string $id
     * @param string $value
     */
    public function set_field($field = '', $id = '', $value = '')
    {
        $field == '' || $id == '' || $value == '' && Json::fail('缺少参数');
        if (SpecialHomeRecommenModel::where('id', $id)->update([$field => $value]))
            return Json::successful('保存成功');
        else
            return Json::fail('保存失败');
    }

    /**
     * 设置资源上架|下架
     * @param string $is_show
     * @param string $id
     */
    public function set_show($is_show = '', $id = '')
    {
        ($is_show == '' || $id == '') && Json::fail('缺少参数');
        if (SpecialHomeRecommenModel::where('id', $id)->update(['is_show' => (int) $is_show]))
            return Json::successful('设置成功');
        else
            return Json::fail('设置失败');
    }


    /**
     * 获取课程推荐列表
     * @return string
     * @throws \Exception
     */
    public function specialList()
    {
        return $this->fetch();
    }

    /**
     * 获取课程素材推荐列表
     * @return string
     * @throws \Exception
     */
    public function sourceList()
    {
        return $this->fetch();
    }
}