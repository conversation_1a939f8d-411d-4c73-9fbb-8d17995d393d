<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2021-11-03 10:13:28
 * @Last Modified time: 2023-04-06 10:06:03
 */
namespace app\admin\controller\special;

use app\admin\controller\AuthController;
use think\Request;
use think\facade\Route as Url;
use app\admin\model\user\User;
use app\admin\model\special\{
    Special as SpecialModel,
    SpecialTask as SpecialTaskModel,
    SpecialMessage as MessageModel,
    SpecialComment as SpecialCommentModel,
};
use app\models\routine\RoutineTemplate;
use app\models\system\SystemInformation;
use app\admin\model\system\SystemAttachment;
use app\models\special\SpecialSurpriseRecord;
use crmeb\services\{FormBuilder as Form, JsonService as Json, UtilService as Util};

/**
 * 课程-在线留言
 * Class Message
 * @package app\admin\controller\special
 */
class SpecialMessage extends AuthController
{
    /**
     * 显示资源列表
     *
     * @return \think\Response
     */
    public function index()
    {
        $this->assign([
            'year' => get_month(),
            'status' => $this->request->param('status', ''),
            'messageCount' => MessageModel::MessageCount(),
            'specialList' => SpecialModel::where('is_del',0)->where('is_show',1)->select(),
        ]);
        return $this->fetch();
    }

    /**
     * 获取评测列表
     * return json
     */
    public function message_list()
    {
        $where = Util::getMore([
            ['status', ''],
            ['type', ''],
            ['message_type', ''],
            ['special_id', ''],
            ['data', ''],
            ['keywords', $this->request->param('keywords', '')], //关键字
            ['page', 1],
            ['limit', 20],
        ]);
        return Json::successlayui(MessageModel::MessageList($where));
    }


    /**
     * 显示创建资源表单页.
     *
     * @return \think\Response
     */
    public function create($id = 0)
    {
        $this->assign('id', (int)$id);
        return $this->fetch();
    }



    /**
     * 保存新建的资源
     *
     * @param \think\Request $request
     * @return \think\Response
     */
    public function save($id)
    {
        $data = Util::postMore([
            'message_type',
            'special_id',
            'source_id',
            'uid',
            'title',
            'comment',
            'image',
            'video',
            'audio',
        ]);
        if (!$data['source_id'] && !$data['special_id']) return Json::fail('请关联课程专题或者素材');
        if (!$data['uid']) return Json::fail('请关联留言用户');
        $data['image'] = is_array($data['image']) ? json_encode($data['image'], true) : '';
        $data['video'] = is_array($data['video']) ? json_encode($data['slider_video'], true) : '';
        $data['audio'] = is_array($data['audio']) ? json_encode($data['audio'], true) : '';
        if ($id) {
            MessageModel::edit($data, $id);
            return Json::successful('修改虚拟留言成功!');
         }else{
            $data['status'] = 1;
            $data['add_time'] = time();
            MessageModel::create($data);
            return Json::successful('添加虚拟留言成功!');
         }
    }



    /**
     * 获取留言详细信息
     * @param int $id
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function get_message_info($id = 0)
    {
        $data['messageInfo'] = [];
        if ($id) {
            $messageInfo = MessageModel::get($id);
            if (!$messageInfo) {
                return Json::fail('修改的活动不存在');
            }
            $messageInfo['avatar'] = User::where('uid', $messageInfo['uid'])->value('avatar') ?: '';
            $messageInfo['special_image'] = SpecialModel::where('id', $messageInfo['special_id'])->value('image') ?: '';
            $messageInfo['source_image'] = SpecialTaskModel::where('id', $messageInfo['source_id'])->value('image') ?: '';
            $messageInfo['image'] = is_string($messageInfo['image']) ? json_decode($messageInfo['image'], true) : [];
            $messageInfo['video'] = is_string($messageInfo['video']) ? json_decode($messageInfo['video'], true) : [];
            $messageInfo['audio'] = is_string($messageInfo['audio']) ? json_decode($messageInfo['audio'], true) : [];
            $data['messageInfo'] = $messageInfo;
        }
        return Json::successful($data);
    }


    /**
     * 显示编辑资源表单页.
     *
     * @param int $id
     * @return \think\Response
     */
    public function set_status($id = 0,$type = 1)
    {
        $c = MessageModel::get($id);
        $image =  $c->special_id != 0 ? SpecialModel::where('id',$c->special_id)->value('image')  : '';
        if (!$c) return Json::fail('数据不存在!');
        $field = 
        [
            Form::hidden('type', 2),
            Form::input('title', '留言标题', $c->getData('title')),
            Form::input('special_id','课程专题ID', $c->getData('special_id'))->disabled(true),
            Form::frameImageOne('product', '关联的产品', Url::buildUrl('productList', array('fodder' => 'image')), $image )->icon('image')->width('100%')->height('500px'),
            Form::radio('is_official_comment', '是否官评', $c->getData('is_official_comment'))->options([['label' => '是', 'value' => 1],['label' => '否', 'value' => 0]]),
            Form::radio('status', '是否达成', $c->getData('status'))->options([['label' => '是', 'value' => 1],['label' => '否', 'value' => 0]]),
        ];
        $form = Form::make_post_form('编辑活动状态', $field, Url::buildUrl('save_set_status', array('id' => $id)), 2);
        $this->assign(compact('form'));
        return $this->fetch('public/form-builder');
    }



    /**
     * 快速编辑
     * @param string $field
     * @param string $id
     * @param string $values
     */
    public function set_field($field = '', $id = '', $values = '')
    {
        $field == '' || $id == '' || $values == '' && Json::fail('缺少参数');
        if (MessageModel::where('id', $id)->update([$field => $values]))
            return Json::successful('保存成功');
        else
            return Json::fail('保存失败');
    }

    public function wish_info($wid = '')
    {
        if (!$wid || !($messageInfo = MessageModel::get($wid)))
            return $this->failed('活动不存在!');
        $images = is_string($messageInfo['image']) ? json_decode($messageInfo['image'], true) : [];
        $this->assign(compact('messageInfo','images'));
        return $this->fetch();
    }

    /**
     * 设置分组
     * @param int $uid
     */
    public function save_set_status(Request $request, $id)
    {
 		MessageModel::beginTrans();
        $messageInfo = MessageModel::where('id', $id)->find();
        if (!$messageInfo) return Json::fail('留言不存在！');
            $messageInfo->is_del = 0;
            $messageInfo->status = 1;
        if ($messageInfo->save()) {
            MessageModel::commitTrans();
            return Json::successful('审核成功！');
        } else {
            MessageModel::rollbackTrans();
            return Json::fail('审核失败');
        }
    }

    /**
     * 拒绝留言
     * @param int $uid
     */
    public function reject(Request $request, $id)
    {
        MessageModel::beginTrans();
        $messageInfo = MessageModel::where('id', $id)->find();
        if (!$messageInfo) return Json::fail('留言不存在！');
            $messageInfo->status = 3;
        if ($messageInfo->save()) {
            MessageModel::commitTrans();
            return Json::successful('拒绝成功！');
        } else {
            MessageModel::rollbackTrans();
            return Json::fail('拒绝失败');
        }
    }


    /**
     * 审核指定资源
     * @param $id
     */
    public function review(Request $request,$id)
    {
        MessageModel::beginTrans();
        $messageInfo = MessageModel::where('id', $id)->find();
        if (!$messageInfo) return Json::fail('留言不存在！');
        if ($messageInfo['is_del'] == 1) return Json::fail('留言不存在！');
        $messageInfo->status = 1;
        if ($messageInfo->message_type == 1  && $request->param('is_open_egg') == "1" && $messageInfo->special_id !=0 && $messageInfo->source_id == 0 ) { //说明此次是作业 
        // 判断 是否 已经解锁过 本次彩蛋 
            if (!SpecialSurpriseRecord::isUnlock($messageInfo->uid,$messageInfo->special_id,0,'easter_egg')) {
                $special_title = SpecialModel::getSpecialTitle($messageInfo->special_id,0,5);
                $title = '您在学习【'.$special_title.'】课程中成功解锁彩蛋'; 
                $mark = '点击本条信息可查看彩蛋内容';
                $link = SpecialModel::getCourseEggsLink($messageInfo->special_id);
                RoutineTemplate::sendUnlockCourseEggs($messageInfo->uid,$title,$mark,$link);
                SpecialSurpriseRecord::setRecord($messageInfo->uid,$messageInfo->special_id,0,'easter_egg'); //添加解锁记录 
            }
        }
        if ($messageInfo->save()) {
            MessageModel::commitTrans();
            return Json::successful('审核成功！');
        } else {
            MessageModel::rollbackTrans();
            return Json::fail('审核失败');
        }
    }

    /**
     * 设置单个活动上架|下架
     *
     * @return json
     */
    public function set_is_refining($is_refining = '', $id = '')
    {
       ($is_refining == '' || $id == '') && Json::fail('缺少参数');
        $res = MessageModel::where(['id' => $id])->update(['is_refining' => (int)$is_refining]);
        if ($res) {
            if ($is_refining == 1) {
                $messageObj  = MessageModel::where('id',$id)->find();
                $text = '';
                $title = SpecialModel::getSpecialSourceTitle($messageObj['special_id'],$messageObj['source_id'],5);
                if ($messageObj['source_id'] == 0) {
                    $text = '您在课程'.$title.'的评论被评为精华！';
                }else{
                    $text = '您在素材'.$title.'的评论被评为精华！';
                }
                //微信订阅消息
                RoutineTemplate::sendMessageEssence($messageObj['uid'],$text);
                //发送站内信消息
                SystemInformation::sendEssenceLetter($id,$messageObj['uid'],$messageObj['special_id'],$messageObj['source_id'],$title);
            }
            return Json::successful($is_refining == 1 ? '设置成功' : '设置成功');
        } else {
            return Json::fail($is_refining == 1 ? '设置失败' : '设置失败');
        }
    }

        /**
     * 获取看板详细信息
     * @param int $id
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException\ModelNotFoundException
     * @throws \think\db\exception
     */
    public function message_info($id = 0)
    {
    	$messageInfo = [];
        if ($id) {
            $messageInfo = MessageModel::get($id);
            if (!$messageInfo) {
                return Json::fail('查看的留言不存在');
            }
            $messageInfo['image'] = is_string($messageInfo['image']) ? json_decode($messageInfo['image'], true) : [];
            $messageInfo['video'] = is_string($messageInfo['video']) ? json_decode($messageInfo['video'], true) : [];
            $messageInfo['audio'] = is_string($messageInfo['audio']) ? json_decode($messageInfo['audio'], true) : [];
            //查询出所有关联的留言评论
            $commentList = ($data  = SpecialCommentModel::field(['id','uid','pid','comment','add_time'])->where('special_id',$messageInfo['special_id'])->where('message_id',$messageInfo['id'])->select()) && count($data) ? $data->toArray() : [];
            foreach ($commentList as $key => $item) {
                $commentList[$key]['type_name'] = '一级评论';
                if ($item['pid'] && $item['pid'] > 0) {
                    $pMessageComment = SpecialCommentModel::find($item['pid']);
                    if ($pMessageComment) {
                      $commentList[$key]['type_name'] = '回复:'.User::where('uid', $pMessageComment['uid'])->value('nickname');
                      $commentList[$key]['parant_comment'] = $pMessageComment['comment'];
                      $commentList[$key]['parent_add_time'] = $pMessageComment['add_time'];
                    }
                }
            }
            $messageInfo['commentList'] = $commentList;
        }
        $this->assign(compact('id','messageInfo'));
        return $this->fetch();
    }

    /**
     * 删除指定资源
     *
     * @param int $id
     * @return \think\Response
     */
    public function destroy($id)
    {   
        $data['is_del'] = 1;
        $res = MessageModel::edit($data,$id);
        if (!$res) {
            return Json::fail(MessageModel::getErrorInfo('删除失败,请稍候再试!'));
        }
        $messageObj  = MessageModel::where('id',$id)->find();
        //发送站内信消息
        SystemInformation::sendDeleteLetter($id,$messageObj['uid'],$messageObj['special_id'],$messageObj['source_id']);
        return Json::successful('删除成功!');  
    }
}