<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2021-11-08 10:24:40
 * @Last Modified time: 2023-04-14 09:47:43
 */

namespace app\admin\controller\special;

use app\admin\controller\AuthController;
use app\admin\model\order\StoreOrderCartInfo;
use app\admin\model\system\Express;
use app\admin\model\special\SpecialBuy;
use crmeb\repositories\OrderRepository;
use crmeb\repositories\ShortLetterRepositories;
use crmeb\services\{
  ExpressService,
  JsonService,
  MiniProgramService,
  ByteProgramService,
  WechatService,
  FormBuilder as Form,
  CacheService,
  UtilService as Util,
  JsonService as Json
};
use app\admin\model\order\StoreOrderStatus;
use app\admin\model\ump\StorePink;
use app\admin\model\user\{
  User,
  UserBill
};
use app\admin\model\special\Special as SpecialModel;
use crmeb\basic\BaseModel;
use think\facade\Route as Url;
use app\admin\model\order\StoreOrder as StoreOrderModel;
use app\models\store\StoreOrder as StoreOrderModels;
use crmeb\services\YLYService;
use think\facade\Log;

/**
 * 订单管理控制器 同一个订单表放在一个控制器
 * Class SpecialOrder
 * @package app\admin\controller\special
 */
class SpecialOrder extends AuthController
{
  /**
   * @return mixed
   */
  public function index()
  {
    $this->assign([
      'year' => get_month('y'),
      'real_name' => $this->request->get('real_name', ''),
      'orderCount' => StoreOrderModel::specialOrderCount(0),
      'payTypeCount' => StoreOrderModel::specialPayTypeCount(),
      'specialList' => SpecialModel::where('is_del', 0)->where('pay_type', 1)->select(),
    ]);
    return $this->fetch();
  }

  /**
   * 获取头部订单金额等信息
   * return json
   */
  public function getBadge()
  {
    $where = Util::postMore([
      ['status', ''],
      ['special_id', ''],
      ['real_name', ''],
      ['is_del', 0],
      ['data', ''],
      ['type', ''],
      ['types', 3],
      ['order', '']
    ]);
    return Json::successful(StoreOrderModel::getSpecialBadge($where));
  }

  /**
   * 获取订单列表
   * return json
   */
  public function order_list()
  {
    $where = Util::getMore([
      ['status', ''],
      ['real_name', $this->request->param('real_name', '')],
      ['is_del', 0],
      ['data', ''],
      ['type', ''],
      ['types', $this->request->param('types', 3)],
      ['order', ''],
      ['spread_type', ''],
      ['special_id', ''],
      ['page', 1],
      ['limit', 20],
      ['excel', 0]
    ]);

    return Json::successlayui(StoreOrderModel::SpecialOrderList($where));
  }

  /**
   * 修改支付金额等
   * @param $id
   * @return mixed|\think\response\Json|void
   */
  public function edit($id)
  {
    if (!$id) return $this->failed('数据不存在');
    $order = StoreOrderModel::get($id);
    if (!$order) return Json::fail('数据不存在!');
    $f = [];
    $f[] = Form::input('order_id', '订单编号', $order->getData('order_id'))->disabled(1);
    $f[] = Form::number('total_price', '商品总价', $order->getData('total_price'))->min(0);
    $f[] = Form::number('total_postage', '原始邮费', $order->getData('total_postage'))->min(0);
    $f[] = Form::number('pay_price', '实际支付金额', $order->getData('pay_price'))->min(0);
    $f[] = Form::number('pay_postage', '实际支付邮费', $order->getData('pay_postage'));
    $f[] = Form::number('gain_integral', '赠送积分', $order->getData('gain_integral'));
    $form = Form::make_post_form('修改订单', $f, Url::buildUrl('update', array('id' => $id)));
    $this->assign(compact('form'));
    return $this->fetch('public/form-builder');
  }

  /**
   * 修改订单提交更新
   * @param $id
   */
  public function update($id)
  {
    $data = Util::postMore([
      'order_id',
      'total_price',
      'total_postage',
      'pay_price',
      'pay_postage',
      'gain_integral',
    ]);
    if ($data['total_price'] <= 0) return Json::fail('请输入商品总价');
    if ($data['pay_price'] <= 0) return Json::fail('请输入实际支付金额');
    $order = StoreOrderModel::get($id);
    if (!$order) {
      return Json::fail('订单不存在');
    }
    $order->order_id = StoreOrderModel::changeOrderId($data['order_id']);
    $pay_price = $order->pay_price;
    $order->pay_price = $data['pay_price'];
    $order->total_price = $data['total_price'];
    $order->total_postage = $data['total_postage'];
    $order->pay_postage = $data['pay_postage'];
    $order->gain_integral = $data['gain_integral'];
    if ($order->save()) {
      //改价短信提醒
      if ($data['pay_price'] != $pay_price) {
        $switch = sys_config('price_revision_switch') ? true : false;
        ShortLetterRepositories::send($switch, $order->user_phone, ['order_id' => $order->order_id, 'pay_price' => $order->pay_price], 'PRICE_REVISION_CODE');
      }
      event('StoreProductOrderEditAfter', [$data, $id]);
      StoreOrderStatus::setStatus($id, 'order_edit', '修改商品总价为：' . $data['total_price'] . ' 实际支付金额' . $data['pay_price']);
      return Json::successful('修改成功!');
    } else {
      return Json::fail('订单修改失败');
    }
  }

  /*
     * 删除订单
     * */
  public function del_order()
  {
    $ids = Util::postMore(['ids'])['ids'];
    if (!count($ids)) return Json::fail('请选择需要删除的订单');
    if (StoreOrderModel::where('is_del', 0)->where('id', 'in', $ids)->count())
      return Json::fail('您选择的的订单存在用户未删除的订单，无法删除用户未删除的订单');
    $res = StoreOrderModel::where('id', 'in', $ids)->update(['is_system_del' => 1]);
    if ($res)
      return Json::successful('删除成功');
    else
      return Json::fail('删除失败');
  }


  /**
   * 修改退款状态
   * @param $id
   * @return \think\response\Json|void
   */
  public function refund_y($id)
  {
    if (!$id) return $this->failed('数据不存在');
    $order = StoreOrderModel::get($id);
    if (!$order) return Json::fail('数据不存在!');
    if ($order['paid'] == 1) {
      $pay_price = $order->getData('pay_price');
      $refunded_price = $order->getData('refund_price');;
      $refundable_price = bcsub($pay_price, $refunded_price, 2);
      $f = [];
      $f[] = Form::input('order_id', '退款单号', $order->getData('order_id'))->disabled(1);
      $f[] = Form::number('pay_price', '支付金额', $order->getData('pay_price'))->disabled(1);
      $f[] = Form::number('refunded_price', '已退款金额', $order->getData('refund_price'))->disabled(1);
      $f[] = Form::number('refundable_price', '可退款金额', $refundable_price)->disabled(1);
      $f[] = Form::number('refund_price', '本次退款金额', $refundable_price)->precision(2)->min(0.01);
      $f[] = Form::radio('type', '状态', 1)->options([['label' => '直接退款', 'value' => 1], ['label' => '退款并取消权益', 'value' => 2]]);
      $f[] = Form::textarea('mark', '退款操作备注', $order->getData('mark'));
      $form = Form::make_post_form('退款处理', $f, Url::buildUrl('updateRefundY', array('id' => $id)), 7);
      $this->assign(compact('form'));
      return $this->fetch('public/form-builder');
    } else return Json::fail('数据不存在!');
  }

  /**
   * 退款处理
   * @param $id
   */
  public function updateRefundY($id)
  {
    $data = Util::postMore([
      'refund_price',
      'mark',
      ['type', 1],
    ]);
    if (!$id) return $this->failed('数据不存在');
    $order = StoreOrderModel::get($id);
    if (!$order) return Json::fail('数据不存在!');
    if ($order['pay_price'] == $order['refund_price']) return Json::fail('已退完支付金额!不能再退款了');
    if (!$data['refund_price']) return Json::fail('请输入退款金额');
    $refund_price = $data['refund_price'];
    $data['refund_price'] = bcadd($data['refund_price'], $order['refund_price'], 2);
    $bj = bccomp((float)$order['pay_price'], (float)$data['refund_price'], 2);
    if ($bj < 0) return Json::fail('退款金额大于支付金额，请修改退款金额');
    if ($data['type'] == 1) {
      $data['refund_status'] = 2;
    } else if ($data['type'] == 2) {
      $data['refund_status'] = 0;
    }
    $type = $data['type'];
    unset($data['type']);
    $refund_data['pay_price'] = $order['pay_price'];
    $refund_data['desc'] = $data['mark'];
    $refund_data['refund_price'] = $refund_price;
    $refund_data['refund_id'] = $order['order_id'] . '_' . session_create_id(); //微信退款单号
    if ($order['pay_type'] == 'weixin' || $order['pay_type'] == 'bytedance.weixin' || $order['pay_type'] == 'bytedance.alipay') {
      if ($order['is_channel'] == 1) { //小程序
        try {
          MiniProgramService::payOrderRefund($order['order_id'], $refund_data); //2.5.36
        } catch (\Exception $e) {
          return Json::fail($e->getMessage());
        }
      } else if ($order['is_channel'] == 3) {
        try {
          ByteProgramService::payOrderRefund($order['order_id'], $refund_data); //2.5.36
        } catch (\Exception $e) {
          return Json::fail($e->getMessage());
        }
      } else { //TODO 公众号
        try {
          WechatService::payOrderRefund($order['order_id'], $refund_data);
        } catch (\Exception $e) {
          return Json::fail($e->getMessage());
        }
      }
    } else if ($order['pay_type'] == 'yue') {
      BaseModel::beginTrans();
      $usermoney = User::where('uid', $order['uid'])->value('now_money');
      $res1 = User::bcInc($order['uid'], 'now_money', $refund_price, 'uid');
      $res2 = $res2 = UserBill::income('商品退款', $order['uid'], 'now_money', 'pay_order_refund', $refund_price, $order['id'], bcadd($usermoney, $refund_price, 2), '订单退款到余额' . floatval($refund_price) . '元');
      try {
        OrderRepository::storeOrderYueRefund($order, $refund_data);
      } catch (\Exception $e) {
        BaseModel::rollbackTrans();
        return Json::fail($e->getMessage());
      }
      $res = $res1 && $res2;
      BaseModel::checkTrans($res);
      if (!$res) return Json::fail('余额退款失败!');
    }
    $resEdit = StoreOrderModel::edit($data, $id);
    $res = true;
    if ($resEdit) {
      $data['type'] = $type;
      if ($data['type'] == 1) $res = StorePink::setRefundPink($id);
      if (!$res) return Json::fail('修改失败');
      if ($type  == 2 &&  SpecialBuy::be(['order_id' => $order['order_id'], 'special_id' => $order['cart_id'], 'uid' => $order['uid'], 'is_del' => 0])) {
        $res3 = SpecialBuy::where(['order_id' => $order['order_id'], 'special_id' => $order['cart_id'], 'uid' => $order['uid'], 'is_del' => 0])->update(['is_del' => 1]);
      }
      // try {
      //     OrderRepository::storeProductOrderRefundY($data, $id);
      // } catch (\Exception $e) {
      //     BaseModel::rollbackTrans();
      //     return Json::fail($e->getMessage());
      // }
      StoreOrderStatus::setStatus($id, 'refund_price', '退款给用户' . $refund_price . '元');
      // StoreOrderModels::RegressionStock($order); //回退库存
      BaseModel::commitTrans();
      return Json::successful('修改成功!');
    } else {
      StoreOrderStatus::setStatus($id, 'refund_price', '退款给用户' . $refund_price . '元失败');
      return Json::fail('修改失败!');
    }
  }

  public function order_info($oid = '')
  {
    if (!$oid || !($orderInfo = StoreOrderModel::get($oid)))
      return $this->failed('订单不存在!');
    $userInfo = User::getUserInfos($orderInfo['uid']);
    if ($userInfo['spread_uid']) {
      $spread = User::where('uid', $userInfo['spread_uid'])->value('nickname');
    } else {
      $spread = '';
    }
    $this->assign(compact('orderInfo', 'userInfo', 'spread'));
    return $this->fetch();
  }


  /**
   * 修改退款状态
   * @param $id
   * @return mixed|\think\response\Json|void
   */
  public function refund_n($id)
  {
    if (!$id) return $this->failed('数据不存在');
    $product = StoreOrderModel::get($id);
    if (!$product) return Json::fail('数据不存在!');
    $f[] = Form::input('order_id', '订单号', $product->getData('order_id'))->disabled(1);
    $f[] = Form::input('refund_reason', '退款原因')->type('textarea');
    $form = Form::make_post_form('退款', $f, Url::buildUrl('updateRefundN', array('id' => $id)));
    $this->assign(compact('form'));
    return $this->fetch('public/form-builder');
  }

  /**
   * 不退款原因
   * @param $id
   */
  public function updateRefundN($id)
  {
    $data = Util::postMore([
      'refund_reason',
    ]);
    if (!$id) return $this->failed('数据不存在');
    $product = StoreOrderModel::get($id);
    if (!$product) return Json::fail('数据不存在!');
    if (!$data['refund_reason']) return Json::fail('请输入退款原因');
    $data['refund_status'] = 0;
    StoreOrderModel::edit($data, $id);
    event('SpecialOrderRefundNAfter', [$data['refund_reason'], $id]);
    StoreOrderStatus::setStatus($id, 'refund_n', '不退款原因:' . $data['refund_reason']);
    return Json::successful('修改成功!');
  }

  public function remark()
  {
    $data = Util::postMore(['id', 'remark']);
    if (!$data['id']) return Json::fail('参数错误!');
    if ($data['remark'] == '') return Json::fail('请输入要备注的内容!');
    $id = $data['id'];
    unset($data['id']);
    StoreOrderModel::edit($data, $id);
    return Json::successful('备注成功!');
  }

  public function order_status($oid)
  {
    if (!$oid) return $this->failed('数据不存在');
    $this->assign(StoreOrderStatus::systemPage($oid));
    return $this->fetch();
  }

  /*
     * 订单列表推荐人详细
     */
  public function order_spread_user($uid)
  {
    $spread = User::where('uid', $uid)->find();
    $this->assign('spread', $spread);
    return $this->fetch();
  }
}
