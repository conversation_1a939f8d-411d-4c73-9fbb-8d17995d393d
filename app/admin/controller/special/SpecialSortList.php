<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2021-11-22 14:11:10
 * @Last Modified time: 2021-12-13 10:16:16
 */
namespace app\admin\controller\special;

use think\Request;
use think\facade\Route as Url;
use app\admin\controller\AuthController;
use crmeb\services\upload\storage\OssHelper;
use app\admin\model\special\SpecialSubject as SpecialSubjectCategoryModel;
use app\admin\model\special\SpecialMaterialSortList as SpecialMaterialSortListModel;
use crmeb\services\{FormBuilder as Form, JsonService as Json, UtilService as Util};

/**
 * 专题、素材、排序控制器
 * Class SpecialSortList
 * @package app\admin\controller\special
 */

class SpecialSortList extends AuthController
{
    /**
     * 显示资源列表
     *
     * @return \think\Response
     */
    public function index()
    {
        $this->assign('grade_id', $this->request->get('grade_id', 0));
        $this->assign('cate', SpecialSubjectCategoryModel::getTierList(null, 0));
        return $this->fetch();
    }

    /*
     *  异步获取分类列表
     *  @return json
     */
    public function category_list()
    {
        $where = Util::getMore([
            ['is_show', ''],
            ['grade_id', $this->request->param('grade_id', '')],
            ['name', ''],
            ['page', 1],
            ['limit', 20],
            ['order', '']
        ]);
        
        return Json::successlayui(SpecialSubjectCategoryModel::CategoryList($where));
    }


    /**
     * 属于此分类下、课程专题、素材资源列表
     *
     * @return \think\Response
     */
    public function show_sort_special()
    {   
        $grade_id = $this->request->get('grade_id', 0);
        $special_list = SpecialMaterialSortListModel::getList($grade_id);
        $count = count($special_list);
        foreach ($special_list as $key => $value) {
            $special_list[$key]['sort'] = $count --;
            if (isset($value['task_type'])  && $value['task_type'] == 3) {
                if (!filter_var( $value['task_image'], FILTER_VALIDATE_URL ) !== false ) {
                    $special_list[$key]['task_image'] =  OssHelper::normalVideo($value['task_image']) ?: '';
                }
            }
        }
        $cate_name = SpecialSubjectCategoryModel::where('id',$grade_id)->value('name');
        $this->assign(compact('grade_id','special_list','cate_name'));
        return $this->fetch();
    }

    /**
     * 保存新建的资源
     *
     * @param \think\Request $request
     * @return \think\Response
     */
    public function save(Request $request)
    {
        $data = Util::postMore([
            ['list2', []],
        ]);
        if (count($data['list2']) < 1) return Json::fail('请操作排序后，进行提交');
        $list2 = $data['list2'];
        SpecialMaterialSortListModel::beginTrans();
        $sort_res = SpecialMaterialSortListModel::saveSortList($list2);
        if ($sort_res) {
            SpecialMaterialSortListModel::commitTrans();
            return Json::success('修改成功!');
        } else {
            SpecialMaterialSortListModel::rollbackTrans();
            return Json::fail(SpecialMaterialSortListModel::getErrorInfo());
        }
    }
}