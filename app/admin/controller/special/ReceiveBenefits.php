<?php

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date:   2023-02-10 15:14:54
 * @Last Modified time: 2023-05-25 15:38:02
 */

namespace app\admin\controller\special;

use think\Request;
use think\facade\Route as Url;
use app\admin\model\user\User;
use app\admin\controller\AuthController;
use app\admin\model\special\Special as SpecialModel;
use app\admin\model\special\SpecialBuy as SpecialBuyModel;
use app\admin\model\special\InvitationCodeRecord as InvitationCodeRecordModel;
use crmeb\services\{FormBuilder as Form, JsonService as Json, UtilService as Util};

/**
 * 课程权益领取：控制器
 * Class ReceiveBenefits
 * @package app\admin\controller\special
 */
class ReceiveBenefits extends AuthController
{
  /**
   * 显示资源列表
   *
   * @return \think\Response
   */
  public function index()
  {
    $this->assign('special_list', SpecialModel::where('is_del', 0)->select());
    return $this->fetch();
  }

  /*
     *  异步获取列表
     *  @return json
     */
  public function list()
  {
    $where = Util::getMore([
      ['keywords', ''],
      ['code', ''],
      ['special_id', 0],
      ['type', 2],
      ['status', 0],
      ['add_time', ''],
      ['page', 1],
      ['limit', 20],
      ['excel', 0],
    ]);
    return Json::successlayui(InvitationCodeRecordModel::douyinCourseList($where));
  }

  /**
   * 领取详情
   *
   * @param int $id
   * @return \think\Response
   */
  public function receive_info($id = '')
  {
    if (!$id || !($receiveInfo = SpecialBuyModel::get($id)))
      return $this->failed('领取记录不存在!');
    $userInfo = User::getUserInfos($receiveInfo['uid']);
    $specialInfo = SpecialModel::where('id', $receiveInfo['special_id'])->find();
    $codeInfo = InvitationCodeRecordModel::where('id', $receiveInfo['column_id'])->find();
    $grantUserInfo = User::getUserInfos($codeInfo['uid']);
    $this->assign(compact('receiveInfo', 'specialInfo', 'userInfo', 'codeInfo', 'grantUserInfo'));
    return $this->fetch();
  }

  /**
   * 删除指定资源
   *
   * @param int $id
   * @return \think\Response
   */
  public function delete($id)
  {
    $data['is_del'] = 1;
    $res = SpecialBuyModel::edit($data, $id);
    if (!$res)
      return Json::fail(SpecialBuyModel::getErrorInfo('删除失败,请稍候再试!'));
    else
      return Json::successful('删除成功!');
  }
}
