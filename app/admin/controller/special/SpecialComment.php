<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2021-11-03 10:15:45
 * @Last Modified time: 2022-03-23 20:43:48
 */
namespace app\admin\controller\special;

use think\Request;
use think\facade\Route as Url;
use app\admin\model\user\User;
use app\admin\controller\AuthController;
use app\admin\model\special\{SpecialComment as SpecialCommentModel};
use crmeb\services\{FormBuilder as Form, JsonService as Json, UtilService as Util};

/**
 * 课程-留言评论、素材评论
 * Class SpecialComment
 * @package app\admin\controller\special
 */
class SpecialComment extends AuthController
{
    /**
     * 显示资源列表
     *
     * @return \think\Response
     */
    public function index()
    {
        $this->assign([
            'year' => get_month(),
            'status' => $this->request->param('status', ''),
            'commentCount' => SpecialCommentModel::CommentCount(),
        ]);
        return $this->fetch();
    }

        /**
     * 获取user表
     *
     * @return json
     */
    public function messageList($id = 0)
    {
        $this->assign('id', (int)$id);
        return $this->fetch();
    }



    /**
     * 获取评论列表
     * return json
     */
    public function comment_list()
    {
        $where = Util::getMore([
            ['status', ''],
            ['data', ''],
            ['keywords', $this->request->param('keywords', '')], //关键字
            ['page', 1],
            ['limit', 20],
        ]);
        return Json::successlayui(SpecialCommentModel::CommentList($where));
    }

    public function comment_info($cid = '')
    {
        if (!$cid || !($commentInfo = SpecialCommentModel::get($cid)))
            return $this->failed('订单不存在!');
        $userInfo = User::getUserInfos($commentInfo['uid']);
        $replyUserInfo = '';
        if ($commentInfo['top_id'] != 0) {
            $replyUserInfo = User::getUserInfos(SpecialCommentModel::where('special_id',$commentInfo['special_id'])->value('uid'));
        }
        $commentInfo['image'] = is_string($commentInfo['image']) ? json_decode($commentInfo['image'], true) : [];
        $commentInfo['video'] = is_string($commentInfo['video']) ? json_decode($commentInfo['video'], true) : [];
        $commentInfo['audio'] = is_string($commentInfo['audio']) ? json_decode($commentInfo['audio'], true) : [];
        $this->assign(compact('commentInfo', 'userInfo','replyUserInfo'));
        return $this->fetch();
    }

    /**
     * @return mixed
     */
    public function create()
    {
        $f = [];
        $f[] = Form::frameImageOne('message', '关联留言记录', Url::buildUrl('messageList', array('fodder' => 'message')))->icon('plus')->width('100%')->height('500px');
        $f[] = Form::frameImageOne('avatar', '用户信息', Url::buildUrl('user.user/userListes', array('fodder' => 'avatar')))->icon('plus')->width('100%')->height('500px');
        $f[] = Form::textarea('comment', '评论内容');
        $f[] = Form::hidden('uid', '');
        $f[] = Form::hidden('message_id', '');
        $f[] = Form::hidden('special_id', '');
        $f[] = Form::hidden('source_id', '');
        $form = Form::make_post_form('评论回复', $f, Url::buildUrl('save'));
        $this->assign(compact('form'));
        return $this->fetch('public/form-builder');
    }

    /**
     * 保存新建的资源
     *
     * @param \think\Request $request
     * @return \think\Response
     */
    public function save()
    {
        $data = Util::postMore([
            'message_id',
            'special_id',
            'source_id',
            'uid',
            'comment',
        ]);
        if (!$data['source_id'] && !$data['special_id']) return Json::fail('请关联课程专题或者素材');
        if (!$data['uid']) return Json::fail('请关联留言用户');
        $data['status'] = 1;
        $data['add_time'] = time();
        $res = SpecialCommentModel::create($data);
        if ($res) {
            return Json::successful('添加虚拟留言评论成功!');
        }else{
            return Json::fail('添加用户评论失败');
        }
    }


    /**
     * 设置分组
     * @param int $uid
     */
    public function set_status($id = 0)
    {
        if (!$id) return $this->failed('缺少参数');
        $field[] = Form::select('status', '状态分组')->setOptions(function (){
            $menus = [];
            $menus[0] = ['value' => 1, 'label' => '通过'];
            $menus[1] = ['value' => 3, 'label' => '屏蔽'];
            return $menus;
        })->filterable(1);
        $form = Form::make_post_form('设置状态', $field, Url::buildUrl('save_set_status', ['id' => $id]), 2);
        $this->assign(compact('form'));
        return $this->fetch('public/form-builder');
    } 


    /**
     * 设置分组
     * @param int $uid
     */
    public function save_set_status($id = 0)
    {
        if (!$id) return Json::fail('缺少参数');
        list($status) = Util::postMore([
            ['status', 0],
        ], $this->request, true);
        $ids = explode(',',$id);
        $data = $status == 1 ? ['status' => $status] : ['is_del'=>1,'del_time'=>time()];
        $res = SpecialCommentModel::whereIn('id', $ids)->update($data);
        if ($res) {
            return Json::successful('设置成功');
        } else {
            return Json::successful('设置失败');
        }
    }

    /**
     * 恢复
     * @param int $uid
     */
    public function restore($id = 0)
    {
        $data['status'] = 1;
        $data['is_del'] = 0;
        $data['del_time'] = '';
        if (!SpecialCommentModel::edit($data,$id))
            return Json::fail(SpecialCommentModel::getErrorInfo('恢复失败,请稍候再试!'));
        else
            return Json::successful('恢复成功!');
    }

    /**
     * 删除指定资源
     *
     * @param int $id
     * @return \think\Response
     */
    public function destroy($id)
    {   
        $data['status'] = 3;
        $data['is_del'] = 1;
        $data['del_time'] = time();
        if (!SpecialCommentModel::edit($data,$id))
            return Json::fail(SpecialCommentModel::getErrorInfo('删除失败,请稍候再试!'));
        else
            return Json::successful('删除成功!');
    }
}