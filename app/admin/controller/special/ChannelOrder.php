<?php

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date:   2023-03-21 17:15:48
 * @Last Modified time: 2023-03-21 17:54:31
 */
namespace app\admin\controller\special;

use think\Request;
use app\admin\controller\AuthController;
use app\admin\model\special\Special as SpecialModel;
use app\admin\model\special\ChannelsOrderPushRecords as ChannelsOrderBuyModel;
use crmeb\services\{JsonService as Json, UtilService as Util};

/**
 * 第三方渠道下单记录：控制器
 * Class ChannelOrder
 * @package app\admin\controller\special
 */
class ChannelOrder extends AuthController
{
    /**
     * 显示资源列表
     *
     * @return \think\Response
     */
    public function index()
    {   
        $this->assign('special_list', SpecialModel::where('is_del',0)->where('pay_type',1)->select());
        return $this->fetch();
    }

    /*
     *  异步获取列表
     *  @return json
     */
    public function list()
    {
        $where = Util::getMore([
            ['type', 0],
            ['special_id', 0],
            ['is_uid', 0],
            ['keywords', ''],
            ['add_time', ''],
            ['page', 1],
            ['limit', 20],
        ]);
        return Json::successlayui(ChannelsOrderBuyModel::getRecordsList($where));
    }
}