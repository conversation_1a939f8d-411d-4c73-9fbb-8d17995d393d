<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2021-06-21 11:05:04
 * @Last Modified time: 2022-11-03 17:30:38
 */
namespace app\admin\controller\special;

use app\admin\controller\AuthController;
use crmeb\services\{
    ExpressService,
    JsonService,
    MiniProgramService,
    ByteProgramService,
    WechatService,
    FormBuilder as Form,
    CacheService,
    UtilService as Util,
    JsonService as Json
};
use app\admin\model\user\{
    User, UserBill
};

use app\admin\model\market\{
    StoreOrder as StoreOrderModel,
    StoreActivity as ActivityModel,
};
/**
 * 活动订单管理
 * Class StoreOrder
 * @package app\admin\controller\market
 */
class Data extends AuthController
{
    /**
     * @return mixed
     */
    public function index()
    {
        $this->assign([
            'specialList' => ActivityModel::getActivityLists(),
            'sourceList' => ActivityModel::getActivityLists(),
        ]);
        return $this->fetch();
    }

    /**
     * 获取订单列表
     * return json
     */
    public function data_list()
    {
        $where = Util::getMore([
            ['status', ''],
            ['real_name', $this->request->param('real_name', '')],
            ['is_del', 0],
            ['data', ''],
            ['type', ''],
            ['pay_type', ''],
            ['order_type', ''],
            ['activity_id', ''],
            ['order', ''],
            ['page', 1],
            ['limit', 20],
            ['excel', 0]
        ]);

        return Json::successlayui(StoreOrderModel::OrderList($where));
    }

    public function data_info($oid = '')
    {
        if (!$oid || !($orderInfo = StoreOrderModel::get($oid)))
            return $this->failed('订单不存在!');
        $orderInfo->invite_order_id = StoreOrderModel::where('id',$orderInfo['invite_oid'])->value('order_id');
        $orderInfo->skills = is_string($orderInfo['skills']) ? json_decode($orderInfo['skills'], true) : [];
        $userInfo = User::getUserInfos($orderInfo['uid']);
        if ($userInfo['spread_uid']) {
            $spread = User::where('uid', $userInfo['spread_uid'])->value('nickname');
        } else {
            $spread = '';
        }
        $activityId = 0;
        $studentInfo =  [];
        $this->assign(compat('orderInfo','studentInfo', 'userInfo', 'spread'));
        return $this->fetch();
    }
}
