<?php

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date:   2023-05-04 15:40:37
 * @Last Modified time: 2023-05-11 16:00:11
 */

namespace app\admin\controller\special;

use think\Request;
use think\facade\Route as Url;
use app\admin\model\special\Special;
use app\admin\controller\AuthController;
use crmeb\services\upload\storage\OssHelper;
use app\admin\model\special\SpecialLive as SpecialLiveModel;
use app\admin\model\special\SpecialActivity as SpecialActivityModel;
use crmeb\services\{FormBuilder as Form, JsonService as Json, UtilService as Util};

/**
 * 第三方渠道课程商品配置：控制器
 * Class Live
 * @package app\admin\controller\special
 */
class Live extends AuthController
{
    /**
     * 显示资源列表
     *
     * @return \think\Response
     */
    public function index()
    {   
        return $this->fetch();
    }

    /*
     *  异步获取列表
     *  @return json
     */
    public function live_list()
    {
        $where = Util::getMore([
            ['type', 1],
            ['title', ''],
            ['keywords', ''],
            ['add_time', ''],
            ['page', 1],
            ['limit', 20],
        ]);
        return Json::successlayui(SpecialActivityModel::getActivityList($where));
        // return Json::successlayui(SpecialLiveModel::getLiveList($where));
    }

    /*
     *  异步获取列表
     *  @return json
     */
    public function get_replay_list()
    {
        $where = Util::getMore([
            ['room_id', 0],
            ['page', 0],
            ['limit', 100],
        ]);
        return Json::successlayui(SpecialActivityModel::getLiveReplayList($where));
    }

    

    /**
     * 显示创建资源表单页.
     *
     * @return \think\Response
     */
    public function create()
    {
        $field = [
            Form::frameImageOne('image', '选择课程', Url::buildUrl('6GqvmHa8HRGHoQEQ/special.live/select_special', array('fodder' => 'image')))->icon('plus')->width('100%')->height('500px'),
            Form::input('title', '直播房间名'),
            Form::input('live_id', '直播房间号'),
            Form::dateTimeRange('live_start_and_end_time', '直播时间'),
            Form::input('info', '文字提示语'),
            Form::radio('status', '状态', 0)->options([['label' => '直播预告', 'value' => 0],['label' => '开始', 'value' => 1],['label' => '关闭', 'value' => 2]]),
            Form::hidden('special_id', 0),
        ];
        $form = Form::make_post_form('添加-关联直播房间', $field, Url::buildUrl('save'), 2);
        $this->assign(compact('form'));
        return $this->fetch('public/form-builder');
    }



    /**
     * 显示编辑资源表单页.
     *
     * @param int $id
     * @return \think\Response
     */
    public function edit($id)
    {
        $c = SpecialLiveModel::get($id);
        if (!$c) return Json::fail('数据不存在!');
        $special_image = Special::where('id',$c['special_id'])->value('image');
        $field = [
            Form::frameImageOne('image', '选择课程', Url::buildUrl('6GqvmHa8HRGHoQEQ/special.live/select_special', array('fodder' => 'image')),$special_image)->icon('plus')->width('100%')->height('500px'),
            Form::input('title', '直播房间名',$c->getData('title')),
            Form::input('live_id', '直播房间号',$c->getData('live_id')),
            Form::dateTimeRange('live_start_and_end_time', '活动时间', date("Y-m-d H:i:s", $c->getData('live_time')), date("Y-m-d H:i:s", $c->getData('end_live_time'))),//->format("yyyy-MM-dd HH:mm:ss");
            Form::input('info', '文字提示',$c->getData('info')),
            Form::radio('status', '状态',$c->getData('status'))->options([['label' => '直播预告', 'value' => 0],['label' => '开始', 'value' => 1],['label' => '关闭', 'value' => 2]]),
            Form::hidden('special_id',$c->getData('special_id')),
        ];
        $form = Form::make_post_form('编辑-关联直播房间', $field, Url::buildUrl('save', array('id' => $id)), 2);
        $this->assign(compact('form'));
        return $this->fetch('public/form-builder');
    }

    /**
     * 保存新建的资源
     *
     * @param \think\Request $request
     * @return \think\Response
     */
    public function save($id = 0)
    {
        $data = Util::postMore([
            'title',
            'live_id',
            'info',
            'live_start_and_end_time',
            ['special_id', 0],
            ['status', 0],
        ]);
        if ($data['special_id'] == 0) return Json::fail('请选择关联课程');
        if ($data['title'] == '') return Json::fail('请输入直播房间名');
        if ($data['live_id'] == '') return Json::fail('请输入直播房间ID');
        $live_start_and_end_time = $data['live_start_and_end_time'];
        if (SpecialLiveModel::be(['live_id'=>$data['live_id'],'special_id'=>$data['special_id'],'status'=>$data['status']])) return Json::fail('已存在相同直播配置');
        if (count($live_start_and_end_time) < 1) return Json::fail('请选直播生效起止时间');
        if (!$live_start_and_end_time[0]) return Json::fail('请选直播生效起止时间');
        if (!$live_start_and_end_time[1]) return Json::fail('请选直播生效起止时间');
        $data['live_time'] = strtotime($live_start_and_end_time[0]);
        $data['end_live_time'] = strtotime($live_start_and_end_time[1]);
        unset($data['live_start_and_end_time']);
        if ($id) {
            SpecialLiveModel::edit($data, $id);
            return Json::successful('编辑成功!');
        } else {
            $data['add_time'] = time();
            $res = SpecialLiveModel::create($data);
            return Json::successful('添加成功!');
        }
    }

    /**
     * 删除指定资源
     *
     * @param int $id
     * @return \think\Response
     */
    public function delete($id)
    {
        $data['is_del'] = 1;
        if (!SpecialLiveModel::edit($data,$id))
            return Json::fail(SpecialLiveModel::getErrorInfo('删除失败,请稍候再试!'));
        else
            return Json::successful('删除成功!');
    }

    /**
     * 删除指定资源
     *
     * @param int $id
     * @return \think\Response
     */
    public function download($id,$url)
    {
        $c = SpecialActivityModel::get($id);
        if (!$c) return Json::fail('直播不存在!');
        $mime = 'application/force-download';
        $fileName = $c['title'].'-直播回放视频-'.time().'.mp4';
        header('Content-Type: '.$mime);
        header('Content-Type: video/mp4'); 
        header("Content-disposition: attachment; filename=\"" . $fileName . "\""); 
        readfile($url);
    }


    /**
     * 选择课程
     * @param int $id
     */
    public function replay_list()
    {
        $this->assign([
            'id' => $this->request->get('id', ''),
            'live_id' => $this->request->get('live_id', ''),
        ]);
        return $this->fetch();
    }

    /**
     * 选择课程
     * @param int $id
     */
    public function select_special()
    {
        return $this->fetch();
    }
}