<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2021-09-23 15:05:09
 * @Last Modified time: 2022-04-20 10:39:12
 */

namespace app\admin\controller\special;

use think\Request;
use think\facade\Route as Url;
use app\admin\controller\AuthController;
use app\admin\model\special\SpecialTool as SpecialToolModel;
use crmeb\services\{FormBuilder as Form, JsonService as Json, UtilService as Util};

/**
 * 课程工具控制器
 * Class Tool
 * @package app\admin\controller\special
 */
class Tool extends AuthController
{
  /**
   * 显示资源列表
   *
   * @return \think\Response
   */
  public function index()
  {
    return $this->fetch();
  }

  /*
     *  异步获取工具列表
     *  @return json
     */
  public function tool_list()
  {
    $where = Util::getMore([
      ['is_show', ''],
      ['name', ''],
      ['page', 1],
      ['limit', 20],
      ['order', '']
    ]);

    return Json::successlayui(SpecialToolModel::List($where));
  }

  /**
   * 设置工具显示|隐藏
   * @param string $is_show
   * @param string $id
   */
  public function set_show($is_show = '', $id = '')
  {
    ($is_show == '' || $id == '') && Json::fail('缺少参数');
    if (SpecialToolModel::setShow($id, (int)$is_show)) {
      return Json::successful($is_show == 1 ? '显示成功' : '隐藏成功');
    } else {
      return Json::fail(SpecialToolModel::getErrorInfo($is_show == 1 ? '显示失败' : '隐藏失败'));
    }
  }

  /**
   * 快速编辑
   * @param string $field
   * @param string $id
   * @param string $value
   */
  public function set_tool($field = '', $id = '', $value = '')
  {
    $field == '' || $id == '' || $value == '' && Json::fail('缺少参数');
    if (SpecialToolModel::where('id', $id)->update([$field => $value]))
      return Json::successful('保存成功');
    else
      return Json::fail('保存失败');
  }

  /**
   * 显示创建资源表单页.
   *
   * @return \think\Response
   */
  public function create()
  {
    $field = [
      Form::input('title', '工具名'),
      Form::radio('link_type', '跳转方式', 1)->options([['value' => 1, 'label' => 'H5'], ['value' => 2, 'label' => '游戏']]),
      Form::input('link', '跳转链接'),
      Form::number('sort', '排序'),
      Form::radio('is_show', '状态', 1)->options([['label' => '显示', 'value' => 1], ['label' => '隐藏', 'value' => 0]])
    ];
    $form = Form::make_post_form('添加工具', $field, Url::buildUrl('save'), 2);
    $this->assign(compact('form'));
    return $this->fetch('public/form-builder');
  }

  /**
   * 保存新建的资源
   *
   * @param \think\Request $request
   * @return \think\Response
   */
  public function save(Request $request)
  {
    $data = Util::postMore([
      'title',
      'link_type',
      'link',
      'sort',
      ['is_show', 1]
    ], $request);
    if (!$data['title']) return Json::fail('请输入工具名称');
    if (!$data['link']) return Json::fail('请输入跳转链接');
    if ($data['sort'] < 0) $data['sort'] = 0;
    $data['type'] = 1;
    $data['add_time'] = time();
    SpecialToolModel::create($data);
    return Json::successful('添加工具成功!');
  }

  /**
   * 显示编辑资源表单页.
   *
   * @param int $id
   * @return \think\Response
   */
  public function edit($id)
  {
    $c = SpecialToolModel::get($id);
    if (!$c) return Json::fail('数据不存在!');
    $field = [
      Form::input('title', '工具名', $c->getData('title')),
      Form::radio('link_type', '跳转方式', $c->getData('link_type'))->options([['value' => 1, 'label' => 'H5'], ['value' => 2, 'label' => '游戏']]),
      Form::input('link', '跳转链接', $c->getData('link')),
      Form::number('sort', '排序', $c->getData('sort')),
      Form::radio('is_show', '状态', $c->getData('is_show'))->options([['label' => '显示', 'value' => 1], ['label' => '隐藏', 'value' => 0]])
    ];
    $form = Form::make_post_form('编辑工具', $field, Url::buildUrl('update', array('id' => $id)), 2);

    $this->assign(compact('form'));
    return $this->fetch('public/form-builder');
  }

  /**
   * 保存更新的资源
   *
   * @param \think\Request $request
   * @param int $id
   * @return \think\Response
   */
  public function update(Request $request, $id)
  {
    $data = Util::postMore([
      'title',
      'link_type',
      'link',
      'sort',
      ['is_show', 1]
    ], $request);
    if (!$data['title']) return Json::fail('请输入工具名称');
    if (!$data['link']) return Json::fail('请输入跳转链接');
    if ($data['sort'] < 0) $data['sort'] = 0;
    SpecialToolModel::edit($data, $id);
    return Json::successful('修改成功!');
  }

  /**
   * 删除指定资源
   *
   * @param int $id
   * @return \think\Response
   */
  public function delete($id)
  {
    if (!SpecialToolModel::delTool($id))
      return Json::fail(SpecialToolModel::getErrorInfo('删除失败,请稍候再试!'));
    else
      return Json::successful('删除成功!');
  }
}
