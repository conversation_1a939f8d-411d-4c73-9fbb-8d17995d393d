<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2021-09-23 16:35:20
 */

namespace app\admin\controller\special;

use think\Request;
use think\facade\Route as Url;
use app\admin\model\system\Recommend;
use crmeb\services\SystemConfigService;
use app\admin\model\store\StoreProduct;
use app\admin\controller\AuthController;
use app\admin\model\special\SpecialTask;
use app\admin\model\special\SpecialSource;
use app\admin\model\special\SpecialCourse;
use app\admin\model\special\SpecialContent;
use crmeb\services\upload\storage\OssHelper;
use app\admin\model\system\RecommendRelation;
use app\admin\model\special\SpecialActivity;
use app\admin\model\special\SpecialTaskTodoList;
use app\admin\model\special\SpecialSourceCategory;
use app\admin\model\special\SpecialAssociated;
use app\admin\model\special\SpecialTaskAssociated;
use app\admin\model\special\Special as SpecialModel;
use app\admin\model\special\SpecialActivityAssociated;
use app\admin\model\market\StoreCategory as CategoryModel;
use app\admin\model\special\SpecialLabel as SpecialLabelModel;
use app\admin\model\special\SpecialSourceLabelData as SpecialSourceLabelDataModel;
use app\admin\model\system\SystemPollster as SystemPollsterModel;
use app\admin\model\special\SpecialTaskCate as SpecialTaskCateModel;
use app\admin\model\special\SpecialSubject as SpecialSubjectCategoryModel;
use app\admin\model\special\SpecialTaskCategory as SpecialTaskCategoryModel;
use crmeb\services\{FormBuilder as Form, JsonService as Json, UtilService as Util};
use app\admin\model\special\SpecialMaterialSortList as SpecialMaterialSortListModel;
use  app\admin\model\store\StoreProductAttrValue;
use  app\admin\model\store\StoreProductAttrResult;

/**
 * 课程专题控制器
 * Class SpecialType
 * @package app\admin\controller\special
 */
class SpecialType extends AuthController
{
  /**
   * 专题列表模板渲染
   *
   * @return \think\Response
   */
  public function index($subject_id = 0)
  {
    $special_type = $this->request->param('special_type');
    $subjectlist = SpecialSubjectCategoryModel::specialCategoryAll();
    $this->assign([
      'pid' => 0,
      'cate' => SpecialSubjectCategoryModel::getTierList(null, 0),
      'activity_type' => $this->request->param('activity_type', 1),
      'subject_id' => $subject_id,
      'special_title' => self::SPECIAL_TYPE[$special_type],
      'special_type' => $special_type,
      'subject_list' => $subjectlist,
    ]);
    $template = $this->switch_template($special_type, request()->action());
    // dd($template);
    if (!$template) $template = "";
    return $this->fetch($template);
  }

  /**
   * 素材页面渲染
   * @return
   * */
  public function source_index($coures_id = 0)
  {
    $special_type = $this->request->param('special_type');
    $this->assign('coures_id', $coures_id);
    $this->assign('special_title', SPECIAL_TYPE[$special_type]);
    $this->assign('special_type', $special_type); //图文专题
    $this->assign('activity_type', $this->request->param('activity_type', 1));
    $this->assign('special_id', SpecialCourse::where('id', $coures_id)->value('special_id'));
    $this->assign('specialList', SpecialModel::PreWhere()->field(['id', 'title'])->select());
    $template = $this->switch_template($special_type, request()->action());
    if (!$template) $template = "";
    return $this->fetch($template);
  }


  /**
   * 素材页面渲染
   * @return
   * */
  public function tool_index($coures_id = 0)
  {
    $this->assign(['category' => SpecialTaskCategoryModel::taskCategoryAll()]);
    return $this->fetch('special/task/tool_index');
  }



  /**
   * 素材管理
   */
  public function sources_index()
  {
    $this->assign(['category' => SpecialTaskCategoryModel::taskCategoryAll()]);
    return $this->fetch('special/task/source_index');
  }

  /**
   * 统一添加素材
   */
  public function addSources($id = 0)
  {
    if ($id) {
      $task = SpecialTask::where('id', $id)->find();
      $task->detail = htmlspecialchars_decode($task->detail);
      if ($task['type'] != 1) {
        $task->content = $task->link ? ($task->content ? htmlspecialchars_decode($task->content) : '') : '';
      } else {
        $task->content = $task->content != "" ?  htmlspecialchars_decode($task->content) : '';
      }
      $task->image = $task->image;
      $this->assign('special', $task);
    }
    $alicloud_account_id = SystemConfigService::get('alicloud_account_id'); //阿里云账号ID
    $configuration_item_region = SystemConfigService::get('configuration_item_region'); //配置项region
    $demand_switch = SystemConfigService::get('demand_switch'); //视频点播开关
    $this->assign('alicloud_account_id', $alicloud_account_id);
    $this->assign('configuration_item_region', $configuration_item_region);
    $this->assign('demand_switch', $demand_switch);
    $this->assign('id', $id);
    return $this->fetch('special/task/add_source');
  }

  /**
   * 添加和修改素材
   * @param int $id 修改
   * @return json
   * */
  public function save_source($id = 0)
  {
    $special_type = $this->request->param('special_type');
    if (!$special_type) return Json::fail('专题类型参数缺失');
    $data = Util::postMore([
      ['source_type', 1],
      ['title', ''],
      ['recommend_navigator_title', ''],
      ['image', ''],
      ['content', ''],
      ['content_json', ''],
      ['detail', ''],
      ['image', ''],
      ['audio_link', ''],
      ['video_link', ''],
      ['video_data', ''],
      ['videoId', ''],
      ['file_type', ''],
      ['file_name', ''],
      ['label', []],
      ['tags', []],
      ['attrs', []],
      ['check_product_sure', []],
      ['check_special_sure', []],
      ['check_source_sure', []],
      ['sort', 0],
      // ['pid', 0],
      ['special_category_id', ''],
      ['paid_special_id', 0],
      ['is_snapshot', false],
      ['is_show', 1],
      ['is_edit_product', 0], //是否更改商品
      ['is_edit_special', 0], //是否更改课程
      ['is_edit_source', 0], //是否更改学习资料
      ['is_edit_todo', 0], //是否更改学习节点
      ['is_node', 0], //是否更改学习节点
      ['add_time', ''],
      // 新增音视频处理相关字段
      ['original_file_path', ''],
      ['preview_start_time', 0],
      ['preview_end_time', 0],
      ['preview_duration', 30],
      ['enable_preprocessing', 0],
      ['file_duration', 0],
      ['processing_status', 0], // 0=未处理, 2=处理中, 1=处理完成, 3=处理失败
      ['preview_file_path', ''],
      ['processing_log', ''],
    ]);
    if (!$data['title']) return Json::fail('请输入课程标题');
    if ($data['source_type'] == 1) {
      if (!$data['image']) return Json::fail('请上传封面图');
    }
    if (!$data['special_category_id']) return Json::fail('请选择课程大分类');
    // if (!$data['pid']) return Json::fail('请选择素材分类');
    $data['type'] = $data['source_type'];
    $data['pid'] = $data['source_type'];
    if ($data['type'] == 2) {
      $data['link'] = $data['audio_link'];
    }
    if ($data['type'] == 3) {
      //判断是否封面，如果没有用阿里云oss 进行截取一帧 进行首页展示
      if ($data['is_snapshot']) {
        $data['image'] = OssHelper::videoCover($data['video_link'], $data['video_data']);
      }
      $data['link'] = $data['video_link'];
    }
    $cate_id =  explode(",", $data['special_category_id']);
    $label_group = $data['label'];
    $data['label'] = implode(',', $data['label']);
    $data['tags'] = implode(',', $data['tags']);
    if (isset($data['check_product_sure']) && is_array($data['check_product_sure'])) {
      $productResult = $data['check_product_sure'];
    }
    if (isset($data['check_special_sure']) && is_array($data['check_special_sure'])) {
      $specialResult = $data['check_special_sure'];
    }
    if (isset($data['check_source_sure']) && is_array($data['check_source_sure'])) {
      $specialSourceResult = $data['check_source_sure'];
    }

    if (isset($data['attrs']) && is_array($data['attrs'])) {
      $toDoResult = $data['attrs'];
    }
    $specialEggsResult = [];
    $is_edit_product = $data['is_edit_product'];
    $is_edit_special = $data['is_edit_special'];
    $is_edit_source = $data['is_edit_source'];
    $is_edit_todo = $data['is_node'];


    $data['add_time'] = strtotime($data['add_time']);
    unset($data['video_data']);
    unset($data['audio_link']);
    unset($data['video_link']);
    unset($data['source_type']);
    unset($data['is_snapshot']);
    unset($data['attrs']);
    unset($data['is_node']);
    unset($data['special_category_id']);
    // 格式化 编辑状态 
    unset($data['is_edit_product'], $data['is_edit_special'], $data['is_edit_source'], $data['is_edit_todo']);
    if (isset($data['check_product_sure'])) unset($data['check_product_sure']);
    if (isset($data['check_special_sure'])) unset($data['check_special_sure']);
    if (isset($data['check_source_sure'])) unset($data['check_source_sure']);
    if ($id) {
      unset($data['is_show']);
      $task_res = SpecialTask::where('id', $id)->find();
      $task_cate_id = SpecialTaskCateModel::where('task_id', $id)->column('cate_id');
      $offset =  count($cate_id) == count($task_cate_id)  ?  array_diff($task_cate_id, $cate_id) : [1];
      if (!empty($offset) || $data['paid_special_id'] != $task_res['paid_special_id']) {
        $task_cate_res = SpecialTaskCateModel::where('task_id', $id)->delete();
        $task_sort_res = SpecialMaterialSortListModel::where('task_id', $id)->where('type', 2)->delete();
        foreach ($cate_id as $cid) {
          if (SpecialTaskCateModel::insert(['task_id' => $id, 'cate_id' => $cid, 'add_time' => time()])) {
            $isShow = $data['paid_special_id'] == 0 ? 1 : 0;
            SpecialMaterialSortListModel::addSpecialSort(2, $cid, $id, $isShow);
          }
        }
      }
      $save_label = SpecialSourceLabelDataModel::saveSpecialSourceLabelData($label_group, 0, $id);
      if (!$save_label) return Json::fail('添加失败');
      if ($is_edit_product == 1) {
        SpecialTaskAssociated::addRelatedProducts(2, 0, $productResult, $id); //关联商品
      }
      if ($is_edit_special == 1) {
        SpecialTaskAssociated::addRelatedSpecial(2, 0, $specialResult, $id); //关联课程
      }
      if ($is_edit_source == 1) {
        SpecialTaskAssociated::addRelatedSpecialSource(2, 0, $specialSourceResult, $id); //关联学习资料
      }
      // if ($is_edit_todo == 1) {
      //   SpecialTaskTodoList::addRelatedResult(2, $toDoResult, $id);
      // }
      SpecialTask::update($data, ['id' => $id]);
      
      // 如果是本地上传且启用预处理
      // if ($data['original_file_path'] && $data['enable_preprocessing']) {
        $this->triggerAsyncProcessing($id);
        return Json::successful('修改成功，正在后台处理文件');
      // }
      
      return Json::successful('修改成功');
    } else {
      // $data['add_time'] = time();
      $task_id = SpecialTask::insertGetId($data);
      //分类
      foreach ($cate_id as $cid) {
        if (SpecialTaskCateModel::insert(['task_id' => $task_id, 'cate_id' => $cid, 'add_time' => time()])) {
          $isShow = $data['paid_special_id'] == 0 ? 1 : 0;
          SpecialMaterialSortListModel::addSpecialSort(2, $cid, $task_id);
        }
      }
      if (count($label_group) >= 1) {
        SpecialSourceLabelDataModel::saveSpecialSourceLabelData($label_group, 0, $task_id);
      }
      //关联商品和课程合集和彩蛋的课程合集
      SpecialTaskAssociated::addRelatedProducts(1, 0, $productResult, $id);
      SpecialTaskAssociated::addRelatedSpecial(1, 0, $specialResult, $id);
      SpecialTaskAssociated::addRelatedSpecialSource(1, 0, $specialSourceResult, $id);
      // SpecialTaskTodoList::addRelatedResult(1, $toDoResult, $id);
      
      if ($task_id) {
        // 如果是本地上传且启用预处理
        if ($data['original_file_path'] && $data['enable_preprocessing']) {
          $this->triggerAsyncProcessing($task_id);
          return Json::successful('添加成功，正在后台处理文件');
        }
        return Json::successful('添加成功');
      } else {
        return Json::fail('添加失败');
      }
    }
  }

  /**
   * 本地音视频文件上传
   */
  public function upload_local_media()
  {
    $file = request()->file('file');
    $type = request()->post('type', 1); // 2=音频, 3=视频
    
    if (!$file) {
      return Json::fail('请选择文件');
    }
    
    // 验证文件类型
    $allowedTypes = [
      2 => ['mp3', 'wav', 'aac', 'm4a', 'flac'],
      3 => ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv']
    ];
    
    $extension = strtolower($file->getOriginalExtension());
    if (!in_array($extension, $allowedTypes[$type] ?? [])) {
      return Json::fail('不支持的文件格式');
    }
    
    // 验证文件大小 (音频最大100MB，视频最大500MB)
    $maxSize = $type == 2 ? 100 * 1024 * 1024 : 500 * 1024 * 1024;
    if ($file->getSize() > $maxSize) {
      return Json::fail('文件大小超出限制');
    }
    
    try {
      // 创建存储目录
      $uploadPath = 'uploads/media/' . date('Y/m/d');
      $fullPath = public_path($uploadPath);
      if (!is_dir($fullPath)) {
        mkdir($fullPath, 0755, true);
      }
      
      // 生成唯一文件名
      $fileName = uniqid() . '_' . time() . '.' . $extension;
      $filePath = $uploadPath . '/' . $fileName;
      
      // 移动文件
      $file->move($fullPath, $fileName);
      
      return Json::successful('上传成功', [
        'file_path' => $filePath,
        'file_name' => $file->getOriginalName(),
        'file_size' => $file->getSize()
      ]);
      
    } catch (\Exception $e) {
      return Json::fail('上传失败: ' . $e->getMessage());
    }
  }

  /**
   * 检查处理状态
   */
  public function check_processing_status($id)
  {
    $task = SpecialTask::find($id);
    if (!$task) {
      return Json::fail('素材不存在');
    }
    
    return Json::successful('获取成功', [
      'processing_status' => $task->processing_status,
      'link' => $task->link,
      'preview_file_path' => $task->preview_file_path,
      'processing_log' => $task->processing_log
    ]);
  }

  /**
   * 异步处理媒体文件
   */
  public function process_media_async()
  {
    $taskId = request()->post('task_id');
    if (!$taskId) {
      return Json::fail('缺少任务ID');
    }
    
    // 设置长时间执行
    set_time_limit(0);
    ignore_user_abort(true);
    
    try {
      $this->processMediaFile($taskId);
      return Json::successful('处理完成');
    } catch (\Exception $e) {
      // 更新处理状态为失败
      SpecialTask::where('id', $taskId)->update([
        'processing_status' => 3,
        'processing_log' => $e->getMessage()
      ]);
      return Json::fail('处理失败: ' . $e->getMessage());
    }
  }

  /**
   * 触发异步处理
   * @param int $taskId
   * @return bool
   */
  private function triggerAsyncProcessing($taskId)
  {
    try {
      // 检查队列服务是否可用
      if (!class_exists('\think\facade\Queue')) {
        \think\facade\Log::error('队列服务类不存在，启用同步处理模式');
        return $this->fallbackToSyncProcessing($taskId);
      }
      
      // 尝试推送到队列
      try {
        \think\facade\Queue::push('app\job\MediaProcessingJob', [
          'task_id' => $taskId,
          'created_at' => time(),
          'priority' => 'normal'
        ]);
        
        \think\facade\Log::info("任务 {$taskId} 已成功推送到异步队列");
        
        // 更新任务状态为队列中
        SpecialTask::updateProcessingStatus($taskId, SpecialTask::PROCESSING_STATUS_QUEUED, '任务已加入处理队列');
        
        return true;
        
      } catch (\Exception $queueException) {
        \think\facade\Log::error("队列推送失败: " . $queueException->getMessage());
        
        // 队列服务异常，降级到同步处理
        \think\facade\Log::warning("队列服务异常，降级到同步处理模式");
        return $this->fallbackToSyncProcessing($taskId);
      }
      
    } catch (\Exception $e) {
      \think\facade\Log::error("异步处理触发失败: " . $e->getMessage());
      
      // 更新任务状态为失败
      SpecialTask::updateProcessingStatus($taskId, SpecialTask::PROCESSING_STATUS_FAILED, '异步处理启动失败: ' . $e->getMessage());
      
      return false;
    }
  }
  
  /**
   * 降级到同步处理
   * @param int $taskId
   * @return bool
   */
  private function fallbackToSyncProcessing($taskId)
  {
    try {
      \think\facade\Log::info("任务 {$taskId} 开始同步处理");
      
      // 更新状态为处理中
      SpecialTask::updateProcessingStatus($taskId, SpecialTask::PROCESSING_STATUS_PROCESSING, '开始同步处理');
      
      // 直接调用处理逻辑
      $job = new \app\job\MediaProcessingJob();
      $result = $job->fire(['task_id' => $taskId]);
      
      if ($result) {
        \think\facade\Log::info("任务 {$taskId} 同步处理完成");
        return true;
      } else {
        \think\facade\Log::error("任务 {$taskId} 同步处理失败");
        return false;
      }
      
    } catch (\Exception $e) {
      \think\facade\Log::error("同步处理失败: " . $e->getMessage());
      SpecialTask::updateProcessingStatus($taskId, SpecialTask::PROCESSING_STATUS_FAILED, '同步处理失败: ' . $e->getMessage());
      return false;
    }
  }

  /**
   * 获取视频封面图
   *
   * @param \think\Request $request
   * @param int $id
   * @return \think\Response
   */
  public function source_video_snapshot()
  {
    $data = Util::postMore([
      ['link', ''],
      ['time', ''],
      ['width', ''],
      ['height', ''],
    ]);

    $result = OssHelper::normalVideo($data['link'], 3, $data['time'], $data['width'], $data['height']);
    if ($result)
      return Json::successful('ok', $result);
    else
      return Json::fail('添加失败');
  }

  /**
   * 编辑和新增
   *
   * @return json
   */
  public function save_special($id = 0)
  {
    $special_type = $this->request->param('special_type');
    if (!$special_type || !is_numeric($special_type)) return Json::fail('专题类型参数缺失');
    $data = Util::postMore([
      ['title', ''],
      ['recommend_navigator_title', ''],
      ['fixed_sidebar_button_text', ''],
      ['abstract', ''],
      ['phrase', ''],
      ['keyword', ''],
      ['subject_id', 0],
      ['lecturer_id', 0],
      ['fake_sales', 0],
      ['browse_count', 0],
      ['label', []],
      ['image', ''],
      ['banner', []],
      ['poster_image', ''],
      ['service_status', 0],
      ['service_text', ''],
      ['service_button_text', ''],
      ['service_code', ''],
      ['byte_service_code', ''],
      ['alert_text', ''],
      ['live_info', ''],
      ['notice_time', ''],
      ['money', 0],
      ['beta_money', 0],
      ['content', ''],
      ['byte_content', ''],
      ['common_problem', ''],
      ['work_submit_explain', ''],
      ['is_pink', 0],
      ['pink_money', 0],
      ['pink_number', 0],
      ['pink_time', 0],
      ['pink_strar_time', ''],
      ['pink_end_time', ''],
      ['subjectIds', ''],
      ['is_fake_pink', 0],
      ['is_live_info', 0],
      ['is_easter_eggs', 0],
      ['get_condition', 0], //
      ['sort', 0],
      ['fake_pink_number', 0],
      ['member_money', 0],
      ['member_pay_type', 0],
      ['pay_type', 0], //支付方式，是否需要付费：免费、付费、密码
      ['is_multi_period', 0], //
      ['is_problems', 0], //
      ['is_live', 0], //
      ['is_beta_test', 0], //
      ['is_edit_source', 0], //
      ['is_edit_label', 0], //
      ['is_edit_product', 0], //
      ['is_edit_special', 0], //
      ['is_edit_special_eggs', 0], //
      ['is_edit_tool', 0], //
      ['is_activity', 0], //
      ['is_pollster', 0], //
      ['pollster_id', 0], //
      ['total_period_number', 0], //支付方式：免费、付费、密码
      ['check_product_sure', []],
      ['check_live_sure', []],
      ['check_special_sure', []],
      ['check_tool_sure', []],
      ['check_special_eggs_sure', []],
      ['check_source_sure_sub', []],
    ]);
    $data['type'] = $special_type;
    if (!$data['subject_id']) return Json::fail('请选择分类');
    $data['subject_id'] = $data['subject_id'][0];
    if (!$data['title']) return Json::fail('请输入专题标题');
    if (!$data['abstract']) return Json::fail('请输入专题简介');
    // if (!$data['phrase']) return Json::fail('请输入专题短语');
    // if (!$data['keyword']) return Json::fail('请输入专题关键字');
    if (!$data['image']) return Json::fail('请上传专题封面图');
    if (!count($data['banner'])) return Json::fail('请上传专题banner图');
    if (!$data['poster_image']) return Json::fail('请上传推广海报');
    if (!$data['service_code']) return Json::fail('请上传客服二维码');
    // if (!$data['byte_service_code']) return Json::fail('请上传抖音客服二维码');
    // if (!count($data['label'])) return Json::fail('请填写标签');
    if (!$data['content']) return Json::fail('请填写专题详情内容！');
    // if (!$data['byte_content']) return Json::fail('请填写字节专题详情内容！');
    if ($data['is_problems'] == 1) {
      if (!$data['common_problem']) return Json::fail('请填写常见问题详情内容！');
    }
    if ($data['is_pollster'] == 1) {
      if (!$data['pollster_id']) return Json::fail('请选择关联的调查问卷！');
    }
    if ($data['is_multi_period'] == 1) {
      if (!$data['check_source_sure_sub']) return Json::fail('请选择素材');
      if ($data['total_period_number'] == 0) {
        return Json::fail('请填写正确的素材总期数');
      }
      // if ($data['check_source_sure']) {
      //   if ($data['total_period_number'] < count($data['check_source_sure'])) {
      //         Json::fail('素材总期数小于已选择素材数');
      //     }
      // }
    }
    if ($data['is_live_info'] == 1) {
      if (!$data['live_info']) return Json::fail('请填写跑马灯提示语');
      if (!$data['notice_time']) return Json::fail('请填写预告直播时间文字');
    }
    if ($data['pay_type'] == 1 && ($data['money'] == '' || $data['money'] == 0.00 || $data['money'] < 0)) return Json::fail('购买金额未填写或者金额非法');
    // if ($data['member_pay_type'] == MEMBER_PAY_MONEY && ($data['member_money'] == '' || $data['member_money'] == 0.00 || $data['member_money'] < 0)) return Json::fail('会员购买金额未填写或金额非法');
    if ($data['pay_type'] == 0) {
      $data['money'] = 0;
    }
    $label_group = $data['label'];
    $content = htmlspecialchars($data['content']);
    $byte_content = htmlspecialchars($data['byte_content']);
    $common_problem = htmlspecialchars($data['common_problem']);
    $data['label'] = implode(',', $data['label']);
    $data['banner'] = json_encode($data['banner']);
    if (isset($data['check_product_sure']) && is_array($data['check_product_sure'])) {
      $productResult = $data['check_product_sure'];
    }
    if (isset($data['check_special_sure']) && is_array($data['check_special_sure'])) {
      $specialResult = $data['check_special_sure'];
    }
    if (isset($data['check_tool_sure']) && is_array($data['check_tool_sure'])) {
      $toolResult = $data['check_tool_sure'];
    }
    if (isset($data['check_special_eggs_sure']) && is_array($data['check_special_eggs_sure'])) {
      $specialEggsResult = $data['check_special_eggs_sure'];
    }
    SpecialModel::beginTrans();
    try {
      $liveCheckList = $data['check_live_sure'];
      $sourceCheckList = $data['check_source_sure_sub'];
      $toolResult = $data['check_source_sure_sub'];
      $is_edit_source = $data['is_edit_source'];
      $is_edit_label = $data['is_edit_label'];
      $is_edit_product = $data['is_edit_product'];
      $is_edit_special = $data['is_edit_special'];
      $is_edit_tool = $data['is_edit_tool'];
      $is_edit_special_eggs = $data['is_edit_special_eggs'];
      unset($data['subjectIds']);
      unset($data['content']);
      unset($data['byte_content']);
      unset($data['common_problem']);
      unset($data['is_edit_source'], $data['is_edit_label'], $data['is_edit_product'], $data['is_edit_special'], $data['is_edit_special_eggs']);
      unset($data['check_source_sure_sub']);
      if (isset($data['check_live_sure'])) unset($data['check_live_sure']);
      if (isset($data['check_product_sure'])) unset($data['check_product_sure']);
      if (isset($data['check_special_sure'])) unset($data['check_special_sure']);
      if (isset($data['check_tool_sure'])) unset($data['check_tool_sure']);
      if (isset($data['check_special_eggs_sure'])) unset($data['check_special_eggs_sure']);
      if ($id) {
        SpecialModel::update($data, ['id' => $id]);
        SpecialContent::update(['content' => $content, 'byte_content' => $byte_content, 'common_problem' => $common_problem], ['special_id' => $id]);
        if ($is_edit_product == 1) {
          $save_product = SpecialAssociated::addRelatedProducts(2, 1, $productResult, $id); //关联商品
          if (!$save_product) return Json::fail('添加失败');
        }
        if ($is_edit_special == 1) {
          $save_special = SpecialAssociated::addRelatedSpecial(2, 1, $specialResult, $id); //关联课程
          if (!$save_special) return Json::fail('添加失败');
        }
        if ($is_edit_source == 1) {
          $save_source = SpecialSource::saveSpecialSource($sourceCheckList, $id, $special_type);
          if (!$save_source) return Json::fail('添加失败');
        }
        if ($is_edit_tool == 1) {
          $save_tool = SpecialSource::saveSpecialTool($toolResult, $id, $special_type);
          if (!$save_tool) return Json::fail('添加失败');
        }
        if ($is_edit_label == 1) {
          $save_label = SpecialLabelModel::saveSpecialLabel($label_group, $id);
          if (!$save_label) return Json::fail('添加失败');
        }
        $save_sort = SpecialMaterialSortListModel::SaveSpecialSort(1, $data['subject_id'], $id, 1);
        if (!$save_sort) return Json::fail('添加失败');
        $save_live = SpecialActivityAssociated::saveLive($liveCheckList, $id);
        if (!$save_live) return Json::fail('添加失败');
        SpecialModel::commitTrans();
        return Json::successful('修改成功');
      } else {
        $data['add_time'] = time();
        $data['is_show'] = 1;
        $data['is_fake_pink'] = $data['is_pink'] ? $data['is_fake_pink'] : 0;
        $res1 = SpecialModel::insertGetId($data);
        $res2 = SpecialContent::create(['special_id' => $res1, 'content' => $content, 'byte_content' => $byte_content, 'common_problem' => $common_problem, 'add_time' => time()]);
        $res4 =  SpecialActivityAssociated::saveLive($liveCheckList, $res1);
        $res5 = true;
        $res6 = SpecialActivityAssociated::saveLive($liveCheckList, $res1);
        $res3 = SpecialSource::saveSpecialSource($sourceCheckList, $res1, $special_type);
        if (count($label_group) > 1) {
          $res4 = SpecialLabelModel::saveSpecialLabel($label_group, $res1);
        }
        //加入到分类辅助排序表
        $res5 = SpecialMaterialSortListModel::addSpecialSort(1, 1, $data['subject_id'], $res1, 1);
        if ($res1 && $res2 && $res3 && $res4 && $res5 && $res6) {
          if (!empty($productResult)) {
            SpecialAssociated::addRelatedProducts(1, 1, $productResult, $res1); //关联商品
          }
          if (!empty($specialResult)) {
            SpecialAssociated::addRelatedSpecial(1, 1, $specialResult, $res1); //关联课程
          }
          SpecialModel::commitTrans();
          return Json::successful('添加成功');
        } else {
          SpecialModel::rollbackTrans();
          return Json::fail('添加失败');
        }
      }
    } catch (\Exception $e) {
      SpecialModel::rollbackTrans();
      return Json::fail($e->getMessage());
    }
  }

  /**
   * 后台素材列表
   */
  public function get_source_list()
  {
    $where = Util::getMore([
      ['page', 1],
      ['is_show', ''],
      ['limit', 20],
      ['title', ''],
      ['pid', ''],
      ['type', ''],
      ['order', ''],
    ]);
    $special_task = SpecialTask::getTaskList($where);
    if (isset($special_task['data']) && $special_task['data']) {
      foreach ($special_task['data'] as $k => $v) {
        if ($v['type'] == 3) {
          if (!filter_var($v['image'], FILTER_VALIDATE_URL) !== false) {
            $special_task['data'][$k]['image'] =  OssHelper::normalVideo($v['image']) ?: '';
          }
        }
        $cate_id_arr = SpecialTaskCateModel::where('task_id', $v['id'])->column('cate_id');
        $category_name = SpecialSubjectCategoryModel::where('id', 'in', $cate_id_arr)->column('name');
        $special_task['data'][$k]['add_time'] = $v['add_time'] ? date('Y-m-d H:i:s', $v['add_time']) : '';
        $special_task['data'][$k]['category_name'] = $category_name;
        $special_task['data'][$k]['use'] = SpecialSource::where(['source_id' => $v['id']])->count();
        $special_task['data'][$k]['is_pay_status'] = SpecialSource::where(['source_id' => $v['id'], 'pay_status' => 1])->count();
        $special_task['data'][$k]['recommend'] = RecommendRelation::where('a.link_id', $v['id'])->where('a.type', 'in', [10])->alias('a')
          ->join('recommend r', 'a.recommend_id=r.id')->column('a.id,r.title');
        switch ($v['type']) {
          case 1:
            $special_task['data'][$k]['types'] = '图文';
            break;
          case 2:
            $special_task['data'][$k]['types'] = '音频';
            break;
          case 3:
            $special_task['data'][$k]['types'] = '视频';
            break;
          case 5:
            $special_task['data'][$k]['types'] = '工具';
            break;
        }
      }
    }
    return Json::successlayui($special_task);
  }


  /**
   * 图文、音频、视频、专栏专题素材列表获取
   * @return json
   * */
  public function source_list()
  {
    $where = Util::getMore([
      ['page', 1],
      ['is_show', 1],
      ['limit', 20],
      ['title', ''],
      ['pid', ''],
      ['type', ''],
      ['order', ''],
      ['special_id', 0],
      ['all_type', 1],
      ['special_type', 0],
      ['check_source_sure', '']
    ]);
    $special_source = array();
    $special_source = SpecialSource::where(['special_id' => $where['special_id']])->select()->toArray();
    $special_source = array_column($special_source, 'pay_status', 'source_id');
    $special_task = SpecialTask::getTaskList2($where);
    if (isset($special_task['data']) && $special_task['data']) {
      foreach ($special_task['data'] as $k => $v) {
        if (array_key_exists($v['id'], $special_source)) {
          $special_task['data'][$k]['is_check'] = 1;
          $special_task['data'][$k]['LAY_CHECKED'] = true;
          if ($special_source[$v['id']] && $special_source[$v['id']] == self::PAY_MONEY) {
            $special_task['data'][$k]['pay_status'] = self::PAY_MONEY;
          } else {
            $special_task['data'][$k]['pay_status'] = self::PAY_NO_MONEY;
          }
        } else {
          $special_task['data'][$k]['is_check'] = 0;
          $special_task['data'][$k]['pay_status'] = self::PAY_MONEY;
        }
        switch ($v['type']) {
          case 1:
            $special_task['data'][$k]['types'] = '图文';
            break;
          case 2:
            $special_task['data'][$k]['types'] = '音频';
            break;
          case 3:
            if (!filter_var($v['image'], FILTER_VALIDATE_URL) !== false) {
              $special_task['data'][$k]['image'] =  OssHelper::normalVideo($v['image']) ?: '';
            }
            $special_task['data'][$k]['types'] = '视频';
            break;
            break;
          case 4:
            $special_task['data'][$k]['types'] = '专栏';
            break;
          case 5:
            $special_task['data'][$k]['types'] = '交互式';
            break;
        }
      }
    }
    $special_task['source'] = $special_source;
    return Json::successlayui($special_task);
  }


  /**
   * 商品列表获取
   * @return json
   * */
  public function product_list()
  {
    $where = Util::getMore([
      ['page', 1],
      ['limit', 20],
      ['store_name', ''],
      ['type', 1],
      ['related_id', 0],
      ['category', 0],
    ]);
    $product_list = StoreProduct::ProductFieldList($where);
    return Json::successlayui($product_list);
  }


  /**
   * 学习素材列表获取
   * @return json
   * */
  public function special_source_list()
  {
    $where = Util::getMore([
      ['page', 1],
      ['limit', 20],
      ['store_name', ''],
      ['type', 1],
      ['is_show', 1],
      ['related_id', 0],
      ['category', 0],
    ]);
    $special_source_list = SpecialTask::getSpecialSourceList($where);
    return Json::successlayui($special_source_list);
  }

  /**
   * 获取活动详细信息
   * @param int $id
   * @throws \think\db\exception\DataNotFoundException
   * @throws \think\db\exception\DbException
   * @throws \think\db\exception\ModelNotFoundException
   */
  public function get_special_info($id = 0)
  {
    $list = SpecialSubjectCategoryModel::specialCategoryAll();
    $menus = [];
    $storelists = [];
    foreach ($list as $menu) {
      $menus[] = ['id' => $menu['id'], 'label' => $menu['html'] . $menu['name'], 'disabled' => $menu['grade_id'] == 0 ? 0 : 1]; //,'disabled'=>$menu['pid']== 0];
    }
    $data['cateList'] = $menus;
    if ($id) {
      $special_info = SpecialModel::where('id', $id)->find();
      $special = SpecialModel::getOne($id, 0);
      if ($special === false) {
        return $this->failed(SpecialModel::getErrorInfo('您修改的专题不存在'));
      }
      //关联素材
      $specialSourceId = SpecialSource::getSpecialSource($id)->toArray();
      $sourceCheckList = array();
      if ($specialSourceId) {
        foreach ($specialSourceId as $k => $v) {
          $task_list = SpecialTask::where(['id' => $v['source_id'], 'is_del' => 0, 'is_show' => 1])->field('id,type,title,image')->find();
          if ($task_list) {
            if (!filter_var($task_list['image'], FILTER_VALIDATE_URL) !== false) {
              $task_list['image'] =  OssHelper::normalVideo($task_list['image']) ?: '';
            }
            $type = $special_info['type'] != 5 ? 0 : 0;
            $task_list['is_check'] = 1;
            $task_list['directory'] = SpecialSourceCategory::where('id', $v['category_id'])->where('type', $type)->value('title');
            $task_list['sort'] = $v['sort'];
            $task_list['pay_status'] = $v['pay_status'];
            $task_list['is_trial'] = $v['is_trial'];
            $task_list['is_eggs'] = $v['is_eggs'];
            array_push($sourceCheckList, $task_list);
          } else {
            array_splice($specialSourceId, $k, 1);
            continue;
          }
        }
      }
      // 关联商品 
      $sourceProductId = SpecialAssociated::getProductList($id, 1)->toArray();
      $productCheckList = array();
      if ($sourceProductId) {
        foreach ($sourceProductId as $k => $v) {
          $prducts = StoreProduct::where(['id' => $v['related_id'], 'is_del' => 0, 'is_show' => 1])->field('id,store_name,image')->find();
          if ($prducts) {
            $prducts['is_check'] = 1;
            $prducts['store_name'] = $prducts['store_name'];
            $prducts['image'] = $prducts['image'];
            $prducts['sort'] = $v['sort'];
            array_push($productCheckList, $prducts);
          } else {
            array_splice($sourceProductId, $k, 1);
            continue;
          }
        }
      }
      //关联课程
      $sourceSpecialId = SpecialAssociated::getSpecialList($id, 1)->toArray();
      $specialCheckList = array();
      if ($sourceSpecialId) {
        foreach ($sourceSpecialId as $k => $v) {
          $specials = SpecialModel::where(['id' => $v['related_id'], 'is_del' => 0, 'is_show' => 1])->field('id,title,image')->find();
          if ($specials) {
            $specials['is_check'] = 1;
            $specials['title'] = $specials['title'];
            $specials['image'] = $specials['image'];
            $specials['sort'] = $v['sort'];
            array_push($specialCheckList, $specials);
          } else {
            array_splice($sourceSpecialId, $k, 1);
            continue;
          }
        }
      }

      // 关联工具
      $toolSpecialId = SpecialAssociated::getToolList($id, 1)->toArray();
      $toolCheckList = array();
      if ($toolSpecialId) {
        foreach ($toolSpecialId as $k => $v) {
          $specials = SpecialModel::where(['id' => $v['related_id'], 'is_del' => 0, 'is_show' => 1])->field('id,title,image')->find();
          if ($specials) {
            $specials['is_check'] = 1;
            $specials['title'] = $specials['title'];
            $specials['image'] = $specials['image'];
            $specials['sort'] = $v['sort'];
            array_push($toolCheckList, $specials);
          } else {
            array_splice($toolSpecialId, $k, 1);
            continue;
          }
        }
      }

      //关联彩蛋
      $specialEggsSourceCheckList = array();
      // 关联的直播活动
      $liveId = SpecialActivityAssociated::where('special_id', $id)->column('activity_id');
      $liveCheckList = array();
      if ($liveId) {
        foreach ($liveId as $k => $activityId) {
          $lives = SpecialActivity::where(['id' => $activityId, 'is_del' => 0, 'is_show' => 1])->find();
          if ($lives) {
            $lives['is_check'] = 1;
            $lives['title'] = $lives['title'];
            $lives['image'] = $lives['image'];
            $lives['sort'] = $lives['sort'];
            array_push($liveCheckList, $lives);
          } else {
            array_splice($liveId, $k, 1);
            continue;
          }
        }
      }

      if (!$special_info) {
        return Json::fail('修改的专题不存在');
      }
      $special_info['live_shop_url'] = 'pages/yknowledge/course/detail?sid=' . $id . '&id=0&isJumpLive=1';
      $special_info['content'] = '';
      $special_info['byte_content'] = '';
      $special_info['common_problem'] = '';
      $special_content = SpecialContent::where('special_id', $id)->find();
      if ($special_content) {
        $special_info['content'] = htmlspecialchars_decode($special_content->content);
        $special_info['byte_content'] = htmlspecialchars_decode($special_content->byte_content);
        $special_info['common_problem'] = htmlspecialchars_decode($special_content->common_problem);
      }
      if ($special_info['is_pollster'] == 1 && $special_info['pollster_id'] != 0) {
        $special_info['pollster_title'] = SystemPollsterModel::where('id', $special_info['pollster_id'])->value('title');
      }
      $special_info['subject_id'] = [$special_info['subject_id']];
      $special_info['banner'] = is_string($special_info['banner']) ? json_decode($special_info['banner'], true) : [];
      $special_info['label'] = is_string($special_info['label']) && $special_info['label'] != ""  ? explode(',', $special_info['label']) : [];
      // 课程
      $special_info['special_tmp_list'] = $specialCheckList;
      $special_info['check_special_sure'] = $specialCheckList;

      // 素材
      $special_info['source_tmp_list'] = $sourceCheckList;
      $special_info['check_source_sure'] = $sourceCheckList;

      // 产品
      $special_info['product_tmp_list'] = $productCheckList;
      $special_info['check_product_sure'] = $productCheckList;

      // 教学工具
      $special_info['tool_tmp_list'] = $toolCheckList;
      $special_info['check_tool_sure'] = $toolCheckList;

      // 直播
      $special_info['live_tmp_list'] = $liveCheckList;
      $special_info['check_live_sure'] = $liveCheckList;

      // 彩蛋
      $special_info['special_eggs_tmp_list'] = $specialEggsSourceCheckList;
      $special_info['check_special_eggs_sure'] = $specialEggsSourceCheckList;

      $data['specialInfo'] = $special_info;
    }
    return Json::successful($data);
  }


  /**
   * 获取素材详细信息
   * @param int $id
   * @throws \think\db\exception\DataNotFoundException
   * @throws \think\db\exception\DbException
   * @throws \think\db\exception\ModelNotFoundException
   */
  public function get_sources_info($id = 0)
  {
    $data = [];
    if ($id) {
      $sources_info = SpecialTask::where('id', $id)->find();
      if (!$sources_info) {
        return Json::fail('修改的素材不存在');
      }
      $sources_info['source_type'] = $sources_info['type'];
      $sources_info['pid'] = [$sources_info['pid']];
      $special_category_id = SpecialTaskCateModel::where('task_id', $id)->field('cate_id')->select()->toArray();
      $sources_info['special_category_id'] = count($special_category_id) > 0 ? array_column($special_category_id, 'cate_id') : [];
      if (!filter_var($sources_info['image'], FILTER_VALIDATE_URL) !== false) {
        $sources_info['image'] =  OssHelper::normalVideo($sources_info['image']) ?: '';
      }
      if ($sources_info['type'] == 2) {
        $sources_info['audio_link'] = $sources_info['link'];
        $sources_info['oss_audio_link'] = sys_config('documentUploadUrl') . '/' . $sources_info['link'];
        $sources_info['audio_signature_link'] = OssHelper::normalVideo($sources_info['link']);

        $sources_info['preview_file_path'] = $sources_info['preview_file_path'];
        $sources_info['preview_file_path_signature'] = OssHelper::normalVideo($sources_info['preview_file_path']);
        $sources_info['oss_preview_file_path'] = sys_config('documentUploadUrl') . '/' . $sources_info['preview_file_path'];
        // $sources_info['audio_link_signature'] = '/uploads/audio/2025/08/14/868b6cw7jkgnqaneri94kqg3hn1qg0hh.mp3';
        // $sources_info['preview_file_path_signature'] = '/uploads/audio/2025/08/14/689d8aacc92c5_test.mp3';
        // $sources_info['audio_signature_link'] = '/uploads/audio/2025/08/14/868b6cw7jkgnqaneri94kqg3hn1qg0hh.mp3';
      }
      if ($sources_info['type'] == 3) {
        if (!filter_var($sources_info['image'], FILTER_VALIDATE_URL) !== false) {
          $sources_info['image'] =  OssHelper::normalVideo($sources_info['image'], 3) ?: '';
        }
        $sources_info['video_link'] = $sources_info['link'];
        $sources_info['oss_video_link'] = sys_config('documentUploadUrl') . '/' . $sources_info['link'];
        $sources_info['video_signature_link'] = OssHelper::normalVideo($sources_info['link']);
      }
      $sources_info['label'] = is_string($sources_info['label']) && $sources_info['label'] != ""  ? explode(',', $sources_info['label']) : [];
      $sources_info['tags'] = is_string($sources_info['tags']) && $sources_info['tags'] != ""  ? explode(',', $sources_info['tags']) : [];
      //关联商品
      $sourceProductId = SpecialTaskAssociated::getProductList($id, 2)->toArray();
      $productCheckList = array();
      if ($sourceProductId) {
        foreach ($sourceProductId as $k => $v) {
          $prducts = StoreProduct::where(['id' => $v['link_id'], 'is_del' => 0, 'is_show' => 1])->field('id,store_name,image')->find();
          if ($prducts) {
            $prducts['is_check'] = 1;
            $prducts['LAY_CHECKED'] = true;
            $prducts['LAY_TABLE_INDEX'] = $k;
            $prducts['store_name'] = $prducts['store_name'];
            $prducts['image'] = $prducts['image'];
            $prducts['sort'] = $v['sort'];
            array_push($productCheckList, $prducts);
          } else {
            array_splice($sourceProductId, $k, 1);
            continue;
          }
        }
      }
      //关联课程
      $sourceSpecialId = SpecialTaskAssociated::getSpecialList($id, 2)->toArray();
      $specialCheckList = array();
      if ($sourceSpecialId) {
        foreach ($sourceSpecialId as $k => $v) {
          $specials = SpecialModel::where(['id' => $v['link_id'], 'is_del' => 0, 'is_show' => 1])->field('id,title,image')->find();
          if ($specials) {
            $specials['is_check'] = 1;
            $specials['LAY_CHECKED'] = true;
            $specials['LAY_TABLE_INDEX'] = $k;
            $specials['title'] = $specials['title'];
            $specials['image'] = $specials['image'];
            $specials['sort'] = $v['sort'];
            array_push($specialCheckList, $specials);
          } else {
            array_splice($sourceSpecialId, $k, 1);
            continue;
          }
        }
      }
      // 关联素材
      $sourceId = SpecialTaskAssociated::getSpecialSourceList($id, 2)->toArray();
      $sourceCheckList = array();
      if ($sourceId) {
        foreach ($sourceId as $k => $v) {
          $sources = SpecialTask::where(['id' => $v['link_id'], 'is_del' => 0, 'is_show' => 1])->field('id,title,image')->find();
          if ($sources) {
            $sources['is_check'] = 1;
            $sources['LAY_CHECKED'] = true;
            $sources['LAY_TABLE_INDEX'] = $k;
            $sources['title'] = $sources['title'];
            $sources['image'] = $sources['image'];
            $sources['sort'] = $v['sort'];
            array_push($sourceCheckList, $sources);
          } else {
            array_splice($sourceId, $k, 1);
            continue;
          }
        }
      }
      $sources_info['paid_special_id'] = is_string($sources_info['paid_special_id']) && $sources_info['paid_special_id'] != 0  ? explode(',', $sources_info['paid_special_id']) : [];
      // 合集列表
      $sources_info['product_tmp_list'] = $productCheckList;
      $sources_info['check_product_sure'] = $productCheckList;
      $sources_info['special_tmp_list'] = $specialCheckList;
      $sources_info['check_special_sure'] = $specialCheckList;
      $sources_info['source_tmp_list'] = $sourceCheckList;
      $sources_info['check_source_sure'] = $sourceCheckList;
      $sources_info['add_time'] = $sources_info['add_time'] ? date('Y-m-d H:i:s', $sources_info['add_time']) : '';

      $valueNew = [];
      $count = 0;
      // 根据 special_id 查询任务清单数据
      // $tasks = SpecialTaskTodoList::where('source_id', $id)
      //   ->where('is_del', 0) // 排除删除的记录
      //   ->select(); // 使用 select 获取多条记录

      // if ($tasks->isEmpty()) {
      //   // 如果没有找到任务清单数据，则设置默认值
      //   $valueNew[$count] = [
      //     'type' => 1,
      //     'name' => '',
      //     'info' => '',
      //     'point' => '0.00',
      //     'link' => ''
      //   ];
      // } else {
      //   // 如果存在任务清单数据，则遍历每一条记录
      //   foreach ($tasks as $task) {
      //     // 填充每一条任务的数据
      //     $valueNew[$count]['type'] = $task['type'] ?: 1;
      //     $valueNew[$count]['name'] = $task['name'] ?: '';
      //     $valueNew[$count]['info'] = $task['info'] ?: '';
      //     $valueNew[$count]['point'] = $task['point'] ?: '0.00';
      //     $valueNew[$count]['link'] = $task['link'] ?: '';
      //     $count++;
      //   }
      // }

      // $sources_info['attrs'] = $valueNew;
      // $sources_info['attr'] = [
      //   'type' => 1,
      //   'name' => '',
      //   'info' => '',
      //   'point' => '0.00',
      //   'link' => ''
      // ];
      $sources_info['spec_type'] = 1;
      
      // 确保预处理相关字段正确传递给前端
      $sources_info['enable_preprocessing'] = $sources_info['enable_preprocessing'] ?? 0;
      $sources_info['processing_status'] = $sources_info['processing_status'] ?? 1;
      $sources_info['preview_start_time'] = $sources_info['preview_start_time'] ?? 0;
      $sources_info['preview_end_time'] = $sources_info['preview_end_time'] ?? 0;
      $sources_info['preview_duration'] = $sources_info['preview_duration'] ?? 0;
      $sources_info['file_duration'] = $sources_info['file_duration'] ?? 0;
      $sources_info['original_file_path'] = $sources_info['original_file_path'] ?? '';
      
      $data['specialInfo'] = $sources_info;
    }
    return Json::successful($data);
  }


  public function is_task_format_attr($id = 0, $type = 0)
  {
    $attr = [];
    $valueNew = [];
    $count = 0;
    // 检查是否有指定的id
    // if ($id) {
    //   // 根据 special_id 查询任务清单数据
    //   $tasks = SpecialTaskTodoList::where('source_id', $id)
    //     ->where('is_del', 0) // 排除删除的记录
    //     ->select(); // 使用 select 获取多条记录

    //   if ($tasks->isEmpty()) {
    //     // 如果没有找到任务清单数据，则设置默认值
    //     $valueNew[$count] = [
    //       'type' => 1,
    //       'name' => '',
    //       'info' => '',
    //       'point' => '0.00',
    //       'link' => ''
    //     ];
    //   } else {
    //     // 如果存在任务清单数据，则遍历每一条记录
    //     foreach ($tasks as $task) {
    //       // 填充每一条任务的数据
    //       $valueNew[$count]['type'] = $task['type'] ?: 1;
    //       $valueNew[$count]['name'] = $task['name'] ?: '';
    //       $valueNew[$count]['info'] = $task['info'] ?: '';
    //       $valueNew[$count]['point'] = $task['point'] ?: '0.00';
    //       $valueNew[$count]['link'] = $task['link'] ?: '';

    //       // 编辑商品时，将没有规格的数据不生成默认值
    //       if ($type) {
    //         $valueNew[$count]['type'] = (int) $valueNew[$count]['type'];
    //         $valueNew[$count]['name'] = (string) $valueNew[$count]['name'];
    //         $valueNew[$count]['info'] = (string) $valueNew[$count]['info'];
    //         $valueNew[$count]['point'] = (string) $valueNew[$count]['point'];
    //         $valueNew[$count]['link'] = (string) $valueNew[$count]['link'];
    //       }
    //       $count++;
    //     }
    //   }
    // } else {
    //   // 如果没有传入id，设置默认值
    //   $valueNew[$count] = [
    //     'type' => 1,
    //     'name' => '',
    //     'info' => '',
    //     'point' => '0.00',
    //     'link' => ''
    //   ];
    // }
    // 设置表头数据
    $header[] = ['title' => '类型', 'slot' => 'type', 'align' => 'center', 'minWidth' => 120];
    $header[] = ['title' => '名称', 'slot' => 'name', 'align' => 'center', 'minWidth' => 140];
    $header[] = ['title' => '描述', 'slot' => 'info', 'align' => 'center', 'minWidth' => 140];
    $header[] = ['title' => '时间点', 'slot' => 'point', 'align' => 'center', 'minWidth' => 140];
    $header[] = ['title' => '跳转链接', 'slot' => 'link', 'align' => 'center', 'minWidth' => 140];
    $header[] = ['title' => '操作', 'slot' => 'action', 'align' => 'center', 'minWidth' => 70];

    // 返回响应数据
    $info = ['attr' => $attr, 'value' => $valueNew, 'header' => $header];
    return Json::successful($info);
  }

    /**
   * 获取图文专题列表数据
   */
  public function list($special_type = 6)
  {
    $where = Util::getMore([
      ['subject_id', 0],
      ['page', 1],
      ['limit', 20],
      ['store_name', ''],
      ['start_time', ''],
      ['end_time', ''],
      ['order', ''],
      ['is_show', ''],
      ['is_beta_test', ''],
    ]);
    $where['type'] = $special_type;
    return Json::successlayui(SpecialModel::getSpecialList($where));
  }


  /**
   * 获取图文专题列表数据
   */
  public function all_ist()
  {
    $where = Util::getMore([
      ['subject_id', 0],
      ['page', 1],
      ['limit', 20],
      ['store_name', ''],
      ['start_time', ''],
      ['end_time', ''],
      ['order', ''],
      ['is_show', ''],
    ]);
    $where['type'] = 0;
    return Json::successlayui(SpecialModel::getSpecialList($where));
  }


  /**
   * 获取图文专题列表数据
   */
  public function special_list()
  {
    $where = Util::getMore([
      ['page', 1],
      ['limit', 20],
      ['store_name', ''],
      ['order', ''],
      ['is_show', 1],
      ['related_id', 0],
      ['category', 0],
    ]);
    $where['type'] = 0;
    return Json::successlayui(SpecialModel::getAllSpecialList($where));
  }



  /**
   * 获取图文专题列表数据
   */
  public function tool_list()
  {
    $where = Util::getMore([
      ['title', ''],
      ['pid', 0],
      ['order', ''],
      ['is_show', ''],
      ['page', 1],
      ['limit', 20]
    ]);

    $where['type'] = 5;
    $special_task = SpecialTask::getTaskList($where);
    if (isset($special_task['data']) && $special_task['data']) {
      foreach ($special_task['data'] as $k => $v) {
        if ($v['type'] == 3) {
          if (!filter_var($v['image'], FILTER_VALIDATE_URL) !== false) {
            $special_task['data'][$k]['image'] =  OssHelper::normalVideo($v['image']) ?: '';
          }
        }
        $special_task['data'][$k]['use'] = SpecialSource::where(['source_id' => $v['id']])->count();
        $special_task['data'][$k]['is_pay_status'] = SpecialSource::where(['source_id' => $v['id'], 'pay_status' => 1])->count();
        $special_task['data'][$k]['recommend'] = RecommendRelation::where('a.link_id', $v['id'])->where('a.type', 'in', [10])->alias('a')
          ->join('recommend r', 'a.recommend_id=r.id')->column('a.id,r.title');
        switch ($v['type']) {
          case 1:
            $special_task['data'][$k]['types'] = '图文';
            break;
          case 2:
            $special_task['data'][$k]['types'] = '音频';
            break;
          case 3:
            $special_task['data'][$k]['types'] = '视频';
            break;
          case 5:
            $special_task['data'][$k]['types'] = '工具';
            break;
        }
      }
    }
    return Json::successlayui($special_task);
  }




  /**
   * 获取图文专题列表数据
   */
  public function live_list()
  {
    $where = Util::getMore([
      ['page', 1],
      ['limit', 20],
      ['store_name', ''],
    ]);
    $where['type'] = 0;
    return Json::successlayui(SpecialActivity::getAllLiveList($where));
  }


  /**
   * 获取图文专题列表数据
   */
  public function all_source_list()
  {
    $where = Util::getMore([
      ['title', ''],
      ['pid', 0],
      ['order', ''],
      ['is_show', ''],
      ['page', 1],
      ['limit', 20]
    ]);
    $special_task = SpecialTask::getTaskList($where);
    if (isset($special_task['data']) && $special_task['data']) {
      foreach ($special_task['data'] as $k => $v) {
        if ($v['type'] == 3) {
          if (!filter_var($v['image'], FILTER_VALIDATE_URL) !== false) {
            $special_task['data'][$k]['image'] =  OssHelper::normalVideo($v['image']) ?: '';
          }
        }
        $special_task['data'][$k]['use'] = SpecialSource::where(['source_id' => $v['id']])->count();
        $special_task['data'][$k]['is_pay_status'] = SpecialSource::where(['source_id' => $v['id'], 'pay_status' => 1])->count();
        $special_task['data'][$k]['recommend'] = RecommendRelation::where('a.link_id', $v['id'])->where('a.type', 'in', [10])->alias('a')
          ->join('recommend r', 'a.recommend_id=r.id')->column('a.id,r.title');
        switch ($v['type']) {
          case 1:
            $special_task['data'][$k]['types'] = '图文';
            break;
          case 2:
            $special_task['data'][$k]['types'] = '音频';
            break;
          case 3:
            $special_task['data'][$k]['types'] = '视频';
            break;
        }
      }
    }
    return Json::successlayui($special_task);
  }


  public function get_topay_special_list()
  {
    $list = SpecialModel::field('id,title')->where('is_show', 1)->where('is_del', 0)->where('pay_type', 1)->select();
    $category = [];
    foreach ($list as $menu) {
      $category[] = ['id' => $menu['id'], 'name' =>  $menu['title'], 'disabled' => 1]; //,'disabled'=>$menu['pid']== 0];
    }
    return Json::successful($category);
  }


  public function get_special_task_list()
  {
    $list = SpecialTask::field('id,title')->where('is_show', 1)->where('is_del', 0)->select();
    $category = [];
    foreach ($list as $menu) {
      $category[] = ['id' => $menu['id'], 'name' =>  $menu['id'] . '-' . $menu['title'], 'disabled' => 1]; //,'disabled'=>$menu['pid']== 0];
    }
    return Json::successful($category);
  }



  /**
   * 设置课程显示|隐藏
   * @param string $is_show
   * @param string $id
   */
  public function set_show($is_show = '', $id = '')
  {
    ($is_show == '' || $id == '') && Json::fail('缺少参数');
    if (SpecialModel::where('id', $id)->update(['is_show' => $is_show])) {
      return Json::successful($is_show == 1 ? '显示成功' : '隐藏成功');
    } else {
      return Json::fail(SpecialModel::getErrorInfo($is_show == 1 ? '显示失败' : '隐藏失败'));
    }
  }



  /**
   * 设置分类显示|隐藏
   * @param string $is_show
   * @param string $id
   */
  public function set_sources_show($is_show = '', $id = '')
  {
    ($is_show == '' || $id == '') && Json::fail('缺少参数');
    if (SpecialTask::where('id', $id)->update(['is_show' => $is_show])) {
      return Json::successful($is_show == 1 ? '显示成功' : '隐藏成功');
    } else {
      return Json::fail(SpecialTaskCategoryModel::getErrorInfo($is_show == 1 ? '显示失败' : '隐藏失败'));
    }
  }



  /**
   * 设置课程显示|隐藏
   * @param string $is_show
   * @param string $id
   */
  public function set_beta_test($is_beta_test = '', $id = '')
  {
    ($is_beta_test == '' || $id == '') && Json::fail('缺少参数');
    if (SpecialModel::where('id', $id)->update(['is_beta_test' => $is_beta_test])) {
      return Json::successful($is_beta_test == 1 ? '设置成功' : '设置成功');
    } else {
      return Json::fail(SpecialModel::getErrorInfo($is_beta_test == 1 ? '设置失败' : '设置失败'));
    }
  }



  /**
   * 设置分类显示|隐藏
   * @param string $is_show
   * @param string $id
   */
  public function set_list_show($is_list_show = '', $id = '')
  {
    ($is_list_show == '' || $id == '') && Json::fail('缺少参数');
    $task = SpecialTask::where('id', $id)->find();
    $isShow = $task['paid_special_id'] == 0 ?: 0;
    if (SpecialTask::where('id', $id)->update(['is_list_show' => $is_list_show]) && SpecialMaterialSortListModel::where('type', 2)->where('task_id', $id)->update(['is_show' => $isShow])) {
      return Json::successful($is_list_show == 1 ? '显示成功' : '隐藏成功');
    } else {
      return Json::fail(SpecialTask::getErrorInfo($is_list_show == 1 ? '显示失败' : '隐藏失败'));
    }
  }


  /**
   * 设置是否支付后显示|隐藏
   * @param string $is_show
   * @param string $id
   */
  public function set_paid_show($is_paid_show = '', $id = '')
  {
    ($is_paid_show == '' || $id == '') && Json::fail('缺少参数');
    if (SpecialTask::where('id', $id)->update(['is_paid_show' => $is_paid_show])) {
      return Json::successful($is_paid_show == 1 ? '显示成功' : '隐藏成功');
    } else {
      return Json::fail(SpecialTask::getErrorInfo($is_paid_show == 1 ? '显示失败' : '隐藏失败'));
    }
  }


  /**
   * 快速编辑
   * @param string $field
   * @param string $id
   * @param string $value
   */
  public function set_category($field = '', $id = '', $value = '')
  {
    $field == '' || $id == '' || $value == '' && Json::fail('缺少参数');
    if (SpecialTask::where('id', $id)->update([$field => $value]))
      return Json::successful('保存成功');
    else
      return Json::fail('保存失败');
  }


  /**
   * 添加页面
   * @param int $id
   * @param int $is_live
   * @return mixed|void
   */
  public function create($id = 0)
  {
    $special_type = $this->request->param('special_type');
    if ($id) {
      $special = SpecialModel::getOne($id, 0);
      if ($special === false) {
        return $this->failed(SpecialModel::getErrorInfo('您修改的专题不存在'));
      }
      $specialSourceId = SpecialSource::getSpecialSource($id);
      $specialCheckList = array();
      if ($specialSourceId) {
        foreach ($specialSourceId as $k => $v) {
          if ($special_type == 4) {
            $task_list = SpecialModel::where(['id' => $v['source_id'], 'is_del' => 0, 'is_show' => 1])->find();
          } else {
            $task_list = SpecialTask::where(['id' => $v['source_id'], 'is_del' => 0, 'is_show' => 1])->find();
          }
          if ($task_list) {
            $task_list['is_check'] = 1;
            $task_list['sort'] = $v['sort'];
            $task_list['pay_status'] = $v['pay_status'];
            array_push($specialCheckList, $task_list);
          }
        }
      }
      $specialSourceId = SpecialSource::getSpecialSource($id);
      $sourceCheckList = array();
      if ($specialSourceId) {
        foreach ($specialSourceId as $k => $v) {
          if ($special_type == 4) {
            $task_list = SpecialModel::where(['id' => $v['source_id'], 'is_del' => 0, 'is_show' => 1])->find();
          } else {
            $task_list = SpecialTask::where(['id' => $v['source_id'], 'is_del' => 0, 'is_show' => 1])->find();
          }
          if ($task_list) {
            $task_list['is_check'] = 1;
            $task_list['sort'] = $v['sort'];
            $task_list['pay_status'] = $v['pay_status'];
            array_push($sourceCheckList, $task_list);
          }
        }
      }

      $specialSourceId = SpecialSource::getSpecialSource($id);
      $productCheckList = array();
      if ($specialSourceId) {
        foreach ($specialSourceId as $k => $v) {
          if ($special_type == 4) {
            $task_list = SpecialModel::where(['id' => $v['source_id'], 'is_del' => 0, 'is_show' => 1])->find();
          } else {
            $task_list = SpecialTask::where(['id' => $v['source_id'], 'is_del' => 0, 'is_show' => 1])->find();
          }
          if ($task_list) {
            $task_list['is_check'] = 1;
            $task_list['sort'] = $v['sort'];
            $task_list['pay_status'] = $v['pay_status'];
            array_push($productCheckList, $task_list);
          }
        }
      }

      $storeCheckList = [];
      list($specialInfo, $liveInfo) = $special;
      $this->assign('liveInfo', json_encode($liveInfo));
      $this->assign('special', json_encode($specialInfo));
      $this->assign('specialCheckList', json_encode($specialCheckList));
      $this->assign('sourceCheckList', json_encode($sourceCheckList));
      $this->assign('productCheckList', json_encode($productCheckList));
      $this->assign('storeCheckList', json_encode($storeCheckList));
    }
    $this->assign('special_type', $special_type);
    $this->assign('id', $id);
    $template = $this->switch_template($special_type, request()->action());

    // dd($template);
    if (!$template) $template = "";
    return $this->fetch($template);
  }

  /**删除指定专题和素材
   * @param int $id修改的主键
   * @param $model_type要修改的表
   * @throws \think\exception\DbException
   */
  public function delete($id = 0, $model_type = false)
  {
    if (!$id || !isset($model_type) || !$model_type) return Json::fail('缺少参数');
    $model_table = $this->switch_model($model_type);
    if (!$model_table) return Json::fail('缺少参数');
    try {
      $res_get = $model_table::where('id', $id)->find();
      $model_table::startTrans();
      if (!$res_get) return Json::fail('删除的数据不存在');
      $res_del = $res_get->where('id', $id)->update(['is_del' => 1]);
      if ($model_type == 'special' && $res_del) {
        $model_source = $this->switch_model('source');
        $res = $model_source::where('special_id', $id)->delete();
      } else if ($model_type == 'task' && $res_del) {
        $model_source = $this->switch_model('source');
        $res = $model_source::where('source_id', $id)->delete();
      }
      $model_table::commit();
      return Json::successful('删除成功');
    } catch (\Exception $e) {
      $model_table::rollback();
      return Json::fail(SpecialTask::getErrorInfo('删除失败' . $e->getMessage()));
    }
  }

  /**
   * 添加推荐
   * @param int $special_id
   * @return mixed
   * @throws \think\exception\DbException
   */
  public function recommend($special_id = 0)
  {
    if (!$special_id) $this->failed('缺少参数');
    $special = SpecialModel::get($special_id);
    if (!$special) $this->failed('没有查到此专题');
    if ($special->is_del) $this->failed('此专题已删除');
    $field = [
      Form::select('recommend_id', '推荐')->setOptions(function () {
        $model = Recommend::where(['is_show' => 1, 'is_fixed' => 0]);
        $model = $model->where('type', 0);
        $list = $model->field('title,id')->order('sort desc,add_time desc')->select();
        foreach ($list as $menu) {
          $menus[] = ['value' => $menu['id'], 'label' => $menu['html'] . $menu['title']];
        }
        return $menus;
      })->filterable(1),
      Form::number('sort', '排序'),
    ];
    $form = Form::make_post_form('推荐设置', $field, Url::buildUrl('save_recommend', array('special_id' => $special_id)), 2);
    $this->assign(compact('form'));
    return $this->fetch('public/form-builder');
  }

  /**素材推荐
   * @param int $source_id
   */
  public function sourceRecommend($source_id = 0)
  {
    if (!$source_id) $this->failed('缺少参数');
    $source = SpecialTask::where('id', $source_id)->find();
    if (!$source) $this->failed('没有查到此素材');
    if ($source->is_del) $this->failed('此素材已删除');
    $field = [
      Form::select('recommend_id', '推荐')->setOptions(function () {
        $model = Recommend::where(['is_show' => 1, 'is_fixed' => 0]);
        $model = $model->where('type', 0);
        $list = $model->field('title,id')->order('sort desc,add_time desc')->select();
        foreach ($list as $menu) {
          $menus[] = ['value' => $menu['id'], 'label' => $menu['html'] . $menu['title']];
        }
        return $menus;
      })->filterable(1),
      Form::number('sort', '排序'),
    ];
    $form = Form::make_post_form('推荐设置', $field, Url::buildUrl('save_recommend', array('special_id' => $source_id)), 2);
    $this->assign(compact('form'));
    return $this->fetch('public/form-builder');
  }

  /**
   * 保存推荐
   * @param int $special_id
   * @throws \think\exception\DbException
   */
  public function save_recommend($special_id = 0)
  {
    if (!$special_id) $this->failed('缺少参数');
    $data = Util::postMore([
      ['recommend_id', 0],
      ['sort', 0]
    ]);
    if (!$data['recommend_id']) return Json::fail('请选择推荐');
    $recommend = Recommend::get($data['recommend_id']);
    if (!$recommend) return Json::fail('导航菜单不存在');
    $data['add_time'] = time();
    $data['type'] = $recommend->type;
    $data['link_id'] = $special_id;
    if (RecommendRelation::be(['type' => $recommend->type, 'link_id' => $special_id, 'recommend_id' => $data['recommend_id']])) return Json::fail('已推荐,请勿重复推荐');
    if (RecommendRelation::create($data))
      return Json::successful('推荐成功');
    else
      return Json::fail('推荐失败');
  }

  public function searchs_task($coures_id = 0)
  {
    $special_type = $this->request->param('special_type');
    $special_id = $this->request->param('special_id');
    $this->assign('coures_id', $coures_id);
    $this->assign('special_title', self::SPECIAL_TYPE[$special_type]);
    $this->assign('special_type', $special_type); //图文专题
    $this->assign('special_id', $special_id);
    $this->assign('cateList', SpecialTaskCategoryModel::taskCategoryAll());
    return $this->fetch('special/task/searchs_task');
  }

  public function searchs_tasks($coures_id = 0)
  {
    $special_type = $this->request->param('special_type');
    $special_id = $this->request->param('special_id');
    $this->assign('coures_id', $coures_id);
    $this->assign('special_title', self::SPECIAL_TYPE[$special_type]);
    $this->assign('special_type', $special_type); //图文专题
    $this->assign('special_id', $special_id);
    $this->assign('cateList', SpecialTaskCategoryModel::taskCategoryAll());
    return $this->fetch('special/task/searchs_tasks');
  }


  public function searchs_eggs_task($coures_id = 0)
  {
    $special_type = $this->request->param('special_type');
    $special_id = $this->request->param('special_id');
    $this->assign('coures_id', $coures_id);
    $this->assign('special_title', self::SPECIAL_TYPE[$special_type]);
    $this->assign('special_type', $special_type); //图文专题
    $this->assign('special_id', $special_id);
    $this->assign('cateList', SpecialTaskCategoryModel::taskCategoryAll());
    return $this->fetch('special/task/searchs_eggs_task');
  }

  public function searchs_all_task($coures_id = 0)
  {
    $special_type = $this->request->param('special_type');
    $special_id = $this->request->param('special_id');
    $this->assign('coures_id', $coures_id);
    $this->assign('cateList', SpecialTaskCategoryModel::taskCategoryAll());
    return $this->fetch('special/task/searchs_all_task');
  }


  public function searchs_special($coures_id = 0)
  {
    $type = $this->request->param('type', 0);
    $id = $this->request->param('id');
    $this->assign('type', $type); //图文专题
    $this->assign('id', $id);
    return $this->fetch('special/task/searchs_special');
  }


  public function searchs_source($coures_id = 0)
  {
    $type = $this->request->param('type', 0);
    $id = $this->request->param('id');
    $this->assign('type', $type); //图文专题
    $this->assign('id', $id);
    return $this->fetch('special/task/searchs_source');
  }


  public function searchs_buy_special($coures_id = 0)
  {
    $type = $this->request->param('type', 0);
    $id = $this->request->param('id');
    $this->assign('type', $type); //图文专题
    $this->assign('id', $id);
    return $this->fetch('special/task/searchs_buy_special');
  }


  public function searchs_tool($coures_id = 0)
  {
    $type = $this->request->param('type', 0);
    $id = $this->request->param('id');
    $this->assign('type', $type); //教学专题
    $this->assign('id', $id);
    return $this->fetch('special/task/searchs_tool');
  }


  public function searchs_live($coures_id = 0)
  {
    $type = $this->request->param('type', 0);
    $id = $this->request->param('id');
    $this->assign('type', $type); //图文专题
    $this->assign('id', $id);
    return $this->fetch('special/task/searchs_live');
  }



  public function searchs_product($coures_id = 0)
  {
    $type = $this->request->param('type', 0);
    $id = $this->request->param('id');
    $this->assign('type', $type); //图文专题
    $this->assign('id', $id);
    return $this->fetch('special/task/searchs_product');
  }



  /**
   * 显示编辑资源表单页.
   *
   * @param int $id
   * @return \think\Response
   */
  public function edit_list_show($id)
  {
    $c = SpecialTask::where('id', $id)->find();
    if (!$c) return Json::fail('数据不存在!');
    $cate_id = [];
    $cate_id_arr = SpecialTaskCateModel::field('cate_id')->where('task_id', $id)->select()->toArray();
    if (count($cate_id_arr)) {
      $cate_id = array_column($cate_id_arr, 'cate_id');
    }
    $field = [
      Form::input('title', '素材名称', $c->getData('title')),
      Form::checkbox('cate_id', '素材分类', $cate_id)->setOptions(function () {
        $list = SpecialSubjectCategoryModel::specialCategoryAll();
        $menus = [];
        foreach ($list as $menu) {
          $menus[] = ['value' => $menu['id'], 'label' => $menu['name'], 'disabled' => $menu['grade_id'] == 0 ? 1 : 0];
        }
        return $menus;
      })
    ];
    $form = Form::make_post_form('编辑素材分类', $field, Url::buildUrl('update_list_show', array('id' => $id)), 2);
    $this->assign(compact('form'));
    return $this->fetch('public/form-builder');
  }

  /**
   * 保存更新的资源
   *
   * @param \think\Request $request
   * @param int $id
   * @return \think\Response
   */
  public function update_list_show(Request $request, $id)
  {
    $data = Util::postMore([
      'cate_id',
    ], $request);
    $task = SpecialTask::where('id', $id)->find();
    if (is_null($data['cate_id'])) return Json::fail('请选择标签分类');
    $cate_id = $data['cate_id'];
    $task_cate_res = SpecialTaskCateModel::where('task_id', $id)->delete();
    $task_sort_res = SpecialMaterialSortListModel::where('task_id', $id)->where('type', 2)->delete();
    foreach ($cate_id as $cid) {
      if (SpecialTaskCateModel::insert(['task_id' => $id, 'cate_id' => $cid, 'add_time' => time()])) {
        $isShow = $task['paid_special_id'] == 0 ? 1 : 0;
        SpecialMaterialSortListModel::addSpecialSort(2, $cid, $id, $isShow);
      }
    }
    return Json::successful('修改素材分类成功!');
  }

  /**根据标识选着模型对象
   * @param $model_type 表名
   * @return Special|SpecialTask|bool
   */
  protected function switch_model($model_type)
  {
    if (!$model_type) {
      return false;
    }
    switch ($model_type) {
      case 'task':
        return new SpecialTask();
        break;
      case 'special':
        return new SpecialModel();
        break;
      case 'source':
        return new SpecialSource();
        break;
      case 'live_goods':
        return new LiveGoods();
        break;
      default:
        return false;
    }
  }

  /**渲染模板
   * @param $special_type
   * @param $template_type
   * @return bool|string|void
   */
  protected function switch_template($special_type, $template_type)
  {
    if (!$special_type || !$template_type) {
      return false;
    }
    switch ($special_type) {
      case 1:
      case 2:
      case 3:
        // return 'special/image_text/' . $template_type;
        return 'special/audio_video/' . $template_type;
        break;
      // case 2:
      //     return 'special/audio_video/' . $template_type;
      //     break;
      // case 3:
      //     return 'special/audio_video/' . $template_type;
      //     break;
      case 4:
        return 'special/column/' . $template_type;
        break;
      // case 5: //直播列表
      //   return 'special/live/' . $template_type;
      //   break;

      case 5: //直播列表
        return 'special/interactive/' . $template_type;
        break;

      default:
        return $this->failed('没有对应模板 ');
    }
  }


  /**
   * 获取调查问卷列表
   * @return string
   * @throws \Exception
   */
  public function pollsterList()
  {
    return $this->fetch();
  }



  /**
   * 获取图文专题列表数据
   */
  public function all_pollster()
  {
    $where = Util::getMore([
      ['type', 2],
      ['page', 1],
      ['limit', 20],
      ['keywords', ''],
      ['add_time', '']
    ]);
    return Json::successlayui(SystemPollsterModel::getPollsterList($where));
  }
}
