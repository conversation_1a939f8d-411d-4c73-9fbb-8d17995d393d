<?php

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date:   2023-05-04 15:40:37
 * @Last Modified time: 2023-05-11 16:00:11
 */

namespace app\admin\controller\special;

use think\Request;
use think\facade\Route as Url;
use app\admin\controller\AuthController;
use app\admin\model\store\StoreChannelProduct as ChannelProductModel;
use crmeb\services\{FormBuilder as Form, JsonService as Json, UtilService as Util};

/**
 * 第三方渠道课程商品配置：控制器
 * Class ChannelProduct
 * @package app\admin\controller\special
 */
class ChannelProduct extends AuthController
{
    /**
     * 显示资源列表
     *
     * @return \think\Response
     */
    public function index()
    {   
        return $this->fetch();
    }

    public function channel_product_sku_list()
    {
        $where = Util::getMore([
            ['type', 0],
            ['channel_product_id', 0],
            ['page', 1],
            ['limit', 20],
        ]);
        return Json::successlayui(ChannelProductModel::getChannelProductSkuList($where));
    }

    /*
     *  异步获取列表
     *  @return json
     */
    public function channel_product_list()
    {
        $where = Util::getMore([
            ['type', 0],
            ['title', ''],
            ['keywords', ''],
            ['add_time', ''],
            ['page', 1],
            ['limit', 20],
        ]);
        return Json::successlayui(ChannelProductModel::getChannelProductList($where));
    }

    /**
     * 显示创建资源表单页.
     *
     * @return \think\Response
     */
    public function create()
    {
        $field = [
            Form::select('type', '渠道')->setOptions(function () {
                $menus = [['value' => 1, 'label' => '视频号'],['value' => 2, 'label' => '抖音小店'],['value' => 3, 'label' => '小红书小店']];
                return $menus;
            })->filterable(1),
            Form::select('product_type', '商品类型')->setOptions(function () {
                $menus = [['value' => 1, 'label' => '普通商品'],['value' => 2, 'label' => '虚拟课程']];
                return $menus;
            })->filterable(1),
            Form::input('title', '商品名'),
            Form::input('product_id', '着调儿Id'),
            Form::input('channel_product_id', '渠道商品Id'),
            Form::input('channel_product_sku_id', '渠道商品skuId'),
            Form::radio('is_send_sms', '是否发送短信', 0)->options([['label' => '是', 'value' => 1], ['label' => '否', 'value' => 0]])
        ];
        $form = Form::make_post_form('添加-关联渠道商品', $field, Url::buildUrl('save'), 2);
        $this->assign(compact('form'));
        return $this->fetch('public/form-builder');
    }


    /**
     * 显示创建资源表单页.
     *
     * @return \think\Response
     */
    public function create_sku($id = 0)
    {
        $this->assign('id', (int)$id);
        return $this->fetch();
    }

    /**
     * 保存新建的资源
     *
     * @param \think\Request $request
     * @return \think\Response
     */
    public function save(Request $request)
    {
        $data = Util::postMore([
            'type',
            'product_type',
            'title',
            'product_id',
            'channel_product_id',
            'channel_product_sku_id',
            ['is_send_sms', 0]
        ], $request);
        if ($data['type'] == '') return Json::fail('请选择渠道');
        if ($data['product_type'] == '') return Json::fail('请选择商品类型');
        if ($data['title'] == '') return Json::fail('请输入商品名');
        if ($data['product_id'] == '') return Json::fail('请输入着调儿Id');
        if ($data['channel_product_id'] == '') return Json::fail('请输入渠道商品Id');
        if ($data['channel_product_sku_id'] == '') return Json::fail('请输入渠道商品skuId');
        $data['add_time'] = time();
        ChannelProductModel::create($data);
        return Json::successful('添加关联渠道商品成功!');
    }

    /**
     * 删除指定资源
     *
     * @param int $id
     * @return \think\Response
     */
    public function delete($id)
    {
        $data['is_del'] = 1;
        if (!ChannelProductModel::edit($data,$id))
            return Json::fail(ChannelProductModel::getErrorInfo('删除失败,请稍候再试!'));
        else
            return Json::successful('删除成功!');
    }
}