<?php

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date:   2023-03-21 11:07:01
 * @Last Modified time: 2023-03-21 14:39:14
 */

namespace app\admin\controller\special;

use think\Request;
use think\facade\Route as Url;
use app\admin\controller\AuthController;
use app\admin\model\special\Special as SpecialModel;
use app\admin\model\special\SpecialBuy as SpecialBuyModel;
use crmeb\services\{FormBuilder as Form, JsonService as Json, UtilService as Util};

/**
 * 课程用户订阅权益：控制器
 * Class Subscribe
 * @package app\admin\controller\special
 */
class Subscribe extends AuthController
{
  /**
   * 显示资源列表
   *
   * @return \think\Response
   */
  public function index()
  {
    $this->assign('special_list', SpecialModel::where('is_del', 0)->where('pay_type', 1)->select());
    return $this->fetch();
  }

  /*
     *  异步获取列表
     *  @return json
     */
  public function list()
  {
    $where = Util::getMore([
      ['keywords', ''],
      ['code', ''],
      ['special_id', 0],
      ['type', 0],
      ['source', 0],
      ['add_time', ''],
      ['page', 1],
      ['limit', 20],
    ]);
    return Json::successlayui(SpecialBuyModel::subscribeCourseList($where));
  }


  /**
   * 显示创建资源表单页.
   *
   * @return \think\Response
   */
  public function create()
  {
    $field = [
      Form::select('special_id', '课程')->setOptions(function () {
        $list = SpecialModel::where('is_del', 0)->where('pay_type', 1)->select();
        foreach ($list as $menu) {
          $menus[] = ['value' => $menu['id'], 'label' => $menu['html'] . $menu['title']];
        }
        return $menus;
      })->filterable(1),
      Form::frameImageOne('image', '用户', Url::buildUrl('system.SystemStoreStaff/select', array('fodder' => 'image')))->icon('plus')->width('100%')->height('500px'),
      Form::hidden('uid', 0),
      Form::input('mark', '备注'),
    ];
    $form = Form::make_post_form('添加订阅', $field, Url::buildUrl('save'), 2);
    $this->assign(compact('form'));
    return $this->fetch('public/form-builder');
  }

  /**
   * 保存新建的资源
   *
   * @param \think\Request $request
   * @return \think\Response
   */
  public function save(Request $request)
  {
    $data = Util::postMore([
      'special_id',
      'uid',
      'mark',
    ], $request);
    if ($data['special_id'] == '') return Json::fail('请选择课程');
    if ($data['uid'] == '') return Json::fail('请选择要订阅的用户');
    if (SpecialBuyModel::be(['special_id' => $data['special_id'], 'uid' => $data['uid'], 'is_del' => 0])) return Json::fail('重复订阅');
    $data['order_id'] = 'admin' . mt_rand(10000, ************);
    $data['type'] = 10;
    $data['is_dels'] = 0;
    $data['add_time'] = time();
    SpecialBuyModel::create($data);
    return Json::successful('添加订阅记录成功!');
  }
  /**
   * 删除指定资源
   *
   * @param int $id
   * @return \think\Response
   */
  public function delete($id)
  {
    $data['is_del'] = 1;
    $res = SpecialBuyModel::edit($data, $id);
    if (!$res)
      return Json::fail(SpecialBuyModel::getErrorInfo('删除失败,请稍候再试!'));
    else
      return Json::successful('删除成功!');
  }

  /**
   * 恢复
   * @param int $uid
   */
  public function recover($id = 0)
  {
    $data['is_del'] = 0;
    if (!SpecialBuyModel::edit($data, $id))
      return Json::fail(SpecialBuyModel::getErrorInfo('恢复失败,请稍候再试!'));
    else
      return Json::successful('恢复成功!');
  }
}
