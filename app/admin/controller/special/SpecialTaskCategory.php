<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2021-09-26 09:59:49
 * @Last Modified time: 2021-11-24 15:14:28
 */
namespace app\admin\controller\special;

use think\Request;
use think\facade\Route as Url;
use app\admin\controller\AuthController;
use app\admin\model\special\SpecialTaskCategory as SpecialTaskCategoryModel;
use app\admin\model\special\SpecialSubject as SpecialSubjectCategoryModel;
use crmeb\services\{FormBuilder as Form, JsonService as Json, UtilService as Util};

/**
 * 素材分类控制器
 * Class SpecialTaskCategory
 * @package app\admin\controller\special
 */
class SpecialTaskCategory extends AuthController
{
    /**
     * 显示资源列表
     *
     * @return \think\Response
     */
    public function index()
    {
        $this->assign('pid', $this->request->get('pid', 0));
        $this->assign('cate', SpecialTaskCategoryModel::getTierList(null, 0));
        return $this->fetch();
    }

    /*
     *  异步获取分类列表
     *  @return json
     */
    public function category_list()
    {
        $where = Util::getMore([
            ['pid', $this->request->param('pid', '')],
            ['title', ''],
            ['page', 1],
            ['limit', 20],
            ['order', '']
        ]);
        
        return Json::successlayui(SpecialTaskCategoryModel::CategoryList($where));
    }

    
    public function get_cate_list()
    {
        $category=SpecialTaskCategoryModel::taskCategoryAll(2);
        return Json::successful($category);
    }

    public function get_special_cate_list()
    {
        $list = SpecialSubjectCategoryModel::specialCategoryAll();
        $category = [];
        foreach ($list as $menu) {
            $category[] = ['id' => $menu['id'], 'name' => $menu['html'] . $menu['name'], 'disabled' => $menu['grade_id'] == 0 ? 0 : 1];//,'disabled'=>$menu['pid']== 0];
        }
        return Json::successful($category);
    }

    /**
     * 设置分类显示|隐藏
     * @param string $is_show
     * @param string $id
     */
    public function set_show($is_show = '', $id = '')
    {
        ($is_show == '' || $id == '') && Json::fail('缺少参数');
        if (SpecialTaskCategoryModel::setCategoryShow($id, (int)$is_show)) {
            return Json::successful($is_show == 1 ? '显示成功' : '隐藏成功');
        } else {
            return Json::fail(SpecialTaskCategoryModel::getErrorInfo($is_show == 1 ? '显示失败' : '隐藏失败'));
        }
    }

    /**
     * 快速编辑
     * @param string $field
     * @param string $id
     * @param string $value
     */
    public function set_category($field = '', $id = '', $value = '')
    {
        $field == '' || $id == '' || $value == '' && Json::fail('缺少参数');
        if (SpecialTaskCategoryModel::where('id', $id)->update([$field => $value]))
            return Json::successful('保存成功');
        else
            return Json::fail('保存失败');
    }

    /**
     * 显示创建资源表单页.
     *
     * @return \think\Response
     */
    public function create()
    {
        $field = [
            Form::select('pid', '顶级素材分类')->setOptions(function () {
                $list = SpecialTaskCategoryModel::getTierList(null, 0);
                $menus = [['value' => 0, 'label' => '顶级分类']];
                foreach ($list as $menu) {
                    $menus[] = ['value' => $menu['id'], 'label' => $menu['html'] . $menu['title']];
                }
                return $menus;
            })->filterable(1),
            Form::input('title', '分类名称'),
            Form::number('sort', '排序'),
        ];
        $form = Form::make_post_form('添加分类', $field, Url::buildUrl('save'), 2);
        $this->assign(compact('form'));
        return $this->fetch('public/form-builder');
    }

    /**
     * 保存新建的资源
     *
     * @param \think\Request $request
     * @return \think\Response
     */
    public function save(Request $request)
    {
        $data = Util::postMore([
            'pid',
            'title',
            'sort',
        ], $request);
        if ($data['pid'] == '') return Json::fail('请选择父类');
        if (!$data['title']) return Json::fail('请输入分类名称');
        if ($data['sort'] < 0) $data['sort'] = 0;
        $data['add_time'] = time();
        SpecialTaskCategoryModel::create($data);
        return Json::successful('添加分类成功!');
    }

    /**
     * 显示编辑资源表单页.
     *
     * @param int $id
     * @return \think\Response
     */
    public function edit($id)
    {
        $c = SpecialTaskCategoryModel::get($id);
        if (!$c) return Json::fail('数据不存在!');
        $field = [
            Form::select('pid', '父级', (string)$c->getData('pid'))->setOptions(function () use ($id) {
                $list = SpecialTaskCategoryModel::getTierList(SpecialTaskCategoryModel::where('id', '<>', $id), 0);
                $menus = [['value' => 0, 'label' => '顶级菜单']];
                foreach ($list as $menu) {
                    $menus[] = ['value' => $menu['id'], 'label' => $menu['html'] . $menu['title']];
                }
                return $menus;
            })->filterable(1),
            Form::input('title', '分类名称', $c->getData('title')),
            Form::number('sort', '排序', $c->getData('sort')),
        ];
        $form = Form::make_post_form('编辑分类', $field, Url::buildUrl('update', array('id' => $id)), 2);

        $this->assign(compact('form'));
        return $this->fetch('public/form-builder');
    }

    /**
     * 保存更新的资源
     *
     * @param \think\Request $request
     * @param int $id
     * @return \think\Response
     */
    public function update(Request $request, $id)
    {
        $data = Util::postMore([
            'pid',
            'title',
            'sort',
        ], $request);
        if ($data['pid'] == '') return Json::fail('请选择父类');
        if (!$data['title']) return Json::fail('请输入分类名称');
        if ($data['sort'] < 0) $data['sort'] = 0;
        SpecialTaskCategoryModel::edit($data, $id);
        return Json::successful('修改成功!');
    }

    /**
     * 删除指定资源
     *
     * @param int $id
     * @return \think\Response
     */
    public function delete($id)
    {
        if (!SpecialTaskCategoryModel::delCategory($id))
            return Json::fail(SpecialTaskCategoryModel::getErrorInfo('删除失败,请稍候再试!'));
        else
            return Json::successful('删除成功!');
    }
}
