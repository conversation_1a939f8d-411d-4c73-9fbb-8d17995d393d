<?php

/**
 * @Author: <PERSON>
 * @Date:   2022-11-29 17:15:28
 * @Last Modified time: 2022-11-29 18:17:06
 */
namespace app\admin\controller\edu;

use app\admin\controller\AuthController;
use crmeb\services\{
    FormBuilder as Form,
    UtilService as Util,
    JsonService as Json
};
use app\admin\model\user\User;
use think\facade\Route as Url;
use app\admin\model\wechat\{
     WechatUser as UserModel
};

use app\admin\model\system\{
    SystemTeacher as SystemTeacherModel
};

/**
 * 教务-教师管理
 * Class Teacher
 * @package app\admin\controller\edu
 */
class Teacher extends AuthController
{
    /**
     * 显示资源列表
     *
     * @return \think\Response
     */
    public function index()
    {
        $this->assign(SystemTeacherModel::getList(0));
        return $this->fetch();
    }

    /**
     * 显示创建资源表单页.
     *
     * @return \think\Response
     */
    public function create()
    {
        $where = Util::getMore([
            ['type', 1],
            ['nickname', ''],
            ['data', ''],
            ['tagid_list', ''],
            ['groupid', '-1'],
            ['sex', ''],
            ['export', ''],
            ['stair', ''],
            ['second', ''],
            ['order_stair', ''],
            ['order_second', ''],
            ['subscribe', ''],
            ['now_money', ''],
            ['is_promoter', ''],
        ], $this->request);
        $this->assign('where', $where);
        $this->assign('type', $where['type']);
        $this->assign(UserModel::systemPage($where));
        $this->assign(['title' => '添加教师', 'save' => Url::buildUrl('save')]);
        return $this->fetch();
    }

    /**
     * 保存新建的资源
     */
    public function save()
    {
        $params = request()->post();
        if (count($params["checked_menus"]) <= 0) return Json::fail('请选择要添加的用户!');
        if (SystemTeacherModel::where(array('type'=>$params['type'],"uid" => array("in", implode(',', $params["checked_menus"]))))->count()) return Json::fail('添加用户中存在已有的老师!');
        foreach ($params["checked_menus"] as $key => $value) {
            $now_user = UserModel::get($value);
            $data[$key]["type"] = $params['type'];
            $data[$key]["uid"] = $now_user["uid"];
            $data[$key]["status"] = 1;
            $data[$key]["add_time"] = time();
        }
        SystemTeacherModel::setAll($data);
        return Json::successful('添加成功!');
    }

    /**
     * 显示编辑资源表单页.
     *
     * @param int $id
     * @return \think\Response
     */
    public function edit($id)
    {
        $service = SystemTeacherModel::get($id);
        if (!$service) return Json::fail('数据不存在!');
        $f = array();
        $chat_user_info = User::where(['uid' => $service['uid']])->find();
        $f[] = Form::frameImageOne('avatar', '头像', Url::buildUrl('widget.images/index', array('fodder' => 'avatar')), $chat_user_info['avatar'])->icon('image')->width('100%')->height('500px');
        $f[] = Form::input('nickname', '名称', $chat_user_info["nickname"])->disabled(1);
        $f[] = Form::radio('type', '类型', $service['type'])->options([['value' => 1, 'label' => '老师'], ['value' => 2, 'label' => '助理']]);
        $f[] = Form::radio('status', '状态', $service['status'])->options([['value' => 1, 'label' => '启用'], ['value' => 0, 'label' => '关闭']]);
        $form = Form::make_post_form('修改数据', $f, Url::buildUrl('update', compact('id')));
        $this->assign(compact('form'));
        return $this->fetch('public/form-builder');
    }

    /**
     * 保存新建的资源
     *
     * @param \think\Request $request
     * @return \think\Response
     */
    public function update($id)
    {
        $params = request()->post();
        $data = array("type" => $params["type"],"status"=> $params['status']);
        SystemTeacherModel::edit($data, $id);
        return Json::successful('修改成功!');
    }

    /**
     * 删除指定资源
     *
     * @param int $id
     * @return \think\Response
     */
    public function delete($id)
    {
        $data['status'] = 0;
        $data['is_del'] = 1;
        if (!SystemTeacherModel::edit($data,$id))
            return Json::fail(SystemTeacherModel::getErrorInfo('删除失败,请稍候再试!'));
        else
            return Json::successful('删除成功!');
    }
}