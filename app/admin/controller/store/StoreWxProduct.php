<?php

namespace app\admin\controller\store;

use app\admin\controller\AuthController;
use app\admin\model\store\{
    StoreDescription,
    StoreWxProductAttrValue,
    StoreProductAttr,
    StoreProductAttrResult,
    StoreProductCate,
    StoreProductLabel,
    StoreProductStore,
    StoreProductRelation,
    StoreProductAttrValue,
    StoreCategory as CategoryModel,
    StoreWxProduct as ProductModel,
    StoreQualification as QualificationModel,
};
use app\admin\model\evaluation\{
    StoreLabelCate,StoreLabelCategory,StoreLabelData
};
use app\admin\model\ump\StoreBargain;
use app\admin\model\ump\StoreCombination;
use app\admin\model\ump\StoreSeckill;
use crmeb\services\{
    JsonService, UtilService as Util, JsonService as Json, FormBuilder as Form
};
use crmeb\traits\CurdControllerTrait;
use think\facade\Route as Url;
use app\admin\model\system\{
    SystemAttachment, ShippingTemplates,
    SystemStore as SystemStoreModel,
};


/**
 * 产品管理
 * Class StoreWxProduct
 * @package app\admin\controller\store
 */
class StoreWxProduct extends AuthController
{

    use CurdControllerTrait;

    protected $bindModel = ProductModel::class;

    /**
     * 显示资源列表
     *
     * @return \think\Response
     */
    public function index()
    {
        $this->assign([
            'year' => get_month(),
            'end_year' => get_month(),
            'title' => $this->request->get('title', ''),
            'status' => $this->request->param('status', ''),
            'edit_status' => $this->request->param('edit_status', ''),
            'data_type' => $this->request->param('data_type', ''),
            'statusCount' => ProductModel::statusCount(),
            'editStatusCount' => ProductModel::editStatusCount(),
            'TypeCount' => ProductModel::TypeCount(),
            'dateTypeCount' => ProductModel::dateTypeCount(),
        ]);
        return $this->fetch();

    }


    // /**
    //  * 异步查找产品
    //  *
    //  * @return json
    //  */
    // public function product_ist()
    // {
    //     $where = Util::getMore([
    //         ['status', 5],
    //         ['start_create_time', ''],
    //         ['end_create_time', ''],
    //         ['start_update_time', ''],
    //         ['end_update_time', ''],
    //         ['page', 1],
    //         ['page_size', 20],
    //         ['need_edit_spu', 0],
    //     ]);
    //     return Json::successlayui(WxProductModel::ProductList($where));
    // }
    // 
    /**
     * 异步查找产品
     *
     * @return json
     */
    public function product_ist()
    {
        $where = Util::getMore([
            ['type', ''],
            ['title', ''],
            ['page', 1],
            ['limit', 20],
            ['status', ''],
            ['edit_status', ''],
            ['data_type', ''],
            ['data', ''],
            ['update_data', ''],
            ['excel', 0],
        ]);
        return Json::successlayui(ProductModel::ProductList($where));
    }
    /**
     * 设置单个产品上架|下架
     *
     * @return json
     */
    public function set_show($is_show = '', $id = '')
    {
        ($is_show == '' || $id == '') && Json::fail('缺少参数');
        $res = ProductModel::where(['id' => $id])->update(['is_show' => (int)$is_show]);
        if ($res) {
            return Json::successful($is_show == 1 ? '上架成功' : '下架成功');
        } else {
            return Json::fail($is_show == 1 ? '上架失败' : '下架失败');
        }
    }


    /**
     * 设置批量产品上架
     *
     * @return json
     */
    public function product_show()
    {
        $post = Util::postMore([
            ['ids', []]
        ]);
        if (empty($post['ids'])) {
            return Json::fail('请选择需要上架的产品');
        } else {
            $res = ProductModel::where('id', 'in', $post['ids'])->update(['is_show' => 1]);
            if ($res)
                return Json::successful('上架成功');
            else
                return Json::fail('上架失败');
        }
    }

    /**
     * 显示创建资源表单页.
     *
     * @return \think\Response
     */
    public function create($id = 0)
    {
        $this->assign('id', (int)$id);
        return $this->fetch();
    }

    /**
     * 获取规则属性模板
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function get_rule()
    {
        return Json::successful(\app\models\store\StoreProductRule::field(['rule_name', 'rule_value'])->select()->each(function ($item) {
            $item['rule_value'] = json_decode($item['rule_value'], true);
        })->toArray());
    }

    /**
     * 获取产品详细信息
     * @param int $id
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function get_product_info($id = 0, $is_new = 0 )
    {
        //获取已经审核成功的类目资质和品牌
        $categorys = QualificationModel::getTierShop(1)['data'];
        $brands = QualificationModel::getTierShop(2)['data'];
        $category_list = [];
        $brand_list = [];
        foreach ($categorys as $categorys) {
            $category_list[] = ['value' => $categorys['third_cat_id'], 'label' => $categorys['third_cat_name'], 'disabled' => 1];
        }
        foreach ($brands as $brands) {
            $brand_list[] = ['value' => $brands['brand_id'], 'label' => $brands['brand_wording'], 'disabled' => 1];
        }
        $data['thirdCatList'] = $category_list;
        $data['brandList'] = $brand_list;
        $data['productInfo'] = [];
        if ($id) {
            $productInfo = ProductModel::get($id);
            if (!$productInfo) {
                return Json::fail('修改的产品不存在');
            }
            $productInfo['third_cat_id'] = explode(',', $productInfo['third_cat_id']);
            $productInfo['brand_id'] = explode(',', $productInfo['brand_id']);
            $productInfo['head_img'] = is_string($productInfo['head_img']) ? json_decode($productInfo['head_img'], true) : [];
            $productInfo['qualification_pics'] = is_string($productInfo['qualification_pics']) ? json_decode($productInfo['qualification_pics'], true) : [];
            $productInfo['desc_info'] = is_string($productInfo['desc_info']) ? json_decode($productInfo['desc_info'], true) : [];
            $skus = is_string($productInfo['skus']) ? json_decode($productInfo['skus'], true) : []; 
            $items = is_string($productInfo['items']) ? json_decode($productInfo['items'], true) : [];
            if ($productInfo['spec_type'] == 1) {
                //是否要获取最新的attr
                $result['attr'] = $items;
                $result['value'] = $skus;
                foreach ($result['attr'] as $k => $v) {
                    $header[$k]['title'] = $v['value'];
                    $header[$k]['align'] = 'center';
                    $header[$k]['minWidth'] = 130;
                }
                foreach ($result['value'] as $k => $v) {
                    $num = 1;
                    foreach ($v['detail'] as $dv) {
                        $result['value'][$k]['value' . $num] = $dv;
                        $num++;
                    }
                }
                $header[] = ['title' => 'sku小图(尺寸：750*1000)', 'slot' => 'thumb_img', 'align' => 'center', 'minWidth' => 80];
                $header[] = ['title' => '售卖价格', 'slot' => 'sale_price', 'align' => 'center', 'minWidth' => 120];
                $header[] = ['title' => '市场价格', 'slot' => 'market_price', 'align' => 'center', 'minWidth' => 140];
                $header[] = ['title' => '库存', 'slot' => 'stock_num', 'align' => 'center', 'minWidth' => 140];
                $header[] = ['title' => '条形码', 'slot' => 'sku_code', 'align' => 'center', 'minWidth' => 140];
                $header[] = ['title' => '商品编码', 'slot' => 'barcode', 'align' => 'center', 'minWidth' => 140];
                $header[] = ['title' => '操作', 'slot' => 'action', 'align' => 'center', 'minWidth' => 70];
                $productInfo['attr'] = [];
                $productInfo['header'] = $header;
                $productInfo['items'] = $result['attr'];
                $productInfo['attrs'] = $result['value'];
            } else {
                $single = is_string($productInfo['skus']) && $productInfo['skus'] != "[]"  ? json_decode($productInfo['skus'], true)[0] : [];
                $productInfo['items'] = [];
                $productInfo['attrs'] = [];
                $productInfo['attr'] = [
                    'out_product_id' => $single['out_product_id'] ?? 0,
                    'out_sku_id' => $single['out_sku_id'] ?? 0,
                    'thumb_img' => $single['thumb_img'] ?? '',
                    'sale_price' => $single['sale_price'] ?? 0,
                    'market_price' => $single['market_price'] ?? 0,
                    'stock_num' => $single['stock_num'] ?? 0,
                    'sku_code' => $single['sku_code'] ?? '',
                    'barcode' => $single['barcode'] ?? '',
                    'sku_attrs'=> ['attr_key' => '规格', 'attr_value' => '默认']
                ];
            }
            //评测标签
            $data['productInfo'] = $productInfo;
        }
        return JsonService::successful($data);
    }

        /**
     * 获取产品详细信息
     * @param int $id
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function get_exempt_product_info($id = 0)
    {
        $data['productInfo'] = [];
        if ($id) {
            $productInfo = ProductModel::get($id);
            if (!$productInfo) {
                return Json::fail('修改的产品不存在');
            }
            //获取状态 如果是在线上获取线上的，如果还没有上线获取本地的 
            if ($productInfo['status'] == 5) {
                $skus = ProductModel::getShopSpu(1,$productInfo['product_id'],$productInfo['out_product_id'],1);
            }else{
                $skus = is_string($productInfo['skus']) ? json_decode($productInfo['skus'], true) : [];
            }
            $items = is_string($productInfo['items']) ? json_decode($productInfo['items'], true) : [];
            if ($productInfo['spec_type'] == 1) {
                //是否要获取最新的attr
                $result['attr'] = $items;
                $result['value'] = $skus;
                foreach ($result['attr'] as $k => $v) {
                    $header[$k]['title'] = $v['value'];
                    $header[$k]['align'] = 'center';
                    $header[$k]['minWidth'] = 130;
                }
                foreach ($result['value'] as $k => $v) {
                    $num = 1;
                    foreach ($v['detail'] as $dv) {
                        $result['value'][$k]['value' . $num] = $dv;
                        $num++;
                    }
                }
                $header[] = ['title' => 'sku小图(尺寸：750*1000)', 'slot' => 'thumb_img', 'align' => 'center', 'minWidth' => 80];
                $header[] = ['title' => '售卖价格', 'slot' => 'sale_price', 'align' => 'center', 'minWidth' => 120];
                $header[] = ['title' => '市场价格', 'slot' => 'market_price', 'align' => 'center', 'minWidth' => 140];
                $header[] = ['title' => '库存', 'slot' => 'stock_num', 'align' => 'center', 'minWidth' => 140];
                $header[] = ['title' => '条形码', 'slot' => 'sku_code', 'align' => 'center', 'minWidth' => 140];
                $header[] = ['title' => '商品编码', 'slot' => 'barcode', 'align' => 'center', 'minWidth' => 140];
                $header[] = ['title' => '操作', 'slot' => 'action', 'align' => 'center', 'minWidth' => 70];
                $productInfo['attr'] = [];
                $productInfo['header'] = $header;
                $productInfo['items'] = $result['attr'];
                $productInfo['attrs'] = $result['value'];
            } else {
                if ($productInfo['status'] == 5) {
                    $single = ProductModel::getShopSpu(1,$productInfo['product_id'],$productInfo['out_product_id'],1);
                    if (!empty($single)) $single = $single[0];
                }else{
                    $single = is_string($productInfo['skus']) && $productInfo['skus'] != "[]"  ? json_decode($productInfo['skus'], true)[0] : [];
                }
                $productInfo['items'] = [];
                $productInfo['attrs'] = [];
                $productInfo['attr'] = [
                    'out_product_id' => $single['out_product_id'] ?? 0,
                    'out_sku_id' => $single['out_sku_id'] ?? 0,
                    'thumb_img' => $single['thumb_img'] ?? '',
                    'sale_price' => $single['sale_price'] ?? 0,
                    'market_price' => $single['market_price'] ?? 0,
                    'stock_num' => $single['stock_num'] ?? 0,
                    'sku_code' => $single['sku_code'] ?? '',
                    'barcode' => $single['barcode'] ?? '',
                    'sku_attrs'=> ['attr_key' => '规格', 'attr_value' => '默认']
                ];
            }
            $data['productInfo'] = $productInfo;
        }
        return JsonService::successful($data);
    }

    /**
     * 保存新建的资源
     *
     *
     */
    public function save($id)
    {
        $data = Util::postMore([
            'out_product_id',
            'title',
            'path',
            'direct_path',
            ['head_img', []],
            ['qualification_pics', []],
            ['desc_info', []],
            ['third_cat_id', 0], 
            ['brand_id', 0], 
            ['info_version', ''], 
            ['spec_type', 0], 
            ['items', []],
            ['attrs', []],
            ['skus', []],
            ['scene_group_list', 1], 
        ]);
        if (!$data['third_cat_id']) return Json::fail('请选择类目');
        if (!$data['out_product_id']) return Json::fail('请关联商品');
        if (!$data['title']) return Json::fail('请输入商品名称');
        if (!$data['path']) return Json::fail('请输入小程序商品路径');
        if (count($data['head_img']) < 1) return Json::fail('请上传产品主图');
        if (count($data['attrs']) < 1) return Json::fail('请填写商品规格数据');
        $data['skus'] = $data['attrs'];
        unset($data['attrs']);
        ProductModel::beginTrans();
        if ($id) {
            $res = ProductModel::editShop($data, $id);
            if ($res) {
                ProductModel::commitTrans();
                return Json::success('修改成功!');
            } else {
                ProductModel::rollbackTrans();
                return Json::fail(StoreProductAttr::getErrorInfo());
            }
        } else {
            $res = ProductModel::addShop($data);
            if ($res) {
                ProductModel::commitTrans();
                return Json::success('添加产品成功!');
            } else {
                ProductModel::rollbackTrans();
                return Json::fail(StoreProductAttr::getErrorInfo());
            }
        }
    }

    public function update_without_audit($id)
    {
        $data = Util::postMore([
            'out_product_id',
            'product_id',
            ['attrs', []],
        ]);
        if (count($data['attrs']) < 1) return Json::fail('请填写商品规格数据');
        ProductModel::beginTrans();
        $res = ProductModel::updateWithoutShop($data, $id);
        if ($res) {
            ProductModel::commitTrans();
            return Json::success('修改成功!');
        } else {
            ProductModel::rollbackTrans();
            return Json::fail(StoreProductAttr::getErrorInfo());
        }
    }





    /**
     * 生成属性
     * @param int $id
     */
    public function is_format_attr($id = 0, $type = 0)
    {
        $data = Util::postMore([
            ['attrs', []],
            ['items', []]
        ]);
        $attr = $data['attrs'];
        $value = attr_format($attr)[1];
        $valueNew = [];
        $count = 0;

        // dd($value);
        foreach ($value as $key => $item) {
            $detail = $item['detail'];

            // dd($detail);
            sort($item['detail'], SORT_STRING);
            $suk = implode(',', $item['detail']);
            $types = 1;
            if ($id) {
                $sukValue = StoreWxProductAttrValue::where('product_id', $id)->where('type', 0)->where('suk', $suk)->column('bar_code,cost,price,vip_price,ot_price,stock,image as pic,weight,volume,brokerage,brokerage_two', 'suk');
                if (!count($sukValue)) {
                    if ($type == 0) $types = 0; //编辑商品时，将没有规格的数据不生成默认值
                    $sukValue[$suk]['thumb_img'] = '';
                    $sukValue[$suk]['sale_price'] = 0;
                    $sukValue[$suk]['market_price'] = 0;
                    $sukValue[$suk]['stock_num'] = 0;
                    $sukValue[$suk]['sku_code'] = '';
                    $sukValue[$suk]['barcode'] = '';
                }
            } else {
                    $sukValue[$suk]['thumb_img'] = '';
                    $sukValue[$suk]['sale_price'] = 0;
                    $sukValue[$suk]['market_price'] = 0;
                    $sukValue[$suk]['stock_num'] = 0;
                    $sukValue[$suk]['sku_code'] = '';
                    $sukValue[$suk]['barcode'] = '';
            }
            if ($types) { //编辑商品时，将没有规格的数据不生成默认值
                foreach (array_keys($detail) as $k => $title) {
                    $header[$k]['title'] = $title;
                    $header[$k]['align'] = 'center';
                    $header[$k]['minWidth'] = 130;
                }
                foreach (array_values($detail) as $k => $v) {
                    $valueNew[$count]['value' . ($k + 1)] = $v;
                    $header[$k]['key'] = 'value' . ($k + 1);
                }
                $valueNew[$count]['detail'] = $detail;
                $valueNew[$count]['thumb_img'] = $sukValue[$suk]['pic'] ?? '';
                $valueNew[$count]['sale_price'] = $sukValue[$suk]['sale_price'] ? floatval($sukValue[$suk]['sale_price']) : 0;
                $valueNew[$count]['market_price'] = $sukValue[$suk]['market_price'] ? floatval($sukValue[$suk]['market_price']) : 0;
                $valueNew[$count]['stock_num'] = $sukValue[$suk]['stock_num'] ? intval($sukValue[$suk]['stock_num']) : 0;
                $valueNew[$count]['sku_code'] = $sukValue[$suk]['sku_code'] ?? '';
                $valueNew[$count]['barcode'] = $sukValue[$suk]['barcode'] ?? '';
                $count++;
            }
        }
        $header[] = ['title' => 'sku小图(尺寸：750*1000)', 'slot' => 'thumb_img', 'align' => 'center', 'minWidth' => 80];
        $header[] = ['title' => '售卖价格', 'slot' => 'sale_price', 'align' => 'center', 'minWidth' => 120];
        $header[] = ['title' => '市场价格', 'slot' => 'market_price', 'align' => 'center', 'minWidth' => 140];
        $header[] = ['title' => '库存', 'slot' => 'stock_num', 'align' => 'center', 'minWidth' => 140];
        $header[] = ['title' => '条形码', 'slot' => 'sku_code', 'align' => 'center', 'minWidth' => 140];
        $header[] = ['title' => '商品编码', 'slot' => 'barcode', 'align' => 'center', 'minWidth' => 140];
        $header[] = ['title' => '操作', 'slot' => 'action', 'align' => 'center', 'minWidth' => 70];
        $info = ['attr' => $attr, 'value' => $valueNew, 'header' => $header];
        return Json::successful($info);
    }

    public function set_attr($id)
    {
        if (!$id) return $this->failed('产品不存在!');
        list($attr, $detail) = Util::postMore([
            ['items', []],
            ['attrs', []]
        ], null, true);
        $res = StoreProductAttr::createProductAttr($attr, $detail, $id);
        if ($res)
            return $this->successful('编辑属性成功!');
        else
            return $this->failed(StoreProductAttr::getErrorInfo());
    }

    public function clear_attr($id)
    {
        if (!$id) return $this->failed('产品不存在!');
        if (false !== StoreProductAttr::clearProductAttr($id) && false !== StoreProductAttrResult::clearResult($id))
            return $this->successful('清空产品属性成功!');
        else
            return $this->failed(StoreProductAttr::getErrorInfo('清空产品属性失败!'));
    }

    /**
     * 删除指定资源
     *
     * @param int $id
     * @return \think\Response
     */
    public function del($id)
    {
        if (!$id) return $this->failed('数据不存在');
        if (!ProductModel::be(['id' => $id])) return $this->failed('产品数据不存在');
        if (false !== ProductModel::delShop($id))
            return $this->successful('成功删除!');
        else
            return $this->failed(ProductModel::getErrorInfo());
    }


    /**
     * 下架指定资源
     *
     * @param int $id
     * @return \think\Response
     */
    public function listing($id)
    {
        if (!$id) return $this->failed('数据不存在');
        if (!ProductModel::be(['id' => $id])) return $this->failed('产品数据不存在');
        if (false !== ProductModel::listingShop($id))
            return $this->successful('上架成功!');
        else
            return $this->failed(ProductModel::getErrorInfo());
    }



    /**
     * 撤销审核
     *
     * @param int $id
     * @return \think\Response
     */
    public function del_audit($id)
    {
        if (!$id) return $this->failed('数据不存在');
        if (!ProductModel::be(['id' => $id])) return $this->failed('产品数据不存在');
        if (false !== ProductModel::delAuditShop($id))
            return $this->successful('撤回商品审核成功!');
        else
            return $this->failed(ProductModel::getErrorInfo());
    }



    public function delisting()
    {
        $data = Util::postMore(['id', 'remark']);
        if (!$data['id']) return Json::fail('参数错误!');
        if ($data['remark'] == '') return Json::fail('请输入要手动下架的原因!');
        $id = $data['id'];
        $data['take_down_reason'] = json_encode($data['remark']);
        unset($data['id'],$data['remark']);
        if (false !== ProductModel::delistingShop($data, $id))
            return $this->successful('下架成功!');
        else
            return $this->failed(ProductModel::getErrorInfo());
    }


    /**
     * 更新状态
     *
     * @param int $id
     * @return \think\Response
     */
    public function update_status($id)
    {
        if (!$id) return $this->failed('数据不存在');
        if (!ProductModel::be(['id' => $id])) return $this->failed('不存在');
        if (!ProductModel::update_status($id))
            return Json::fail(ProductModel::getErrorInfo());
        else
            return Json::successful('状态已更新，请刷新当前界面!');
    }


    /**
     * 获取商品列表
     * @return string
     * @throws \Exception
     */
    public function productList($type = 1)
    {
        $id = 0;
        $cate = CategoryModel::getTierList(null, 1);
        $this->assign('cate', $cate);
        $this->assign('id', (int)$id);
        return $this->fetch();
    }


    /**
     * 修改产品库存
     *
     */
    public function edit_product_stock()
    {
        $data = Util::postMore([
            ['id', 0],
            ['stock', 0],
        ]);
        if (!$data['id']) return Json::fail('参数错误');
        $res = ProductModel::edit(['stock' => $data['stock']], $data['id']);
        if ($res) return Json::successful('修改成功');
        else return Json::fail('修改失败');
    }


    /**
     * 检测商品是否开活动
     * @param $id
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function check_activity($id)
    {
        if ($id != 0) {
            if (ProductModel::be(['id' => $id, 'edit_status'=>4 ])) return  Json::successful('该商品目前审核成功状态，无法删除属性');
            if (ProductModel::be(['id' => $id, 'status'=> 5])) return  Json::successful('该商品目前上架状态，无法删除属性');
            return Json::successful('删除成功');
        } else {
            return Json::successful('没有参数ID');
        }
    }

    public function sku_info($id = '')
    {
        $this->assign('id', (int)$id);
        return $this->fetch();
    }

}