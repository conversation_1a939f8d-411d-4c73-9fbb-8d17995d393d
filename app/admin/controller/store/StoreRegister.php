<?php

namespace app\admin\controller\store;

use app\admin\controller\AuthController;
use app\admin\model\store\{
    StoreQualification as QualificationModel,
};
use crmeb\services\{
    JsonService as Json
};
use crmeb\traits\CurdControllerTrait;


/**
 * 获取接入相关信息
 * Class StoreRegister
 * @package app\admin\controller\store
 */
class StoreRegister extends AuthController
{

    use CurdControllerTrait;

    protected $bindModel = QualificationModel::class;

    /**
     * 显示资源列表
     *
     * @return \think\Response
     */
    public function index()
    {
        $type = $this->request->param('type', 1);
        $this->assign(compact('type' ));
        return $this->fetch();
    }

    /**
     * 异步查找产品
     *
     * @return json
     */
    public function list()
    {
    	$type = $this->request->param('type', 1);
        return Json::successlayui(QualificationModel::getTierShop($type));
    }
}
