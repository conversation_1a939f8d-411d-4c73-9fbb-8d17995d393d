<?php

namespace app\admin\controller\store;

use app\admin\controller\AuthController;
use think\facade\Route as Url;
use app\admin\model\wechat\WechatUser as UserModel;
use app\admin\model\store\{StoreCouponIssue, StoreCoupon as CouponModel,StoreProduct as ProductModel};
use crmeb\services\{FormBuilder as Form, UtilService as Util, JsonService as Json};

/**
 * 优惠券控制器
 * Class StoreCategory
 * @package app\admin\controller\system
 */
class StoreCoupon extends AuthController
{

    /**
     * @return mixed
     */
    public function index()
    {
        $this->assign([
            'year' => get_month(),
            'statusCount' => CouponModel::statusCount(),
            'typeCount' => CouponModel::typeCount(),
        ]);
        return $this->fetch();
    }


    /**
     * 异步查找产品
     *
     * @return json
     */
    public function coupon_ist()
    {
        $where = Util::getMore([
            ['name', ''],
            ['type', ''],
            ['page', 1],
            ['limit', 20],
            ['status', ''],
            ['data', ''],
            ['excel', 0],
        ]);
        return Json::successlayui(CouponModel::CouponList($where));
    }


    /**
     * @return mixed
     */
    public function create()
    {
        $data = Util::getMore(['type',]);//接收参数
        $tab_id = !empty(request()->param('tab_id')) ? request()->param('tab_id') : 1;
        //前面通用字段
        $f = [];
        // 优惠券推广类型
        $f[] = Form::input("coupon_info[name]", '优惠券名称');
        $f[] = Form::frameImageOne('image', '关联已有优惠卷', Url::buildUrl('6GqvmHa8HRGHoQEQ/store.StoreCoupon/select_coupon', array('fodder' => 'image')))->maxLength(1)->icon('plus')->width('100%')->height('500px');
        $f[] = Form::hidden("out_coupon_id", '');
        $f[] = Form::hidden("coupon_info[promote_info][promote_type]", 4);
        $f[] = Form::input("coupon_info[promote_info][finder][nickname]", '推广视频号昵称');
        // // 优惠券优惠
        $f[] = Form::input("coupon_info[discount_info][discount_condition][product_cnt]", '优惠条件所需的商品数');
        $f[] = Form::input("coupon_info[discount_info][discount_condition][product_price]", '优惠条件所需满足的金额');
        $f[] = Form::frameImages('product', '指定商品商家侧ID', Url::buildUrl('6GqvmHa8HRGHoQEQ/store.StoreCoupon/select_product', array('multiple' => 1,'fodder' => 'product','field'=>'coupon_info[discount_info][discount_condition][out_product_ids]')))->icon('plus')->width('100%')->height('500px');
        $f[] = Form::hidden('coupon_info[discount_info][discount_condition][out_product_ids]', 0);
        //不同类型不同字段
        $formbuider = [];
        switch ($data['type']) {
            case 1://商品条件折扣券
                // $formbuider = CouponModel::createProductConditionalDiscountsRule($tab_id);
                $f[] = Form::input("coupon_info[discount_info][discount_num]", '折扣数,比如5.1折,则填5100');
                break;
            case 2://商品满减券
                // $formbuider = CouponModel::createProductReductionsRule($tab_id);
                //满减券
                $f[] = Form::input("coupon_info[discount_info][discount_fee]", '减金额,单位为分');
                break;
            case 3://商品统一折扣券
                // $formbuider = CouponModel::createProductFlatDiscountRule($tab_id);
                $f[] = Form::input("coupon_info[discount_info][discount_num]", '折扣数,比如5.1折,则填5100');
                break;
            case 4://商品直减券
                // $formbuider = CouponModel::createProductDirectReductionRule($tab_id);
                $f[] = Form::input("coupon_info[discount_info][discount_fee]", '减金额,单位为分');
                break;
            case 5://商品换购券
                // $formbuider = CouponModel::createProductExchangeRule($tab_id);
                $f[] = Form::frameImages('tradein_product', '选择换购商品', Url::buildUrl('6GqvmHa8HRGHoQEQ/store.StoreCoupon/select_product', array('multiple' => 0, 'fodder' => 'tradein_product','field'=>'coupon_info[discount_info][discount_condition][tradein_info][out_product_id]')))->icon('plus')->width('100%')->height('500px');
                $f[] = Form::input("coupon_info[discount_info][discount_condition][tradein_info][price]", '需要支付的金额');
                $f[] = Form::hidden("coupon_info[discount_info][discount_condition][tradein_info][out_product_id]", '');

                break;
            case 6://   商品买赠券
                // $formbuider = CouponModel::createProductBuyFreeRule($tab_id);
                $f[] = Form::frameImages('buy_product', '购买商品商家侧ID', Url::buildUrl('6GqvmHa8HRGHoQEQ/store.StoreCoupon/select_product', array('multiple' => 0,'fodder' => 'buy_product','field'=>'coupon_info[discount_info][discount_condition][buyget_info][buy_out_product_id]')))->icon('plus')->width('100%')->height('500px');
                $f[] = Form::input("coupon_info[discount_info][discount_condition][buyget_info][buy_product_cnt]", '购买商品数');
                $f[] = Form::frameImages('get_product', '赠送商品商家侧ID', Url::buildUrl('6GqvmHa8HRGHoQEQ/store.StoreCoupon/select_product', array('multiple' => 0,'fodder' => 'get_product','field'=>'coupon_info[discount_info][discount_condition][buyget_info][get_out_product_id]')))->icon('plus')->width('100%')->height('500px');
                $f[] = Form::input("coupon_info[discount_info][discount_condition][buyget_info][get_product_cnt]", '赠送商品数');
                $f[] = Form::hidden("coupon_info[discount_info][discount_condition][buyget_info][buy_out_product_id]", 0);
                $f[] = Form::hidden("coupon_info[discount_info][discount_condition][buyget_info][get_out_product_id]", 0);
                break;
        }
        //领取
        $f[] = Form::number("coupon_info[receive_info][total_num]", '总发放量');
        $f[] = Form::number("coupon_info[receive_info][limit_num_one_person]", '个人限领张数');
        $f[] = Form::datetime("coupon_info[receive_info][start_time]", '领取开始时间');
        $f[] = Form::datetime("coupon_info[receive_info][end_time]", '领取结束时间');
        //生效
        $f[] = Form::select('coupon_info[valid_info][valid_type]', '选择有效期类型')->setOptions(function () {
            $list = [
                ['name'=>'商品指定时间区间','value'=>1],
                ['name'=>'生效天数','value'=>2],
                ['name'=>'生效秒数','value'=>3],
            ];
            $menus = [];
            foreach ($list as $menu) {
                $menus[] = ['value' => $menu['value'], 'label' => $menu['name']];
            }
            return $menus;
        })->filterable(1)->col(12);
        // 生效
        $f[] = Form::input("coupon_info[valid_info][valid_day_num]", '生效天数');
        $f[] = Form::input("coupon_info[valid_info][valid_second]", '生效秒数');
        $f[] = Form::datetime("coupon_info[valid_info][start_time]", '生效开始');
        $f[] = Form::datetime("coupon_info[valid_info][end_time]", '生效结束开始');
        //后面通用字段
        $f[] = Form::hidden('promote_type', 4);
        $f[] = Form::hidden('type', $data['type']);
        $form = Form::make_post_form('添加优惠券', $f, Url::buildUrl('save'));
        $this->assign(compact('form'));
        $this->assign('get', request()->param());
        return $this->fetch();
    }



    /**
     * 保存
     */
    public function save()
    {
        $data = Util::postMore([
            ['out_coupon_id', ''],
            ['type', 1],
            ['promote_type', 4],
            ['coupon_info', []],
        ]);
        $data['type'] = (int) $data['type'];
        $data['promote_type'] = (int) $data['promote_type'];
        //基础
        if (!$data['coupon_info']['name']) return Json::fail('请输入优惠卷名称');
        if (!$data['out_coupon_id']) return Json::fail('请关联已有优惠卷');
        if (!$data['coupon_info']['promote_info']['finder']['nickname']) return Json::fail('请输入推广视频号昵称');
        //详细
        if (!$data['coupon_info']['discount_info']['discount_condition']['out_product_ids']) return Json::fail('请选择指定商品商家侧ID');
        if ($data['type'] == 1 || $data['type'] == 3) {//折扣券
            if (!$data['coupon_info']['discount_info']['discount_num']) return Json::fail('请填写折扣数,比如5.1折,则填5100');
        }
        if ($data['type'] == 2 || $data['type'] == 4) {//直减券、满减券
            if (!$data['coupon_info']['discount_info']['discount_fee']) return Json::fail('请填写减金额,单位为分');
        }
        if ($data['type'] == 5) {//换购券
            if (!$data['coupon_info']['discount_info']['discount_condition']['tradein_info']['out_product_id']) return Json::fail('请选择换购商品商家侧ID');
            if (!$data['coupon_info']['discount_info']['discount_condition']['tradein_info']['price']) return Json::fail('请填写换购商品,需要支付的金额');
        }
        if ($data['type'] == 6) {//买赠券
            if (!$data['coupon_info']['discount_info']['discount_condition']['buyget_info']['buy_out_product_id']) return Json::fail('请选择购买商品商家侧ID');
            if (!$data['coupon_info']['discount_info']['discount_condition']['buyget_info']['buy_product_cnt']) return Json::fail('请填写购买商品数');
            if (!$data['coupon_info']['discount_info']['discount_condition']['buyget_info']['get_out_product_id']) return Json::fail('请选择赠送商品商家侧ID');
            if (!$data['coupon_info']['discount_info']['discount_condition']['buyget_info']['get_product_cnt']) return Json::fail('请填写赠送商品数');
        }
        //领取条件
        if (!$data['coupon_info']['receive_info']['total_num']) return Json::fail('请输入总发放量');
        if (!$data['coupon_info']['receive_info']['limit_num_one_person']) return Json::fail('请输入个人限领张数');
        if (!$data['coupon_info']['receive_info']['start_time']) return Json::fail('请输入领取开始时间');
        if (!$data['coupon_info']['receive_info']['end_time']) return Json::fail('请输入领取结束时间');
        // 格式化时间
        $data['coupon_info']['receive_info']['start_time']  = strtotime($data['coupon_info']['receive_info']['start_time']);
        $data['coupon_info']['receive_info']['end_time']  = strtotime($data['coupon_info']['receive_info']['end_time']);
        //发放条件
        if (!$data['coupon_info']['valid_info']['valid_type']) return Json::fail('请输入有效期类型');
        if ($data['coupon_info']['valid_info']['valid_type'] == 2 && !$data['coupon_info']['valid_info']['valid_day_num']) return Json::fail('请输入生效天数');
        if ($data['coupon_info']['valid_info']['valid_type'] == 3 && !$data['coupon_info']['valid_info']['valid_second']) return Json::fail('请输入生效秒数');
        if ($data['coupon_info']['valid_info']['valid_type'] == 1 && !$data['coupon_info']['valid_info']['start_time']) return Json::fail('请输入生效开始时间');
        if ($data['coupon_info']['valid_info']['valid_type'] == 1 && !$data['coupon_info']['valid_info']['end_time']) return Json::fail('请输入生效结束时间');
        // 格式化时间
        if ($data['coupon_info']['valid_info']['start_time'] != "") $data['coupon_info']['valid_info']['start_time']  = strtotime($data['coupon_info']['valid_info']['start_time']);
        if ($data['coupon_info']['valid_info']['end_time'] != "") $data['coupon_info']['valid_info']['end_time']  = strtotime($data['coupon_info']['valid_info']['end_time']);
        $res = CouponModel::addCoupon($data);
        if ($res) {
            CouponModel::commitTrans();
            return Json::success('添加优惠券成功!');
        } else {
            CouponModel::rollbackTrans();
            return Json::fail(CouponModel::getErrorInfo());
        }
    }

    /**
     * 显示编辑资源表单页.
     * @param $id
     * @return string|void
     * @throws \FormBuilder\exception\FormBuilderException
     */
    public function edit($id)
    {
        $data = Util::getMore(['type',]);//接收参数
        $tab_id = !empty(request()->param('tab_id')) ? request()->param('tab_id') : 1;
        $coupon = CouponModel::get($id);
        if (!$coupon) return Json::fail('数据不存在!');
        $coupon_info = json_decode($coupon->coupon_info, true);
        $f = [];
        // 优惠券推广类型
        $f[] = Form::input("coupon_info[name]", '优惠券名称',$coupon_info['name']);
        $f[] = Form::frameImages('image', '关联已有优惠卷', Url::buildUrl('6GqvmHa8HRGHoQEQ/store.StoreCoupon/select', array('fodder' => 'image')),['http://cshop.arthorize.com/attach/2022/08/bc228202208291342599129.png'])->icon('plus')->width('100%')->height('500px');
        $f[] = Form::hidden("coupon_info[promote_info][promote_type]", 4);
        $f[] = Form::hidden("coupon_info[out_coupon_id]", $coupon->getData('out_coupon_id'));
        $f[] = Form::input("coupon_info[promote_info][finder][nickname]", '推广视频号昵称',$coupon_info['promote_info']['finder']['nickname']);
        // 优惠券优惠
        $f[] = Form::input("coupon_info[discount_info][discount_condition][product_cnt]", '优惠条件所需的商品数',$coupon_info['discount_info']['discount_condition']['product_cnt']);
        $f[] = Form::input("coupon_info[discount_info][discount_condition][product_price]", '优惠条件所需满足的金额',$coupon_info['discount_info']['discount_condition']['product_price']);
        //获取关联商品图
        $out_product_ids = $coupon_info['discount_info']['discount_condition']['out_product_ids'];
        $out_product_images = ProductModel::whereIn('id',$out_product_ids)->column('image');
        $f[] = Form::frameImages('product', '指定商品商家侧ID', Url::buildUrl('6GqvmHa8HRGHoQEQ/store.StoreCoupon/select_product', array('multiple' => 1,'fodder' => 'product','field'=>'coupon_info[discount_info][discount_condition][out_product_ids]')),$out_product_images)->maxLength(128)->icon('plus')->width('100%')->height('500px');
        if (is_array($out_product_ids)) {
            foreach ($out_product_ids as $key => $value) {
                $f[] = Form::hidden("coupon_info[discount_info][discount_condition][out_product_ids]", $value);
            }
        }else{
            $f[] = Form::hidden("coupon_info[discount_info][discount_condition][out_product_ids]", '');
        }
        $formbuider = [];
        switch ($data['type']) {
            case 1://商品条件折扣券
                $f[] = Form::input("coupon_info[discount_info][discount_num]", '折扣数,比如5.1折,则填5100',$coupon_info['discount_info']['discount_num']);
                // $formbuider = CouponModel::createProductConditionalDiscountsRule($tab_id);
                break;
            case 2://商品满减券
                // $formbuider = CouponModel::createProductReductionsRule($tab_id);
                //满减券
                $f[] = Form::input("coupon_info[discount_info][discount_fee]", '减金额,单位为分',$coupon_info['discount_info']['discount_fee']);
                break;
            case 3://商品统一折扣券
                // $formbuider = CouponModel::createProductFlatDiscountRule($tab_id);
                $f[] = Form::input("coupon_info[discount_info][discount_num]", '折扣数,比如5.1折,则填5100',$coupon_info['discount_info']['discount_num']);
                break;
            case 4://商品直减券
                // $formbuider = CouponModel::createProductDirectReductionRule($tab_id);
                $f[] = Form::input("coupon_info[discount_info][discount_fee]", '减金额,单位为分',$coupon_info['discount_info']['discount_fee']);
                break;
            case 5://商品换购券
                // $formbuider = CouponModel::createProductExchangeRule($tab_id);
                $tradein_info_out_product_ids = $coupon_info['discount_info']['discount_condition']['tradein_info']['out_product_id'];
                $tradein_info_out_product_images = ProductModel::whereIn('id',$tradein_info_out_product_ids)->column('image');
                $f[] = Form::frameImages('tradein_product', '选择换购商品', Url::buildUrl('6GqvmHa8HRGHoQEQ/store.StoreCoupon/select_product', array('multiple' => 0, 'fodder' => 'tradein_product','field'=>'coupon_info[discount_info][discount_condition][tradein_info][out_product_id]')),$tradein_info_out_product_images)->icon('plus')->width('100%')->height('500px');
                $f[] = Form::input("coupon_info[discount_info][discount_condition][tradein_info][price]", '需要支付的金额',$coupon_info['discount_info']['discount_condition']['tradein_info']['price']);
                $f[] = Form::hidden("coupon_info[discount_info][discount_condition][tradein_info][out_product_id]", '');
                break;
            case 6://   商品买赠券
                // $formbuider = CouponModel::createProductBuyFreeRule($tab_id);
                $buyget_info_buy_out_product_id = $coupon_info['discount_info']['discount_condition']['buyget_info']['buy_out_product_id'];
                $buyget_info_buy_out_product_images = ProductModel::whereIn('id',$buyget_info_buy_out_product_id)->column('image');
                $buyget_info_get_out_product_id = $coupon_info['discount_info']['discount_condition']['buyget_info']['get_out_product_id'];
                $buyget_info_get_out_product_images = ProductModel::whereIn('id',$buyget_info_get_out_product_id)->column('image');
                $f[] = Form::frameImages('buy_product', '购买商品商家侧ID', Url::buildUrl('6GqvmHa8HRGHoQEQ/store.StoreCoupon/select_product', array('fodder' => 'buy_product','field'=>'coupon_info[discount_info][discount_condition][buyget_info][buy_out_product_id]')),$buyget_info_buy_out_product_images)->icon('plus')->width('100%')->height('500px');
                $f[] = Form::input("coupon_info[discount_info][discount_condition][buyget_info][buy_product_cnt]", '购买商品数',$coupon_info['discount_info']['discount_condition']['buyget_info']['buy_product_cnt']);
                $f[] = Form::frameImages('get_product', '赠送商品商家侧ID', Url::buildUrl('6GqvmHa8HRGHoQEQ/store.StoreCoupon/select_product', array('fodder' => 'get_product','field'=>'coupon_info[discount_info][discount_condition][buyget_info][get_out_product_id]')),$buyget_info_get_out_product_images)->icon('plus')->width('100%')->height('500px');
                $f[] = Form::input("coupon_info[discount_info][discount_condition][buyget_info][get_product_cnt]", '赠送商品数',$coupon_info['discount_info']['discount_condition']['buyget_info']['get_product_cnt']);
                $f[] = Form::hidden("coupon_info[discount_info][discount_condition][buyget_info][buy_out_product_id]", $coupon_info['discount_info']['discount_condition']['buyget_info']['buy_out_product_id']);
                $f[] = Form::hidden("coupon_info[discount_info][discount_condition][buyget_info][get_out_product_id]", $coupon_info['discount_info']['discount_condition']['buyget_info']['get_out_product_id']);
                break;
        }
        //领取
        $f[] = Form::number("coupon_info[receive_info][total_num]", '总发放量',$coupon_info['receive_info']['total_num']);
        $f[] = Form::number("coupon_info[receive_info][limit_num_one_person]", '个人限领张数',$coupon_info['receive_info']['limit_num_one_person']);
        $f[] = Form::datetime("coupon_info[receive_info][start_time]", '领取开始时间',$coupon_info['receive_info']['start_time']);
        $f[] = Form::datetime("coupon_info[receive_info][end_time]", '领取结束时间',$coupon_info['receive_info']['end_time']);

        //生效
        $f[] = Form::select('coupon_info[valid_info][valid_type]', '选择品类',$coupon_info['valid_info']['valid_type'])->setOptions(function () {
            $list = [
                ['name'=>'商品指定时间区间','value'=>1],
                ['name'=>'生效天数','value'=>2],
                ['name'=>'生效秒数','value'=>3],
            ];
            $menus = [];
            foreach ($list as $menu) {
                $menus[] = ['value' => $menu['value'], 'label' => $menu['name']];
            }
            return $menus;
        })->filterable(1)->col(12);
        // // 生效
        $f[] = Form::input("coupon_info[valid_info][valid_day_num]", '生效天数', $coupon_info['valid_info']['valid_day_num']);
        $f[] = Form::input("coupon_info[valid_info][valid_second]", '生效秒数',$coupon_info['valid_info']['valid_second']);
        $f[] = Form::datetime("coupon_info[valid_info][start_time]", '生效开始',$coupon_info['valid_info']['start_time']);
        $f[] = Form::datetime("coupon_info[valid_info][end_time]", '生效结束开始',$coupon_info['valid_info']['end_time']);
        //后面通用字段
        $f[] = Form::hidden('promote_type', 4);
        $f[] = Form::hidden('out_coupon_id', $coupon->getData('out_coupon_id'));
        $f[] = Form::hidden('type', $data['type']);
        if ($coupon['status'] == 2) {
            $f[] = Form::radio('status', '状态', $coupon->getData('status'))->options([['label' => '已过期', 'value' => 3],['label' => '已作废', 'value' => 4]]);
        }
        $form = Form::make_post_form('优惠券', $f, Url::buildUrl('update', array('id' => $id)));
        $this->assign(compact('form'));
        $this->assign('get', request()->param());
        return $this->fetch();
    }


    /**
     * 保存更新的资源
     *
     * @param $id
     */
    public function update($id)
    {
        $data = Util::postMore([
            ['out_coupon_id', ''],
            ['type', 1],
            ['promote_type', 4],
            ['coupon_info', []],
            ['status', 1]
        ]);
        //基础
        if (!$data['coupon_info']['name']) return Json::fail('请输入优惠卷名称');
        if (!$data['out_coupon_id']) return Json::fail('请关联已有优惠卷');
        if (!$data['coupon_info']['promote_info']['finder']['nickname']) return Json::fail('请输入推广视频号昵称');
        //详细
        if (!$data['coupon_info']['discount_info']['discount_condition']['out_product_ids']) return Json::fail('请选择指定商品商家侧ID');
        if ($data['type'] == 1 || $data['type'] == 3) {//折扣券
            if (!$data['coupon_info']['discount_info']['discount_num']) return Json::fail('请填写折扣数,比如5.1折,则填5100');
        }
        if ($data['type'] == 2 || $data['type'] == 4) {//直减券、满减券
            if (!$data['coupon_info']['discount_info']['discount_fee']) return Json::fail('请填写减金额,单位为分');
        }
        if ($data['type'] == 5) {//换购券
            if (!$data['coupon_info']['discount_info']['discount_condition']['tradein_info']['out_product_id']) return Json::fail('请选择换购商品商家侧ID');
            if (!$data['coupon_info']['discount_info']['discount_condition']['tradein_info']['price']) return Json::fail('请填写换购商品,需要支付的金额');
        }
        if ($data['type'] == 6) {//买赠券
            if (!$data['coupon_info']['discount_info']['discount_condition']['buyget_info']['buy_out_product_id']) return Json::fail('请选择购买商品商家侧ID');
            if (!$data['coupon_info']['discount_info']['discount_condition']['buyget_info']['buy_product_cnt']) return Json::fail('请填写购买商品数');
            if (!$data['coupon_info']['discount_info']['discount_condition']['buyget_info']['get_out_product_id']) return Json::fail('请选择赠送商品商家侧ID');
            if (!$data['coupon_info']['discount_info']['discount_condition']['buyget_info']['get_product_cnt']) return Json::fail('请填写赠送商品数');
        }
        //领取条件
        if (!$data['coupon_info']['receive_info']['total_num']) return Json::fail('请输入总发放量');
        if (!$data['coupon_info']['receive_info']['limit_num_one_person']) return Json::fail('请输入个人限领张数');
        if (!$data['coupon_info']['receive_info']['start_time']) return Json::fail('请输入领取开始时间');
        if (!$data['coupon_info']['receive_info']['end_time']) return Json::fail('请输入领取结束时间');
        // 格式化时间
        $data['coupon_info']['receive_info']['start_time']  = strtotime($data['coupon_info']['receive_info']['start_time']);
        $data['coupon_info']['receive_info']['end_time']  = strtotime($data['coupon_info']['receive_info']['end_time']);
        //发放条件
        if (!$data['coupon_info']['valid_info']['valid_type']) return Json::fail('请输入有效期类型');
        if ($data['coupon_info']['valid_info']['valid_type'] == 2 && !$data['coupon_info']['valid_info']['valid_day_num']) return Json::fail('请输入生效天数');
        if ($data['coupon_info']['valid_info']['valid_type'] == 3 && !$data['coupon_info']['valid_info']['valid_second']) return Json::fail('请输入生效秒数');
        if ($data['coupon_info']['valid_info']['valid_type'] == 1 && !$data['coupon_info']['valid_info']['start_time']) return Json::fail('请输入生效开始时间');
        if ($data['coupon_info']['valid_info']['valid_type'] == 1 && !$data['coupon_info']['valid_info']['end_time']) return Json::fail('请输入生效结束时间');
        // 格式化时间
        if ($data['coupon_info']['valid_info']['start_time'] != "") $data['coupon_info']['valid_info']['start_time']  = strtotime($data['coupon_info']['valid_info']['start_time']);
        if ($data['coupon_info']['valid_info']['end_time'] != "") $data['coupon_info']['valid_info']['end_time']  = strtotime($data['coupon_info']['valid_info']['end_time']);
        $res = CouponModel::updateCoupon($data,$id);
        if ($res) {
            CouponModel::commitTrans();
            return Json::success('修改优惠券成功!');
        } else {
            CouponModel::rollbackTrans();
            return Json::fail(CouponModel::getErrorInfo());
        }
    }

    /**
     * 删除指定资源
     *
     * @param int $id
     * @return \think\Response
     */
    public function delete($id)
    {
        if (!$id) return Json::fail('数据不存在!');
        $data['is_del'] = 1;
        if (!CouponModel::edit($data, $id))
            return Json::fail(CouponModel::getErrorInfo('删除失败,请稍候再试!'));
        else
            return Json::successful('删除成功!');
    }

    /**
     * 修改优惠券状态
     * @param $id
     * @return \think\response\Json
     */
    public function status($id)
    {
        if (!$id) return Json::fail('数据不存在!');
        if (!CouponModel::editIsDel($id))
            return Json::fail(CouponModel::getErrorInfo('修改失败,请稍候再试!'));
        else
            return Json::successful('修改成功!');
    }

    public function coupon_info($id = '')
    {
        if (!$id || !($couponInfo = CouponModel::get($id)))
            return $this->failed('订单不存在!');
        $couponInfo->coupon_info = json_decode($couponInfo->coupon_info, true);
        $type = $couponInfo['type'];
        $out_product_ids = $couponInfo['coupon_info']['discount_info']['discount_condition']['out_product_ids'];
        // $out_product_ids = [21,22,28,27,26,25,80,79];
        $couponInfo->out_product_images = ProductModel::whereIn('id',$out_product_ids)->column('image,store_name');
        if ($type == 5) {
            // $tradein_out_product_ids = [77];
            $tradein_out_product_ids = $couponInfo['coupon_info']['discount_info']['discount_condition']['tradein_info']['out_product_id'];
            $couponInfo->tradein_out_product_images = ProductModel::whereIn('id',$tradein_out_product_ids)->column('image,store_name');
        }elseif ($type == 6) {
            // $buy_out_product_ids = [77];
            // $get_out_product_ids = [96];
            $buy_out_product_ids = $couponInfo['coupon_info']['discount_info']['discount_condition']['buyget_info']['buy_out_product_id'];
            $couponInfo->buy_out_product_images = ProductModel::whereIn('id',$buy_out_product_ids)->column('image,store_name');
            $get_out_product_ids = $couponInfo['coupon_info']['discount_info']['discount_condition']['buyget_info']['get_out_product_id'];
            $couponInfo->get_out_product_images = ProductModel::whereIn('id',$get_out_product_ids)->column('image,store_name');
        }
        $this->assign(compact('couponInfo'));
        return $this->fetch();
    }


    /**
     * 修改库存信息
     * @param $id
     * @return mixed|\think\response\Json|void
     */
    public function edit_stock($id)
    {
        if (!$id) return $this->failed('数据不存在');
        $coupon = CouponModel::get($id);
        if (!$coupon) return Json::fail('数据不存在!');
        $coupon->stock_info = json_decode($coupon->stock_info, true);
        $f = [];
        $f[] = Form::input('name', '优惠卷名称', $coupon->getData('name'))->disabled(1);
        $f[] = Form::input("stock_info[issued_num]", '优惠券剩余量',$coupon['stock_info']['issued_num']);
        $f[] = Form::input("stock_info[receive_num]", '优惠券发放量',$coupon['stock_info']['receive_num']);
        $f[] = Form::hidden('out_coupon_id', '商家侧优惠券ID', $coupon->getData('out_coupon_id'));
        $form = Form::make_post_form('更新优惠卷库存', $f, Url::buildUrl('updateCouponStock', array('id' => $id)), 7);
        $this->assign(compact('form'));
        return $this->fetch('public/form-builder');
    }

    public function updateCouponStock($id)
    {
        $data = Util::postMore([
            'out_coupon_id',
            ['stock_info',[]],
        ]);
        if (!$id) return $this->failed('数据不存在');
        $coupon = CouponModel::get($id);
        if (!$coupon) return Json::fail('数据不存在!');
        $res = CouponModel::updateCouponStock($data,$id);
        if ($res) {
            CouponModel::commitTrans();
            return Json::success('修改成功!');
        } else {
            CouponModel::rollbackTrans();
            return Json::fail(CouponModel::getErrorInfo());
        }
    }

    /**
     * 修改状态信息
     * @param $id
     * @return mixed|\think\response\Json|void
     */
    public function edit_status($id)
    {
        if (!$id) return $this->failed('数据不存在');
        $coupon = CouponModel::get($id);
        if (!$coupon) return Json::fail('数据不存在!');
        $f = [];
        $f[] = Form::input('name', '优惠卷名称', $coupon->getData('name'))->disabled(1);
        // $f[] = Form::input('out_coupon_id', '商家侧优惠券ID', $coupon->getData('out_coupon_id'))->disabled(1);
        $f[] = Form::input('current_status', '当前状态', '生效')->disabled(1);
        if ($coupon['status'] == 2) {
            $f[] = Form::radio('status', '新状态', $coupon->getData('status'))->options([['label' => '已过期', 'value' => 3], ['label' => '已作废', 'value' => 4]]);
        }
        $form = Form::make_post_form('更新优惠卷状态', $f, Url::buildUrl('updateStatus', array('id' => $id)), 7);
        $this->assign(compact('form'));
        return $this->fetch('public/form-builder');
    }

    /**
     * 修改配送信息
     * @param $id
     */
    public function updateStatus($id)
    {
        $data = Util::postMore([
            'out_coupon_id',
            'status',
        ]);
        if (!$id) return $this->failed('数据不存在');
        $coupon = CouponModel::get($id);
        if (!$coupon) return Json::fail('数据不存在!');
        $res = CouponModel::updateCouponStatus($data,$id);
        if ($res) {
            CouponModel::commitTrans();
            return Json::success('修改成功!');
        } else {
            CouponModel::rollbackTrans();
            return Json::fail(CouponModel::getErrorInfo());
        }
    }

    /**
     * 选择商品
     * @param int $id
     */
    public function select_coupon()
    {
        return $this->fetch();
    }

    /**
     * 选择
     * @param int $id
     */
    public function select_product()
    {
        return $this->fetch();
    }
}
