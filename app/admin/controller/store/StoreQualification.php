<?php

namespace app\admin\controller\store;

use app\admin\controller\AuthController;
use app\admin\model\store\{
    StoreQualification as QualificationModel,
};
use crmeb\services\{
    JsonService, UtilService as Util, JsonService as Json, FormBuilder as Form
};
use crmeb\services\minishop\Shop;
use crmeb\traits\CurdControllerTrait;
use think\facade\Route as Url;

/**
 * 产品管理
 * Class StoreProduct
 * @package app\admin\controller\store
 */
class StoreQualification extends AuthController
{

    use CurdControllerTrait;

    protected $bindModel = QualificationModel::class;

    /**
     * 显示资源列表
     *
     * @return \think\Response
     */
    public function index()
    {
        $type = $this->request->param('type', 1);
        $category_count = QualificationModel::where('type', 1)->count();
        $brand_count = QualificationModel::where('type', 2)->count();
        $this->assign(compact('type','category_count','brand_count'));
        return $this->fetch();
    }

    /**
     * 异步查找产品
     *
     * @return json
     */
    public function qualification_list()
    {
        $where = Util::getMore([
            ['page', 1],
            ['limit', 20],
            [['type', 'd'], $this->request->param('type/d')]
        ]);
        return Json::successlayui(QualificationModel::ProductList($where));
    }

     /**
     * 异步查找产品
     *
     * @return json
     */
    public function download_cate()
    {
        return Json::successlayui(QualificationModel::DownloadCate());
    }

    /**
     * 显示创建资源表单页.
     *
     * @return \think\Response
     */
    public function create($id = 0,$type = 1)
    {
        $this->assign('id', (int)$id);
        $this->assign('type', (int)$type);
        return $this->fetch();
    }

    /**
     * 获取产品详细信息
     * @param int $id
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function get_qualification_info($id = 0,$type = 1)
    {
        $data['qualificationInfo'] = [];
        if ($id) {
            $qualificationInfo = QualificationModel::get($id);
            if (!$qualificationInfo) {
                return Json::fail('修改的产品不存在');
            }
            $qualificationInfo['license'] = is_string($qualificationInfo['license']) ? json_decode($qualificationInfo['license'], true) : [];
            if ($qualificationInfo['type'] == 1 ) {
                $qualificationInfo['category_info'] = is_string($qualificationInfo['info']) ? json_decode($qualificationInfo['info'], true) : [];
            }else{
                $qualificationInfo['brand_info'] = is_string($qualificationInfo['info']) ? json_decode($qualificationInfo['info'], true) : [];
            }
            $qualificationInfo['scene_group_list'] = is_string($qualificationInfo['scene_group_list']) ? json_decode($qualificationInfo['scene_group_list'], true) : [];
            $data['qualificationInfo'] = $qualificationInfo;
        }
        return JsonService::successful($data);
    }

    /**
     * 保存新建的资源
     *
     *
     */
    public function save($id)
    {
        $data = Util::postMore([
            ['type', 1],
            'name',
            ['license', []],
            ['brand_info', []],
            ['category_info', []],
            ['scene_group_list', []],
        ]);
        //根据
        if ($data['type'] == 1) {
            if (count($data['category_info']) < 1) return Json::fail('请上传分类资质信息');
            $data['info'] = $data['category_info'];
         } else {
            if (count($data['brand_info']) < 1) return Json::fail('请上传品牌信息');
            $data['info'] = $data['brand_info'];
         }
        //格式化掉变量
        unset($data['brand_info']);
        unset($data['category_info']);
        QualificationModel::beginTrans();
        if ($id) {
           //重新将资质上传到微信进行审核
            $shop = new  Shop('wechat');
            $audit_res =  $shop->audit($data['type'],$data['license'],$data['info'],$data['scene_group_list']);
            if ($audit_res && $audit_res['errcode'] != 0) {
                  return Json::fail($audit_res['errmsg']);
            }
            $data['audit_id'] = $audit_res['audit_id'];
            $data['license'] = json_encode($data['license']);
            $data['info'] = json_encode($data['info']);
            $data['scene_group_list'] = json_encode($data['scene_group_list']);
            $res = QualificationModel::edit($data, $id);
            if ($res) {
                QualificationModel::commitTrans();
                return Json::success('已重新提交资质，请稍后刷新查看审核结果!');
            } else {
                QualificationModel::rollbackTrans();
                return Json::fail(StoreProductAttr::getErrorInfo());
            }
        } else {
           //将资质上传到微信进行审核
            $shop = new  Shop('wechat');
            $audit_res =  $shop->audit($data['type'],$data['license'],$data['info'],$data['scene_group_list']);
            if ($audit_res && $audit_res['errcode'] != 0) {
                  return Json::fail($audit_res['errmsg']);
            }
            $data['audit_id'] = $audit_res['audit_id'];
            $data['license'] = json_encode($data['license']);
            $data['info'] = json_encode($data['info']);
            $data['scene_group_list'] = json_encode($data['scene_group_list']);
            $data['add_time'] = time();
            $res = QualificationModel::create($data);
            if ($res) {
                QualificationModel::commitTrans();
                return Json::success('提交资质成功，请稍后刷新查看审核结果!');
            } else {
                QualificationModel::rollbackTrans();
                return Json::fail(StoreProductAttr::getErrorInfo());
            }
        }
    }

    /**
     * 更新状态
     *
     * @param int $id
     * @return \think\Response
     */
    public function update_status($id)
    {
        if (!$id) return $this->failed('数据不存在');
        if (!QualificationModel::be(['id' => $id])) return $this->failed('不存在');
        if (!QualificationModel::update_status($id))
            return Json::fail(QualificationModel::getErrorInfo());
        else
            return Json::successful('状态已更新，请刷新当前界面!');
    }
}
