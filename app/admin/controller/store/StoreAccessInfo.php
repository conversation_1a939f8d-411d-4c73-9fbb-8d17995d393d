<?php

namespace app\admin\controller\store;

use app\admin\controller\AuthController;
use app\admin\model\store\{
    StoreWxProduct as ProductModel,
};
use crmeb\services\{
    JsonService as Json
};
use crmeb\traits\CurdControllerTrait;


/**
 * 获取接入相关信息
 * Class StoreAccessInfo
 * @package app\admin\controller\store
 */
class StoreAccessInfo extends AuthController
{

    use CurdControllerTrait;

    protected $bindModel = ProductModel::class;

    /**
     * 显示资源列表
     *
     * @return \think\Response
     */
    public function index()
    {
        $type = $this->request->param('type', 1);
        $this->assign(compact('type' ));
        return $this->fetch();
    }

    /**
     * 异步查找产品
     *
     * @return json
     */
    public function check()
    {
    	$type = $this->request->param('type', 1);
        return Json::successlayui(ProductModel::registerCheck($type));
    }
}
