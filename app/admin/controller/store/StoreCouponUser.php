<?php

namespace app\admin\controller\store;

use app\admin\controller\AuthController;
use think\facade\Route as Url;
use app\admin\model\store\StoreCouponUser as CouponUserModel;
use app\admin\model\wechat\WechatUser as UserModel;
use crmeb\services\{FormBuilder as Form, UtilService as Util, JsonService as Json};

/**
 * 优惠券发放记录控制器
 * Class StoreCouponUser
 * @package app\admin\controller\store
 */
class StoreCouponUser extends AuthController
{
    /**
     * @return mixed
     */
    public function index()
    {
        $this->assign([
            'userList' => CouponUserModel::getReceiveUserList(),
        ]);
        return $this->fetch();
    }

    /**
     * 异步查找发放用户优惠券列表
     *
     * @return json
     */
    public function usercoupon_list()
    {
        $where = Util::getMore([
            ['openid', ''],
            ['page', 1],
            ['limit', 20],
        ]);
        return Json::successlayui(CouponUserModel::userCouponList($where));
    }

    /**
     * @return mixed
     */
    public function create()
    {
        //前面通用字段
        $f = [];
        $f[] = Form::frameImageOne('image', '用户', Url::buildUrl('6GqvmHa8HRGHoQEQ/store.StoreCouponUser/select_user', array('fodder' => 'image')))->icon('plus')->width('100%')->height('500px');
        $f[] = Form::frameImageOne('coupon_image', '优惠券', Url::buildUrl('6GqvmHa8HRGHoQEQ/store.StoreCouponUser/select_coupon', array('fodder' => 'coupon_image')))->maxLength(1)->icon('plus')->width('100%')->height('500px');
        $f[] = Form::radio('user_coupon[status]', '状态', 100)->options([['label' => '生效中', 'value' => 100],['label' => '已过期', 'value' => 101],['label' => '已过期', 'value' => 102],['label' => '已主动废弃', 'value' => 103]]);
        //领取
        $f[] = Form::datetime("recv_time", '领取时间')->format('yyyy-MM-DD HH:mm:ss');
        //后面通用字段
        $f[] = Form::hidden("uid", '');
        $f[] = Form::hidden("openid", '');
        $f[] = Form::hidden("user_coupon[out_user_coupon_id]", '');
        $f[] = Form::hidden("user_coupon[out_coupon_id]", '');
        $form = Form::make_post_form('添加用户优惠券', $f, Url::buildUrl('save'));
        $this->assign(compact('form'));
        return $this->fetch();
    }


    /**
     * 保存
     */
    public function save()
    {
        $data = Util::postMore([
            'uid',
            'openid',
            ['user_coupon', []],
            ['recv_time', 0],
        ]);
        if (!$data['openid']) return Json::fail('请选择要发放的用户');
        if (count($data['user_coupon']) < 1 && $data['user_coupon']['out_user_coupon_id'] != "" && $data['user_coupon']['out_coupon_id'] != "") return Json::fail('请选择要发放的优惠卷');
        if (!$data['recv_time']) return Json::fail('请选择领取时间');
        $res = CouponUserModel::addUserCoupon($data);
        if ($res) {
            CouponUserModel::commitTrans();
            return Json::success('发放优惠券成功!');
        } else {
            CouponUserModel::rollbackTrans();
            return Json::fail(CouponUserModel::getErrorInfo());
        }
    }


    /**
     * 修改信息
     * @param $id
     * @return mixed|\think\response\Json|void
     */
    public function update($id)
    {
        if (!$id) return $this->failed('数据不存在');
        $coupon = CouponUserModel::get($id);
        if (!$coupon) return Json::fail('数据不存在!');
        $f = [];
        //获取图片
        $user_image  = UserModel::where('uid', $coupon['uid'])->value('headimgurl');
        $coupon_image = $coupon['out_user_coupon_id'] != 0 ? 'https://cshop.arthorize.com/attach/2022/08/bc228202208291342599129.png' : '';
        $f[] = Form::frameImageOne('image', '用户', Url::buildUrl('6GqvmHa8HRGHoQEQ/store.StoreCouponUser/select_user', array('fodder' => 'image')),$user_image)->icon('plus')->width('100%')->height('500px');
        $f[] = Form::frameImageOne('coupon_image', '优惠券', Url::buildUrl('6GqvmHa8HRGHoQEQ/store.StoreCouponUser/select_coupon', array('fodder' => 'coupon_image')),$coupon_image)->maxLength(1)->icon('plus')->width('100%')->height('500px');

        $use_time = $coupon['use_time'] ? date('Y-m-d H:i:s',$coupon->getData('use_time')) : '';

        $f[] = Form::datetime("user_coupon[ext_info][use_time]", '核销时间',$use_time)->format('yyyy-MM-DD HH:mm:ss');
        //领取
        $f[] = Form::datetime("recv_time", '领取时间',date('Y-m-d H:i:s',$coupon->getData('recv_time')))->format('yyyy-MM-DD HH:mm:ss');
        //后面通用字段
        $f[] = Form::hidden("uid", $coupon->getData('uid'));
        $f[] = Form::hidden("openid", $coupon->getData('openid'));
        $f[] = Form::hidden("user_coupon[out_user_coupon_id]", $coupon->getData('out_user_coupon_id'));
        $f[] = Form::hidden("user_coupon[out_coupon_id]", $coupon->getData('out_coupon_id'));
        $form = Form::make_post_form('', $f, Url::buildUrl('updateUserCoupon', array('id' => $id)), 7);
        $this->assign(compact('form'));
        return $this->fetch('public/form-builder');
    }


    /**
     * 保存
     */
    public function updateUserCoupon($id)
    {
        $data = Util::postMore([
            'uid',
            'openid',
            ['user_coupon', []],
            ['recv_time', 0],
        ]);
        if (!$data['openid']) return Json::fail('请选择要发放的用户');
        if (count($data['user_coupon']) < 1 && $data['user_coupon']['out_user_coupon_id'] != "" && $data['user_coupon']['out_coupon_id'] != "") return Json::fail('请选择要发放的优惠卷');
        if (!$data['recv_time']) return Json::fail('请选择领取时间');
        $res = CouponUserModel::updateUserCoupon($data ,$id);
        if ($res) {
            CouponUserModel::commitTrans();
            return Json::success('更新优惠券成功!');
        } else {
            CouponUserModel::rollbackTrans();
            return Json::fail(CouponUserModel::getErrorInfo());
        }
    }

    /**
     * 修改状态信息
     * @param $id
     * @return mixed|\think\response\Json|void
     */
    public function update_status($id)
    {
        if (!$id) return $this->failed('数据不存在');
        $coupon = CouponUserModel::get($id);
        if (!$coupon) return Json::fail('数据不存在!');
        $f = [];
        $f[] = Form::radio('status', '状态', $coupon->getData('status'))->options([['label' => '生效中', 'value' => 100],['label' => '已过期', 'value' => 101],['label' => '   已经使用', 'value' => 102],['label' => '已主动废弃', 'value' => 103]]);
        //后面通用字段
        $f[] = Form::hidden("openid", $coupon->getData('openid'));
        $f[] = Form::hidden("out_user_coupon_id", $coupon->getData('out_user_coupon_id'));
        $f[] = Form::hidden("out_coupon_id", $coupon->getData('out_coupon_id'));
        $form = Form::make_post_form('', $f, Url::buildUrl('updateStatus', array('id' => $id)), 7);
        $this->assign(compact('form'));
        return $this->fetch('public/form-builder');

    }

    /**
     * 保存
     */
    public function updateStatus($id)
    {
        $data = Util::postMore([
            'openid',
            'out_coupon_id',
            'out_user_coupon_id',
            'status',
        ]);
        $res = CouponUserModel::updateUserCouponStatus($data,$id);
        if ($res) {
            CouponUserModel::commitTrans();
            return Json::success('更新优惠券成功!');
        } else {
            CouponUserModel::rollbackTrans();
            return Json::fail(CouponUserModel::getErrorInfo());
        }
    }

    /**
     * 选择用户
     * @param int $id
     */
    public function select_user()
    {
        return $this->fetch();
    }

    /**
     * 选择优惠卷
     * @param int $id
     */
    public function select_coupon()
    {
        return $this->fetch();
    }

}
