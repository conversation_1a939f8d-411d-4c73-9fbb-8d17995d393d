<?php
/**
 *
 * @author: xaboy<<EMAIL>>
 * @day: 2017/11/11
 */

namespace app\admin\controller\user;

use app\admin\controller\AuthController;
use app\admin\model\order\StoreOrderCartInfo;
use app\admin\model\system\Express;
use crmeb\repositories\OrderRepository;
use crmeb\repositories\ShortLetterRepositories;
use crmeb\services\{
    ExpressService,
    JsonService,
    MiniProgramService,
    WechatService,
    FormBuilder as Form,
    CacheService,
    UtilService as Util,
    JsonService as Json
};
use app\admin\model\order\StoreOrderStatus;
use app\admin\model\ump\StorePink;
use app\admin\model\user\{
    User, UserBill
};
use crmeb\basic\BaseModel;
use think\facade\Route as Url;
use app\admin\model\user\UserSales as UserSalesModel;
use crmeb\services\YLYService;
use think\facade\Log;

/**
 * 订单管理控制器 同一个订单表放在一个控制器
 * Class StoreOrder
 * @package app\admin\controller\store
 */
class UserSales extends AuthController
{
    /**
     * @return mixed
     */
    public function index()
    {
        $this->assign([
            'year' => get_month(),
            'real_name' => $this->request->get('real_name', ''),
            'status' => $this->request->param('status', ''),
            'orderCount' => UserSalesModel::orderCount(),
            'payTypeCount' => UserSalesModel::payTypeCount(),
        ]);
        return $this->fetch();
    }

    /**
     * 获取订单列表
     * return json
     */
    public function sales_list()
    {
        $where = Util::getMore([
            ['status', ''],
            ['real_name', $this->request->param('real_name', '')],
            ['data', ''],
            ['pay_type', ''],
            ['order', ''],
            ['page', 1],
            ['limit', 20],
            ['excel', 0]
        ]);
        return Json::successlayui(UserSalesModel::SalesList($where));
    }

    /**
     * 修改订单提交更新
     * @param $id
     */
    public function update($id)
    {
        $data = Util::postMore([
            'order_id',
            'total_price',
            'total_postage',
            'pay_price',
            'pay_postage',
            'gain_integral',
        ]);
        if ($data['total_price'] <= 0) return Json::fail('请输入商品总价');
        if ($data['pay_price'] <= 0) return Json::fail('请输入实际支付金额');
        $orderInfo = UserSalesModel::get($id);
        if (!$orderInfo) {
            return Json::fail('订单不存在');
        }
        $orderInfo->order_id = UserSalesModel::changeOrderId($data['order_id']);
        $pay_price = $orderInfo->pay_price;
        $orderInfo->pay_price = $data['pay_price'];
        $orderInfo->total_price = $data['total_price'];
        $orderInfo->total_postage = $data['total_postage'];
        $orderInfo->pay_postage = $data['pay_postage'];
        $orderInfo->gain_integral = $data['gain_integral'];
        if ($orderInfo->save()) {
            //改价短信提醒
            if ($data['pay_price'] != $pay_price) {
                $switch = sys_config('price_revision_switch') ? true : false;
                ShortLetterRepositories::send($switch, $orderInfo->user_phone, ['order_id' => $orderInfo->order_id, 'pay_price' => $orderInfo->pay_price], 'PRICE_REVISION_CODE');
            }
            event('StoreProductOrderEditAfter', [$data, $id]);
            StoreOrderStatus::setStatus($id, 'order_edit', '修改商品总价为：' . $data['total_price'] . ' 实际支付金额' . $data['pay_price']);
            return Json::successful('修改成功!');
        } else {
            return Json::fail('订单修改失败');
        }
    }

    /*
     * 删除订单
     * */
    public function del_order()
    {
        $ids = Util::postMore(['ids'])['ids'];
        if (!count($ids)) return Json::fail('请选择需要删除的订单');
        if (UserSalesModel::where('is_del', 0)->where('id', 'in', $ids)->count())
            return Json::fail('您选择的的订单存在用户未删除的订单，无法删除用户未删除的订单');
        $res = UserSalesModel::where('id', 'in', $ids)->update(['is_system_del' => 1]);
        if ($res)
            return Json::successful('删除成功');
        else
            return Json::fail('删除失败');
    }

    public function remark()
    {
        $data = Util::postMore(['id', 'remark']);
        if (!$data['id']) return Json::fail('参数错误!');
        if ($data['remark'] == '') return Json::fail('请输入要备注的内容!');
        $id = $data['id'];
        unset($data['id']);
        UserSalesModel::edit($data, $id);
        return Json::successful('备注成功!');
    }

    public function order_status($oid)
    {
        if (!$oid) return $this->failed('数据不存在');
        $this->assign(StoreOrderStatus::systemPage($oid));
        return $this->fetch();
    }

     /**
     * 修改退款状态
     * @param $id
     * @return \think\response\Json|void
     */
    public function refund_y($id)
    {
        if (!$id) return $this->failed('数据不存在');
        $sales = UserSalesModel::get($id);
        if (!$sales) return Json::fail('数据不存在!');
        if ($sales['paid'] == 1) {
            $pay_price = $sales->getData('pay_price');
            $refunded_price = $sales->getData('refund_price');;
            $refundable_price = bcsub($pay_price,$refunded_price,2);
            $f = [];
            $f[] = Form::input('order_id', '退款单号', $sales->getData('order_id'))->disabled(1);
            $f[] = Form::number('pay_price', '支付金额', $sales->getData('pay_price'))->disabled(1);
            $f[] = Form::number('refunded_price', '已退款金额', $sales->getData('refund_price'))->disabled(1);
            $f[] = Form::number('refundable_price', '可退款金额', $refundable_price)->disabled(1);
            $f[] = Form::number('refund_price', '本次退款金额', $refundable_price)->precision(2)->min(0.01);
            $f[] = Form::radio('type', '状态', 1)->options([['label' => '直接退款', 'value' => 1], ['label' => '退款后,重置会员原状态', 'value' => 2]]);
             $f[] = Form::textarea('mark', '退款操作备注', $sales->getData('mark'));
            $form = Form::make_post_form('退款处理', $f, Url::buildUrl('updateRefundY', array('id' => $id)), 7);
            $this->assign(compact('form'));
            return $this->fetch('public/form-builder');
        } else return Json::fail('数据不存在!');
    }

        /**
     * 退款处理
     * @param $id
     */
    public function updateRefundY($id)
    {
        $data = Util::postMore([
            'refund_price',
            'mark',
            ['type', 1],
        ]);
        if (!$id) return $this->failed('数据不存在');
        $sales = UserSalesModel::get($id);
        if (!$sales) return Json::fail('数据不存在!');
        if ($sales['pay_price'] == $sales['refund_price']) return Json::fail('已退完支付金额!不能再退款了');
        if (!$data['refund_price']) return Json::fail('请输入退款金额');
        $refund_price = $data['refund_price'];
        $data['refund_price'] = bcadd($data['refund_price'], $sales['refund_price'], 2);
        $bj = bccomp((float)$sales['pay_price'], (float)$data['refund_price'], 2);
        if ($bj < 0) return Json::fail('退款金额大于支付金额，请修改退款金额');
        if ($data['type'] == 1) {
            $data['refund_status'] = 2;
        } else if ($data['type'] == 2) {
            $data['refund_status'] = 0;
        }
        $type = $data['type'];
        unset($data['type']);
        $refund_data['pay_price'] = $sales['pay_price'];
        $refund_data['refund_price'] = $refund_price;
        $refund_data['refund_id'] = $sales['order_id'].'_'.session_create_id(); //微信退款单号
        if ($sales['pay_type'] == 'weixin') {
            if ($sales['is_channel'] == 1) {//小程序
                try {
                    MiniProgramService::payOrderRefund($sales['order_id'], $refund_data);//2.5.36
                } catch (\Exception $e) {
                    return Json::fail($e->getMessage());
                }
            } else {//TODO 公众号
                try {
                    WechatService::payOrderRefund($sales['order_id'], $refund_data);
                } catch (\Exception $e) {
                    return Json::fail($e->getMessage());
                }
            }
        }
        $resEdit = UserSalesModel::edit($data, $id);
        $res = true;
        if ($resEdit) {
            $data['type'] = $type;
            if ($data['type'] == 1) $res = StorePink::setRefundPink($id);
            if (!$res) return Json::fail('修改失败');
            StoreOrderStatus::setStatus($id, 'refund_price', '退款给用户' . $refund_price . '元');
            BaseModel::commitTrans();
            return Json::successful('修改成功!');
        } else {
            StoreOrderStatus::setStatus($id, 'refund_price', '退款给用户' . $refund_price . '元失败');
            return Json::fail('修改失败!');
        }
    }
}
