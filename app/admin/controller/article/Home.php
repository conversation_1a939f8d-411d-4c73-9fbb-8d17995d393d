<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2021-11-15 10:27:58
 * @Last Modified time: 2023-03-29 14:24:57
 */
namespace app\admin\controller\article;

use app\admin\controller\AuthController;
use crmeb\services\{
    JsonService,
    FormBuilder as Form,
    UtilService as Util,
    JsonService as Json
};
use think\Request;
use think\facade\Route as Url;
use app\admin\model\evaluation\StoreBanner as BannerModel;

/**
 * 首页配置-横幅处理
 * Class Home
 * @package app\admin\controller\evaluation
 */
class Home extends AuthController
{
    public function index()
    {
        return $this->fetch();
    }

    /**
     * 获取横幅列表
     * return json
     */
    public function banner_list()
    {
        $where = Util::getMore([
            ['page', 1],
            ['limit', 20],
        ]);
        return Json::successlayui(BannerModel::BannerList($where));
    }

    /**
     * 显示创建资源表单页.
     *
     * @return \think\Response
     */
    public function create($id = 0)
    {
        $this->assign('id', (int)$id);
        return $this->fetch();
    }

    /**
     * 获取横幅详细信息
     * @param int $id
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function get_banner_info($id = 0)
    {
    	$data['bannerInfo'] = [];
        if ($id) {
            $bannerInfo = BannerModel::get($id);
            if (!$bannerInfo) {
                return Json::fail('修改的活动不存在');
            }
            $bannerInfo['type'] =  (int) $bannerInfo['type'];
            $data['bannerInfo'] = $bannerInfo;
        }
        return JsonService::successful($data);
    }


    /**
     * 保存新建的资源
     *
     * @param \think\Request $request
     * @return \think\Response
     */
    public function save($id)
    {
        $data = Util::postMore([
            'type',
            'title',
            'image',
            'position',
            'routine_url',
            'toutiao_url',
            'mp_url',
            'feedId',
            'finderUserName',
            'sort',
            'is_show',
        ]);
        if (!$data['title']) return Json::fail('请填写横幅标题');
        if (!$data['image']) return Json::fail('请填写横幅图片');
        if ($data['type'] == 0 && !$data['routine_url'] && !$data['toutiao_url']) return Json::fail('请填写微信与头条内部跳转链接');
        if ($data['type'] == 1 && !$data['mp_url']) return Json::fail('请填写公众号跳转链接');
        if ($data['type'] == 2 && !$data['finderUserName']) return Json::fail('请填写视频号ID');
        if ($data['type'] == 2 && !$data['feedId']) return Json::fail('请填写视频号视频FeedId');
        if ($data['type'] == 3 && !$data['finderUserName']) return Json::fail('请填写视频号直播间ID');
        if ($id) {
         	BannerModel::edit($data, $id);
         	$data['updated_time'] = time();
         	return Json::successful('修改横幅成功!');
         }else{
        	$data['status'] = 1;
            $data['add_time'] = time();
        	$data['updated_time'] = time();
	        BannerModel::create($data);
	        return Json::successful('添加横幅成功!');
         }
    }


    /**
     * 删除指定资源
     *
     * @param int $id
     * @return \think\Response
     */
    public function destroy($id)
    {
        if (!$id) return $this->failed('数据不存在');
        if (!BannerModel::be(['id' => $id])) return $this->failed('横幅数据不存在');
        if (BannerModel::be(['id' => $id, 'is_system_del' => 1])) {
            return Json::successful('横幅数据已被删除!');
        } else {
            $data['is_system_del'] = 1;
            $data['del_time'] = time();
            if (!BannerModel::edit($data, $id)){
                return Json::fail('删除失败,请稍候再试!');
            }else{
                return Json::successful('成功删除横幅!');
            }
        }
    }

    /**
     * 快速编辑
     * @param string $field
     * @param string $id
     * @param string $value
     */
    public function set_field($field = '', $id = '', $value = '')
    {
        $field == '' || $id == '' || $value == '' && Json::fail('缺少参数');
        if (BannerModel::where('id', $id)->update([$field => $value]))
            return Json::successful('保存成功');
        else
            return Json::fail('保存失败');
    }

    /**
     * 设置资源上架|下架
     * @param string $is_show
     * @param string $id
     */
    public function set_show($is_show = '', $id = '')
    {
        ($is_show == '' || $id == '') && Json::fail('缺少参数');
        if (BannerModel::setBannerShow($id, (int)$is_show)) {
            return Json::successful($is_show == 1 ? '显示成功' : '隐藏成功');
        } else {
            return Json::fail(BannerModel::getErrorInfo($is_show == 1 ? '显示失败' : '隐藏失败'));
        }
    }

    /**
     * 设置资源上架|下架
     * @param string $set_bytedance_show
     * @param string $id
     */
    public function set_bytedance_show($is_bytedance_show = '', $id = '')
    {
        ($is_bytedance_show == '' || $id == '') && Json::fail('缺少参数');
        if (BannerModel::setBannerByteDanceShow($id, (int)$is_bytedance_show)) {
            return Json::successful($is_bytedance_show == 1 ? '显示成功' : '隐藏成功');
        } else {
            return Json::fail(BannerModel::getErrorInfo($is_bytedance_show == 1 ? '显示失败' : '隐藏失败'));
        }
    }
}