<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2018/4/16 0016
 * Time: 10:39
 */

namespace app\admin\controller\record;

use app\admin\controller\AuthController;
use crmeb\services\UtilService as Util;
use crmeb\services\PHPExcelService;
use app\admin\model\record\StoreStatistics as StatisticsModel;

/**
 * Class StoreStatistics
 * @package app\admin\controller\record
 */
class StoreStatistics extends AuthController
{
    /**
     * 显示列表
     */
    public function index()
    {
        $where = Util::getMore([
            ['date', ''],
            ['export', ''],
            ['data', '']
        ], $this->request);
        $where['date'] = $this->request->param('date');
        $where['data'] = $this->request->param('data');
        $where['export'] = $this->request->param('export');
        $trans = StatisticsModel::trans();//最近交易
        $seckill = StatisticsModel::getSeckill($where);//秒杀商品
        $ordinary = StatisticsModel::getOrdinary($where);//普通商品
        $pink = StatisticsModel::getPink($where);//拼团商品
        $card = StatisticsModel::getCard($where);//源始社会卡
        $recharge = StatisticsModel::getRecharge($where);//充值
        $extension = StatisticsModel::getExtension($where);//推广金
        $orderCount = [
            urlencode('微信支付') => StatisticsModel::getTimeWhere($where, StatisticsModel::statusByWhere('weixin'))->count(),
            urlencode('余额支付') => StatisticsModel::getTimeWhere($where, StatisticsModel::statusByWhere('yue'))->count(),
            urlencode('线下支付') => StatisticsModel::getTimeWhere($where, StatisticsModel::statusByWhere('offline'))->count(),
        ];
        $Statistic = [
            ['name' => '营业额', 'type' => 'line', 'data' => []],
            ['name' => '支出', 'type' => 'line', 'data' => []],
            ['name' => '盈利', 'type' => 'line', 'data' => []],
        ];
        $orderinfos = StatisticsModel::getOrderInfo($where);
        $orderinfo = $orderinfos['orderinfo'];
        $orderDays = [];
        if (empty($orderinfo)) {
            $orderDays[] = date('Y-m-d', time());
            $Statistic[0]['data'][] = 0;
            $Statistic[1]['data'][] = 0;
            $Statistic[2]['data'][] = 0;
        }
        foreach ($orderinfo as $info) {
            $orderDays[] = $info['pay_time'];
            $Statistic[0]['data'][] = $info['total_price'] + $info['pay_postage'];
            $Statistic[1]['data'][] = $info['coupon_price'] + $info['deduction_price'] + $info['cost'];
            $Statistic[2]['data'][] = ($info['total_price'] + $info['pay_postage']) - ($info['coupon_price'] + $info['deduction_price'] + $info['cost']);
        }
        $price = $orderinfos['price'] + $orderinfos['postage'];
        $cost = $orderinfos['deduction'] + $orderinfos['coupon'] + $orderinfos['cost'];
        $Consumption = StatisticsModel::getConsumption($where)['number'];
        $header = [
            ['name' => '总营业额', 'class' => 'fa-line-chart', 'value' => '￥' . $price, 'color' => 'red'],
            ['name' => '总支出', 'class' => 'fa-area-chart', 'value' => '￥' . ($cost + $extension), 'color' => 'lazur'],
            ['name' => '总盈利', 'class' => 'fa-bar-chart', 'value' => '￥' . bcsub($price, $cost, 0), 'color' => 'navy'],
            ['name' => '新增消费', 'class' => 'fa-pie-chart', 'value' => '￥' . ($Consumption == 0 ? 0 : $Consumption), 'color' => 'yellow']
        ];
        $data = [
            ['value' => $orderinfos['cost'], 'name' => '商品成本'],
            ['value' => $orderinfos['coupon'], 'name' => '优惠券抵扣'],
            ['value' => $orderinfos['deduction'], 'name' => '积分抵扣'],
            ['value' => $extension, 'name' => '推广人佣金']
        ];

        $this->assign(StatisticsModel::systemTable($where));
        $this->assign(compact('where', 'trans', 'orderCount', 'orderDays', 'header', 'Statistic', 'ordinary', 'pink', 'card', 'recharge', 'data', 'seckill'));
        $this->assign('price', StatisticsModel::getOrderPrice($where));

        return $this->fetch();
    }

    
    
        /**
     * 调查问卷统计首页
     */
    public function pollster_index()
    {
        $where = Util::getMore([
            ['date', ''],
            ['export', ''],
            ['data', '']
        ], $this->request);
        $where['date'] = $this->request->param('date');
        $where['data'] = $this->request->param('data');
        $where['export'] = $this->request->param('export');
        
        // 检查是否为导出请求
        if ($where['export'] == 1) {
            return $this->exportPollsterStatistics($where);
        }
        
        // 获取调查问卷统计数据
        $pollsterResult = \app\admin\model\user\UserPollster::getPollsterStatistics($where);
        $pollsterStats = $pollsterResult['code'] == 200 ? $pollsterResult['data'] : [];
        
        // 构建统计图表数据
        $Statistic = [
            ['name' => '问卷总数', 'type' => 'line', 'data' => []],
            ['name' => '参与用户', 'type' => 'line', 'data' => []],
            ['name' => '完成率', 'type' => 'line', 'data' => []],
        ];
        
        // 处理趋势数据
        $trendDays = [];
        if (!empty($pollsterStats['trend'])) {
            foreach ($pollsterStats['trend'] as $trend) {
                $trendDays[] = $trend['date'];
                $Statistic[0]['data'][] = $trend['count'];
                $Statistic[1]['data'][] = $trend['count'];
                $Statistic[2]['data'][] = $trend['count'] > 0 ? 100 : 0;
            }
        } else {
            $trendDays[] = date('Y-m-d', time());
            $Statistic[0]['data'][] = 0;
            $Statistic[1]['data'][] = 0;
            $Statistic[2]['data'][] = 0;
        }
        
        // 构建头部统计卡片
        $baseStats = $pollsterStats['base'] ?? [];
        $header = [
            ['name' => '问卷总数', 'class' => 'fa-file-text-o', 'value' => $baseStats['total'] ?? 0, 'color' => 'red'],
            ['name' => '今日新增', 'class' => 'fa-plus-circle', 'value' => $baseStats['today'] ?? 0, 'color' => 'lazur'],
            ['name' => '参与用户', 'class' => 'fa-users', 'value' => $baseStats['user_count'] ?? 0, 'color' => 'navy']
        ];
        
        // 构建饼图数据 - 身份分布
        $identityData = [];
        $identityStats = [];
        if (!empty($pollsterStats['identity'])) {
            $total = array_sum($pollsterStats['identity']);
            foreach ($pollsterStats['identity'] as $identity => $count) {
                $identityData[] = ['value' => $count, 'name' => $identity];
                $percentage = $total > 0 ? round(($count / $total) * 100, 2) : 0;
                $identityStats[] = [
                    'identity' => $identity,
                    'count' => $count,
                    'percentage' => $percentage
                ];
            }
        }
        
        // 构建性别分布数据
        $genderData = [];
        $genderStats = [];
        if (!empty($pollsterStats['gender'])) {
            $total = array_sum($pollsterStats['gender']);
            foreach ($pollsterStats['gender'] as $gender => $count) {
                $genderData[] = ['value' => $count, 'name' => $gender];
                $percentage = $total > 0 ? round(($count / $total) * 100, 2) : 0;
                $genderStats[] = [
                    'gender' => $gender,
                    'count' => $count,
                    'percentage' => $percentage
                ];
            }
        }
        
        // 构建年龄分布数据
        $ageData = [];
        $ageStats = [];
        if (!empty($pollsterStats['age'])) {
            $total = array_sum($pollsterStats['age']);
            foreach ($pollsterStats['age'] as $age => $count) {
                $ageData[] = ['value' => $count, 'name' => $age];
                $percentage = $total > 0 ? round(($count / $total) * 100, 2) : 0;
                $ageStats[] = [
                    'age_range' => $age,
                    'count' => $count,
                    'percentage' => $percentage
                ];
            }
        }
        
        // 构建地区分布数据
        $regionData = [];
        $regionStats = [];
        if (!empty($pollsterStats['region'])) {
            $total = array_sum($pollsterStats['region']);
            foreach ($pollsterStats['region'] as $region => $count) {
                $regionData[] = ['value' => $count, 'name' => $region];
                $percentage = $total > 0 ? round(($count / $total) * 100, 2) : 0;
                $regionStats[] = [
                    'region' => $region,
                    'count' => $count,
                    'percentage' => $percentage
                ];
            }
        }
        
        // 构建渠道分布数据
        $channelData = [];
        $channelStats = [];
        if (!empty($pollsterStats['channel'])) {
            $total = array_sum($pollsterStats['channel']);
            foreach ($pollsterStats['channel'] as $channel => $count) {
                $channelData[] = ['value' => $count, 'name' => $channel];
                $percentage = $total > 0 ? round(($count / $total) * 100, 2) : 0;
                $channelStats[] = [
                    'channel' => $channel,
                    'count' => $count,
                    'percentage' => $percentage
                ];
            }
        }
        
        // 构建来源分布数据
        $sourceData = [];
        $sourceStats = [];
        if (!empty($pollsterStats['source'])) {
            $total = array_sum($pollsterStats['source']);
            foreach ($pollsterStats['source'] as $source => $count) {
                $sourceData[] = ['value' => $count, 'name' => $source];
                $percentage = $total > 0 ? round(($count / $total) * 100, 2) : 0;
                $sourceStats[] = [
                    'source' => $source,
                    'count' => $count,
                    'percentage' => $percentage
                ];
            }
        }
        
        // 重新构建pollsterStats以匹配视图期望的格式
        $pollsterStats['identity_statistics'] = $identityStats;
        $pollsterStats['gender_statistics'] = $genderStats;
        $pollsterStats['age_statistics'] = $ageStats;
        $pollsterStats['region_statistics'] = $regionStats;
        $pollsterStats['channel_statistics'] = $channelStats;
        $pollsterStats['source_statistics'] = $sourceStats;
        
        $this->assign(compact(
            'where', 'header', 'Statistic', 'trendDays', 
            'identityData', 'genderData', 'ageData', 'regionData', 'channelData', 'sourceData',
            'pollsterStats'
        ));
        $this->assign([
            'is_layui' => true,
            'year' => get_month()
        ]);
        
        return $this->fetch();
    }

    /**
     * 导出调查问卷统计数据
     * @param array $where
     * @return void
     */
    private function exportPollsterStatistics($where)
    {
        // 获取调查问卷统计数据
        $pollsterResult = \app\admin\model\user\UserPollster::getPollsterStatistics($where);
        $pollsterStats = $pollsterResult['code'] == 200 ? $pollsterResult['data'] : [];
        
        // 准备导出数据
        $exportData = [];
        
        // 1. 基础统计数据
        $baseStats = $pollsterStats['base'] ?? [];
        $exportData[] = ['统计项目', '数值', '说明'];
        $exportData[] = ['问卷总数', $baseStats['total'] ?? 0, '系统中所有问卷的总数量'];
        $exportData[] = ['今日新增', $baseStats['today'] ?? 0, '今天新增的问卷数量'];
        $exportData[] = ['参与用户', $baseStats['user_count'] ?? 0, '参与填写问卷的用户总数'];
        $exportData[] = ['问卷类型', $baseStats['pollster_count'] ?? 0, '不同类型问卷的数量'];
        $exportData[] = ['', '', '']; // 空行分隔
        
        // 2. 身份分布统计
        if (!empty($pollsterStats['identity'])) {
            $exportData[] = ['身份分布统计', '', ''];
            $exportData[] = ['身份类型', '数量', '占比(%)'];
            $total = array_sum($pollsterStats['identity']);
            foreach ($pollsterStats['identity'] as $identity => $count) {
                $percentage = $total > 0 ? round(($count / $total) * 100, 2) : 0;
                $exportData[] = [$identity, $count, $percentage . '%'];
            }
            $exportData[] = ['', '', '']; // 空行分隔
        }
        
        // 3. 性别分布统计
        if (!empty($pollsterStats['gender'])) {
            $exportData[] = ['性别分布统计', '', ''];
            $exportData[] = ['性别', '数量', '占比(%)'];
            $total = array_sum($pollsterStats['gender']);
            foreach ($pollsterStats['gender'] as $gender => $count) {
                $percentage = $total > 0 ? round(($count / $total) * 100, 2) : 0;
                $exportData[] = [$gender, $count, $percentage . '%'];
            }
            $exportData[] = ['', '', '']; // 空行分隔
        }
        
        // 4. 年龄分布统计
        if (!empty($pollsterStats['age'])) {
            $exportData[] = ['年龄分布统计', '', ''];
            $exportData[] = ['年龄段', '数量', '占比(%)'];
            $total = array_sum($pollsterStats['age']);
            foreach ($pollsterStats['age'] as $age => $count) {
                $percentage = $total > 0 ? round(($count / $total) * 100, 2) : 0;
                $exportData[] = [$age, $count, $percentage . '%'];
            }
            $exportData[] = ['', '', '']; // 空行分隔
        }
        
        // 5. 地区分布统计
        if (!empty($pollsterStats['region'])) {
            $exportData[] = ['地区分布统计', '', ''];
            $exportData[] = ['地区', '数量', '占比(%)'];
            $total = array_sum($pollsterStats['region']);
            foreach ($pollsterStats['region'] as $region => $count) {
                $percentage = $total > 0 ? round(($count / $total) * 100, 2) : 0;
                $exportData[] = [$region, $count, $percentage . '%'];
            }
            $exportData[] = ['', '', '']; // 空行分隔
        }
        
        // 6. 渠道分布统计
        if (!empty($pollsterStats['channel'])) {
            $exportData[] = ['渠道分布统计', '', ''];
            $exportData[] = ['渠道', '数量', '占比(%)'];
            $total = array_sum($pollsterStats['channel']);
            foreach ($pollsterStats['channel'] as $channel => $count) {
                $percentage = $total > 0 ? round(($count / $total) * 100, 2) : 0;
                $exportData[] = [$channel, $count, $percentage . '%'];
            }
            $exportData[] = ['', '', '']; // 空行分隔
        }
        
        // 7. 来源分布统计
        if (!empty($pollsterStats['source'])) {
            $exportData[] = ['来源分布统计', '', ''];
            $exportData[] = ['来源类型', '数量', '占比(%)'];
            $total = array_sum($pollsterStats['source']);
            foreach ($pollsterStats['source'] as $source => $count) {
                $percentage = $total > 0 ? round(($count / $total) * 100, 2) : 0;
                $exportData[] = [$source, $count, $percentage . '%'];
            }
            $exportData[] = ['', '', '']; // 空行分隔
        }
        
        // 8. 趋势数据统计
        if (!empty($pollsterStats['trend'])) {
            $exportData[] = ['趋势数据统计', '', ''];
            $exportData[] = ['日期', '问卷数量', '参与用户数'];
            foreach ($pollsterStats['trend'] as $trend) {
                $exportData[] = [
                    $trend['date'] ?? '',
                    $trend['count'] ?? 0,
                    $trend['user_count'] ?? 0
                ];
            }
        }
        
        // 生成文件名
        $dateRange = '';
        if (!empty($where['date'])) {
            $dateRange = '_' . str_replace([' - ', ' '], ['至', ''], $where['date']);
        }
        $fileName = '调查问卷统计报表' . $dateRange;
        
        // 使用PHPExcelService导出
        PHPExcelService::instance()
            ->setExcelHeader(['项目', '数值', '说明/占比'])
            ->setExcelTile(
                '调查问卷统计报表',
                '统计数据',
                '导出时间：' . date('Y-m-d H:i:s') . ($dateRange ? ' | 统计时间：' . $where['date'] : '')
            )
            ->setExcelContent($exportData)
            ->ExcelSave($fileName);
    }

    
}