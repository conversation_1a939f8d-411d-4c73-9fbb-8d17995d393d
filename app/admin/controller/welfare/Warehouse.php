<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2020-09-02 16:02:38
 * @Last Modified time: 2020-12-28 20:31:24
 */
/*
	奖品管理控制器
 */

namespace app\admin\controller\welfare;

use app\admin\controller\AuthController;
use crmeb\services\{
    JsonService,
    FormBuilder as Form,
    UtilService as Util,
    JsonService as Json
};
use think\Request;
use think\facade\Route as Url;
use app\admin\model\welfare\StoreWelfare as WarehouseModel;
use app\admin\model\evaluation\StoreActivity as ActivityModel;

/**
 * 奖品-仓库
 * Class Billboard
 * @package app\admin\controller\welfare
 */
class Warehouse extends AuthController
{
    public function index()
    {
        $this->assign('activity', ActivityModel::getTierList(null, 0));
        return $this->fetch();
    }

    /**
     * 获取奖品列表
     * return json
     */
    public function warehouse_list()
    {
        $where = Util::getMore([
            ['activity_id', []], //活动分类id
            ['name', $this->request->param('name', '')], //奖品名称
            ['page', 1],
            ['limit', 20],
        ]);
        return Json::successlayui(WarehouseModel::WareList($where));
    }

    /**
     * 获取奖品列表
     * return json
     */
    public function welfare_list($id = 0)
    {
        $cate = ActivityModel::getTierList(null, 0);
        $this->assign('cate', $cate);
        $this->assign('id', (int)$id);
        return $this->fetch();
    }


    /**
     * 显示创建活动单页.
     * @return \think\Response
     */
    public function create($id = 0)
    {
        $f = [];
        $f[] = Form::input('name', '奖品名称');
        $f[] = Form::frameImageOne('image', '奖品图', Url::buildUrl('widget.images/index', array('fodder' => 'image')))->icon('image')->width('100%')->height('500px');
        $f[] = Form::input('number', '库存数');
        $form = Form::make_post_form('添加奖品', $f, Url::buildUrl('save'));
        $this->assign(compact('form'));
        return $this->fetch('public/form-builder');
        return $this->fetch();
    }

    /**
     * 显示编辑资源表单页.
     *
     * @param int $id
     * @return \think\Response
     */
    public function edit($id)
    {
        $c = WarehouseModel::get($id);
        if (!$c) return Json::fail('数据不存在!');
        $field = [
            Form::input('name', '奖品名称', $c->getData('name')),
            Form::frameImageOne('image', '奖品图', Url::buildUrl('widget.images/index', array('fodder' => 'image')), $c->getData('image'))->icon('image')->width('100%')->height('500px'),
            Form::input('number', '库存数', $c->getData('number')),
        ];
        $form = Form::make_post_form('编辑奖品', $field, Url::buildUrl('update', array('id' => $id)), 2);

        $this->assign(compact('form'));
        return $this->fetch('public/form-builder');
    }

    /**
     * 保存新建的资源
     *
     * @param \think\Request $request
     * @return \think\Response
     */
    public function save(Request $request)
    {
        $data = Util::postMore([
            'name',
            ['image', []],
            ['number', 0], //关联商品id
        ], $request);
        if (!$data['name']) return Json::fail('请填写奖品名');
        if (count($data['image']) < 1) return Json::fail('请上传奖品图');
        if ($data['number'] == 0) return Json::fail('请填写库存数');
        $data['image'] = $data['image'][0];
        $data['add_time'] = time();
        $data['status'] = 1;
        $data['is_show'] = 1;
        WarehouseModel::create($data);
        return Json::successful('添加奖品成功!');
    }

    /**
     * 保存更新的资源
     *
     * @param \think\Request $request
     * @param int $id
     * @return \think\Response
     */
    public function update(Request $request, $id)
    {
        $data = Util::postMore([
            'name',
            ['image', []],
            ['number', 0], //关联商品id
        ], $request);
        if (!$data['name']) return Json::fail('请填写奖品名');
        if (count($data['image']) < 1) return Json::fail('请上传奖品图');
        if ($data['number'] == 0) return Json::fail('请填写库存数');
        $data['image'] = $data['image'][0];
        WarehouseModel::edit($data, $id);
        return Json::successful('修改成功!');
    }

    /**
     * 删除指定资源
     *
     * @param int $id
     * @return \think\Response
     */
    public function destroy($id)
    {
        if (!$id) return $this->failed('数据不存在');
        if (!WarehouseModel::be(['id' => $id])) return $this->failed('奖品数据不存在');
        if (WarehouseModel::be(['id' => $id, 'is_del' => 1])) {
            return Json::successful('奖品数据已被删除!');
        } else {
            $res1 = false;
            if ($res1) {
                return Json::fail(WarehouseModel::getErrorInfo('该奖品已参加活动，无法删除!'));
            } else {
                $data['is_del'] = 1;
                $data['is_system_del'] = 1;
                $data['del_time'] = time();
                if (!WarehouseModel::edit($data, $id))
                    return Json::fail('删除失败,请稍候再试!');
                else
                    return Json::successful('成功删除奖品!');
            }
        }
    }
}