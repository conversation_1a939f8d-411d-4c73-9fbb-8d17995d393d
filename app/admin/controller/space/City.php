<?php

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date:   2022-12-21 10:16:38
 * @Last Modified time: 2022-12-21 10:49:06
 */
namespace app\admin\controller\space;

use app\admin\controller\AuthController;
use think\Request;
use think\facade\Route as Url;
use app\admin\model\space\StoreBandCity as CityModel;
use crmeb\services\{FormBuilder as Form, JsonService as Json, UtilService as Util};

/**
 * 评测看版-城市
 * Class Activity
 * @package app\admin\controller\evaluation
 */
class City extends AuthController
{
    /**
     * 显示资源列表
     *
     * @return \think\Response
     */
    public function index()
    {
        return $this->fetch();
    }
    
    /*
     *  异步获取城市列表
     *  @return json
     */
    public function city_list()
    {
        $where = Util::getMore([
            ['is_show', ''],
            ['name', ''],
            ['page', 1],
            ['limit', 20],
            ['order', '']
        ]);
        return Json::successlayui(CityModel::CityList($where));
    }

    /**
     * 设置看版城市上架|下架
     * @param string $is_show
     * @param string $id
     */
    public function set_show($is_show = '', $id = '')
    {
        ($is_show == '' || $id == '') && Json::fail('缺少参数');
        if (CityModel::setCityShow($id, (int)$is_show)) {
            return Json::successful($is_show == 1 ? '显示成功' : '隐藏成功');
        } else {
            return Json::fail(CityModel::getErrorInfo($is_show == 1 ? '显示失败' : '隐藏失败'));
        }
    }

    /**
     * 快速编辑
     * @param string $field
     * @param string $id
     * @param string $value
     */
    public function set_city($field = '', $id = '', $value = '')
    {
        $field == '' || $id == '' || $value == '' && Json::fail('缺少参数');
        if (CityModel::where('id', $id)->update([$field => $value]))
            return Json::successful('保存成功');
        else
            return Json::fail('保存失败');
    }

    /**
     * 显示创建资源表单页.
     *
     * @return \think\Response
     */
    public function create()
    {
        $field = [
            Form::input('name', '城市名称'),
            Form::input('lng', '经度'),
            Form::input('lat', '维度'),
            Form::number('sort', '排序'),
            Form::radio('is_show', '状态', 1)->options([['label' => '显示', 'value' => 1], ['label' => '隐藏', 'value' => 0]])
        ];
        $form = Form::make_post_form('添加城市', $field, Url::buildUrl('save'), 2);
        $this->assign(compact('form'));
        return $this->fetch('public/form-builder');
    }

    /**
     * 保存新建的资源
     *
     * @param \think\Request $request
     * @return \think\Response
     */
    public function save(Request $request)
    {
        $data = Util::postMore([
            'name',
            'lng',
            'lat',
            'sort',
            ['is_show', 0]
        ], $request); 
        if (!$data['name']) return Json::fail('请输入城市名称');
        if (!$data['lng'] && !$data['lat']) return Json::fail('请输入城市的经纬度');
        if ($data['sort'] < 0) $data['sort'] = 0;
        $data['add_time'] = time();
        CityModel::create($data);
        return Json::successful('添加城市成功!');
    }

    /**
     * 显示编辑资源表单页.
     *
     * @param int $id
     * @return \think\Response
     */
    public function edit($id)
    {
        $c = CityModel::get($id);
        if (!$c) return Json::fail('数据不存在!');
        $field = [
            Form::input('name', '城市名称', $c->getData('name')),
            Form::input('lng', '经度', $c->getData('lng')),
            Form::input('lat', '纬度', $c->getData('lat')),
            Form::number('sort', '排序', $c->getData('sort')),
            Form::radio('is_show', '状态', $c->getData('is_show'))->options([['label' => '显示', 'value' => 1], ['label' => '隐藏', 'value' => 0]])
        ];
        $form = Form::make_post_form('编辑城市', $field, Url::buildUrl('update', array('id' => $id)), 2);

        $this->assign(compact('form'));
        return $this->fetch('public/form-builder');
    }

    /**
     * 保存更新的资源
     *
     * @param \think\Request $request
     * @param int $id
     * @return \think\Response
     */
    public function update(Request $request, $id)
    {
        $data = Util::postMore([
            'name',
            'lng',
            'lat',
            'sort',
            ['is_show', 0]
        ], $request);
        if (!$data['name']) return Json::fail('请输入城市名称');
        if (!$data['lng'] && !$data['lat']) return Json::fail('请输入城市的经纬度');
        if ($data['sort'] < 0) $data['sort'] = 0;
        CityModel::edit($data, $id);
        return Json::successful('修改成功!');
    }

    /**
     * 删除指定资源
     *
     * @param int $id
     * @return \think\Response
     */
    public function delete($id)
    {
        if (!CityModel::delCity($id))
            return Json::fail(CityModel::getErrorInfo('删除失败,请稍候再试!'));
        else
            return Json::successful('删除成功!');
    }
}