<?php

/**
 * @Author: <PERSON><PERSON> x<PERSON><PERSON><PERSON>
 * @Date:   2022-12-08 13:53:50
 * @Last Modified time: 2022-12-29 16:57:11
 */
namespace app\admin\controller\space;

use app\admin\controller\AuthController;
use think\Request;
use think\facade\Route as Url;
use app\admin\model\system\SystemAttachment;
use app\admin\model\space\StoreBand as BandModel;
use app\admin\model\space\StoreBandMaterial as BandMaterialModel;
use app\admin\model\space\StoreBandMaterialCategory as CategoryModel;
use crmeb\services\{FormBuilder as Form, JsonService as Json, UtilService as Util};

/**
 * 评测看版-分类
 * Class Activity
 * @package app\admin\controller\evaluation
 */
class BandMaterial extends AuthController
{
    /**
     * 显示资源列表
     *
     * @return \think\Response
     */
    public function index()
    {
        $this->assign('cate_id', $this->request->get('cate_id', 0));
        $this->assign('cate', CategoryModel::getTierList(null, 0));
        $this->assign('band', BandModel::getTierList(null, 0));
        return $this->fetch();
    }


    /*
     *  异步获取分类列表
     *  @return json
     */
    public function get_category()
    {
         return Json::successful(\app\models\evaluation\StoreBandMaterial::field(['id','title', 'is_single','is_required','is_scoring','is_show'])->select()->each(function ($item) {
            $item['label_value'] = json_decode($item['rule_value'], true);
         })->toArray());
    }


    /*
     *  异步获取分类列表
     *  @return json
     */
    public function material_list()
    {
        $where = Util::getMore([
            ['cate_id', $this->request->param('cate_id', '')],
            ['band_space_id', $this->request->param('band_space_id', '')],
            ['file_name', ''],
            ['page', 1],
            ['limit', 20],
            ['order', '']
        ]);
        return Json::successlayui(BandMaterialModel::BandMaterialList($where));
    }

    /**
     * 设置看版分类上架|下架
     * @param string $is_show
     * @param string $id
     */
    public function set_status($status = '', $id = '')
    {
        ($status == '' || $id == '') && Json::fail('缺少参数');
        if (BandMaterialModel::setBandMaterialStatus($id, (int)$status)) {
            return Json::successful($status == 1 ? '显示成功' : '隐藏成功');
        } else {
            return Json::fail(BandMaterialModel::getErrorInfo($status == 1 ? '显示失败' : '隐藏失败'));
        }
    }

    /**
     * 快速编辑
     * @param string $field
     * @param string $id
     * @param string $value
     */
    public function set_band_material($field = '', $id = '', $value = '')
    {
        $field == '' || $id == '' || $value == '' && Json::fail('缺少参数');
        if (BandMaterialModel::where('id', $id)->update([$field => $value]))
            return Json::successful('保存成功');
        else
            return Json::fail('保存失败');
    }

    /**
     * 显示创建资源表单页.
     *
     * @return \think\Response
     */
    public function create($id = 0)
    {
        $this->assign('id', (int)$id);
        return $this->fetch();
    }

    /**
     * 添加和修改素材
     * @param int $id 修改
     * @return json
     * */
    public function save_material($id = 0)
    {
        $data = Util::postMore([
            ['type',1],
            ['source_type',1],
            ['check_special_sure', []],
            ['check_source_sure', []],
            ['cate_id', 0],
            ['band_space_id', 0],
            ['image', ''],
            ['file_name', ''],
            ['video_link', ''],
            ['audio_link', ''],
            ['file_link', ''],
            ['zip_link', ''],
            ['sort', 0],
        ]);
        // 格式化
        $data['cate_id'] =  explode(",", $data['cate_id']);
        $data['band_space_id'] =  explode(",", $data['band_space_id']);
        if ($data['type'] == 3) {
            switch ($data['source_type']) {
                case 1:
                    $data['file'] = $data['image'];
                    break;
                case 2:
                    $data['file'] = $data['video_link'];
                    break;
                case 3: 
                    $data['file'] = $data['audio_link'];
                    break;
                case 4: 
                    $data['file'] = $data['file_link'];
                    break;
                case 5: 
                    $data['file'] = $data['zip_link'];
                    break;
            }
        }
        $material_id = BandMaterialModel::saveMaterial($data['type'],$data);
        if ($material_id)
            return Json::successful('添加成功');
        else
            return Json::fail('添加失败');
    }

    /**
     * 显示编辑资源表单页.
     *
     * @param int $id
     * @return \think\Response
     */
    public function edit($id)
    {
        $c = BandMaterialModel::get($id);
        if (!$c) return Json::fail('数据不存在!');
        // $this->assign('band', BandModel::getTierList(null, 0));
        $defaultField = [
            Form::radio('type', '类型', $c->getData('type'))->options([['label' => '平台内课程', 'value' => 1], ['label' => '下载文件', 'value' => 2]]),
            Form::select('cate_id', '分类', (string)$c->getData('cate_id'))->setOptions(function () use ($id) {
                $list = CategoryModel::getTierList(null, 0);
                $menus = [];
                foreach ($list as $menu) {
                    $menus[] = ['value' => $menu['id'], 'label' => $menu['cate_name']];
                }
                return $menus;
            })->filterable(1),
            
            
        ];
        $type = $c->getData('type');
        if ($type == 1) {
            $specialId = $c->getData('special_id');
            $sourceId = $c->getData('source_id');
            if ($specialId !=0 && $sourceId == 0) {
                $customizeField = [
                    Form::radio('file_type', '课程专题类型', $c->getData('file_type'))->options([['label' => '图文专题', 'value' => 1], ['label' => '音频专题', 'value' => 2],['label' => '视频专题', 'value' => 3]]),
                    Form::input('file_name', '课程专题名称', $c->getData('file_name')),
                   Form::input('special_id', '课程id', $c->getData('special_id')),
                ];
            }else{
                $customizeField = [
                    Form::radio('file_type', '课程素材类型', $c->getData('file_type'))->options([['label' => '图文素材', 'value' => 1], ['label' => '音频素材', 'value' => 2],['label' => '视频素材', 'value' => 3]]),
                    Form::input('file_name', '素材名称', $c->getData('file_name')),
                   Form::input('source_id', '素材id', $c->getData('source_id')),
                ];
            }
        }else{
            $customizeField = [
                Form::radio('file_type', '文件类型', $c->getData('file_type'))->options([['label' => '图片格式', 'value' => 1], ['label' => '音频格式', 'value' => 2],['label' => '视频格式', 'value' => 3],['label' => '文档格式', 'value' => 4],['label' => 'zip压缩格式', 'value' => 5]]),
                Form::input('file_name', '文件名称', $c->getData('file_name')),
               Form::input('file', '下载文件', $c->getData('file')),
            ];
        }
        // 底部
        $bottomField = [
            Form::number('sort', '排序', $c->getData('sort')),
        ];
        $field = array_merge($defaultField,$customizeField,$bottomField);
        $form = Form::make_post_form('编辑资料', $field, Url::buildUrl('update', array('id' => $id)), 2);
        $this->assign(compact('form'));
        return $this->fetch('public/form-builder');
    }
    /**
     * 保存更新的资源
     *
     * @param \think\Request $request
     * @param int $id
     * @return \think\Response
     */
    public function update(Request $request, $id)
    {
        $data = Util::postMore([
            'type',
            'cate_id',
            'special_id',
            'source_id',
            'file_type',
            'file_name',
            'file',
            'sort',
        ], $request);
        if ($data['sort'] < 0) $data['sort'] = 0;
        BandMaterialModel::edit($data, $id);
        return Json::successful('修改成功!');
    }

    /**
     * 删除指定资源
     *
     * @param int $id
     * @return \think\Response
     */
    public function delete($id)
    {
        if (!BandMaterialModel::delBandMaterial($id))
            return Json::fail(BandMaterialModel::getErrorInfo('删除失败,请稍候再试!'));
        else
            return Json::successful('删除成功!');
    }



    /**
     * 获取素材详细信息
     * @param int $id
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function get_material_info($id = 0)
    {
        $data = [];
        // 空间资料分类
        $category_data = CategoryModel::where('is_del', 0)->where('is_show', 1)->select()->toArray();
        $cateList = [];
        foreach ($category_data as $cate) {
            $cateList[] = ['id' => $cate['id'], 'name' => $cate['cate_name'], 'disabled' => 1];//,'disabled'=>$menu['pid']== 0];
        }
        // 乐队空间
        $band_data = BandModel::where('is_show', 1)->where('is_del',0)->whereIn('status',[1,2])->select();
        $bandList = [];
        foreach ($band_data as $band) {
            $bandList[] = ['id' => $band['id'], 'name' => $band['store_name'], 'disabled' => 1];//,'disabled'=>$menu['pid']== 0];
        }
        $data['cateList'] = $cateList;
        $data['bandList'] = $bandList;
        return Json::successful($data);
    }
}