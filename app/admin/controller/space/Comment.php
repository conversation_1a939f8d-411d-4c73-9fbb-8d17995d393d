<?php

/**
 * @Author: <PERSON>
 * @Date:   2022-11-30 16:26:27
 * @Last Modified time: 2022-12-19 17:00:27
 */
namespace app\admin\controller\space;

use app\admin\controller\AuthController;
use think\Request;
use think\facade\Route as Url;
use app\admin\model\user\{
    User, UserBill
};
use app\admin\model\space\{
    StoreComment as StoreCommentModel,
};
use crmeb\services\{FormBuilder as Form, JsonService as Json, UtilService as Util};

/**
 * 乐队空间-发帖评论
 * Class StoreComment
 * @package app\admin\controller\market
 */
class Comment extends AuthController
{
    /**
     * 显示资源列表
     *
     * @return \think\Response
     */
    public function index()
    {
        $this->assign([
            'year' => get_month(),
            'status' => $this->request->param('status', ''),
            'commentCount' => StoreCommentModel::CommentCount(),
        ]);
        return $this->fetch();
    }

    /**
     * 获取评论列表
     * return json
     */
    public function comment_list()
    {
        $where = Util::getMore([
            ['status', ''],
            ['data', ''],
            ['keywords', $this->request->param('keywords', '')], //关键字
            ['page', 1],
            ['limit', 20],
        ]);
        return Json::successlayui(StoreCommentModel::CommentList($where));
    }

    public function comment_info($cid = '')
    {
        if (!$cid || !($commentInfo = StoreCommentModel::get($cid)))
            return $this->failed('订单不存在!');
        $userInfo = User::getUserInfos($commentInfo['uid']);
        $replyUserInfo = '';
        if ($commentInfo['top_id'] != 0) {
            $replyUserInfo = User::getUserInfos(StoreCommentModel::where('related_id',$commentInfo['related_id'])->value('uid'));
        }
        $this->assign(compact('commentInfo', 'userInfo','replyUserInfo'));
        return $this->fetch();
    }

    /**
     * 设置分组
     * @param int $uid
     */
    public function set_status($id = 0)
    {
        if (!$id) return $this->failed('缺少参数');
        $field[] = Form::select('status', '状态分组')->setOptions(function (){
            $menus = [];
            $menus[0] = ['value' => 1, 'label' => '通过'];
            $menus[1] = ['value' => 3, 'label' => '屏蔽'];
            return $menus;
        })->filterable(1);
        $form = Form::make_post_form('设置状态', $field, Url::buildUrl('save_set_status', ['id' => $id]), 2);
        $this->assign(compact('form'));
        return $this->fetch('public/form-builder');
    } 

    /**
     * 设置分组
     * @param int $uid
     */
    public function save_set_status($id = 0)
    {
        if (!$id) return Json::fail('缺少参数');
        list($status) = Util::postMore([
            ['status', 0],
        ], $this->request, true);
        $ids = explode(',',$id);
        $data = $status == 1 ? ['status' => $status] : ['is_del'=>1,'del_time'=>time()];
        $res = StoreCommentModel::whereIn('id', $ids)->update($data);
        if ($res) {
            return Json::successful('设置成功');
        } else {
            return Json::successful('设置失败');
        }
    }

    /**
     * 审核
     * @param int $uid
     */
    public function restore($id = 0)
    {
        $data['status'] = 1;
        $data['is_del'] = 0;
        $data['del_time'] = '';
        if (!StoreCommentModel::edit($data,$id))
            return Json::fail(StoreCommentModel::getErrorInfo('恢复失败,请稍候再试!'));
        else
            return Json::successful('恢复成功!');
    }

    /**
     * 删除指定资源
     *
     * @param int $id
     * @return \think\Response
     */
    public function destroy($id)
    {   
        $data['status'] = 3;
        $data['is_del'] = 1;
        $data['del_time'] = date('Y-m-d H:i:s',time());
        if (!StoreCommentModel::edit($data,$id))
            return Json::fail(StoreCommentModel::getErrorInfo('删除失败,请稍候再试!'));
        else
            return Json::successful('删除成功!');
    }
}