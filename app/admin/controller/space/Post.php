<?php

/**
 * @Author: <PERSON>
 * @Date:   2022-11-30 16:26:02
 * @Last Modified time: 2022-12-19 16:42:32
 */
namespace app\admin\controller\space;

use app\admin\controller\AuthController;
use think\Request;
use think\facade\Route as Url;
use app\admin\model\system\SystemAttachment;
use app\admin\model\space\{

    StoreBandPosts as PostsModel,
    StoreActivity as ActivityModel,
};
use crmeb\services\{FormBuilder as Form, JsonService as Json, UtilService as Util};

/**
 * 乐队空间-发帖
 * Class StorePost
 * @package app\admin\controller\evaluation
 */
class Post extends AuthController
{
    /**
     * 显示资源列表
     *
     * @return \think\Response
     */
    public function index()
    {
        $this->assign([
            'year' => get_month(),
            'status' => $this->request->param('status', ''),
            'postCount' => PostsModel::postCount(),
        ]);
        return $this->fetch();
    }

    /**
     * 获取评测列表
     * return json
     */
    public function post_list()
    {
        $where = Util::getMore([
            ['status', ''],
            ['data', ''],
            ['keywords', $this->request->param('keywords', '')], //关键字
            ['page', 1],
            ['limit', 20],
        ]);
        return Json::successlayui(PostsModel::postList($where));
    }

    /**
     * 显示创建评测单页.
     * @return \think\Response
     */
    public function create($id = 0)
    {
        $f = [];
        // $f[] = Form::hidden('uid', '');
        // $f[] = Form::frameImageOne('avatar', '用户信息', Url::buildUrl('admin/user.user/userListes', array('fodder' => 'avatar')))->icon('plus')->width('100%')->height('500px');
        $f[] = Form::input('name', '名称');
        $f[] = Form::input('position', '位置');
        $f[] = Form::input('date', '时间');
        $f[] = Form::textarea('desc', '介绍');
        $f[] =  Form::frameImages('images', '活动介绍图(640*640px)', Url::buildUrl('widget.images/index', array('fodder' => 'images')))->maxLength(5)->icon('images')->width('100%')->height('500px');
        $form = Form::make_post_form('添加活动', $f, Url::buildUrl('save'));
        $this->assign(compact('form'));
        return $this->fetch('public/form-builder');
    }
     /**
     * 保存新建的资源
     *
     *
     */
    public function save()
    {
        $data = Util::postMore([
            // 'uid', //用户id
            'name', //用户id
            'position',
            'date',
            'desc',
            'images',
        ]);
        //设置基础信息
        if (!$data['name']) return Json::fail('请填写想测项目名称');
        if (!$data['position']) return Json::fail('请填写地址');
        if (!$data['date']) return Json::fail('请填写时间');
        $data['image'] = json_encode($data['images']);
        $data['status'] = 2;
        $data['type'] = 2;
        $data['add_time'] = time();
        PostsModel::beginTrans();
        $res = PostsModel::create($data);
        if ($res) {
            PostsModel::commitTrans();
            return Json::success('添加活动成功!');
        }else{
            PostsModel::rollbackTrans();
            return Json::fail('添加活动失败');
        }
    }


    /**
     * 显示编辑资源表单页.
     *
     * @param int $id
     * @return \think\Response
     */
    public function edit($id)
    {
        $c = PostsModel::get($id);
        if (!$c) return Json::fail('数据不存在!');
        $field = [
            Form::input('name', '项目名称', $c->getData('name')),
            Form::input('position', '位置', $c->getData('position')),
            Form::input('date', '时间', $c->getData('date')),
            Form::input('desc', '简介', $c->getData('desc')),
            Form::input('date', '分类名称', $c->getData('date')),
            Form::frameImages('images', '产品轮播图(640*640px)', Url::buildUrl('widget.images/index', array('fodder' => 'slider_image')), json_decode($c->getData('image'), 1) ?: [])->maxLength(5)->icon('images')->width('100%')->height('500px'),
        ];
        $form = Form::make_post_form('编辑活动', $field, Url::buildUrl('update', array('id' => $id)), 2);

        $this->assign(compact('form'));
        return $this->fetch('public/form-builder');
    }


    /**
     * 保存更新的资源
     *
     * @param \think\Request $request
     * @param int $id
     * @return \think\Response
     */
    public function update(Request $request, $id)
    {
        $data = Util::postMore([
            'title', //用户id
            'position',
            'date',
            'desc',
            'images'
        ], $request);
        if (!$data['name']) return Json::fail('请填写想测项目名称');
        if (!$data['position']) return Json::fail('请填写地址');
        if (!$data['date']) return Json::fail('请填写时间');
        $data['image'] = json_encode($data['images']);
        PostsModel::edit($data, $id);
        return Json::successful('修改成功!');
    }

    /**
     * 显示编辑资源表单页.
     *
     * @param int $id
     * @return \think\Response
     */
    public function set_status($id = 0,$type = 1)
    {
        $c = PostsModel::get($id);
        $image =  $c->activity_id != 0 ? ActivityModel::where('id',$c->activity_id)->value('image')  : '';
        if (!$c) return Json::fail('数据不存在!');
        $field = 
        [
            Form::hidden('type', 2),
            Form::input('title', '留言标题', $c->getData('title')),
            Form::input('activity_id','活动ID', $c->getData('activity_id'))->disabled(true),
            Form::frameImageOne('product', '关联的产品', Url::buildUrl('productList', array('fodder' => 'image')), $image )->icon('image')->width('100%')->height('500px'),
            Form::radio('is_official_comment', '是否官评', $c->getData('is_official_comment'))->options([['label' => '是', 'value' => 1],['label' => '否', 'value' => 0]]),
            Form::radio('status', '是否达成', $c->getData('status'))->options([['label' => '是', 'value' => 1],['label' => '否', 'value' => 0]]),
        ];
        $form = Form::make_post_form('编辑活动状态', $field, Url::buildUrl('save_set_status', array('id' => $id)), 2);
        $this->assign(compact('form'));
        return $this->fetch('public/form-builder');
    }

    public function wish_info($wid = '')
    {
        if (!$wid || !($postInfo = PostsModel::get($wid)))
            return $this->failed('活动不存在!');
        $images = is_string($postInfo['image']) ? json_decode($postInfo['image'], true) : [];
        $this->assign(compact('postInfo','images'));
        return $this->fetch();
    }

    /**
     * 设置分组
     * @param int $uid
     */
    public function save_set_status(Request $request, $id)
    {
 		PostsModel::beginTrans();
        $postInfo = PostsModel::where('id', $id)->find();
        if (!$postInfo) return Json::fail('活动不存在！');
            $postInfo->is_del = 0;
            $postInfo->status = 1;
        if ($postInfo->save()) {
            PostsModel::commitTrans();
            return Json::successful('审核成功！');
        } else {
            PostsModel::rollbackTrans();
            return Json::fail('审核失败');
        }
    }

    /**
     * 审核指定资源
     * @param $id
     */
    public function review($id)
    {
        PostsModel::beginTrans();
        $postInfo = PostsModel::where('id', $id)->find();
        if (!$postInfo) return Json::fail('活动不存在！');
        if ($postInfo['is_del'] == 1) return Json::fail('活动不存在！');
            $postInfo->status = 1;
        if ($postInfo->save()) {
            PostsModel::commitTrans();
            return Json::successful('审核成功！');
        } else {
            PostsModel::rollbackTrans();
            return Json::fail('审核失败');
        }
    }

        /**
     * 设置单个活动上架|下架
     *
     * @return json
     */
    public function set_is_refining($is_refining = '', $id = '')
    {
        ($is_refining == '' || $id == '') && Json::fail('缺少参数');
        $res = PostsModel::where(['id' => $id])->update(['is_refining' => (int)$is_refining]);
        if ($res) {
            return Json::successful($is_refining == 1 ? '设置成功' : '设置成功');
        } else {
            return Json::fail($is_refining == 1 ? '设置失败' : '设置失败');
        }
    }

        /**
     * 获取看板详细信息
     * @param int $id
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException\ModelNotFoundException
     * @throws \think\db\exception
     */
    public function post_info($id = 0)
    {
    	$postInfo = [];
        if ($id) {
            $postInfo = PostsModel::get($id);
            if (!$postInfo) {
                return Json::fail('查看的留言不存在');
            }
            $postInfo['image'] = is_string($postInfo['image']) ? json_decode($postInfo['image'], true) : [];
            $postInfo['video'] = is_string($postInfo['video']) ? json_decode($postInfo['video'], true) : [];
            $postInfo['audio'] = is_string($postInfo['audio']) ? json_decode($postInfo['audio'], true) : [];
        }
        $this->assign(compact('id','postInfo'));
        return $this->fetch();
    }

    /**
     * 删除指定资源
     *
     * @param int $id
     * @return \think\Response
     */
    public function destroy($id)
    {   
        $data['is_del'] = 1;
        if (!PostsModel::edit($data,$id))
            return Json::fail(PostsModel::getErrorInfo('删除失败,请稍候再试!'));
        else
            return Json::successful('删除成功!');
    }
}