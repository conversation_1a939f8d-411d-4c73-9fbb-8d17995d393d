<?php

/**
 * @Author: <PERSON>
 * @Date:   2022-11-30 16:21:45
 * @Last Modified time: 2022-12-29 17:02:50
 */
namespace app\admin\controller\space;

use crmeb\basic\BaseModel;
use think\facade\Route as Url;
use app\admin\model\market\{
    StoreOrder as StoreOrderModel,
    StoreDescription,
    StoreActivityAttrValue,
    StoreActivityAttr,
    StoreActivityAttrResult,
    StoreActivityCate,
    StoreActivityRule,
    StoreCategory as CategoryModel,
    StoreActivity as ActivityModel,
    StoreOrder as AdminStoreOrder,
    StoreActivityWish
};
use app\admin\model\user\User;
use app\admin\model\space\{
    StoreBand as StoreBandModel,
    StoreBandCity as StoreBandCityModel,
    StoreBandPostsTags,
    StoreBandSpaceAssociatedTeaching
}; 
use app\admin\model\system\{
    SystemTeacher as SystemTeacherModel
};
use app\admin\model\store\StoreProduct;
use crmeb\traits\CurdControllerTrait;
use app\models\routine\RoutineTemplate;
use app\admin\controller\AuthController;
use crmeb\services\{
    JsonService, UtilService as Util, JsonService as Json, FormBuilder as Form
};

/**
 * 活动管理
 * Class StoreActivity
 * @package app\admin\controller\market
 */
class Band extends AuthController
{

    use CurdControllerTrait;
    protected $bindModel = StoreBandModel::class;

    /**
     * 显示资源列表
     *
     * @return \think\Response
     */
    public function index()
    {
        $type = $this->request->param('type', 0);
        //获取分类
        $cate =  CategoryModel::getTierList(null, 1);
        //全部
        $all = StoreBandModel::where('is_del', 0)->where('is_show', 1)->count();
        //报名中活动
        $registered = StoreBandModel::where('is_del', 0)->where('is_show', 1)->where('status',1)->count();
        //进行中活动
        $processing = StoreBandModel::where('is_del', 0)->where('is_show', 1)->where('status',2)->count();
        //已结束活动
        $ended = StoreBandModel::where('is_del', 0)->where('is_show', 1)->where('status',3)->count();
        //已取消活动
        $cancelled = StoreBandModel::where('is_del', 0)->where('is_show', 1)->where('status',4)->count();
        //回收站
        $recycle = StoreBandModel::where('is_del', 1)->count();
        $this->assign(compact('cate','type', 'all', 'registered', 'processing','ended','cancelled', 'recycle'));
        return $this->fetch();
    }

    /**
     * 异步查找活动
     *
     * @return json
     */
    public function band_ist()
    {
        $where = Util::getMore([
            ['name', ''],
            ['cate_id', ''],
            ['order', ''],
            [['type', 'd'], $this->request->param('type/d')],
            ['excel', 0],
            ['page', 1],
            ['limit', 20],
        ]);
        return Json::successlayui(StoreBandModel::ActivityList($where));
    }

    /**
     * 显示创建资源表单页.
     *
     * @return \think\Response
     */
    public function create($id = 0)
    {
        $this->assign('id', (int)$id);
        return $this->fetch();
    }

    /**
     * 添加和修改素材
     * @param int $id 修改
     * @return json
     * */
    public function save($id = 0)
    {
        $data = Util::postMore([
            ['type',1],
            ['store_name', ''],
            ['limit_band_size', 0],
            ['detailed_address', ''],
            ['introduction', ''],
            ['check_assistant_sure', []],
            ['check_instructor_sure', []],
            ['label', []],
            ['sort', 0],
            ['city_id', 0],
            ['activity_id', 0],
            ['is_show', 1],
        ]);
        if (!$data['city_id']) return Json::fail('请选择课程大分类');
        if (!$data['activity_id']) return Json::fail('请选择素材分类');

        // 声明
        $tags = $data['label'];
        $assistantResult = [];
        $instructorResult = [];
        // 指导老师
        if (isset($data['check_assistant_sure']) && is_array($data['check_assistant_sure'])) {
            $assistantResult = $data['check_assistant_sure'];
        }
        // 助理
        if (isset($data['check_instructor_sure']) && is_array($data['check_instructor_sure'])) {
            $instructorResult = $data['check_instructor_sure'];
        }
        unset($data['label'],$data['check_assistant_sure'],$data['check_instructor_sure']);
        // dd($data);
        if ($id) {
            //指导老师与助理
            StoreBandSpaceAssociatedTeaching::saveTeaching(2,$id,$assistantResult,$instructorResult);
            //标签
            StoreBandPostsTags::createSystemTags($id,$tags);
            StoreBandModel::update($data, ['id' => $id]);
            return Json::successful('修改成功');
        } else {
            $data['add_time'] = time();
            $spaceId = StoreBandModel::insertGetId($data);
            //指导老师与助理
            $teacher_res = StoreBandSpaceAssociatedTeaching::saveTeaching(1,$spaceId,$assistantResult,$instructorResult);
            //标签
            $tags_res = StoreBandPostsTags::createSystemTags($spaceId,$tags);
            if ($spaceId && $teacher_res && $tags_res)
                return Json::successful('添加成功');
            else
                return Json::fail('添加失败');
        }
    }



    /**
     * 设置单个活动显示|隐藏
     *
     * @return json
     */
    public function set_show($is_show = '', $id = '')
    {
        ($is_show == '' || $id == '') && Json::fail('缺少参数');
        $res = ActivityModel::where(['id' => $id])->update(['is_show' => (int)$is_show]);
        if ($res) {
            return Json::successful($is_show == 1 ? '上架成功' : '下架成功');
        } else {
            return Json::fail($is_show == 1 ? '上架失败' : '下架失
                败');
        }
    }


    /**
     * 快速编辑
     *
     * @return json
     */
    public function set_band($field = '', $id = '', $value = '')
    {
        $field == '' || $id == '' || $value == '' && Json::fail('缺少参数');
        if (ActivityModel::where(['id' => $id])->update([$field => $value]))
            return Json::successful('保存成功');
        else
            return Json::fail('保存失败');
    }



    /**
     * 获取素材详细信息
     * @param int $id
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function get_band_info($id = 0)
    {
        $data = [];
        // 活动列表
        $activity_data = ActivityModel::where('is_del', 0)->where('is_show', 1)->whereIn('status',[0,1])->select();
        $activityList = [];
        foreach ($activity_data as $activity) {
            $activityList[] = ['id' => $activity['id'], 'name' => $activity['store_name'], 'disabled' => 1];
        }
        $data['activityList'] = $activityList;
        // 乐队举办的城市
        $city_data = StoreBandCityModel::where('is_show', 1)->where('status',1)->select();
        $cityList = [];
        foreach ($city_data as $city) {
            $cityList[] = ['id' => $city['id'], 'name' => $city['name'], 'disabled' => 1];
        }
        $data['cityList'] = $cityList;
        if ($id) {
            $sources_info = StoreBandModel::where('id',$id)->find();
            if (!$sources_info) {
                return Json::fail('修改的素材不存在');
            }
            $sources_info['source_type'] = $sources_info['type'];
            $sources_info['pid'] = [$sources_info['pid']];
            $special_category_id =[];
            $sources_info['special_category_id'] = count($special_category_id) > 0 ? array_column($special_category_id, 'cate_id') : [];
            $sources_info['label'] = StoreBandPostsTags::where('band_space_id',$id)->where('is_del',0)->where('is_system',1)->column('name');
            //指导老师
            $teacherId = StoreBandSpaceAssociatedTeaching::where('type',1)->where('band_space_id',$id)->select()->toArray();
            $sourceCheckList = array();
            if ($teacherId) {
                foreach ($teacherId as $k => $v) {
                    $task_list = SystemTeacherModel::where(['id' => $v['teacher_id'],'is_del'=>0])->find();
                    if($task_list){
                        $userInfo = User::where('uid', $task_list['uid'])->field(['avatar', 'nickname'])->find();
                        if ($userInfo) {
                            $task_list['nickname'] = $userInfo['nickname'];
                            $task_list['avatar'] = $userInfo['avatar'];
                        }
                        $task_list['is_check'] = 1;
                        $task_list['sort'] = $v['sort'];
                        array_push($sourceCheckList,$task_list);
                    }else{
                        array_splice($teacherId,$k,1);
                        continue;
                    }
                }
            }
            // 助理
            $instructorTeacherId = StoreBandSpaceAssociatedTeaching::where('type',2)->where('band_space_id',$id)->select()->toArray();
            $instructorCheckList = array();
            if ($instructorTeacherId) {
                foreach ($instructorTeacherId as $k => $v) {
                    $task_list = SystemTeacherModel::where(['id' => $v['teacher_id'],'is_del'=>0])->find();
                    if($task_list){
                      $userInfo = User::where('uid', $task_list['uid'])->field(['avatar', 'nickname'])->find();
                        if ($userInfo) {
                            $task_list['nickname'] = $userInfo['nickname'];
                            $task_list['avatar'] = $userInfo['avatar'];
                        }
                        $task_list['is_check'] = 1;
                        $task_list['sort'] = $v['sort'];
                        array_push($instructorCheckList,$task_list);
                    }else{
                        array_splice($instructorTeacherId,$k,1);
                        continue;
                    }
                }
            }
            $sources_info['assistant_tmp_list'] = $sourceCheckList;
            $sources_info['check_assistant_sure'] = $sourceCheckList;
            $sources_info['instructor_tmp_list'] = $instructorCheckList;
            $sources_info['check_instructor_sure'] = $instructorCheckList;
            $sources_info['add_time'] = $sources_info['add_time'] ? date('Y-m-d H:i:s',$sources_info['add_time']) : '';
            $data['specialInfo'] = $sources_info;
         }
        return Json::successful($data);
    }

    /**
     * 删除指定资源
     *
     * @param int $id
     * @return \think\Response
     */
    public function delete($id)
    {
        if (!$id) return $this->failed('数据不存在');
        if (!ActivityModel::be(['id' => $id])) return $this->failed('活动数据不存在');
        if (ActivityModel::be(['id' => $id, 'is_del' => 1])) {
            $data['is_del'] = 0;
            if (!ActivityModel::edit($data, $id))
                return Json::fail(ActivityModel::getErrorInfo('恢复失败,请稍候再试!'));
            else
                return Json::successful('成功恢复活动!');
        } else {
            $data['is_del'] = 1;
            if (!ActivityModel::edit($data, $id))
                return Json::fail(ActivityModel::getErrorInfo('删除失败,请稍候再试!'));
            else
                return Json::successful('成功移到回收站!');
        }
    }

    /**
     * 商品列表获取
     * @return json
     * */
    public function system_teacher_list()
    {
        $where = Util::getMore([
            ['page', 1],
            ['limit', 20],
            ['keyword', ''],
            ['category', 1],
        ]);
        $teacher_list =  SystemTeacherModel::getTeacherList($where);
        return Json::successlayui($teacher_list);
    }

    


    public function get_band_list()
    {
        $list = StoreBandModel::where('is_show', 1)->where('is_del',0)->whereIn('status',[1,2])->select();
        $band_ist = [];
        foreach ($list as $menu) {
            $band_ist[] = ['id' => $menu['id'], 'name' => $menu['store_name'], 'disabled' => 1];//,'disabled'=>$menu['pid']== 0];
        }
        return Json::successful($band_ist);
    }



    public function get_city_list()
    {
        $list = StoreBandCityModel::where('is_show', 1)->where('status',1)->select();
        $category = [];
        foreach ($list as $menu) {
            $category[] = ['id' => $menu['id'], 'name' => $menu['name'], 'disabled' => 1];//,'disabled'=>$menu['pid']== 0];
        }
        return Json::successful($category);
    }


    public function get_activity_list()
    {
        $list = ActivityModel::where('is_del', 0)->where('is_show', 1)->whereIn('status',[0,1])->select();
        $category = [];
        foreach ($list as $menu) {
            $category[] = ['id' => $menu['id'], 'name' => $menu['store_name'], 'disabled' => 1];//,'disabled'=>$menu['pid']== 0];
        }
        return Json::successful($category);
    }



    public function searchs_teacher($coures_id=0)
    {
        $type = $this->request->param('type',0);
        $id = $this->request->param('id');
        $this->assign('type', $type);//图文专题
        $this->assign('id', $id);
        return $this->fetch();
    }

        public function searchs_assistant($coures_id=0)
    {
        $type = $this->request->param('type',0);
        $id = $this->request->param('id');
        $this->assign('type', $type);//图文专题
        $this->assign('id', $id);
        return $this->fetch();
    }

}
