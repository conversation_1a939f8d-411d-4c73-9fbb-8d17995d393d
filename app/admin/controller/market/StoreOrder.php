<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2021-06-21 11:05:04
 * @Last Modified time: 2022-11-03 17:30:38
 */

namespace app\admin\controller\market;

use app\admin\controller\AuthController;
use app\admin\model\system\Express;
use crmeb\repositories\ActivityOrderRepository;
use crmeb\repositories\ShortLetterRepositories;
use crmeb\services\{
  ExpressService,
  JsonService,
  MiniProgramService,
  ByteProgramService,
  WechatService,
  FormBuilder as Form,
  CacheService,
  UtilService as Util,
  JsonService as Json
};
use app\admin\model\user\{
  User,
  UserBill
};
use crmeb\basic\BaseModel;
use think\facade\Route as Url;
use app\admin\model\market\{
  StoreOrder as StoreOrderModel,
  StoreOrderCartInfo,
  StoreOrderStatus,
  StorePromoCodeList as PromoCodeListModel,
  StorePromoCode as PromoCodeModel,
  StoreActivity as ActivityModel,
};
use app\models\market\StoreOrder as HomeStoreOrderModel;
use app\admin\model\evaluation\StoreActivity as ActivityModels;
use app\models\market\StorePromoCodeUser;
use app\models\routine\RoutineTemplate;

/**
 * 活动订单管理
 * Class StoreOrder
 * @package app\admin\controller\market
 */
class StoreOrder extends AuthController
{
  /**
   * @return mixed
   */
  public function index()
  {
    $this->assign([
      'year' => get_month(),
      'real_name' => $this->request->get('real_name', ''),
      'status' => $this->request->param('status', ''),
      'orderCount' => StoreOrderModel::orderCount(),
      'payTypeCount' => StoreOrderModel::payTypeCount(),
      'activityList' => ActivityModel::getActivityLists(),
    ]);
    return $this->fetch();
  }

  /**
   * 获取订单列表
   * return json
   */
  public function order_list()
  {
    $where = Util::getMore([
      ['status', ''],
      ['real_name', $this->request->param('real_name', '')],
      ['is_del', 0],
      ['data', ''],
      ['type', ''],
      ['pay_type', ''],
      ['order_type', ''],
      ['activity_id', ''],
      ['order', ''],
      ['page', 1],
      ['limit', 20],
      ['excel', 0]
    ]);

    return Json::successlayui(StoreOrderModel::OrderList($where));
  }

  /**
   * 核销码核销
   * @param string $verify_code
   * @return html
   */
  public function write_order($verify_code = '', $is_confirm = 0)
  {
    if ($this->request->isAjax()) {
      if (!$verify_code) return Json::fail('缺少核销码！');
      StoreOrderModel::beginTrans();
      $orderInfo = StoreOrderModel::where('verify_code', $verify_code)->where('status', 1)->where('refund_status', 0)->find();
      if (!$orderInfo) return Json::fail('核销订单不存在！');
      if ($orderInfo->status > 1) return Json::fail('订单已核销！');
      if ($is_confirm == 0) {
        $orderInfo['nickname'] = User::where(['uid' => $orderInfo['uid']])->value('nickname');
        return Json::successful($orderInfo);
      }
      $orderInfo->status = 2;
      if ($orderInfo->save()) {
        StoreOrderStatus::setStatus($orderInfo->id, 'take_delivery', '已核销');
        //发送短信
        // event('ShortMssageSend', [$orderInfo['order_id'], 'Receiving']);
        StoreOrderModel::commitTrans();
        return Json::successful('核销成功！');
      } else {
        StoreOrderModel::rollbackTrans();
        return Json::fail('核销失败');
      }
    } else
      $this->assign('is_layui', 1);
    return $this->fetch();
  }

  /**
   * 修改支付金额等
   * @param $id
   * @return mixed|\think\response\Json|void
   */
  public function edit($id)
  {
    if (!$id) return $this->failed('数据不存在');
    $order  = StoreOrderModel::get($id);
    if (!$order) return Json::fail('数据不存在!');
    $f = [];
    //共用信息
    $f[] = Form::input('order_id', '订单编号', $order->getData('order_id'))->disabled(1);
    //其他信息
    if ($order['order_type'] == 1) {
      $f[] = Form::number('total_price', '订单总价', $order->getData('total_price'))->min(0);
      $f[] = Form::input('promo_code', '优惠码', $order->getData('promo_code'));
      $f[] = Form::number('pay_price', '实际支付金额', $order->getData('pay_price'))->min(0);
      $f[] = Form::input('add_time', '下单时间', date('Y-m-d H:i:s', $order->getData('add_time')))->disabled(1);
    } else if ($order['order_type'] == 2) {
      $f[] = Form::input('deposit_order_id', '定金编号', $order->getData('deposit_order_id'))->disabled(1);
      $f[] = Form::input('final_payment_order_id', '尾款编号', $order->getData('final_payment_order_id'))->disabled(1);
      $f[] = Form::number('total_price', '订单总价', $order->getData('total_price'))->min(0);
      $f[] = Form::input('promo_code', '优惠码', $order->getData('promo_code'));
      $f[] = Form::number('deposit_pay_price', '定金支付金额', $order->getData('pay_price'))->min(0);
      $f[] = Form::input('deposit_pay_type', '定金支付方式', $order->getData('deposit_pay_type'));
      $f[] = Form::radio('deposit_paid', '定金支付状态', $order->getData('deposit_paid'))->options([['label' => '已支付', 'value' => 1], ['label' => '未支付', 'value' => 0]]);
      $f[] = Form::number('pay_price', '尾款支付金额', $order->getData('pay_price'))->min(0);
      $f[] = Form::input('pay_type', '尾款支付方式', $order->getData('pay_type'));
      $f[] = Form::radio('paid', '定金支付状态',  $order->getData('paid'))->options([['label' => '已支付', 'value' => 1], ['label' => '未支付', 'value' => 0]]);
    } else if ($order['order_type'] == 3) {
      $f[] = Form::number('total_price', '订单总价', $order->getData('total_price'))->min(0);
      $f[] = Form::input('promo_code', '优惠码', $order->getData('promo_code'));
      $f[] = Form::number('pay_price', '实际支付金额', 0)->min(0);
      $f[] = Form::input('add_time', '下单时间', date('Y-m-d H:i:s', $order->getData('add_time')))->disabled(1);
    }
    if ($order['order_type'] == 1) {  //全款订单
      if ($order['paid'] == 0  && $order['status'] == 0) {
        $status_name = '未支付';
      } else if ($order['paid'] == 1 && $order['status'] == 1 && $order['refund_status'] == 0) {
        $status_name = '待核销';
      } else if ($order['paid'] == 1 && $order['status'] == 2 && $order['refund_status'] == 0) {
        $status_name = '交易完成';
      } else if ($order['paid'] == 1 && $order['refund_status'] == 1) {

        $status_name = '退款中';
      } else if ($order['paid'] == 1 && $order['refund_status'] == 2) {
        $status_name = '已退款';
      }
    } else if ($order['order_type'] == 2) { //定金—+尾款
      if ($order['deposit_paid'] == 0  && $order['paid'] == 0 && $order['status'] == 0 && $order['is_del'] == 0) {
        $status_name = '未支付';
      } else if ($order['deposit_paid'] == 1 && $order['paid'] == 0 && $order['status'] == 0  && $order['refund_status'] == 0 && $order['is_del'] == 0) {
        $status_name =  $order['is_cancel'] == 1 ? '用户取消订单-待支付尾款' : '待支付尾款';
      } else if ($order['status'] == 1 && $order['refund_status'] == 0 && $order['is_del'] == 0) {
        $status_name = '待核销';
      } else if ($order['status'] == 2 && $order['refund_status'] == 0 && $order['is_del'] == 0) {
        $status_name = '交易完成';
      } else if ($order['status'] == 3 && $order['refund_status'] == 0 && $order['is_del'] == 0) {
        $status_name = '交易完成,由系统执行';
      } else if ($order['refund_status'] == 1 && $order['is_del'] == 0) {
        $status_name = '申请退款中';
      } else if ($order['refund_status'] == 2 && $order['is_del'] == 0) {
        $status_name = '已退款';
      }
    } else if ($order['order_type'] == 3) { //定金—+尾款
      $status_name = '预报名成功';
    }
    $f[] = Form::input('status', '订单当前状态', $status_name)->disabled(1);
    $f[] = Form::textarea('mark', '退款操作备注', $order->getData('mark'));
    $form = Form::make_post_form('修改订单', $f, Url::buildUrl('update', array('id' => $id)));
    $this->assign(compact('form'));
    return $this->fetch('public/form-builder');
  }

  /**
   * 修改订单提交更新
   * @param $id
   */
  public function update($id)
  {
    $data = Util::postMore([
      'total_price',
      'promo_code',
      'pay_price',
      'paid',
      'pay_type',
      'deposit_pay_price',
      'deposit_paid',
      'deposit_pay_type',
      'mark',
    ]);
    if ($data['total_price'] <= 0) return Json::fail('请输入商品总价');
    $orderInfo = StoreOrderModel::get($id);
    if (!$orderInfo) {
      return Json::fail('订单不存在');
    }
    $promo_code = $orderInfo->promo_code;
    $pay_price = $orderInfo->pay_price;
    $deposit_pay_price = $orderInfo->deposit_pay_price;
    $order_type = $orderInfo->order_type;
    $orderInfo->total_price = $data['total_price'];
    $orderInfo->pay_price = $data['pay_price'];
    $orderInfo->pay_type = $data['pay_type'];
    $orderInfo->paid = $data['paid'];
    $orderInfo->deposit_pay_price = $data['deposit_pay_price'];
    $orderInfo->deposit_paid = $data['deposit_paid'];
    $orderInfo->deposit_pay_type = $data['deposit_pay_type'];
    $orderInfo->mark = $data['mark'];
    $code_use_status = false;
    //优惠码更换，并
    if ($data['promo_code'] != $promo_code) {
      //查询优惠码
      if ((int) sys_config('coupon_code_status') == 1) {
        $couponInfo = PromoCodeListModel::where('code', $data['promo_code'])->where('end_time', '>', time())->where('status', 0)->find();
        if ($couponInfo) {
          $orderInfo->promo_code_id = $couponInfo['id'];
          $orderInfo->promo_code = $data['promo_code'];
          $orderInfo->promo_code_price = $couponInfo['price'];
          $code_use_status = true;
        }
      }
    }
    if ($orderInfo->save()) {
      //改价短信提醒
      if ($data['pay_price'] != $pay_price && $order_type == 1) {
        StoreOrderStatus::setStatus($id, 'order_edit', '修改订单总价为：' . $data['total_price'] . ' 实际支付金额' . $data['pay_price']);
      }
      if ($data['pay_price'] != $pay_price && $order_type == 2) {
        StoreOrderStatus::setStatus($id, 'order_edit', '修改订单总价为：' . $data['total_price'] . '尾款支付金额' . $data['pay_price']);
      }
      if ($data['deposit_pay_price'] != $deposit_pay_price && $order_type == 2) {
        StoreOrderStatus::setStatus($id, 'order_edit', '修改订单总价为：' . $data['total_price'] . ' 定金支付金额' . $data['deposit_pay_price']);
      }
      if ($data['promo_code'] != $promo_code && $code_use_status) {
        $couponInfo = PromoCodeListModel::where('code', $data['promo_code'])->where('end_time', '>', time())->where('status', 0)->find();
        if ($couponInfo) {
          $couponInfo->uid = $orderInfo['uid'];
          $couponInfo->status = 1;
          $couponInfo->use_time = time();
          $couponInfo->save();
          StorePromoCodeUser::addUserUsageRecord($couponInfo['id'], $orderInfo['uid'], $orderInfo['id']);
          StoreOrderStatus::setStatus($id, 'order_edit', '修改订单优惠码' . '修改前:' . $promo_code . ',修改后:' . $data['promo_code']);
        }
      }
      return Json::successful('修改成功!');
    } else {
      return Json::fail('订单修改失败');
    }
  }

  /*
     * 删除订单
     * */
  public function del_order()
  {
    $ids = Util::postMore(['ids'])['ids'];
    if (!count($ids)) return Json::fail('请选择需要删除的订单');
    if (StoreOrderModel::where('is_del', 0)->where('id', 'in', $ids)->count())
      return Json::fail('您选择的的订单存在用户未删除的订单，无法删除用户未删除的订单');
    $res = StoreOrderModel::where('id', 'in', $ids)->update(['is_system_del' => 1]);
    if ($res)
      return Json::successful('删除成功');
    else
      return Json::fail('删除失败');
  }



  /**
   * 修改退款状态
   * @param $id
   * @return \think\response\Json|void
   */
  public function refund_y($id)
  {
    if (!$id) return $this->failed('1数据不存在');
    $order  = StoreOrderModel::get($id);
    if (!$order) return Json::fail('2数据不存在!');
    if ($order['paid'] == 1 && $order['order_type'] == 1) {
      $pay_price = $order->getData('pay_price');
      $refunded_price = $order->getData('refund_price');
      $refundable_price = bcsub($pay_price, $refunded_price, 2);
      $f = [];
      $f[] = Form::input('order_id', '退款单号', $order->getData('order_id'))->disabled(1);
      $f[] = Form::number('pay_price', '支付金额', $order->getData('pay_price'))->disabled(1);
      $f[] = Form::number('promo_code_price', '优惠码优惠金额', $order->getData('promo_code_price'))->disabled(1);
      $f[] = Form::number('refunded_price', '已退款金额', $order->getData('refund_price'))->disabled(1);
      $f[] = Form::number('refundable_price', '可退款金额', $refundable_price)->disabled(1);
      $f[] = Form::number('refund_price', '本次退款金额', $refundable_price)->precision(2)->min(0.01)->max($refundable_price);
      $f[] = Form::radio('type', '状态', 1)->options([['label' => '直接退款', 'value' => 1], ['label' => '退款后,返回原状态', 'value' => 2]]);
      $f[] = Form::textarea('mark', '退款操作备注', $order->getData('mark'));
      $form = Form::make_post_form('退款处理', $f, Url::buildUrl('updateRefundY', array('id' => $id)), 7);
      $this->assign(compact('form'));
      return $this->fetch('public/form-builder');
    } else if ($order['paid'] == 1 && $order['deposit_paid'] == 1 && $order['order_type'] == 2) {
      $pay_price = $order->getData('pay_price');
      $deposit_pay_price = $order->getData('deposit_pay_price');
      $refund_price = $order->getData('refund_price');
      $deposit_refund_price = $order->getData('deposit_refund_price');
      $total_pay_price = bcadd($order->getData('pay_price'), $order->getData('deposit_pay_price'), 2);
      //可退款总金额
      $can_refund_price = bcsub($total_pay_price, $refund_price, 2) > 0 ? bcsub($total_pay_price, $refund_price, 2) : 0;
      //先减定金  后减尾款
      $can_refund_price_dj = $deposit_pay_price > $deposit_refund_price ?  bcsub($deposit_pay_price, $deposit_refund_price, 2) : 0;
      //获取此时，剩余的退款金额
      $this_refund_price = bcadd($can_refund_price_dj, $refund_price, 2);
      $can_refund_price_wk = $this_refund_price == $total_pay_price ? 0 : bcsub($total_pay_price, $this_refund_price, 2);
      $f = [];
      $f[] = Form::input('order_id', '退款单号', $order->getData('order_id'))->disabled(1);
      $f[] = Form::number('total_price', '支付总金额', $order->getData('total_price'))->disabled(1);
      $f[] = Form::number('deposit_pay_price', '定金支付金额', $order->getData('deposit_pay_price'))->disabled(1);
      $f[] = Form::number('pay_price', '尾款支付金额', $order->getData('pay_price'))->disabled(1);
      $f[] = Form::number('deposit_deductions_price', '定金折扣金额', $order->getData('deposit_deductions_price'))->disabled(1);
      $f[] = Form::number('promo_code_price', '优惠码优惠金额', $order->getData('promo_code_price'))->disabled(1);
      $f[] = Form::number('refunded_price', '已退款金额', $order->getData('refund_price'))->disabled(1);
      $f[] = Form::number('refundable_price', '可退款总金额', $can_refund_price)->disabled(1);
      $f[] = $can_refund_price_dj <= 0 ? Form::number('refund_price', '本次退款可定金金额', 0)->disabled(1) : Form::number('refund_price', '本次退款可定金金额', $can_refund_price_dj)->precision(2)->min(0.01)->max($can_refund_price_dj);
      $f[] = $can_refund_price_wk <= 0 ? Form::number('refund_price_wk', '本次可退款尾款金额', 0)->disabled(1) : Form::number('refund_price_wk', '本次可退款尾款金额', $can_refund_price_wk)->precision(2)->min(0.01)->max($can_refund_price_wk);
      $f[] = Form::radio('type', '状态', 1)->options([['label' => '直接退款', 'value' => 1], ['label' => '退款后,返回原状态', 'value' => 2]]);
      $f[] = Form::textarea('mark', '退款操作备注', $order->getData('mark'));
      $form = Form::make_post_form('退款处理', $f, Url::buildUrl('updateRefundY', array('id' => $id)), 7);
      $this->assign(compact('form'));
      return $this->fetch('public/form-builder');
    } else if ($order['paid'] == 0 && $order['deposit_paid'] == 1 && $order['order_type'] == 2) {
      $pay_price = $order->getData('pay_price');
      $deposit_pay_price = $order->getData('deposit_pay_price');
      $refund_price = $order->getData('refund_price');
      $deposit_refund_price = $order->getData('deposit_refund_price');
      $total_pay_price = bcadd($order->getData('pay_price'), $order->getData('deposit_pay_price'), 2);
      //可退款总金额
      $can_refund_price = $deposit_pay_price;
      //先减定金  后减尾款
      $can_refund_price_dj = $deposit_pay_price > $deposit_refund_price ?  bcsub($deposit_pay_price, $deposit_refund_price, 2) : 0;
      //获取此时，剩余的退款金额
      $this_refund_price = bcadd($can_refund_price_dj, $refund_price, 2);
      $f = [];
      $f[] = Form::input('order_id', '退款单号', $order->getData('order_id'))->disabled(1);
      $f[] = Form::number('total_price', '支付总金额', $order->getData('total_price'))->disabled(1);
      $f[] = Form::number('deposit_pay_price', '定金支付金额', $order->getData('deposit_pay_price'))->disabled(1);
      $f[] = Form::number('pay_price', '尾款支付金额', $order->getData('pay_price'))->disabled(1);
      $f[] = Form::number('deposit_deductions_price', '定金折扣金额', $order->getData('deposit_deductions_price'))->disabled(1);
      $f[] = Form::number('promo_code_price', '优惠码优惠金额', $order->getData('promo_code_price'))->disabled(1);
      $f[] = Form::number('refunded_price', '已退款金额', $order->getData('refund_price'))->disabled(1);
      $f[] = Form::number('refundable_price', '可退款总金额', $can_refund_price)->disabled(1);
      $f[] = $can_refund_price_dj <= 0 ? Form::number('refund_price', '本次退款可定金金额', 0)->disabled(1) : Form::number('refund_price', '本次退款可定金金额', $can_refund_price_dj)->precision(2)->min(0.01)->max($can_refund_price_dj);
      $f[] = Form::radio('type', '状态', 1)->options([['label' => '直接退款', 'value' => 1], ['label' => '退款后,返回原状态', 'value' => 2]]);
      $f[] = Form::textarea('mark', '退款操作备注', $order->getData('mark'));
      $form = Form::make_post_form('退款处理', $f, Url::buildUrl('updateRefundY', array('id' => $id)), 7);
      $this->assign(compact('form'));
      return $this->fetch('public/form-builder');
    } else {
      return Json::fail('3数据不存在!');
    }
  }

  /**
   * 退款处理
   * @param $id
   */
  public function updateRefundY($id)
  {
    $data = Util::postMore([
      'refund_price', //定金
      'refund_price_wk', //尾款
      'mark'
    ]);
    if (!$id) return $this->failed('数据不存在');
    $order  = StoreOrderModel::get($id);
    if (!$order) return Json::fail('数据不存在!');
    if ($data['mark'] == "") return Json::fail('请输入退款备注');
    $refund_price = 0;
    $refund_data = [];
    $refund_data_wk = [];
    //判断退款金额 权重
    if ($order['order_type'] == 1) {
      if ($order['pay_price'] == $order['refund_price']) return Json::fail('已退完支付金额!不能再退款了');
      if (!$data['refund_price']) return Json::fail('请输入退款金额');
      $refund_price = $data['refund_price'];
      $data['refund_price'] = bcadd($data['refund_price'], $order['refund_price'], 2);
      $rewrite_pay_price = $order['order_type'] == 2 ?  bcadd($order['pay_price'], $order['deposit_pay_price']) : $order['pay_price'];
      $bj = bccomp((float)$rewrite_pay_price, (float)$data['refund_price'], 2);
      if ($bj < 0) return Json::fail('退款金额大于支付金额，请修改退款金额');
      //默认，继续走之前的处理逻辑
      $refund_data['pay_price'] = $order['pay_price'];
      $refund_data['refund_price'] = $refund_price;
      $refund_data['desc'] = $data['mark'];
      $refund_data['refund_id'] = $order['order_id'] . '_' . session_create_id(); //微信退款单号
      $data['refund_status'] = 2;
      $refund_price = $data['refund_price'];
    } elseif ($order['order_type'] == 2) {
      if ($data['refund_price'] != 0 && $data['refund_price'] <= $order['deposit_pay_price']) {
        $refund_price = $data['refund_price'];
        //先退定金的order_id 如果超出的金额值 调用尾款id 进行相应的退款，直到尾款的金额，全部退款给用户，退款流程结束
        $refund_data['pay_price'] = $order['deposit_pay_price'];
        $refund_data['refund_price'] = $refund_price;
        $refund_data['refund_id'] = $order['order_id'] . '_' . session_create_id(); //微信退款单号
        $refund_data['desc'] = $data['mark'];
        $data['deposit_refund_price'] = $data['refund_price'];
      }
      if ($data['refund_price_wk'] != 0 && $data['refund_price_wk'] <= $order['pay_price']) {
        $refund_price_wk = $data['refund_price_wk'];
        $refund_data_wk['pay_price'] = $order['pay_price'];
        $refund_data_wk['refund_price'] = $refund_price_wk;
        $refund_data_wk['desc'] = $data['mark'];
        $refund_data_wk['refund_id'] = $order['final_payment_order_id'] . '_' . session_create_id(); //微信退款单号
        $data['refund_price'] = bcadd($data['refund_price'], $data['refund_price_wk'], 2);
        unset($data['refund_price_wk']);
      }
      $refund_price = $data['refund_price'];
      $data['refund_status'] = 2;
    }
    if ($order['pay_type'] == 'weixin' || $order['pay_type'] == 'bytedance.weixin' || $order['pay_type'] == 'bytedance.alipay' || $order['deposit_pay_type'] == 'weixin' || $order['deposit_pay_type'] == 'bytedance.weixin') {
      if ($order['is_channel'] == 1) { //小程序
        try {
          if ($refund_data) {
            MiniProgramService::payOrderRefund($order['order_id'], $refund_data); //2.5.36
          }
          if ($refund_data_wk) {
            MiniProgramService::payOrderRefund($order['final_payment_order_id'], $refund_data_wk); //2.5.36
          }
        } catch (\Exception $e) {
          return Json::fail($e->getMessage());
        }
      } else if ($order['is_channel'] == 3) { //小程序
        try {
          if ($refund_data) {
            ByteProgramService::payOrderRefund($order['order_id'], $refund_data); //2.5.36
          }
          if ($refund_data_wk) {
            ByteProgramService::payOrderRefund($order['final_payment_order_id'], $refund_data_wk); //2.5.36
          }
        } catch (\Exception $e) {
          return Json::fail($e->getMessage());
        }
      } else { //TODO 公众号
        try {
          if ($refund_data) {
            WechatService::payOrderRefund($order['order_id'], $refund_data);
          }
          if ($refund_data_wk) {
            WechatService::payOrderRefund($order['final_payment_order_id'], $refund_data_wk); //2.5.36
          }
        } catch (\Exception $e) {
          return Json::fail($e->getMessage());
        }
      }
    } else if ($order['pay_type'] == 'yue') {
      BaseModel::beginTrans();
      $usermoney = User::where('uid', $order['uid'])->value('now_money');
      $res1 = User::bcInc($order['uid'], 'now_money', $refund_price, 'uid');
      $res2 = $res2 = UserBill::income('商品退款', $order['uid'], 'now_money', 'pay_product_refund', $refund_price, $order['id'], bcadd($usermoney, $refund_price, 2), '订单退款到余额' . floatval($refund_price) . '元');
      $res = $res1 && $res2;
      BaseModel::checkTrans($res);
      if (!$res) return Json::fail('余额退款失败!');
    }
    $resEdit = StoreOrderModel::edit($data, $id);
    $res = true;
    if ($resEdit) {
      try {
        $orderInfo = StoreOrderModel::where('id', $id)->find();
        $store_name = StoreOrderModel::getActivityTitle($orderInfo['cart_id']);
        $pay_price = bcadd($orderInfo['pay_price'], $orderInfo['deposit_pay_price'], 2);
        RoutineTemplate::sendOrderRefundSuccess($orderInfo['uid'], $orderInfo['order_id'], $store_name, $pay_price, $data['refund_price']); //订阅提醒
      } catch (\Exception $e) {
        BaseModel::rollbackTrans();
        return Json::fail($e->getMessage());
      }
      StoreOrderStatus::setStatus($id, 'refund_price', '退款给用户' . $refund_price . '元');
      if ($order['is_regression_stock'] == 0) {
        StoreOrderModel::RegressionStock($order); //回退报名或者众筹人数
        StoreOrderModel::edit(['is_regression_stock' => 1], $id);
      }
      BaseModel::commitTrans();
      return Json::successful('修改成功!');
    } else {
      StoreOrderStatus::setStatus($id, 'refund_price', '退款给用户' . $refund_price . '元失败');
      return Json::fail('修改失败!');
    }
  }

  public function order_info($oid = '')
  {
    if (!$oid || !($orderInfo = StoreOrderModel::get($oid)))
      return $this->failed('订单不存在!');
    $orderInfo->invite_order_id = StoreOrderModel::where('id', $orderInfo['invite_oid'])->value('order_id');
    $orderInfo->skills = is_string($orderInfo['skills']) ? json_decode($orderInfo['skills'], true) : [];
    $userInfo = User::getUserInfos($orderInfo['uid']);
    if ($userInfo['spread_uid']) {
      $spread = User::where('uid', $userInfo['spread_uid'])->value('nickname');
    } else {
      $spread = '';
    }
    $activityId = StoreOrderCartInfo::where('oid', $orderInfo['id'])->value('activity_id') ?: 0;
    $studentInfo = HomeStoreOrderModel::getApplyInfoInfo($orderInfo, $activityId) ?: [];
    $this->assign(compact('orderInfo', 'studentInfo', 'userInfo', 'spread'));
    return $this->fetch();
  }



  /**
   * 修改退款状态
   * @param $id
   * @return mixed|\think\response\Json|void
   */
  public function refund_n($id)
  {
    if (!$id) return $this->failed('数据不存在');
    $order  = StoreOrderModel::get($id);
    if (!$order) return Json::fail('数据不存在!');
    $f[] = Form::input('order_id', '订单号', $order->getData('order_id'))->disabled(1);
    $f[] = Form::input('refund_reason', '退款原因')->type('textarea');
    $form = Form::make_post_form('退款', $f, Url::buildUrl('updateRefundN', array('id' => $id)));
    $this->assign(compact('form'));
    return $this->fetch('public/form-builder');
  }

  /**
   * 不退款原因
   * @param $id
   */
  public function updateRefundN($id)
  {
    $data = Util::postMore([
      'refund_reason',
    ]);
    if (!$id) return $this->failed('数据不存在');
    $order  = StoreOrderModel::get($id);
    if (!$order) return Json::fail('数据不存在!');
    if (!$data['refund_reason']) return Json::fail('请输入退款原因');
    $data['refund_status'] = 0;
    StoreOrderModel::edit($data, $id);
    event('StoreActivityOrderRefundNAfter', [$data['refund_reason'], $id]);
    StoreOrderStatus::setStatus($id, 'refund_n', '不退款原因:' . $data['refund_reason']);
    return Json::successful('修改成功!');
  }

  /**
   * 立即支付
   * @param $id
   */
  public function offline($id)
  {
    $res = StoreOrderModel::updateOffline($id);
    if ($res) {
      event('StoreActivityOrderOffline', [$id]);
      StoreOrderStatus::setStatus($id, 'offline', '线下付款');
      return Json::successful('修改成功!');
    } else {
      return Json::fail(StoreOrderModel::getErrorInfo('修改失败!'));
    }
  }


  public function remark()
  {
    $data = Util::postMore(['id', 'remark']);
    if (!$data['id']) return Json::fail('参数错误!');
    if ($data['remark'] == '') return Json::fail('请输入要备注的内容!');
    $id = $data['id'];
    unset($data['id']);
    StoreOrderModel::edit($data, $id);
    return Json::successful('备注成功!');
  }

  public function order_status($oid)
  {
    if (!$oid) return $this->failed('数据不存在');
    $this->assign(StoreOrderStatus::systemPage($oid));
    return $this->fetch();
  }

  /*
     * 订单列表推荐人详细
     */
  public function order_spread_user($uid)
  {
    $spread = User::where('uid', $uid)->find();
    $this->assign('spread', $spread);
    return $this->fetch();
  }

  /**
   * 立即核销
   * @param $id
   */
  public function verify($id)
  {
    StoreOrderModel::beginTrans();
    $orderInfo = StoreOrderModel::where('id', $id)->find();
    if (!$orderInfo) return Json::fail('核销订单不存在！');
    if ($orderInfo->status > 1) return Json::fail('订单已核销！');
    $orderInfo->status = 2;
    if ($orderInfo->save()) {
      StoreOrderStatus::setStatus($orderInfo->id, 'take_delivery', '已核销');
      //发送短信
      event('ShortMssageSend', [$orderInfo['order_id'], 'Receiving']);
      StoreOrderModel::commitTrans();
      return Json::successful('核销成功！');
    } else {
      StoreOrderModel::rollbackTrans();
      return Json::fail('核销失败');
    }
  }
}
