<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2021-06-25 17:08:01
 * @Last Modified time: 2021-08-16 13:30:48
 */
namespace app\admin\controller\market;

use app\admin\controller\AuthController;
use think\facade\Route as Url;
use app\admin\model\market\{
    StoreOrder as StoreOrderModel,
    StorePromoCodeList as PromoCodeListModel
};
use app\admin\model\user\User;
use crmeb\services\{FormBuilder as Form, UtilService as Util, JsonService as Json};

/**
 * 优惠码控制器
 * Class StoreCategory
 * @package app\admin\controller\system
 */
class StorePromoCodeList extends AuthController
{

    /**
     * @return mixed
     */
    public function index()
    {
        $where = Util::getMore([
            ['status', ''],
            ['name', ''],
            ['page', 1],
            ['limit', 20]
        ], $this->request);
        $this->assign('where', $where);
        $this->assign(PromoCodeListModel::systemPage($where));
        return $this->fetch();
    }

    /**
     * @return mixed
     */
    public function create()
    {
        $field = [
            Form::input('title', '名称'),
            Form::input('price', '面值'),
            Form::number('number', '发放个数'),
            Form::date('valid_time', '有效时间'),
            Form::textarea('remarks', '备注')
        ];
        $form = Form::make_post_form('添加优惠码', $field, Url::buildUrl('save'), 2);
        $this->assign(compact('form'));
        return $this->fetch('public/form-builder');
    }

    /**
     * 保存
     */
    public function save()
    {
        $data = Util::postMore([
            'title',
            'price',
            'remarks',
            'valid_time',
            ['number', 0]
        ]);
        if (!$data['title']) return Json::fail('请输入优惠码名称');
        if (!$data['price']) return Json::fail('请输入优惠码面值');
        if (!$data['valid_time']) return Json::fail('请输入优惠码有效期限');
        if (!$data['number'] && $data['number'] == 0) return Json::fail('请输入优惠码发放个数');
        $data['status'] = 1;
        $data['add_time'] = time();
        $data['valid_time'] = strtotime($data['valid_time']);
        //按照发放个数，生成唯一code 码
        $res =  PromoCodeListModel::create($data);
        if (!$res) {
            return Json::fail('添加失败');
        }
        $promocode = [];
        for ($i=0; $i < $data['number']; $i++) { 
           $promocode[$i]['cid'] = $res['id'];
           $promocode[$i]['coupon_title'] = make_coupon_card();
           $promocode[$i]['coupon_price'] = $data['price'];
           $promocode[$i]['end_time'] = $data['valid_time'];
           $promocode[$i]['add_time'] = time();
        }
        PromoCodeListModel::insertAll($promocode);
        return Json::successful('添加优惠码成功!');
    }

    /**
     * 显示编辑资源表单页.
     * @param $id
     * @return string|void
     * @throws \FormBuilder\exception\FormBuilderException
     */
    public function edit($id)
    {
        $code = PromoCodeListModel::get($id);
        if (!$code) return Json::fail('数据不存在!');
        $f = [];
        $f[] = Form::input('title', '名称', $code->getData('title'));
        $f[] = Form::number('price', '面值', $code->getData('price'))->min(0);
        $f[] = Form::number('number', '发放个数', $code->getData('number'))->min(0);
        $f[] = Form::date('valid_time', '有效期限', date('Y-m-d',$code->getData('valid_time')));
        $f[] = Form::textarea('remarks', '备注', $code->getData('remarks'));
        $f[] = Form::radio('status', '状态', $code->getData('status'))->options([['label' => '开启', 'value' => 1], ['label' => '关闭', 'value' => 0]]);
        $form = Form::make_post_form('编辑优惠码', $f, Url::buildUrl('update', array('id' => $id)));
        $this->assign(compact('form'));
        return $this->fetch('public/form-builder');
    }


    /**
     * 保存更新的资源
     *
     * @param $id
     */
    public function update($id)
    {
        $data = Util::postMore([
            'title',
            'price',
            'remarks',
            'valid_time',
            ['number', 0]
        ]);
        if (!$data['title']) return Json::fail('请输入优惠码名称');
        if (!$data['price']) return Json::fail('请输入优惠码面值');
        if (!$data['valid_time']) return Json::fail('请输入优惠码有效期限');
        if (!$data['number'] && $data['number'] == 0) return Json::fail('请输入优惠码发放个数');
        PromoCodeListModel::edit($data, $id);
        return Json::successful('修改成功!');
    }

    /**
     * 删除指定资源
     *
     * @param int $id
     * @return \think\Response
     */
    public function delete($id)
    {
        if (!$id) return Json::fail('数据不存在!');
        $data['is_del'] = 1;
        if (!PromoCodeListModel::edit($data,$id))
            return Json::fail(PromoCodeListModel::getErrorInfo('删除失败,请稍候再试!'));
        else
            return Json::successful('删除成功!');
    }


    public function code_info($id = '')
    {
        if (!$id || !($codeInfo = PromoCodeListModel::get($id)))
            return $this->failed('code不存在!');
        $userInfo = [];
        $orderInfo = [];
        if ($codeInfo['uid']) $userInfo = User::getUserInfos($codeInfo['uid']);
        if ($codeInfo['oid']) $orderInfo = StoreOrderModel::get($codeInfo['oid']);
        $this->assign(compact('codeInfo', 'userInfo','orderInfo'));
        return $this->fetch();
    }

    /**
     * 修改优惠码状态
     * @param $id
     * @return \think\response\Json
     */
    public function status($id)
    {
        if (!$id) return Json::fail('数据不存在!');
        if (!PromoCodeListModel::editIsDel($id))
            return Json::fail(PromoCodeListModel::getErrorInfo('修改失败,请稍候再试!'));
        else
            return Json::successful('修改成功!');
    }
}