<?php

namespace app\admin\controller\market;

use app\admin\model\special\Special;
use app\admin\controller\AuthController;
use app\admin\model\market\{
  StoreActivityOnline as StoreActivityOnlineModel,
  StoreActivityOnlineMessage as StoreActivityOnlineMessageModel,
  StoreActivityOnlineComment as StoreActivityOnlineCommentModel,
  StoreActivityOnlinePartake as StoreActivityOnlinePartakeModel,
};
use crmeb\services\{
  FormBuilder as Form,
  UtilService,
  JsonService as Json
};
use app\admin\model\ump\{StoreCoupon as CouponModel, StoreCouponIssue};

/**
 * 限时活动  控制器
 * Class StoreActivityOnline
 * @package app\admin\controller\market
 */
class StoreActivityOnline extends AuthController
{
  protected $bindModel = StoreActivityOnlineModel::class;

  /**
   * 显示资源列表
   *
   * @return \think\Response
   */
  public function index()
  {
    return $this->fetch();
  }

  /**
   * 异步获取砍价数据
   */
  public function get_activity_list()
  {
    $where = UtilService::getMore([
      ['page', 1],
      ['limit', 20],
      ['status', ''],
      ['store_name', '']
    ]);
    $activityList = StoreActivityOnlineModel::systemPage($where);
    if (is_object($activityList['list'])) $activityList['list'] = $activityList['list']->toArray();
    $data = $activityList['list']['data'];
    return Json::successlayui(['count' => $activityList['list']['total'], 'data' => $data]);
  }

  /**
   * 添加活动活动课程
   * @return form-builder
   */
  public function create($id = 0)
  {
    if ($id) {
      $special_info = StoreActivityOnlineModel::where('id', $id)->find();
      if (!$special_info) {
        return Json::fail('修改的活动不存在');
      }
    }
    $this->assign('id', (int)$id);
    return $this->fetch();
  }

  /**
   * 保存活动活动课程
   * @param int $id
   */
  public function save($id = 0)
  {
    $data = UtilService::postMore([
      ['type', 1],
      'title',
      'image',
      ['slider_image', []],
      'description',
      'push_message_data',
      'content',
      'submit_button_name',
      'contact_title',
      'contact_info',
      'contact_image',
      'url',
      ['sort', 0],
      ['is_show', 1],
      ['is_post_show', 1],
      ['is_push_message', 1],
      ['is_partake_users_show', 1],
      ['status', 0],
    ]);
    if (!$data['title']) return Json::fail('请输入活动课程标题');
    if (count($data['slider_image']) < 1) return Json::fail('请上传活动轮播图');
    $data['slider_image'] = json_encode($data['slider_image']);
    if (in_array($data['type'], [0, 1, 2])) $data['is_push_message'] = 0;
    if ($id) {
      StoreActivityOnlineModel::edit($data, $id);
      return Json::successful('编辑成功!');
    } else {
      if (StoreActivityOnlineModel::be(['type' => $data['type'], 'title' => $data['title'], 'status' => 1, 'is_show' => 1, 'is_del' => 0])) return Json::fail('添加失败，存在相同标题的活动');
      $data['add_time'] = time();
      $res = StoreActivityOnlineModel::create($data);
      return Json::successful('添加成功!');
    }
  }


  /**
   * 获取活动详细信息
   * @param int $id
   * @throws \think\db\exception\DataNotFoundException
   * @throws \think\db\exception\DbException
   * @throws \think\db\exception\ModelNotFoundException
   */
  public function get_activity_info($id = 0)
  {
    $data = [];
    if ($id) {
      $activity_info = StoreActivityOnlineModel::where('id', $id)->find();
      $activity_info['slider_image'] = is_string($activity_info['slider_image']) ? json_decode($activity_info['slider_image'], true) : [];
      if (!$activity_info) {
        return Json::fail('修改的活动不存在');
      }
      $data['activityInfo'] = $activity_info;
    }
    return Json::successful($data);
  }

  /**
   * 设置资源上架|下架
   * @param string $is_show
   * @param string $id
   */
  public function set_status($status = '', $id = '')
  {
    ($status == '' || $id == '') && Json::fail('缺少参数');
    if (StoreActivityOnlineModel::setSpecialActivityStatus($id, (int)$status)) {
      return Json::successful($status == 1 ? '开启成功' : '关闭成功');
    } else {
      return Json::fail(BannerModel::getErrorInfo($status == 1 ? '开启失败' : '关闭失败'));
    }
  }


  /**
   * 活动属性选择页面
   * @param $id
   * @return string|void
   */
  public function roster($id)
  {
    $type = 1;
    if ($id) {
      $special_info = StoreActivityOnlineModel::where('id', $id)->find();
      if ($special_info) {
        $type = $special_info->type;
      }
    }
    $this->assign('type', $type);
    $this->assign('id', $id);
    return $this->fetch();
  }


  /*
     * 异步获取参与活动参与记录列表
     * @param int $id 会员id
     * @param int $page 分页
     * @param int $limit 显示条数
     * @return json
     * */
  public function get_roster_list($id = 0)
  {

    $where['id'] = $id;
    list($page, $limit, $keywords) = UtilService::getMore([
      ['page', 1],
      ['limit', 10],
      ['keywords', '']
    ], $this->request, true);
    $where['keywords'] = $keywords;
    return Json::successlayui(StoreActivityOnlinePartakeModel::getRecordList($where, (int)$page, (int)$limit));
  }

  /**
   * 推送用户消息
   *
   * @param int $id
   * @return \think\Response
   */
  public function store_push_message($id)
  {
    if (!$id) return $this->failed('数据不存在');
    $activity = StoreActivityOnlineModel::get($id);
    if (!$activity) return Json::fail('活动不存在!');
    if ($activity['is_del']) return Json::fail('已删除!');
    if (!StoreActivityOnlinePartakeModel::be(['activity_id' => $id])) return Json::fail('发送失败，还没有预约报名人员!');
    // if ($activity['is_admin_push_message_status']) return Json::fail('推送失败，已推送过一次!');
    if (!StoreActivityOnlinePartakeModel::PushWxMessage($id))
      return Json::fail(StoreActivityOnlinePartakeModel::getErrorInfo('推送失败,请稍候再试!'));
    else
      return Json::successful('推送成功!');
  }
  /**
   * 删除指定资源
   *
   * @param int $id
   * @return \think\Response
   */
  public function delete($id)
  {
    if (!$id) return $this->failed('数据不存在');
    $product = StoreActivityOnlineModel::get($id);
    if (!$product) return Json::fail('数据不存在!');
    if ($product['is_del']) return Json::fail('已删除!');
    $data['is_del'] = 1;
    if (!StoreActivityOnlineModel::edit($data, $id))
      return Json::fail(StoreActivityOnlineModel::getErrorInfo('删除失败,请稍候再试!'));
    else
      return Json::successful('删除成功!');
  }
}
