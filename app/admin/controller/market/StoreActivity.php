<?php

namespace app\admin\controller\market;

use crmeb\basic\BaseModel;
use think\facade\Route as Url;
use app\admin\model\market\{
    StoreOrder as StoreOrderModel,
    StoreDescription,
    StoreActivityAttrValue,
    StoreActivityAttr,
    StoreActivityAttrResult,
    StoreActivityCate,
    StoreActivityRule,
    StoreCategory as CategoryModel,
    StoreActivity as ActivityModel,
    StoreOrder as AdminStoreOrder,
    StoreActivityWish
};
use crmeb\traits\CurdControllerTrait;
use app\models\routine\RoutineTemplate;
use app\admin\controller\AuthController;
use crmeb\services\{
    JsonService, UtilService as Util, JsonService as Json, FormBuilder as Form
};

/**
 * 活动管理
 * Class StoreActivity
 * @package app\admin\controller\market
 */
class StoreActivity extends AuthController
{

    use CurdControllerTrait;
    protected $bindModel = ActivityModel::class;

    /**
     * 显示资源列表
     *
     * @return \think\Response
     */
    public function index()
    {
        $type = $this->request->param('type', 0);
        //获取分类
        $cate =  CategoryModel::getTierList(null, 1);
        //全部
        $all = ActivityModel::where('is_del', 0)->where('is_show', 1)->count();
        //众筹中活动
        $crowdfunding = ActivityModel::where('is_del', 0)->where('is_show', 1)->where('status',0)->count();
        //报名中活动
        $registered = ActivityModel::where('is_del', 0)->where('is_show', 1)->where('status',1)->count();
        //进行中活动
        $processing = ActivityModel::where('is_del', 0)->where('is_show', 1)->where('status',2)->count();
        //已结束活动
        $ended = ActivityModel::where('is_del', 0)->where('is_show', 1)->where('status',3)->count();
        //已取消活动
        $cancelled = ActivityModel::where('is_del', 0)->where('is_show', 1)->where('status',4)->count();
        //回收站
        $recycle = ActivityModel::where('is_del', 1)->count();
        $this->assign(compact('cate','type', 'all', 'crowdfunding', 'registered', 'processing','ended','cancelled', 'recycle'));
        return $this->fetch();
    }


    /**
     * @return mixed
     */
    public function wish_index()
    {
        $this->assign([
            'keywords' => $this->request->get('keywords', ''),
        ]);
        return $this->fetch();
    }

    /**
     * 获取订单列表
     * return json
     */
    public function wish_list()
    {
        $where = Util::getMore([
            ['keywords', $this->request->param('keywords', '')],
            ['page', 1],
            ['limit', 20],
            ['excel', 0]
        ]);
        return Json::successlayui(StoreActivityWish::getWishList($where));
    }

    /**
     * 异步查找活动
     *
     * @return json
     */
    public function activity_ist()
    {
        $where = Util::getMore([
            ['name', ''],
            ['cate_id', ''],
            ['order', ''],
            [['type', 'd'], $this->request->param('type/d')],
            ['excel', 0],
            ['page', 1],
            ['limit', 20],
        ]);
        return Json::successlayui(ActivityModel::ActivityList($where));
    }

    /**
     * 显示创建资源表单页.
     *
     * @return \think\Response
     */
    public function create($id = 0)
    {
        $this->assign('id', (int)$id);
        return $this->fetch();
    }

    /**
     * 保存新建的资源
     *
     *
     */
    public function save($id)
    {
        $data = Util::postMore([
            ['cate_id', []],
            'store_name',
            'info',
            'contact_info',
            'contact_image',
            'douyin_contact_image',
            'wait_wechat_group_image',
            'ok_wechat_group_image',
            'wait_douyin_group_image',
            'ok_douyin_group_image',
            ['image', []],
            ['slider_image', []],
            ['sort', 0],
            ['sales', 0],
            ['ficti', 100],
            'start_end',
            'start_activity_time_str',
            'end_activity_time_str',
            'activity_start_end',
            'crowdfunding_start_end',
            'end_discount_time',
            'hint',
            'offer_notes',
            'latlng',
            'detailed_address',
            'introduction',
            'activity_order_notes',
            ['description', ''],
            ['text_agreement', ''],
            'many_quota',
            'single_quota',
            ['spec_type', 0],
            ['video_link', ''],
            ['items', []],
            ['attrs', []],
            ['limit_signup_number', 0],
            ['limit_crowdfunding_number', 0],
            ['is_show', 0],
            ['is_spare', 1],
            ['is_channels', 1],
            ['channels', []],
            ['is_apply', 1],
            ['is_must_work', 0],
            ['is_manage', 1],
            ['careers', []],
            ['musical_instruments', []],
            ['is_questionnaire', 1],
            ['is_bytedance_show', 0],
            ['is_wish', 0],
            ['is_countdown',0],
            ['wish_title', ''],
            ['wish_desc', ''],
            ['status', 0]
        ]);
        $detail = $data['attrs'];
        $data['price'] = min(array_column($detail, 'price'));
        $data['ot_price'] = min(array_column($detail, 'ot_price'));
        $data['dp_price'] = min(array_column($detail, 'dp_price'));
        $data['dpd_price'] = min(array_column($detail, 'dpd_price'));
        $attr = $data['items'];
        unset($data['items'], $data['video'], $data['attrs']);
        if (count($data['cate_id']) < 1) return Json::fail('请选择活动分类');
        $cate_id = $data['cate_id'];
        $data['cate_id'] = implode(',', $data['cate_id']);
        if (!$data['store_name']) return Json::fail('请输入活动名称');
        if (count($data['image']) < 1) return Json::fail('请上传活动图片');
        if (count($data['slider_image']) < 1) return Json::fail('请上传活动轮播图');
        if ($data['is_channels'] == 1 &&  count($data['channels']) < 1) return Json::fail('请输入渠道'); 
        // if ($data['is_apply'] == 1 &&  count($data['careers']) < 1) return Json::fail('请输入职业'); 
        // if ($data['is_apply'] == 1 &&  count($data['musical_instruments']) < 1) return Json::fail('请输入擅长乐器分类名称'); 
        $data['image'] = $data['image'][0];
        $data['slider_image'] = json_encode($data['slider_image']);
        $data['stock'] = array_sum(array_column($detail, 'stock'));
        $data['latlng'] = is_string($data['latlng']) ? explode(',', $data['latlng']) : $data['latlng'];
        if (count($data['channels']) >= 1){
            $data['channels'] = implode(',', $data['channels']);
        }
        if (count($data['careers']) >= 1){
            $data['careers'] = implode(',', $data['careers']);
        }
        if (count($data['musical_instruments']) >= 1){
            $data['musical_instruments'] = implode(',', $data['musical_instruments']);
        }
        $data['latitude'] = $data['latlng'][0];
        $data['longitude'] = $data['latlng'][1];
        unset($data['latlng']);
        $data['activity_start_end'] = is_string($data['activity_start_end']) ? explode('/', $data['activity_start_end']) : $data['activity_start_end'];
        $data['activity_time'] = strtotime($data['activity_start_end'][0]);
        $data['end_activity_time'] = strtotime($data['activity_start_end'][1]);
        unset($data['activity_start_end']);
        $data['start_end'] = is_string($data['start_end']) ? explode('/', $data['start_end']) : $data['start_end'];
        $data['started_time'] = strtotime($data['start_end'][0]);
        $data['ends_time'] = strtotime($data['start_end'][1]);
        unset($data['start_end']);
        $data['crowdfunding_start_end'] = is_string($data['crowdfunding_start_end']) && $data['crowdfunding_start_end'] != "" ? explode('/', $data['crowdfunding_start_end']) : $data['crowdfunding_start_end'];
        if (is_array($data['crowdfunding_start_end'])) {
            $data['crowdfunding_started_time'] = strtotime($data['crowdfunding_start_end'][0]);
            $data['crowdfunding_ends_time'] = strtotime($data['crowdfunding_start_end'][1]);
        }else{
            $data['crowdfunding_started_time'] = 0;
            $data['crowdfunding_ends_time'] = 0;
        }
        unset($data['crowdfunding_start_end']);
        $data['end_discount_time'] = $data['end_discount_time'] !=""  ? strtotime($data['end_discount_time']) : 0 ;
        ActivityModel::beginTrans();
        if ($id) {
            unset($data['sales']);
            ActivityModel::edit($data, $id);
            $description = $data['description'];
            unset($data['description']);
            StoreDescription::saveDescription($description, $id);
            StoreActivityCate::where('activity_id', $id)->delete();
            $cateData = [];
            foreach ($cate_id as $cid) {
                $cateData[] = ['activity_id' => $id, 'cate_id' => $cid, 'add_time' => time()];
            }
            StoreActivityCate::insertAll($cateData);
            if ($data['spec_type'] == 0) {
                $attr = [
                    [
                        'value' => '规格',
                        'detailValue' => '',
                        'attrHidden' => '',
                        'detail' => ['默认']
                    ]
                ];
                $detail[0]['value1'] = '规格';
                $detail[0]['detail'] = ['规格' => '默认'];
            }
            $attr_res = StoreActivityAttr::createActivityAttr($attr, $detail, $id);
            if ($attr_res) {
                ActivityModel::commitTrans();
                return Json::success('修改成功!');
            } else {
                ActivityModel::rollbackTrans();
                return Json::fail(StoreActivityAttr::getErrorInfo());
            }
        } else {
            $data['add_time'] = time();
            $data['code_path'] = '';
            $res = ActivityModel::create($data);
            $description = $data['description'];
            StoreDescription::saveDescription($description, $res['id']);
            $cateData = [];
            foreach ($cate_id as $cid) {
                $cateData[] = ['activity_id' => $res['id'], 'cate_id' => $cid, 'add_time' => time()];
            }
            StoreActivityCate::insertAll($cateData);
            if ($data['spec_type'] == 0) {
                $attr = [
                    [
                        'value' => '规格',
                        'detailValue' => '',
                        'attrHidden' => '',
                        'detail' => ['默认']
                    ]
                ];
                $detail[0]['value1'] = '规格';
                $detail[0]['detail'] = ['规格' => '默认'];
            }
            $attr_res = StoreActivityAttr::createActivityAttr($attr, $detail, $res['id']);
            if ($attr_res) {
                ActivityModel::commitTrans();
                return Json::success('添加活动成功!');
            } else {
                ActivityModel::rollbackTrans();
                return Json::fail(StoreActivityAttr::getErrorInfo());
            }
        }
    }


    public function update($id)
    {
        $data = Util::postMore([
            'crowdfunding_started_time',
            'crowdfunding_ends_time',
            'started_time',
            'ends_time',
            'activity_time',
            'end_activity_time',
            'start_activity_time_str',
            'end_activity_time_str',
            'is_cancel',
            'cancel_mark',
            ['status',0],
            ['is_pull',0],
            ['type',1],
        ]);
        $data = array_filter($data);
        if ($data['type'] == 1) {
            $data['status'] = 0;
        }
        if (isset($data['crowdfunding_started_time'])) {
            $data['crowdfunding_started_time'] = strtotime($data['crowdfunding_started_time']);
        }
        if (isset($data['crowdfunding_ends_time'])) {
            $data['crowdfunding_ends_time'] = strtotime($data['crowdfunding_ends_time']);
        }
        if (isset($data['started_time'])) {
            $data['started_time'] = strtotime($data['started_time']);
        }
        if (isset($data['ends_time'])) {
            $data['ends_time'] = strtotime($data['ends_time']);
        }
        if (isset($data['activity_time'])) {
            $data['activity_time'] = strtotime($data['activity_time']);
        }
        if (isset($data['end_activity_time'])) {
            $data['end_activity_time'] = strtotime($data['end_activity_time']);
        }
        unset($data['type']);
        //如果活动状态为1 表示活动开启报名，此时向已付定金的用户，推送付尾款通知
        if ($data['status'] == 1 && $data['is_pull'] == 1) {
            $orderGroupIds = AdminStoreOrder::getActivityOrderGroup($id,1);
            foreach ($orderGroupIds as $order) {
                // RoutineTemplate::sendActivityPendingPayment($order['uid'],$order['order_id'],$order['pay_price'],date('Y-m-d H:i:s',$order['deposit_add_time']),date('Y-m-d H:i:s',$data['ends_time'])); 待付款
                $intval_time = date('Y-m-d H:i:s',$data['ends_time']);
                RoutineTemplate::sendActivityFinalPayment($order['uid'],$order['final_payment_order_id'],$order['pay_price'],date('Y-m-d H:i:s',$order['deposit_add_time']),$intval_time);
            }
        }
        //如果活动状态为2 表示活动已开启，此时向已付款的用户，推送活动开始通知
        if ($data['status'] == 2 && $data['is_pull'] == 1) {
            $orderGroupIds = AdminStoreOrder::getActivityOrderGroup($id,2);
            $activity_time = ActivityModel::where('id',$id)->value('store_name');
            $activity_start_time = date('Y-m-d',$data['activity_time']);
            $activity_end_time =date('Y-m-d',$data['end_activity_time']);
            foreach ($orderGroupIds as $order) {
                RoutineTemplate::sendActivityStarted($order['uid'],$activity_time,$activity_start_time,$activity_end_time);
            }
        }
        //如果活动状态为4 表示活动已取消，此时向已经报名的用户，推送活动结束原因
        if ($data['status'] == 4 && $data['is_cancel'] == 1 && $data['is_pull'] == 1) {
            $orderGroupIds = AdminStoreOrder::getActivityOrderGroup($id,3);
            $activity_name = ActivityModel::where('id',$id)->value('store_name');
            foreach ($orderGroupIds as $order) {
                RoutineTemplate::sendEventCancelled($order['uid'],$activity_name,$data['cancel_mark']);
            } 
        }
        ActivityModel::edit($data, $id);
        return Json::successful('修改成功!');
    }

    /**
     * 设置单个活动显示|隐藏
     *
     * @return json
     */
    public function set_show($is_show = '', $id = '')
    {
        ($is_show == '' || $id == '') && Json::fail('缺少参数');
        $res = ActivityModel::where(['id' => $id])->update(['is_show' => (int)$is_show]);
        if ($res) {
            return Json::successful($is_show == 1 ? '上架成功' : '下架成功');
        } else {
            return Json::fail($is_show == 1 ? '上架失败' : '下架失败');
        }
    }

        /**
     * 设置单个活动字节显示|隐藏
     *
     * @return json
     */
    public function set_bytedance_show($is_bytedance_show = '', $id = '')
    {
        ($is_bytedance_show == '' || $id == '') && Json::fail('缺少参数');
        $res = ActivityModel::where(['id' => $id])->update(['is_bytedance_show' => (int)$is_bytedance_show]);
        if ($res) {
            return Json::successful($is_bytedance_show == 1 ? '上架成功' : '下架成功');
        } else {
            return Json::fail($is_bytedance_show == 1 ? '上架失败' : '下架失败');
        }
    }

    /**
     * 快速编辑
     *
     * @return json
     */
    public function set_activity($field = '', $id = '', $value = '')
    {
        $field == '' || $id == '' || $value == '' && Json::fail('缺少参数');
        if (ActivityModel::where(['id' => $id])->update([$field => $value]))
            return Json::successful('保存成功');
        else
            return Json::fail('保存失败');
    }

    /**
     * 设置批量活动显示
     *
     * @return json
     */
    public function activity_show()
    {
        $post = Util::postMore([
            ['ids', []]
        ]);
        if (empty($post['ids'])) {
            return Json::fail('请选择需要上架的活动');
        } else {
            $res = ActivityModel::where('id', 'in', $post['ids'])->update(['is_show' => 1]);
            if ($res)
                return Json::successful('上架成功');
            else
                return Json::fail('上架失败');
        }
    }


    /**
     * @return mixed
     */
    public function activity_status($id,$type)
    {
        $data = Util::getMore(['type',]);//接收参数
        $tab_id = !empty(request()->param('id')) ? request()->param('id') : 1;
        $activity =  ActivityModel::get($id);
        $formbuider = [];
        switch ($data['type']) {
            case 1://报名开始
                $formbuider = [];
                $formbuider[] = Form::date('started_time', '报名开始时间', $activity->getData('started_time') ? date('Y-m-d',$activity->getData('started_time')) : '');
                $formbuider[] = Form::date('ends_time', '报名结束时间', $activity->getData('ends_time') ? date('Y-m-d',$activity->getData('ends_time')) : '');
                $formbuider[] =  Form::input('start_activity_time_str', '报名开始时间字符', $activity->getData('start_activity_time_str'));
                $formbuider[] =  Form::input('end_activity_time_str', '报名结束时间字符', $activity->getData('end_activity_time_str'));
                $formbuider[] = Form::radio('is_pull', '是否推送订阅消息', 1)->options([['label' => '是', 'value' => 1], ['label' => '否', 'value' => 0]]);
                $formbuider[] = Form::hidden('status', 1);
                $formbuider[] = Form::hidden('type', 2);
                break;
            case 2://活动开始
                $formbuider = [];
                $formbuider[] = Form::date('activity_time', '活动开始时间', $activity->getData('activity_time') ? date('Y-m-d',$activity->getData('activity_time')) : '');
                $formbuider[] = Form::date('end_activity_time', '活动结束时间', $activity->getData('end_activity_time') ? date('Y-m-d',$activity->getData('end_activity_time')) : '');
                $formbuider[] =  Form::input('start_activity_time_str', '活动开始时间字符', $activity->getData('start_activity_time_str'));
                $formbuider[] =  Form::input('end_activity_time_str', '活动结束时间字符', $activity->getData('end_activity_time_str'));
                $formbuider[] = Form::radio('is_pull', '是否推送订阅消息', 1)->options([['label' => '是', 'value' => 1], ['label' => '否', 'value' => 0]]);
                $formbuider[] = Form::hidden('status', 2);
                $formbuider[] = Form::hidden('type', 3);
                break;
            case 3://结束
                $formbuider = [];
                $formbuider[] = Form::date('end_activity_time', '结束时间', $activity->getData('end_activity_time') ? date('Y-m-d',$activity->getData('end_activity_time')) : '');
                $formbuider[] =  Form::input('end_activity_time_str', '活动结束时间字符', $activity->getData('end_activity_time_str'));
                $formbuider[] = Form::hidden('status', 3);
                $formbuider[] = Form::hidden('type', 4);
                break;
            case 4://取消
                $formbuider = [];
                $formbuider[] =  Form::radio('is_cancel', '是否取消', $activity->getData('is_cancel'))->options([['label' => '是', 'value' => 1], ['label' => '否', 'value' => 0]]);
                $formbuider[] =  Form::input('end_activity_time_str', '活动取消时间字符', $activity->getData('end_activity_time_str'));
                $formbuider[] =  Form::textarea('cancel_mark', '取消原因', $activity->getData('cancel_mark'));
                $formbuider[] = Form::radio('is_pull', '是否推送订阅消息', 1)->options([['label' => '是', 'value' => 1], ['label' => '否', 'value' => 0]]);
                $formbuider[] = Form::hidden('status', 4);
                $formbuider[] = Form::hidden('type', 5);
                break;
            default ://默认
                $formbuider = [];
                $formbuider[] = Form::date('crowdfunding_started_time', '众筹开始时间', $activity->getData('crowdfunding_started_time') ? date('Y-m-d',$activity->getData('crowdfunding_started_time')) : '');
                $formbuider[] = Form::date('crowdfunding_ends_time', '众筹结束时间', $activity->getData('crowdfunding_ends_time') ? date('Y-m-d',$activity->getData('crowdfunding_ends_time')) : '');
                $formbuider[] = Form::hidden('status', 0);
                $formbuider[] = Form::hidden('type', 1);
                break;
        }
        //后面通用字段
        $form = Form::make_post_form('活动管理', $formbuider, Url::buildUrl('update', array('id' => $id)), 2);
        $this->assign(compact('form'));
        $this->assign('get', request()->param());
        return $this->fetch();
    }

    /**
     * 获取规则属性模板
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function get_rule()
    {
        return Json::successful(StoreActivityRule::field(['rule_name', 'rule_value'])->select()->each(function ($item) {
            $item['rule_value'] = json_decode($item['rule_value'], true);
        })->toArray());
    }

    /**
     * 获取活动详细信息
     * @param int $id
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function get_activity_info($id = 0)
    {
        $list = CategoryModel::getTierList(null, 1);
        $menus = [];
        $storelists = [];
        foreach ($list as $menu) {
            $menus[] = ['value' => $menu['id'], 'label' => $menu['html'] . $menu['cate_name'], 'disabled' => $menu['pid'] == 0 ? 0 : 1];//,'disabled'=>$menu['pid']== 0];
        }
        $data['tempList'] = [];
        $data['cateList'] = $menus;
        $data['storeList'] = $storelists;
        $data['activityInfo'] = [];
        if ($id) {
            $activityInfo = ActivityModel::get($id);
            if (!$activityInfo) {
                return Json::fail('修改的活动不存在');
            }
            $activityInfo['hint'] = $activityInfo['hint'];
            $activityInfo['offer_notes'] = $activityInfo['offer_notes'];
            $activityInfo['info'] = $activityInfo['info'];
            $activityInfo['cate_id'] = explode(',', $activityInfo['cate_id']);
            $activityInfo['contact_info'] = $activityInfo['contact_info'];
            $activityInfo['contact_image'] = $activityInfo['contact_image'];
            $activityInfo['douyin_contact_image'] = $activityInfo['douyin_contact_image'];
            $activityInfo['wait_wechat_group_image'] = $activityInfo['wait_wechat_group_image'];
            $activityInfo['ok_wechat_group_image'] = $activityInfo['ok_wechat_group_image'];
            $activityInfo['wait_douyin_group_image'] = $activityInfo['wait_douyin_group_image'];
            $activityInfo['ok_douyin_group_image'] = $activityInfo['ok_douyin_group_image'];
            $activityInfo['introduction'] = $activityInfo['introduction'];
            $activityInfo['activity_order_notes'] = $activityInfo['activity_order_notes'];
            $activityInfo['limit_signup_number'] = $activityInfo['limit_signup_number'];
            $activityInfo['limit_crowdfunding_number'] = $activityInfo['limit_crowdfunding_number'];
            $activityInfo['latlng'] = $activityInfo['longitude'].'/'.$activityInfo['latitude'];
            $activityInfo['activity_start_end'] = date('Y-m-d H:i:s',$activityInfo['activity_time']). ' / ' . date('Y-m-d H:i:s',$activityInfo['end_activity_time']);
            $activityInfo['start_end'] = date('Y-m-d H:i:s',$activityInfo['started_time']). ' / ' . date('Y-m-d H:i:s',$activityInfo['ends_time']);
            $crowdfunding_started_time = $activityInfo['crowdfunding_started_time'] != 0 ? date('Y-m-d H:i:s',$activityInfo['crowdfunding_started_time']) : '';
            $crowdfunding_ends_time = $activityInfo['crowdfunding_ends_time'] != 0 ? date('Y-m-d H:i:s',$activityInfo['crowdfunding_ends_time']) : '';
            $activityInfo['crowdfunding_start_end'] =  $crowdfunding_started_time != 0 && $crowdfunding_ends_time != 0  ? $crowdfunding_started_time .' / ' . $crowdfunding_ends_time : '';
            $activityInfo['latlng'] =  $activityInfo['latitude'] != '' && $activityInfo['longitude'] != ''  ? $activityInfo['latitude']. ',' .$activityInfo['longitude'] : '';
            $activityInfo['end_discount_time'] =  $activityInfo['end_discount_time'] != 0 ? date('Y-m-d H:i:s',$activityInfo['end_discount_time']) : ''; 
            $activityInfo['address'] = explode(',', $activityInfo['address']);
            $activityInfo['detailed_address'] = $activityInfo['detailed_address'];
            $activityInfo['description'] = htmlspecialchars_decode(StoreDescription::getDescription($id));
            $activityInfo['text_agreement'] = htmlspecialchars_decode($activityInfo['text_agreement']);
            $activityInfo['slider_image'] = is_string($activityInfo['slider_image']) ? json_decode($activityInfo['slider_image'], true) : [];
            $activityInfo['is_show'] = $activityInfo['is_show'];
            $activityInfo['is_wish'] = $activityInfo['is_wish'];
            $activityInfo['wish_title'] = $activityInfo['wish_title'];
            $activityInfo['wish_desc'] = $activityInfo['wish_desc'];
            $activityInfo['status'] = $activityInfo['status'];
            $activityInfo['channels'] = is_string($activityInfo['channels']) ? explode(',', $activityInfo['channels']) : [$activityInfo['channels']];
            $activityInfo['careers'] = is_string($activityInfo['careers']) ? explode(',', $activityInfo['careers']) : [$activityInfo['careers']];
            $activityInfo['musical_instruments'] = is_string($activityInfo['musical_instruments']) ? explode(',', $activityInfo['musical_instruments']) : [$activityInfo['musical_instruments']];            
            if ($activityInfo['spec_type'] == 1) {
                $result = StoreActivityAttrResult::getResult($id, 0);
                foreach ($result['value'] as $k => $v) {
                    $num = 1;
                    foreach ($v['detail'] as $dv) {
                        $result['value'][$k]['value' . $num] = $dv;
                        $num++;
                    }
                }
                $activityInfo['items'] = $result['attr'];
                $activityInfo['attrs'] = $result['value'];
                $activityInfo['attr'] = ['pic' => '', 'price' => 0, 'dp_price' => 0 , 'dpd_price' => 0 ,'ot_price' => 0, 'stock' => 0, 'digits'=>1, 'bar_code' => '', 'weight' => 0, 'volume' => 0];
            } else {
                $result = StoreActivityAttrValue::where('activity_id', $id)->where('type', 0)->find();
                if ($result) {
                    $single = $result->toArray();
                } else {
                    $single = [];
                }
                $activityInfo['items'] = [];
                $activityInfo['attrs'] = [];
                $activityInfo['attr'] = [
                    'pic' => $single['image'] ?? '',
                    'price' => $single['price'] ?? 0,
                    'dp_price' => $single['dp_price'] ?? 0,
                    'dpd_price' => $single['dpd_price'] ?? 0,
                    'ot_price' => $single['ot_price'] ?? 0,
                    'stock' => $single['stock'] ?? 0,
                    'digits' => (int) $single['digits'],
                    'bar_code' => $single['bar_code'] ?? '',
                    'weight' => $single['weight'] ?? 0,
                    'volume' => $single['volume'] ?? 0,
                ];
            }
            $data['activityInfo'] = $activityInfo;
        }
        return JsonService::successful($data);
    }



    public function edit_content($id)
    {
        if (!$id) return $this->failed('数据不存在');
        $Activity = ActivityModel::get($id);
        if (!$Activity) return Json::fail('数据不存在!');
        $this->assign([
            'content' => $Activity->description,
            'field' => 'description',
            'action' => Url::buildUrl('change_field', ['id' => $id, 'field' => 'description'])
        ]);
        return $this->fetch('public/edit_content');
    }

    public function attr($id)
    {
        if (!$id) return $this->failed('数据不存在!');
        $result = StoreActivityAttrValue::getStoreActivityAttrResult($id);
        $image = ActivityModel::where('id', $id)->value('image');
        $this->assign(compact('id', 'result', 'image'));
        return $this->fetch();
    }

    /**
     * 生成属性
     * @param int $id
     */
    public function is_format_attr($id = 0, $type = 0)
    {
        $data = Util::postMore([
            ['attrs', []],
            ['items', []]
        ]);
        $attr = $data['attrs'];
        $value = attr_format($attr)[1];
        $valueNew = [];
        $count = 0;
        foreach ($value as $key => $item) {
            $detail = $item['detail'];
            sort($item['detail'], SORT_STRING);
            $suk = implode(',', $item['detail']);
            $types = 1;
            if ($id) {
                $sukValue = StoreActivityAttrValue::where('activity_id', $id)->where('type', 0)->where('suk', $suk)->column('bar_code,dp_price,dpd_price,price,ot_price,stock,digits,image as pic,weight,volume', 'suk');
                if (!count($sukValue)) {
                    if ($type == 0) $types = 0; //编辑商品时，将没有规格的数据不生成默认值
                    $sukValue[$suk]['pic'] = '';
                    $sukValue[$suk]['price'] = 0;
                    $sukValue[$suk]['dp_price'] = 0;
                    $sukValue[$suk]['dpd_price'] = 0;
                    $sukValue[$suk]['ot_price'] = 0;
                    $sukValue[$suk]['stock'] = 0;
                    $sukValue[$suk]['digits'] = 1;
                    $sukValue[$suk]['bar_code'] = '';
                    $sukValue[$suk]['weight'] = 0;
                    $sukValue[$suk]['volume'] = 0;
                }
            } else {
                $sukValue[$suk]['pic'] = '';
                $sukValue[$suk]['price'] = 0;
                $sukValue[$suk]['dp_price'] = 0;
                $sukValue[$suk]['dpd_price'] = 0;
                $sukValue[$suk]['ot_price'] = 0;
                $sukValue[$suk]['stock'] = 0;
                $sukValue[$suk]['digits'] = 1;
                $sukValue[$suk]['bar_code'] = '';
                $sukValue[$suk]['weight'] = 0;
                $sukValue[$suk]['volume'] = 0;
            }
            if ($types) { //编辑商品时，将没有规格的数据不生成默认值
                foreach (array_keys($detail) as $k => $title) {
                    $header[$k]['title'] = $title;
                    $header[$k]['align'] = 'center';
                    $header[$k]['minWidth'] = 130;
                }
                foreach (array_values($detail) as $k => $v) {
                    $valueNew[$count]['value' . ($k + 1)] = $v;
                    $header[$k]['key'] = 'value' . ($k + 1);
                }
                $valueNew[$count]['detail'] = $detail;
                $valueNew[$count]['pic'] = $sukValue[$suk]['pic'] ?? '';
                $valueNew[$count]['price'] = $sukValue[$suk]['price'] ? floatval($sukValue[$suk]['price']) : 0;
                $valueNew[$count]['dp_price'] = $sukValue[$suk]['dp_price'] ? floatval($sukValue[$suk]['dp_price']) : 0;
                $valueNew[$count]['dpd_price'] = $sukValue[$suk]['dpd_price'] ? floatval($sukValue[$suk]['dpd_price']) : 0;
                $valueNew[$count]['ot_price'] = isset($sukValue[$suk]['ot_price']) ? floatval($sukValue[$suk]['ot_price']) : 0;
                $valueNew[$count]['stock'] = $sukValue[$suk]['stock'] ? intval($sukValue[$suk]['stock']) : 0;
                $valueNew[$count]['digits'] = intval($sukValue[$suk]['digits']);
                $valueNew[$count]['bar_code'] = $sukValue[$suk]['bar_code'] ?? '';
                $valueNew[$count]['weight'] = $sukValue[$suk]['weight'] ?? 0;
                $valueNew[$count]['volume'] = $sukValue[$suk]['volume'] ?? 0;
                $count++;
            }
        }
        $header[] = ['title' => '图片', 'slot' => 'pic', 'align' => 'center', 'minWidth' => 80];
        $header[] = ['title' => '起售数量', 'slot' => 'digits', 'align' => 'center', 'minWidth' => 140];
        $header[] = ['title' => '售价', 'slot' => 'price', 'align' => 'center', 'minWidth' => 120];
        $header[] = ['title' => '定金价', 'slot' => 'dp_price', 'align' => 'center', 'minWidth' => 140];
        $header[] = ['title' => '定金折扣价', 'slot' => 'dpd_price', 'align' => 'center', 'minWidth' => 140];
        $header[] = ['title' => '原价', 'slot' => 'ot_price', 'align' => 'center', 'minWidth' => 140];
        $header[] = ['title' => '库存', 'slot' => 'stock', 'align' => 'center', 'minWidth' => 140];
        $header[] = ['title' => '活动编号', 'slot' => 'bar_code', 'align' => 'center', 'minWidth' => 140];
        $header[] = ['title' => '重量(KG)', 'slot' => 'weight', 'align' => 'center', 'minWidth' => 140];
        $header[] = ['title' => '体积(m³)', 'slot' => 'volume', 'align' => 'center', 'minWidth' => 140];
        $header[] = ['title' => '操作', 'slot' => 'action', 'align' => 'center', 'minWidth' => 70];
        $info = ['attr' => $attr, 'value' => $valueNew, 'header' => $header];
        return Json::successful($info);
    }

    public function set_attr($id)
    {
        if (!$id) return $this->failed('活动不存在!');
        list($attr, $detail) = Util::postMore([
            ['items', []],
            ['attrs', []]
        ], null, true);
        $res = StoreActivityAttr::createActivityAttr($attr, $detail, $id);
        if ($res)
            return $this->successful('编辑属性成功!');
        else
            return $this->failed(StoreActivityAttr::getErrorInfo());
    }

    public function clear_attr($id)
    {
        if (!$id) return $this->failed('活动不存在!');
        if (false !== StoreActivityAttr::clearActivityAttr($id) && false !== StoreActivityAttrResult::clearResult($id))
            return $this->successful('清空活动属性成功!');
        else
            return $this->failed(StoreActivityAttr::getErrorInfo('清空活动属性失败!'));
    }

    /**
     * 删除指定资源
     *
     * @param int $id
     * @return \think\Response
     */
    public function delete($id)
    {
        if (!$id) return $this->failed('数据不存在');
        if (!ActivityModel::be(['id' => $id])) return $this->failed('活动数据不存在');
        if (ActivityModel::be(['id' => $id, 'is_del' => 1])) {
            $data['is_del'] = 0;
            if (!ActivityModel::edit($data, $id))
                return Json::fail(ActivityModel::getErrorInfo('恢复失败,请稍候再试!'));
            else
                return Json::successful('成功恢复活动!');
        } else {
            $data['is_del'] = 1;
            if (!ActivityModel::edit($data, $id))
                return Json::fail(ActivityModel::getErrorInfo('删除失败,请稍候再试!'));
            else
                return Json::successful('成功移到回收站!');
        }
    }

    /**
     * 修改活动价格
     */
    public function edit_activity_price()
    {
        $data = Util::postMore([
            ['id', 0],
            ['price', 0],
        ]);
        if (!$data['id']) return Json::fail('参数错误');
        $res = ActivityModel::edit(['price' => $data['price']], $data['id']);
        if ($res) return Json::successful('修改成功');
        else return Json::fail('修改失败');
    }

    /**
     * 修改活动库存
     *
     */
    public function edit_activity_stock()
    {
        $data = Util::postMore([
            ['id', 0],
            ['stock', 0],
        ]);
        if (!$data['id']) return Json::fail('参数错误');
        $res = ActivityModel::edit(['stock' => $data['stock']], $data['id']);
        if ($res) return Json::successful('修改成功');
        else return Json::fail('修改失败');
    }

    /**
     * 位置选择
     * @return string|void
     */
    public function select_address()
    {
        $key = sys_config('tengxun_map_key');
        if (!$key) return $this->failed('请前往设置->物流设置->物流配置 配置腾讯地图KEY', '#');
        $this->assign(compact('key'));
        return $this->fetch();
    }
}
