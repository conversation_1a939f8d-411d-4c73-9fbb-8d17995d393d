<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2021-06-23 14:06:49
 * @Last Modified time: 2021-08-16 13:42:31
 */
namespace app\admin\controller\market;

use app\admin\controller\AuthController;
use app\admin\model\store\StoreCategory as CategoryModel;
use think\facade\Route as Url;
use app\admin\model\wechat\WechatUser as UserModel;
use app\admin\model\market\{
    StorePromoCodeList as PromoCodeListModel, 
    StorePromoCode as PromoCodeModel
};
use crmeb\services\{FormBuilder as Form, UtilService as Util, JsonService as Json};

/**
 * 优惠码控制器
 * Class StoreCategory
 * @package app\admin\controller\system
 */
class StorePromoCode extends AuthController
{

    /**
     * @return mixed
     */
    public function index()
    {
        $this->assign([
            'year' => get_month(),
            'status' => $this->request->param('status', ''),
        ]);
        return $this->fetch();
    }


    /**
     * 获取评测列表
     * return json
     */
    public function code_list()
    {
        $where = Util::getMore([
            ['status', ''],
            ['keywords', ''],
            ['page', 1],
            ['limit', 20]
        ], $this->request);
        return Json::successlayui(PromoCodeModel::CodeList($where));
    }

    /**
     * @return mixed
     */
    public function create()
    {
        $field = [
            Form::input('title', '名称'),
            Form::input('price', '面值'),
            Form::number('number', '发放个数'),
            Form::date('valid_time', '有效时间'),
            Form::textarea('remarks', '备注')
        ];
        $form = Form::make_post_form('添加优惠码', $field, Url::buildUrl('save'), 2);
        $this->assign(compact('form'));
        return $this->fetch('public/form-builder');
    }

    /**
     * 保存
     */
    public function save()
    {
        $data = Util::postMore([
            'title',
            'price',
            'remarks',
            'valid_time',
            ['number', 0]
        ]);
        if (!$data['title']) return Json::fail('请输入优惠码名称');
        if (!$data['price']) return Json::fail('请输入优惠码面值');
        if (!$data['valid_time']) return Json::fail('请输入优惠码有效期限');
        if (!$data['number'] && $data['number'] == 0) return Json::fail('请输入优惠码发放个数');
        $data['status'] = 1;
        $data['add_time'] = time();
        $data['valid_time'] = strtotime($data['valid_time']);
        //按照发放个数，生成唯一code 码
        $res =  PromoCodeModel::create($data);
        if (!$res) {
            return Json::fail('添加失败');
        }
        $promocode = [];
        for ($i=0; $i < $data['number']; $i++) { 
           $promocode[$i]['cid'] = $res['id'];
           $promocode[$i]['code'] = make_coupon_card();
           $promocode[$i]['price'] = $data['price'];
           $promocode[$i]['end_time'] = $data['valid_time'];
           $promocode[$i]['add_time'] = time();
        }
        PromoCodeListModel::insertAll($promocode);
        return Json::successful('添加优惠码成功!');
    }

    /**
     * 显示编辑资源表单页.
     * @param $id
     * @return string|void
     * @throws \FormBuilder\exception\FormBuilderException
     */
    public function edit($id)
    {
        $code = PromoCodeModel::get($id);
        if (!$code) return Json::fail('数据不存在!');
        $f = [];
        $f[] = Form::input('title', '名称', $code->getData('title'));
        $f[] = Form::number('price', '面值', $code->getData('price'))->min(0);
        $f[] = Form::number('number', '发放个数', $code->getData('number'))->min(0);
        $f[] = Form::date('valid_time', '有效期限', date('Y-m-d',$code->getData('valid_time')));
        $f[] = Form::textarea('remarks', '备注', $code->getData('remarks'));
        $f[] = Form::radio('status', '状态', $code->getData('status'))->options([['label' => '开启', 'value' => 1], ['label' => '关闭', 'value' => 0]]);
        $form = Form::make_post_form('编辑优惠码', $f, Url::buildUrl('update', array('id' => $id)));
        $this->assign(compact('form'));
        return $this->fetch('public/form-builder');
    }


    /**
     * 保存更新的资源
     *
     * @param $id
     */
    public function update($id)
    {
        $data = Util::postMore([
            'title',
            'price',
            'remarks',
            'valid_time',
            ['number', 0]
        ]);
        if (!$data['title']) return Json::fail('请输入优惠码名称');
        if (!$data['price']) return Json::fail('请输入优惠码面值');
        if (!$data['valid_time']) return Json::fail('请输入优惠码有效期限');
        if (!$data['number'] && $data['number'] == 0) return Json::fail('请输入优惠码发放个数');
        PromoCodeModel::edit($data, $id);
        return Json::successful('修改成功!');
    }

    /**
     * 保存更新的资源
     *
     * @param $id
     */
    public function export()
    {
        $post = Util::postMore([
            ['ids', []]
        ]);
        if (empty($post['ids'])) {
            return Json::fail('请选择需要发布的看');
        } else {
            $post['ids'] = explode(',', $post['ids'][0]);
            $res = PromoCodeModel::exportCode($post['ids']);
            if ($res)
                return Json::successful('导出成功');
            else
                return Json::fail('导出失败');
        }
    }


    /**
     * 删除指定资源
     *
     * @param int $id
     * @return \think\Response
     */
    public function delete($id)
    {
        if (!$id) return Json::fail('数据不存在!');
        $data['is_del'] = 1;
        if (!PromoCodeModel::editIsDel($id,1))
            return Json::fail(PromoCodeModel::getErrorInfo('删除失败,请稍候再试!'));
        else
            return Json::successful('删除成功!');
    }

    /**
     * 修改优惠码状态
     * @param $id
     * @return \think\response\Json
     */
    public function status($id)
    {
        if (!$id) return Json::fail('数据不存在!');
        if (!PromoCodeModel::editIsDel($id))
            return Json::fail(PromoCodeModel::getErrorInfo('修改失败,请稍候再试!'));
        else
            return Json::successful('修改成功!');
    }
}
