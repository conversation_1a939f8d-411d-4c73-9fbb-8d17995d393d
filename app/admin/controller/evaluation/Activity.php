<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2020-08-28 18:00:13
 * @Last Modified time: 2021-12-23 11:25:35
 */
/*
	评测看板-活动控制器
 */

namespace app\admin\controller\evaluation;

use app\admin\controller\AuthController;
use app\admin\model\order\StoreOrderCartInfo;
use app\admin\model\system\Express;
use crmeb\repositories\OrderRepository;
use crmeb\repositories\ShortLetterRepositories;
use crmeb\services\{
    ExpressService,
    JsonService,
    FormBuilder as Form,
    CacheService,
    UtilService as Util,
    JsonService as J<PERSON>
};
use app\admin\model\order\StoreOrderStatus;
use app\admin\model\ump\StorePink;
use app\admin\model\user\{
    User, UserBill
};
use crmeb\basic\BaseModel;
use think\facade\Route as Url;
use app\admin\model\evaluation\StoreActivity as ActivityModel;
use app\admin\model\evaluation\StoreQuestion as QuestionModel;
use app\admin\model\evaluation\StoreAnswer as AnswerModel;
use app\admin\model\welfare\StoreWelfare as WarehouseModel;
use app\admin\model\evaluation\StoreActivityWelfare as WelfareModel;
use app\admin\model\evaluation\StoreActivityApply as ActivityApplyModel;
use crmeb\services\YLYService;
use think\facade\Log;
use app\admin\model\user\{User as UserModel};

/**
 * 评测看板-活动
 * Class Activity
 * @package app\admin\controller\evaluation
 */
class Activity extends AuthController
{
    public function index()
    {
        $this->assign([
            'year' => get_month(),
            'real_name' => $this->request->get('real_name', ''),
            'status' => $this->request->param('status', ''),
            'ActivityCount' => ActivityModel::ActivityCount(),
            'ActivityTyepeCount' => ActivityModel::ActivityTyepeCount(),
        ]);
        return $this->fetch();
    }

    /**
     * 获取活动列表
     * return json
     */
    public function activity_list()
    {
        $where = Util::getMore([
            ['status', ''],
            ['data', ''],
            ['type', ''],
            ['reward_method', ''],
            ['real_name', $this->request->param('real_name', '')],
            ['subject_name', ''],
            ['page', 1],
            ['limit', 20],
        ]);
        return Json::successlayui(ActivityModel::ActivityList($where));
    }


    /**
     * 显示创建活动单页.
     * @return \think\Response
     */
    public function create($id = 0)
    {
        $prize_inventory = WarehouseModel::where('is_del',0)->count();
        $this->assign('id', (int)$id);
        $this->assign('prize_inventory', (int)$prize_inventory);
        return $this->fetch();
    }

    /**
    * 保存新建的资源
    *
    *
    * $prize_list  = [
    *     ['welfare_id'=>1,'number'=>10],
    *     ['welfare_id'=>2,'number'=>11],
    *     ['welfare_id'=>3,'number'=>12],
    * ];
    * $question_list = [
    *    ['name' =>'你多大了','type' =>1,'answer' =>[18,19,20,21],'is_empty' =>2,'is_upload' =>3],
    *    ['name' =>'你多大了','type' =>1,'answer' =>[18,19,20,21],'is_empty' =>2,'is_upload' =>3]
    * ];
    * $period_list = [
    *    ['name' =>'你多大了','type' =>1,'answer' =>[18,19,20,21],'is_empty' =>2,'is_upload' =>3],
    *    ['name' =>'你多大了','type' =>1,'answer' =>[18,19,20,21],'is_empty' =>2,'is_upload' =>3],
    * ];
    *
    * 
    */
    public function save($id)
    {
        $data = Util::postMore([
            //基本信息
            ['uid',0],//用户id
            'name', //活动主题
            ['cate_id', []], //看板分类
            ['slider_image', []],
            'address',
            'address_detail',
            ['latlng', []],
            'description',
            ['type',1],//活动类型
            ['identity',0],//参与者身份
            ['ficti',0],//人数限额
            //设置奖励
            ['reward_method',0],//奖励方式
            ['bounty_amount', 0.0], //赏金-赏金总额
            ['receive_condition', 0], //现场|物流礼品-领取条件
            ['collection_method', 0], //现场|物流礼品-领取玩法【系统随机、抽奖】
            ['winning_switch', 0], //现场|物流礼品-中奖设置-抽奖开关
            ['valid_period', 0], //现场|物流礼品-中奖设置-领奖有效期【当天有效、活动期有效】
        ]);
        $prize_data = Util::postMore([
            //详细数据
            ['prize_list', []], //奖品
        ]);
        $question_data = Util::postMore([
            //详细数据
            ['question_list', []], //问卷调查
        ]);
        $period_data = Util::postMore([
            //详细数据
            ['period_list', []], //时间段
        ]);
        //设置基础信息
        if (count($data['cate_id']) < 1) return Json::fail('请选择看板分类');
        if (count($data['type']) < 1) return Json::fail('请设置活动类型');
        if (!$data['name']) return Json::fail('请填写活动主题');
        if (count($data['slider_image']) < 1) return Json::fail('请上传看板导览图');
        if (!$data['address']) return Json::fail('请填写活动地点');
        if (!$data['address_detail']) return Json::fail('请填写活动详细地址');
        if (!$data['description']) return Json::fail('请填写活动说明');
        $data['address'] = implode(',', $data['address']);
        $data['latlng'] = is_string($data['latlng']) ? explode(',', $data['latlng']) : $data['latlng'];
        if (!isset($data['latlng'][0]) || !isset($data['latlng'][1])) return JsonService::fail('请选择门店位置');
        $data['latitude'] = $data['latlng'][0];
        $data['longitude'] = $data['latlng'][1];
        $cate_id = $data['cate_id'];
        $data['cate_id'] = implode(',', $data['cate_id']);
        $data['slider_image'] = json_encode($data['slider_image']);
        $data['address'] = json_encode($data['address']);
        //奖励方式--赏金
        if ($data['reward_method'] == 1) {
            if (!$data['bounty_amount'] < 1) return Json::fail('请输入赏金金额');
        }
        //奖励方式--现场礼品、物流礼品
        if ($data['reward_method'] == 1 || $data['reward_method'] == 2) {
            //中奖设置-领取条件
             if (!$data['receive_condition'] < 1) return Json::fail('请勾选领取条件');
             if (!$data['collection_method'] < 1) return Json::fail('请勾选领取玩法');
             if (!$data['winning_switch'] < 1) return Json::fail('请勾选抽奖开关');
             if (!$data['valid_period'] < 1) return Json::fail('请勾选领奖有效期');
            //中奖设置-奖品【领取玩法等于2，判断是否添加了奖品】
            if ($data['collection_method'] == 2 && count($prize_data['prize_list']) < 1) {
               return Json::fail('请在奖品库选择你要发放奖品');
            }
            //中奖设置-问卷调查
            if ($data['receive_condition'] == 1 && count($question_data['question_list']) < 1) {
               return Json::fail('请配置问卷调查数据');
            }
            //中奖设置-时间段
            if ($data['valid_period'] == 2 && count($period_data['period_list']) < 1) {
               return Json::fail('请配置中奖时间段');
            }
        }

        BaseModel::beginTrans();
        if ($id) {
            $res = ActivityModel::edit($data, $id);
            if ($res) {
                ActivityModel::commitTrans();
                return Json::success('修改活动成功!');
            }else{
                ActivityModel::rollbackTrans();
                return Json::fail('修改活动失败');
            }
        }else{
            //活动
            $data['add_time'] = time();
            $data['status'] = 1;
            $res = ActivityModel::insertGetId($data);
            $res_prize =true;
            $res_question =true;
            $res_time =true;
            //奖品
            if (count($prize_data['prize_list']) > 1) {
                $prize = [];
                foreach ($prize_data['prize_list'] as $key => $value) {
                    $prize[$key]['evaluation_activity_id'] = $res->id;
                    $prize[$key]['welfare_id'] =$value['welfare_id'];
                    $prize[$key]['number'] =$value['number'];
                    $prize[$key]['is_show'] = 1;
                    $prize[$key]['status'] = 1;
                    $prize[$key]['add_time'] = time();
                }
                $res_prize = WelfareModel::insertAll($prize);
            }
            //答题问卷
            if (count($question_data['question_list']) > 1) {
                $question = [];
                foreach ($question_data['question_list'] as $key => $value) {
                    $question[$key]['evaluation_activity_id'] = $res->id;
                    $question[$key]['type'] =$value['type'];
                    $question[$key]['name'] =$value['name'];
                    $question[$key]['is_empty'] =$value['is_empty'];
                    $question[$key]['is_upload'] = $value['is_upload'];
                    $question[$key]['is_show'] = 1;
                    $question[$key]['status'] = 1;
                    $question[$key]['add_time'] = time();
                    $answer_detail_data = $value['answer'];
                    //题目
                    $question_id = QuestionModel::insertGetId($question[$key]);
                    if ($question_id) {
                        $answer = [];
                        foreach ($answer_detail_data as $k => $v) {
                            $answer[$k]['evaluation_activity_question_id'] = $question_id;
                            $answer[$k]['text'] = $v;
                            $answer[$k]['add_time'] = time();
                        }
                        $answer_info =  AnswerModel::insertAll($answer);
                    }
                    
                }
            }
            //开奖时间段
            if (count($period_data['period_list']) > 1) {
                $period = [];
                foreach ($period_data['period_list'] as $key => $value) {
                    $period[$key]['evaluation_activity_id'] = $res->id;
                    $period[$key]['begin_time'] =$value['begin_time'];
                    $period[$key]['last_time'] =$value['last_time'];
                    $period[$key]['number'] = $value['number'];
                    $period[$key]['is_show'] = 1;
                    $period[$key]['status'] = 1;
                    $period[$key]['add_time'] = time();
                }
                $res_period = PeriodModel::insertAll($period);
            }
            if ($res && $res_prize && $res_question && $res_time) {
                BaseModel::commitTrans();
                return Json::success('添加活动成功!');
            }else{
                BaseModel::rollbackTrans();
                return Json::fail('添加活动失败');
            }
        }
    }


    /**
     * 删除指定资源
     *
     * @param int $id
     * @return \think\Response
     */
    public function destroy($id)
    {
        if (!$id) return $this->failed('数据不存在');
        if (!KanbanModel::be(['id' => $id])) return $this->failed('看数据不存在');
        if (KanbanModel::be(['id' => $id, 'is_del' => 1])) {
            return Json::successful('看数据已被删除!');
        } else {
            $data['is_del'] = 1;
            $data['is_system_del'] = 1;
            $data['del_time'] = time();
            if (!KanbanModel::edit($data, $id))
                return Json::fail('删除失败,请稍候再试!');
            else
                return Json::successful('成功删除!');
        }
    }

    /**
     * 活动详情
     * @return form-builder
     */
    public function activity_info($id = '')
    {
        if (!$id || !($activityInfo = ActivityModel::get($id)))
            return $this->failed('活动不存在!');
        $activityInfo->image = is_string($activityInfo->image) ? json_decode($activityInfo->image) : [];
        $userInfo = User::getUserInfos($activityInfo['uid']);
        $count = UserModel::getCountInfo($activityInfo['uid']);
        $this->assign(compact('id','count','activityInfo','userInfo'));
        return $this->fetch();
    }

    /**
     * 获取看板详细信息
     * @param int $id
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException\ModelNotFoundException
     * @throws \think\db\exception
     */
    public function get_activity_info($id = 0)
    {
        $list = ActivityModel::getTierList(null, 1);
        $menus = [];
        $storelists = [];
        foreach ($list as $menu) {
            $menus[] = ['value' => $menu['id'], 'label' => $menu['name'], 'disabled' => 1];//,'disabled'=>$menu['pid']== 0];
        }
        $data['kanbanList'] = $menus;
        $data['activityInfo'] = [];
        if ($id) {
            $activityInfo = ActivityModel::get($id);
            if (!$activityInfo) {
                return Json::fail('修改的活动不存在');
            }
            $activityInfo['cate_id'] = explode(',', $activityInfo['cate_id']);
            $activityInfo['product_id'] = explode(',', $activityInfo['product_id']);
            $activityInfo['dimension_id'] = explode(',', $activityInfo['dimension_id']);
            $activityInfo['slider_image'] = is_string($activityInfo['image']) ? json_decode($activityInfo['image'], true) : [];
            if (is_array($activityInfo['product_id'])) {
               $activityInfo['product_image'] = $activityInfo['slider_image'];
            }
            $activityInfo['product_image'] = $activityInfo['slider_image'];
            $activityInfo['dimensionName'] = ['小伟','是的呢'];
            $activityInfo['form_start_end'] = date('Y-m-d H:i:s',$activityInfo['start_time']). ' / ' . date('Y-m-d H:i:s',$activityInfo['end_time']);
            $activityInfo['latlng'] =  $activityInfo['latitude'] != '' && $activityInfo['longitude'] != ''  ? $activityInfo['latitude']. ',' .$activityInfo['longitude'] : '';
            $activityInfo['address'] = explode(',', $activityInfo['address']);
            //设置奖励
            $activityInfo['prize_list']  = ActivityModel::getPrizeList($id);
            $activityInfo['question_list']  = ActivityModel::getQuestionList($id);
            $activityInfo['period_list']  = ActivityModel::getPeriodList($id);
            $data['activityInfo'] = $activityInfo;
        }
        return JsonService::successful($data);
    }

    /**
     * @return \think\Response
     */
    public function getJoinList($id, $page = 1, $limit = 20)
    {
        return Json::successful(ActivityApplyModel::getJoinList((int)$id, (int)$page, (int)$limit));
    }

    /**
     * @return \think\Response
     */
    public function getIssueList($id, $page = 1, $limit = 20)
    {
        return Json::successful(ActivityModel::getIssueList((int)$id, (int)$page, (int)$limit));
    }

    /**
     * 位置选择
     * @return string|void
     */
    public function select_address()
    {
        $key = sys_config('tengxun_map_key');
        if (!$key) return $this->failed('请前往设置->物流设置->物流配置 配置腾讯地图KEY', '#');
        $this->assign(compact('key'));
        return $this->fetch();
    }
}
