<?php
/*
	评测文章
 */

namespace app\admin\controller\evaluation;

use app\admin\controller\AuthController;
use crmeb\services\{
    JsonService,
    FormBuilder as Form,
    UtilService as Util,
    JsonService as Json
};
use crmeb\basic\BaseModel;
use app\admin\model\user\User as UserModel;
use think\facade\Route as Url;
use app\admin\model\evaluation\StoreKanban as KanbanModel;
use app\admin\model\evaluation\StoreCategory as CategoryModel;
use app\admin\model\evaluation\StoreActivity as ActivityModel;
use app\admin\model\evaluation\StoreComment as CommentModel;
use app\admin\model\evaluation\StoreActivityReviews as ActivityReviewsModel;

/**
 * 评测文章
 * Class Article
 * @package app\admin\controller\evaluation
 */
class Article extends AuthController
{
    public function index()
    {
        $this->assign([
            'year' => get_month(),
            'founder' => $this->request->get('founder', ''),
            'status' => $this->request->param('status', ''),
            'KanbanCount' => json_encode(['name']),
            'evaluationCategory'=>json_encode(CategoryModel::getTierList(null, 0)),
            'evaluationActivity'=>json_encode(ActivityModel::getTierList(null, 0)),
        ]);
        return $this->fetch();
    }

    /**
     * 获取评测文章
     * return json
     */
    public function article_list()
    {
        $where = Util::getMore([
            ['status', ''],
            ['data', ''],
            ['billboard_id', []], //看板
            ['activity_id', []], //活动
            ['page', 1],
            ['limit', 20],
        ]);
        return Json::successlayui(ActivityReviewsModel::ArticleList($where));
    }

    /**
     * 显示创建活动单页.
     * @return \think\Response
     */
    public function create($id = 0)
    {
        $this->assign('id', (int)$id);
        return $this->fetch();
    }

    /**
     * 显示创建评论单页.
     * @return \think\Response
     */
    public function create_comment($activity_reviews_id= 0)
    {
        $f = [];
        $f[] = Form::select('pid', '评论级数')->setOptions(function () use($activity_reviews_id) {
                $list = CommentModel::where('evaluation_activity_reviews_id',$activity_reviews_id)->select()->toArray();
                $menus = [['value' => 0, 'label' => '一级评论']];
                foreach ($list as $menu) {
                    $menus[] = ['value' => $menu['id'], 'label' => '回复：' .  mb_substr($menu['comment'], 0, 30)];
                }
                return $menus;
        })->filterable(1);
        $f[] = Form::hidden('uid', '');
        $f[] = Form::hidden('evaluation_activity_reviews_id', $activity_reviews_id);
        $f[] = Form::frameImageOne('avatar', '用户信息', Url::buildUrl('user.user/userListes', array('fodder' => 'avatar')))->icon('plus')->width('100%')->height('500px');
        $f[] = Form::textarea('comment', '评论内容');
        $form = Form::make_post_form('添加评论', $f, Url::buildUrl('save_comment'));
        $this->assign(compact('form'));
        return $this->fetch('public/form-builder');
    }


    /**
     * 回复评论等
     * @param $id
     * @return mixed|\think\response\Json|void
     */
    public function reply($id = 0)
    {
        if (!$id) return $this->failed('数据不存在');
        $comment = CommentModel::get($id);
        if (!$comment) return Json::fail('数据不存在!');
        $f = [];
        $f[] = Form::textarea('comment', '上级评论内容', $comment->getData('comment'))->disabled(1);
        $f[] = Form::hidden('uid', '');
        $f[] = Form::frameImageOne('avatar', '用户信息', Url::buildUrl('admin/user.user/userListes', array('fodder' => 'avatar')))->icon('plus')->width('100%')->height('500px');
        $f[] = Form::textarea('reply_comment', '回复内容', '');
        $form = Form::make_post_form('回复评论', $f, Url::buildUrl('storereply', array('pid' => $id)));
        $this->assign(compact('form'));
        return $this->fetch('public/form-builder');
    }

     /**
     * 保存新建的资源
     *
     *
     */
    public function save($id)
    {
        $data = Util::postMore([
            ['activity_id', []], //活动id
            'uid', //用户id
            'comment',
            'evaluation_score',
            ['pics', []],
            ['score_info', []],
        ]);
        //设置基础信息
        if (count($data['activity_id']) < 1) return Json::fail('请选择活动');
        if (!$data['uid']) return Json::fail('请选择创建评测人的信息');
        if (!$data['comment']) return Json::fail('请填写评语');
        if (count($data['pics']) < 1) return Json::fail('请上传评测图');
        if (!$data['evaluation_score']) return Json::fail('请填写评分');
        if (count($data['score_info']) < 1) return Json::fail('请上传评测各项数值');
        $activity_id = $data['activity_id'];
        $data['evaluation_activity_id'] = $activity_id[0];
        $data['pics'] = json_encode($data['pics']);
        $data['score_info'] = json_encode($data['score_info']);
        unset($data['activity_id']);
        ActivityReviewsModel::beginTrans();
        if ($id) {
            $res = ActivityReviewsModel::edit($data, $id);
            if ($res) {
                ActivityReviewsModel::commitTrans();
                return Json::success('修改用户评测内容成功!');
            }else{
                ActivityReviewsModel::rollbackTrans();
                return Json::fail('修改用户评测内容失败');
            }
        }else{
            $data['add_time'] = time();
            $data['status'] = 1;
            $res = ActivityReviewsModel::create($data);
            if ($res) {
                ActivityReviewsModel::commitTrans();
                return Json::success('添加用户评测成功!');
            }else{
                ActivityReviewsModel::rollbackTrans();
                return Json::fail('添加用户评测失败');
            }
        }
    }


     /**
     * 保存新建的资源
     *
     *
     */
    public function save_comment()
    {
        $data = Util::postMore([
            'uid', //用户id
            'pid', //用户id
            'comment',
            'evaluation_activity_reviews_id',
        ]);
        //设置基础信息
        if (!$data['evaluation_activity_reviews_id']) return Json::fail('关联的活动');
        if (!$data['uid']) return Json::fail('请选择回复人的信息');
        if (!$data['comment']) return Json::fail('请填写评语');
        $data['add_time'] = time();
        $data['status'] = 1;
        $data['top_id'] = 0;
        $pid = $data['pid'];
        if ($pid) {
            // 计算层级
            $pcomment = CommentModel::find($pid);
            $data['top_id'] = $pcomment->pid == 0 ? $pcomment->id : $pcomment->top_id;
            $level = $pcomment->level;
            $data['level'] = $level + 1;
            $data['evaluation_activity_id'] = $pcomment->evaluation_activity_id;
        }else{
            $data['evaluation_activity_id'] = ActivityReviewsModel::where('id',$data['evaluation_activity_reviews_id'])->value('evaluation_activity_id');
        }
        CommentModel::beginTrans();
        $res = CommentModel::create($data);
        if ($res) {
            CommentModel::commitTrans();
            return Json::success('添加用户评论成功!');
        }else{
            CommentModel::rollbackTrans();
            return Json::fail('添加用户评论失败');
        }
    }


    /**
     * 保存回复的资源
     *
     *
     */
    public function storereply($pid)
    {
        if (!$pid) return $this->failed('回复错误，数据不存在');
        $data = Util::postMore([
            'uid', //用户id
            'reply_comment',
        ]);
        //设置基础信息
        if (!$data['uid']) return Json::fail('请选择回复人用户信息');
        if (!$data['reply_comment']) return Json::fail('请填写回复语');
        $data['comment'] = $data['reply_comment'];
        unset($data['reply_comment']);
        CommentModel::beginTrans();
        $data['add_time'] = time();
        $data['status'] = 1;
        $data['pid'] = $pid;
        $data['top_id'] = 0;
        if ($pid) {
            // 计算层级
            $pcomment = CommentModel::find($pid);
            $data['top_id'] = $pcomment->pid == 0 ? $pcomment->id : $pcomment->top_id;
            $level = $pcomment->level;
            $data['level'] = $level + 1;
            $data['evaluation_activity_id'] = $pcomment->evaluation_activity_id;
            $data['evaluation_activity_reviews_id'] = $pcomment->evaluation_activity_reviews_id;
        }
        $res = CommentModel::create($data);
        if ($res) {
            CommentModel::commitTrans();
            return Json::success('回复成功!');
        }else{
            CommentModel::rollbackTrans();
            return Json::fail('回复失败');
        }
    }

    /**
     * 评测详情
     * @return form-builder
     */
    public function article_info($id = '')
    {
        if (!$id || !($articleInfo = ActivityReviewsModel::get($id)))
            return $this->failed('评测文章不存在!');
        $articleInfo->pics = is_string($articleInfo['pics']) ? json_decode($articleInfo['pics'], true) : [];
        $articleInfo->score_info = is_string($articleInfo['score_info']) ? json_decode($articleInfo['score_info'], true) : [];
        $articleInfo->name = ActivityModel::where('id', $articleInfo['evaluation_activity_id'])->value('name') ? ActivityModel::where('id', $articleInfo['evaluation_activity_id'])->value('name') : '';
        $userInfo = UserModel::getUserInfos($articleInfo['uid']);
        $count = UserModel::getCountInfo($articleInfo['uid']);
        $this->assign(compact('id','count','articleInfo','userInfo'));
        return $this->fetch();
    }

    /**
     * 获取评测详细信息
     * @param int $id
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function get_article_info($id = 0)
    {
        $list = ActivityModel::getTierList(null, 1);
        $menus = [];
        $storelists = [];
        foreach ($list as $menu) {
            $menus[] = ['value' => $menu['id'], 'label' => $menu['name'], 'disabled' => 1];//,'disabled'=>$menu['pid']== 0];
        }
        $data['activityList'] = $menus;
        $data['articleInfo'] = [];
        if ($id) {
            $articleInfo = ActivityReviewsModel::get($id);
            if (!$articleInfo) {
                return Json::fail('修改的评测不存在');
            }
            $articleInfo['avatar'] = 'http://kaifa.crmeb.net/uploads/attach/2019/08/25/4fa00c67ca82f04104617cecbe5638b7.jpg';
            $articleInfo['pics'] = is_string($articleInfo['pics']) ? json_decode($articleInfo['pics'], true) : [];
            $articleInfo['score_info'] = is_string($articleInfo['score_info']) ? json_decode($articleInfo['score_info'], true) : [];
            $articleInfo['dimensionTotal'] = 100;
            $data['articleInfo'] = $articleInfo;
        }
        return JsonService::successful($data);
    }

    /**
     * 删除指定资源
     *
     * @param int $id
     * @return \think\Response
     */
    public function destroy($id)
    {

        if (!$id) return $this->failed('数据不存在');
        if (!ActivityReviewsModel::be(['id' => $id])) return $this->failed('评测数据不存在');
        if (ActivityReviewsModel::be(['id' => $id, 'is_del' => 1])) {
            return Json::successful('评测数据已被删除!');
        } else {
	        $data['is_del'] = 1;
	        $data['is_system_del'] = 1;
            $data['del_time'] = time();
	        $data['updated_time'] = time();
	        if (!ActivityReviewsModel::edit($data, $id))
	            return Json::fail('删除失败,请稍候再试!');
	        else
	            return Json::successful('成功删除评测!');
        }
    }

    /**
     * 审核状态
     * @param $id
     */
    public function review($id)
    {
        ActivityReviewsModel::beginTrans();
        $articleInfo = ActivityReviewsModel::where('id', $id)->find();
        if (!$articleInfo) return Json::fail('审核看版不存在！');
        $articleInfo->status = 1;
        if ($articleInfo->save()) {
            ActivityReviewsModel::commitTrans();
            return Json::successful('审核成功！');
        } else {
            ActivityReviewsModel::rollbackTrans();
            return Json::fail('审核失败');
        }
    }

    /**
     * 评论列表
     * @return form-builder
     */
    public function getCommentList($id, $page = 1, $limit = 20)
    {
        return Json::successful(CommentModel::getCommentList((array)['activity_reviews_id'=>$id], (int)$page, (int)$limit));
    }
}
