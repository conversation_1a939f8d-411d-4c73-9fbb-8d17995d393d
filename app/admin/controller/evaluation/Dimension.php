<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2020-09-02 09:41:57
 * @Last Modified time: 2020-09-19 12:47:12
 */
/*
	评测维度
 */

namespace app\admin\controller\evaluation;

use app\admin\controller\AuthController;
use crmeb\services\{
    JsonService,
    FormBuilder as Form,
    UtilService as Util,
    JsonService as Json
};

use think\Request;
use crmeb\basic\BaseModel;
use app\admin\model\user\User;
use think\facade\Route as Url;
use app\admin\model\evaluation\StoreDimension as DimensionModel;
use app\admin\model\evaluation\StoreCategory as CategoryModel;

/**
 * 评测维度
 * Class Dimension
 * @package app\admin\controller\evaluation
 */
class Dimension extends AuthController
{
    public function index()
    {
        $this->assign([
            'name' => $this->request->get('name', ''),
        ]);
        return $this->fetch();
    }

    /**
     * 获取评测维度列表
     * return json
     */
    public function dimension_list()
    {
        $where = Util::getMore([
            ['operating_id', ''],
            ['type', 0],
            ['status', ''],
            ['cate_id', []], //评测维度分类
            ['name', $this->request->param('name', '')], //评测维度名称
            ['page', 1],
            ['limit', 20],
        ]);
        return Json::successlayui(DimensionModel::DimensionList($where));
    }

    /**
     * 活动详情
     * @return form-builder
     */
    public function dimension_info($id = '')
    {
        if (!$id || !($dimensionInfo = DimensionModel::get($id)))
            return $this->failed('评测维度不存在!');
        $userInfo = User::getUserInfos($dimensionInfo['uid']);
        if ($userInfo['spread_uid']) {
            $spread = User::where('uid', $userInfo['spread_uid'])->value('nickname');
        } else {
            $spread = '';
        }
        $this->assign(compact('dimensionInfo', 'userInfo', 'spread'));
        return $this->fetch();
    }

    /**
     * 显示创建活动单页.
     * @return \think\Response
     */
    public function create()
    {
        $f = [];
        $f[] = Form::select('cate_id', '分类名称')->setOptions(function () {
                $list = CategoryModel::getTierList(null, 0);
                $menus = [['value' => 0, 'label' => '顶级菜单','disabled'=> 'false']];
                foreach ($list as $menu) {
                    $menus[] = ['value' => $menu['id'], 'label' => $menu['html'] . $menu['cate_name']];
                }
                return $menus;
        })->filterable(1);
        $f[] = Form::input('name', '维度名称');
        $form = Form::make_post_form('添加维度', $f, Url::buildUrl('save'));
        $this->assign(compact('form'));
        return $this->fetch('public/form-builder');
    }

    /**
     * 显示编辑资源表单页.
     *
     * @param int $id
     * @return \think\Response
     */
    public function edit($id)
    {
        $c = DimensionModel::get($id);
        if (!$c) return Json::fail('数据不存在!');
        $field = [
            Form::select('cate_id', '分类名称', (string)$c->getData('cate_id'))->setOptions(function () use ($id) {
                $list = CategoryModel::getTierList(CategoryModel::where('id', '<>', $id), 0);
                $menus = [['value' => 0, 'label' => '顶级菜单', 'disabled'=> 'false']];
                foreach ($list as $menu) {
                    $menus[] = ['value' => $menu['id'], 'label' => $menu['html'] . $menu['cate_name']];
                }
                return $menus;
            })->filterable(1),
            Form::input('name', '分类名称', $c->getData('name'))
        ];
        $form = Form::make_post_form('编辑维度', $field, Url::buildUrl('update', array('id' => $id)), 2);

        $this->assign(compact('form'));
        return $this->fetch('public/form-builder');
    }


    /**
     * 保存新建的资源
     *
     * @param \think\Request $request
     * @return \think\Response
     */
    public function save(Request $request)
    {
        $data = Util::postMore([
            'cate_id',
            'name',
        ], $request);
        if ($data['cate_id'] == '') return Json::fail('请选择分类名称');
        if (!$data['name']) return Json::fail('请输入评测维度名称');
        $data['add_time'] = time();
        $data['status'] = 1;
        DimensionModel::create($data);
        return Json::successful('添加评测维度成功!');
    }

    /**
     * 保存更新的资源
     *
     * @param \think\Request $request
     * @param int $id
     * @return \think\Response
     */
    public function update(Request $request, $id)
    {
        $data = Util::postMore([
            'cate_id',
            'name',
        ], $request);
        if ($data['cate_id'] == '') return Json::fail('请选择分类名称');
        if (!$data['name']) return Json::fail('请输入评测维度名称');
        DimensionModel::edit($data, $id);
        return Json::successful('修改成功!');
    }
    
    /**
     * 删除指定资源
     *
     * @param int $id
     * @return \think\Response
     */
    public function destroy($id)
    {

        if (!$id) return $this->failed('数据不存在');
        if (!DimensionModel::be(['id' => $id])) return $this->failed('评测维度数据不存在');
        if (DimensionModel::be(['id' => $id, 'is_del' => 1])) {
            return Json::successful('评测维度数据已被删除!');
        } else {
            $res1 = false;
            if ($res1) {
                return Json::fail(DimensionModel::getErrorInfo('该评测维度已参加活动，无法删除!'));
            } else {
                $data['is_del'] = 1;
                $data['is_system_del'] = 1;
                $data['del_time'] = time();
                if (!DimensionModel::edit($data, $id))
                    return Json::fail('删除失败,请稍候再试!');
                else
                    return Json::successful('成功删除评测维度!');
            }
        }
    }

     /**
     * 获取商品列表
     * @return string
     * @throws \Exception
     */
    public function dimension_lists($id = 0)
    {
        $cate = CategoryModel::getTierList(null, 0);
        $this->assign('cate', $cate);
        $this->assign('id', (int)$id);
        return $this->fetch();
    }

}
