<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2020-12-11 17:12:01
 * @Last Modified time: 2022-04-20 16:43:22
 */
namespace app\admin\controller\evaluation;

use app\admin\controller\AuthController;
use crmeb\services\{
    JsonService,
    FormBuilder as Form,
    UtilService as Util,
    JsonService as Json
};
use think\Request;
use think\facade\Route as Url;
use app\admin\model\evaluation\StoreBanner as BannerModel;

/**
 * 首页配置-横幅处理
 * Class Home
 * @package app\admin\controller\evaluation
 */
class Home extends AuthController
{
    public function index()
    {
        return $this->fetch();
    }

    /**
     * 获取横幅列表
     * return json
     */
    public function banner_list()
    {
        $where = Util::getMore([
            ['page', 1],
            ['limit', 20],
        ]);
        return Json::successlayui(BannerModel::BannerList($where));
    }

    /**
     * 显示创建活动单页.
     * @return \think\Response
     */
    public function create($id = 0)
    {
        $f = [];
        $f[] = Form::input('title', '标题');
        $f[] = Form::frameImageOne('image', '横幅图片(640*640)', Url::buildUrl('widget.images/index', array('fodder' => 'image')))->icon('image')->width('100%')->height('500px');
        $f[] = Form::input('routine_url', '内部地址');
        $f[] = Form::input('mp_url', '外部地址');
        $f[] = Form::radio('position', '位置', 1)->options([['label' => '首页', 'value' => 1], ['label' => '心愿页', 'value' => 2]]);
        $f[] = Form::radio('is_show', '状态', 1)->options([['label' => '显示', 'value' => 1], ['label' => '隐藏', 'value' => 0]]);
        $f[] = Form::number('sort', '排序');
        $form = Form::make_post_form('添加横幅', $f, Url::buildUrl('save'));
        $this->assign(compact('form'));
        return $this->fetch('public/form-builder');
        return $this->fetch();
    }

    /**
     * 显示编辑资源表单页.
     *
     * @param int $id
     * @return \think\Response
     */
    public function edit($id)
    {
        $c = BannerModel::get($id);
        if (!$c) return Json::fail('数据不存在!');
        $field = [
            Form::input('title', '标题', $c->getData('title')),
            Form::frameImageOne('image', '横幅图片(640*640)', Url::buildUrl('widget.images/index', array('fodder' => 'image')), $c->getData('image'))->icon('image')->width('100%')->height('500px'),
            Form::input('routine_url', '内部地址', $c->getData('routine_url')),
            Form::input('mp_url', '外部地址', $c->getData('mp_url')),
            Form::radio('position', '位置', $c->getData('position'))->options([['label' => '首页', 'value' => 1], ['label' => '心愿页', 'value' => 2]]),
            Form::radio('is_show', '状态', $c->getData('is_show'))->options([['label' => '显示', 'value' => 1], ['label' => '隐藏', 'value' => 0]]),
            Form::number('sort', '排序',$c->getData('sort'))
        ];
        $form = Form::make_post_form('编辑横幅', $field, Url::buildUrl('update', array('id' => $id)), 2);

        $this->assign(compact('form'));
        return $this->fetch('public/form-builder');
    }

    /**
     * 保存新建的资源
     *
     * @param \think\Request $request
     * @return \think\Response
     */
    public function save(Request $request)
    {
        $data = Util::postMore([
            'title',
            'image',
            'position',
            'routine_url',
            'mp_url',
            'sort',
            'is_show',
        ], $request);
        if (!$data['title']) return Json::fail('请填写横幅标题');
        if (!$data['image']) return Json::fail('请填写横幅图片');
        if (!$data['routine_url'] && !$data['mp_url']) return Json::fail('跳转的内部和外部地址，必须填写一个');
        $data['status'] = 1;
        $data['add_time'] = time();
        $data['updated_time'] = time();
        BannerModel::create($data);
        return Json::successful('添加横幅成功!');
    }

    /**
     * 保存更新的资源
     *
     * @param \think\Request $request
     * @param int $id
     * @return \think\Response
     */
    public function update(Request $request, $id)
    {
        $data = Util::postMore([
            'title',
            'image',
            'position',
            'routine_url',
            'mp_url',
            'sort',
            'is_show',
        ], $request);
        if (!$data['title']) return Json::fail('请填写横幅标题');
        if (!$data['image']) return Json::fail('请填写横幅图片');
        if (!$data['routine_url'] && !$data['mp_url']) return Json::fail('跳转的内部和外部地址，必须填写一个');
        $data['updated_time'] = time();
        BannerModel::edit($data, $id);
        return Json::successful('修改成功!');
    }

    /**
     * 删除指定资源
     *
     * @param int $id
     * @return \think\Response
     */
    public function destroy($id)
    {
        if (!$id) return $this->failed('数据不存在');
        if (!BannerModel::be(['id' => $id])) return $this->failed('横幅数据不存在');
        if (BannerModel::be(['id' => $id, 'is_system_del' => 1])) {
            return Json::successful('横幅数据已被删除!');
        } else {
            $data['is_system_del'] = 1;
            $data['del_time'] = time();
            if (!BannerModel::edit($data, $id)){
                return Json::fail('删除失败,请稍候再试!');
            }else{
                return Json::successful('成功删除横幅!');
            }
        }
    }

    /**
     * 快速编辑
     * @param string $field
     * @param string $id
     * @param string $value
     */
    public function set_field($field = '', $id = '', $value = '')
    {
        $field == '' || $id == '' || $value == '' && Json::fail('缺少参数');
        if (BannerModel::where('id', $id)->update([$field => $value]))
            return Json::successful('保存成功');
        else
            return Json::fail('保存失败');
    }

    /**
     * 设置资源上架|下架
     * @param string $is_show
     * @param string $id
     */
    public function set_show($is_show = '', $id = '')
    {
        ($is_show == '' || $id == '') && Json::fail('缺少参数');
        if (BannerModel::setBannerShow($id, (int)$is_show)) {
            return Json::successful($is_show == 1 ? '显示成功' : '隐藏成功');
        } else {
            return Json::fail(BannerModel::getErrorInfo($is_show == 1 ? '显示失败' : '隐藏失败'));
        }
    }
}