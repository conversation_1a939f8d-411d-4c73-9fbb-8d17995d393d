<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2020-12-09 13:57:22
 * @Last Modified time: 2021-01-27 10:45:27
 */
namespace app\admin\controller\evaluation;

use app\admin\controller\AuthController;
use think\Request;
use think\facade\Route as Url;
use app\admin\model\system\SystemAttachment;
use app\admin\model\evaluation\{
    StoreWish as WishModel,
    StoreActivity as ActivityModel,
    StoreActivityReviews as ActivityReviewsModel
};
use app\admin\model\store\{
    StoreProduct as ProductModel,
};
use app\admin\model\store\StoreCategory;
use crmeb\services\{FormBuilder as Form, JsonService as Json, UtilService as Util};

/**
 * 评测看版-心愿
 * Class Wish
 * @package app\admin\controller\evaluation
 */
class Wish extends AuthController
{
    /**
     * 显示资源列表
     *
     * @return \think\Response
     */
    public function index()
    {
        $this->assign([
            'year' => get_month(),
            'status' => $this->request->param('status', ''),
            'wishCount' => WishModel::WishCount(),
        ]);
        return $this->fetch();
    }

    /**
     * 活动获取评测商品列表
     * @return string
     * @throws \Exception
     */
    public function productList()
    {
        $cate = StoreCategory::getTierList(null, 1);
        $this->assign('cate', $cate);
        return $this->fetch();
    }

    /**
     * 获取评测列表
     * return json
     */
    public function wish_list()
    {
        $where = Util::getMore([
            ['status', ''],
            ['data', ''],
            ['keywords', $this->request->param('keywords', '')], //关键字
            ['page', 1],
            ['limit', 20],
        ]);
        return Json::successlayui(WishModel::WishList($where));
    }

    /**
     * 显示创建评测单页.
     * @return \think\Response
     */
    public function create($id = 0)
    {
        $f = [];
        // $f[] = Form::hidden('uid', '');
        // $f[] = Form::frameImageOne('avatar', '用户信息', Url::buildUrl('admin/user.user/userListes', array('fodder' => 'avatar')))->icon('plus')->width('100%')->height('500px');
        $f[] = Form::input('name', '名称');
        $f[] = Form::input('position', '位置');
        $f[] = Form::input('date', '时间');
        $f[] = Form::textarea('desc', '介绍');
        $f[] =  Form::frameImages('images', '心愿介绍图(640*640px)', Url::buildUrl('widget.images/index', array('fodder' => 'images')))->maxLength(5)->icon('images')->width('100%')->height('500px');
        $form = Form::make_post_form('添加心愿', $f, Url::buildUrl('save'));
        $this->assign(compact('form'));
        return $this->fetch('public/form-builder');
    }
     /**
     * 保存新建的资源
     *
     *
     */
    public function save()
    {
        $data = Util::postMore([
            // 'uid', //用户id
            'name', //用户id
            'position',
            'date',
            'desc',
            'images',
        ]);
        //设置基础信息
        if (!$data['name']) return Json::fail('请填写想测项目名称');
        if (!$data['position']) return Json::fail('请填写地址');
        if (!$data['date']) return Json::fail('请填写时间');
        $data['image'] = json_encode($data['images']);
        $data['status'] = 2;
        $data['type'] = 2;
        $data['add_time'] = time();
        WishModel::beginTrans();
        $res = WishModel::create($data);
        if ($res) {
            WishModel::commitTrans();
            return Json::success('添加心愿成功!');
        }else{
            WishModel::rollbackTrans();
            return Json::fail('添加心愿失败');
        }
    }


    /**
     * 显示编辑资源表单页.
     *
     * @param int $id
     * @return \think\Response
     */
    public function edit($id)
    {
        $c = WishModel::get($id);
        if (!$c) return Json::fail('数据不存在!');
        $field = [
            Form::input('name', '项目名称', $c->getData('name')),
            Form::input('position', '位置', $c->getData('position')),
            Form::input('date', '时间', $c->getData('date')),
            Form::input('desc', '简介', $c->getData('desc')),
            Form::input('date', '分类名称', $c->getData('date')),
            Form::frameImages('images', '产品轮播图(640*640px)', Url::buildUrl('widget.images/index', array('fodder' => 'slider_image')), json_decode($c->getData('image'), 1) ?: [])->maxLength(5)->icon('images')->width('100%')->height('500px'),
        ];
        $form = Form::make_post_form('编辑心愿', $field, Url::buildUrl('update', array('id' => $id)), 2);

        $this->assign(compact('form'));
        return $this->fetch('public/form-builder');
    }


    /**
     * 保存更新的资源
     *
     * @param \think\Request $request
     * @param int $id
     * @return \think\Response
     */
    public function update(Request $request, $id)
    {
        $data = Util::postMore([
            'name', //用户id
            'position',
            'date',
            'desc',
            'images'
        ], $request);
        if (!$data['name']) return Json::fail('请填写想测项目名称');
        if (!$data['position']) return Json::fail('请填写地址');
        if (!$data['date']) return Json::fail('请填写时间');
        $data['image'] = json_encode($data['images']);
        WishModel::edit($data, $id);
        return Json::successful('修改成功!');
    }

    /**
     * 显示编辑资源表单页.
     *
     * @param int $id
     * @return \think\Response
     */
    public function set_status($id = 0,$type = 1)
    {
        $c = WishModel::get($id);
        $image =  $c->product_id != 0 ? ProductModel::where('id',$c->product_id)->value('image')  : '';
        if (!$c) return Json::fail('数据不存在!');
        $field = $type == 1 ? [
            Form::hidden('type', 1),
            Form::input('name', '心愿名称', $c->getData('name')),
            Form::input('product_id','产品ID', $c->getData('product_id'))->disabled(true),
            Form::frameImageOne('product', '关联的产品', Url::buildUrl('productList', array('fodder' => 'product')), $image )->icon('image')->width('100%')->height('500px'),
            Form::radio('is_official_comment', '是否官评', $c->getData('is_official_comment'))->options([['label' => '是', 'value' => 1],['label' => '否', 'value' => 0]]),
        ] : 
        [
            Form::hidden('type', 2),
            Form::input('name', '心愿名称', $c->getData('name')),
            Form::input('product_id','产品ID', $c->getData('product_id'))->disabled(true),
            Form::frameImageOne('product', '关联的产品', Url::buildUrl('productList', array('fodder' => 'image')), $image )->icon('image')->width('100%')->height('500px'),
            Form::radio('is_official_comment', '是否官评', $c->getData('is_official_comment'))->options([['label' => '是', 'value' => 1],['label' => '否', 'value' => 0]]),
            Form::radio('status', '是否达成', $c->getData('status'))->options([['label' => '是', 'value' => 1],['label' => '否', 'value' => 0]]),
        ];

        $form = Form::make_post_form('编辑心愿状态', $field, Url::buildUrl('save_set_status', array('id' => $id)), 2);
        $this->assign(compact('form'));
        return $this->fetch('public/form-builder');
    }

    public function wish_info($wid = '')
    {
        if (!$wid || !($wishInfo = WishModel::get($wid)))
            return $this->failed('心愿不存在!');
        $images = is_string($wishInfo['image']) ? json_decode($wishInfo['image'], true) : [];
        $this->assign(compact('wishInfo','images'));
        return $this->fetch();
    }

    /**
     * 设置分组
     * @param int $uid
     */
    public function save_set_status(Request $request, $id)
    {
        $data = Util::postMore([
            'is_official_comment',
            'product_id',
            'type',
            'status',
        ], $request);
        if ($data['type'] == 1) unset($data['status']);
        if ($data['type'] == 1 && $data['is_official_comment'] == 1) $data['status'] = 4;
        if ($data['type'] == 2 && $data['status'] == 1) $data['is_located'] = 1;
        if (!$data['product_id']) return Json::fail('请选择关联的产品ID');
        WishModel::edit($data, $id);
        return Json::successful('修改成功!');
    }

    /**
     * 审核指定资源
     * @param $id
     */
    public function review($id)
    {
        WishModel::beginTrans();
        $wishInfo = WishModel::where('id', $id)->find();
        if (!$wishInfo) return Json::fail('心愿不存在！');
        if ($wishInfo['is_del'] == 1) return Json::fail('心愿不存在！');
            $wishInfo->status = 3;
        if ($wishInfo->save()) {
            WishModel::commitTrans();
            return Json::successful('审核成功！');
        } else {
            WishModel::rollbackTrans();
            return Json::fail('审核失败');
        }
    }

    /**
     * 删除指定资源
     *
     * @param int $id
     * @return \think\Response
     */
    public function destroy($id)
    {   
        $data['status'] = 3;
        $data['is_del'] = 1;
        $data['del_time'] = time();
        if (!WishModel::edit($data,$id))
            return Json::fail(WishModel::getErrorInfo('删除失败,请稍候再试!'));
        else
            return Json::successful('删除成功!');
    }
}