<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2020-09-03 13:19:17
 * @Last Modified time: 2020-12-28 13:45:05
 */
namespace app\admin\controller\evaluation;

use app\admin\controller\AuthController;
use crmeb\services\{
    JsonService,
    FormBuilder as Form,
    UtilService as Util,
    JsonService as Json
};
use think\Request;
use think\facade\Route as Url;
use app\admin\model\evaluation\StoreKanban as KanbanModel;
use app\admin\model\evaluation\StoreOfficial as OfficialModel;

/**
 * 官方评测
 * Class Official
 * @package app\admin\controller\evaluation
 */
class Official extends AuthController
{
    public function index()
    {
        $this->assign('kanban', KanbanModel::KanbanList(['status'=>1,'page'=>0,'limit'=>99999], 0)['data']);
        return $this->fetch();
    }

    /**
     * 获取官评列表
     * return json
     */
    public function article_list()
    {
        $where = Util::getMore([
            ['evaluation_id',0], //活动分类id
            ['theme_name', $this->request->param('theme_name', '')], //评测主题名
            ['page', 1],
            ['limit', 20],
        ]);
        return Json::successlayui(OfficialModel::ArticleList($where));
    }


    /**
     * 显示创建活动单页.
     * @return \think\Response
     */
    public function create($id = 0)
    {
        $f = [];
        $f[] = Form::select('evaluation_id', '所属看板')->setOptions(function () {
                $list = KanbanModel::getTierList(null, 0);
                $menus = [['value' => 0, 'label' => '顶级菜单','disabled'=> 'false']];
                foreach ($list as $menu) {
                    $menus[] = ['value' => $menu['id'], 'label' => $menu['main_title'].$menu['sub_title']];
                }
                return $menus;
        })->filterable(1);
        $f[] = Form::input('official_score', '官方评分');
        $f[] = Form::input('routine_url', '内部地址');
        $f[] = Form::input('mp_url', '外部地址');
        $form = Form::make_post_form('添加官评', $f, Url::buildUrl('save'));
        $this->assign(compact('form'));
        return $this->fetch('public/form-builder');
        return $this->fetch();
    }

    /**
     * 显示编辑资源表单页.
     *
     * @param int $id
     * @return \think\Response
     */
    public function edit($id)
    {
        $c = OfficialModel::get($id);
        if (!$c) return Json::fail('数据不存在!');
        $field = [
            Form::select('evaluation_id', '所属看板', (string)$c->getData('evaluation_id'))->setOptions(function () use ($id) {
                $list = KanbanModel::getTierList(KanbanModel::where('id', '<>', $id), 0);
                $menus = [['value' => 0, 'label' => '顶级菜单']];
                foreach ($list as $menu) {
                    $menus[] = ['value' => $menu['id'], 'label' => $menu['main_title'].$menu['sub_title']];
                }
                return $menus;
            })->filterable(1),
            Form::input('official_score', '官方评分', $c->getData('official_score')),
            Form::input('routine_url', '内部地址', $c->getData('routine_url')),
            Form::input('mp_url', '外部地址', $c->getData('mp_url')),
        ];
        $form = Form::make_post_form('编辑官评', $field, Url::buildUrl('update', array('id' => $id)), 2);

        $this->assign(compact('form'));
        return $this->fetch('public/form-builder');
    }

    /**
     * 保存新建的资源
     *
     * @param \think\Request $request
     * @return \think\Response
     */
    public function save(Request $request)
    {
        $data = Util::postMore([
            'routine_url',
            'mp_url',
            ['evaluation_id', 0],
            ['official_score', 0], //官方评分
        ], $request);
        if ($data['evaluation_id'] == 0) return Json::fail('请选择所属看板');
        if ($data['official_score'] == 0) return Json::fail('请填写官方评测分');
        $data['status'] = 1;
        $data['is_show'] = 1;
        $data['add_time'] = time();
        $data['updated_time'] = time();
        OfficialModel::create($data);
        return Json::successful('添加官评成功!');
    }

    /**
     * 保存更新的资源
     *
     * @param \think\Request $request
     * @param int $id
     * @return \think\Response
     */
    public function update(Request $request, $id)
    {
        $data = Util::postMore([
            'routine_url',
            'mp_url',
            ['evaluation_id', 0],
            ['official_score', 0], //官方评分
        ], $request);
        if ($data['evaluation_id'] == 0) return Json::fail('请选择所属看板');
        if ($data['official_score'] == 0) return Json::fail('请填写官方评测分');
        $data['updated_time'] = time();
        OfficialModel::edit($data, $id);
        return Json::successful('修改成功!');
    }

    /**
     * 删除指定资源
     *
     * @param int $id
     * @return \think\Response
     */
    public function destroy($id)
    {
        if (!$id) return $this->failed('数据不存在');
        if (!OfficialModel::be(['id' => $id])) return $this->failed('官评数据不存在');
        if (OfficialModel::be(['id' => $id, 'is_del' => 1])) {
            return Json::successful('官评数据已被删除!');
        } else {
            $data['is_del'] = 1;
            $data['is_system_del'] = 1;
            $data['del_time'] = time();
            if (!OfficialModel::edit($data, $id)){
                return Json::fail('删除失败,请稍候再试!');
            }else{
                return Json::successful('成功删除官评!');
            }
        }
    }
}