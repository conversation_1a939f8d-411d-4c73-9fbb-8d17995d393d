<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2020-12-14 11:25:15
 * @Last Modified time: 2020-12-28 15:39:02
 */
namespace app\admin\controller\evaluation;

use app\admin\controller\AuthController;
use think\Request;
use think\facade\Route as Url;
use app\admin\model\evaluation\StoreLabelCate as LabelCate;
use app\admin\model\evaluation\StoreLabelData as LabelModel;
use app\admin\model\evaluation\StoreLabelCategory as CategoryModel;
use crmeb\services\{FormBuilder as Form, JsonService as Json, UtilService as Util};
/**
 * 维度标签控制器
 * Class LabelData
 * @package app\admin\controller\system
 */
class LabelData extends AuthController
{
    /**
     * 显示资源列表
     *
     * @return \think\Response
     */
    public function index()
    {
    	$this->assign([
            'year' => get_month(),
            'status' => $this->request->param('status', ''),
            'LabelCount' => LabelModel::LabelCount(),
        ]);
        return $this->fetch();
    }

    /*
     *  异步获取分类列表
     *  @return json
     */
    public function label_list()
    {
        $where = Util::getMore([
            ['status', 0],
            ['type', 0],
            ['keywords', ''],
            ['page', 1],
            ['limit', 20],
        ]);
        return Json::successlayui(LabelModel::LabelList($where));
    }

    /**
     * 快速编辑
     * @param string $field
     * @param string $id
     * @param string $value
     */
    public function set_label($field = '', $id = '', $value = '')
    {
        $field == '' || $id == '' || $value == '' && Json::fail('缺少参数');
        if (CategoryModel::where('id', $id)->update([$field => $value]))
            return Json::successful('保存成功');
        else
            return Json::fail('保存失败');
    }

    /**
     * 显示创建资源表单页.
     *
     * @return \think\Response
     */
    public function create()
    {
        $field = [
            Form::input('name', '标签名'),
            Form::checkbox('cate_id', '标签分类')->setOptions(function () {
                $list = CategoryModel::getTierList(null, 0);
                foreach ($list as $menu) {
                    $menus[] = ['value' => $menu['id'], 'label' => $menu['title']];
                }
                return $menus;
            }),
        ];
        $form = Form::make_post_form('添加标签', $field, Url::buildUrl('save'), 2);
        $this->assign(compact('form'));
        return $this->fetch('public/form-builder');
    }

    /**
     * 保存新建的资源
     *
     * @param \think\Request $request
     * @return \think\Response
     */
    public function save(Request $request)
    {
    	LabelModel::beginTrans();
        $data = Util::postMore([
            'name',
            'cate_id',
        ], $request);	
        if (!$data['name']) return Json::fail('请输入标签名称');
        if (is_null($data['cate_id'])) return Json::fail('请选择标签分类');
        if (LabelModel::be(['name'=>$data['name'],'status'=>1,'type'=>1])) return Json::fail('此标签名已存在，请换个标签名'); //标签名名是否重复
       	$cate_id = $data['cate_id'] != [] ? $data['cate_id'] : [];
        $data['cate_id'] = !is_null($data['cate_id']) ? implode(',',  $data['cate_id']) : '';
        $data['type'] = 1;
        $data['status'] = 1;
        $data['add_time'] = time();
        $res = LabelModel::create($data);
        $cateData = [];
        foreach ($cate_id as $cid) {
            $cateData[] = ['label_id' => $res['id'], 'cate_id' => $cid, 'add_time' => time()];
        }
        $cate_res =  LabelCate::insertAll($cateData);
        if ($res && $cate_res) {
			LabelModel::commitTrans();
			return Json::successful('添加标签成功!');
        }else{
			LabelModel::rollbackTrans();
			return Json::successful('添加标签失败!');
        }
    }

    /**
     * 显示编辑资源表单页.
     *
     * @param int $id
     * @return \think\Response
     */
    public function edit($id)
    {
        $c = LabelModel::get($id);
        if (!$c) return Json::fail('数据不存在!');
        $cate_id = [];
        $cate_id_arr = LabelCate::field('cate_id')->where('label_id', $id)->select()->toArray();
        if (count($cate_id_arr)) {
            $cate_id = array_column($cate_id_arr, 'cate_id');
        }
        $list = CategoryModel::getTierList(null, 0);
        $field = [
           Form::input('name', '标签名称', $c->getData('name')),
           Form::checkbox('cate_id', '标签分类',$cate_id)->setOptions(function () {
                $list = CategoryModel::getTierList(null, 0);
                foreach ($list as $menu) {
                    $menus[] = ['value' => $menu['id'], 'label' => $menu['title']];
                }
                return $menus;
            })
        ];
        $form = Form::make_post_form('编辑标签分类', $field, Url::buildUrl('update', array('id' => $id)), 2);
        $this->assign(compact('form'));
        return $this->fetch('public/form-builder');
    }

    /**
     * 保存更新的资源
     *
     * @param \think\Request $request
     * @param int $id
     * @return \think\Response
     */
    public function update(Request $request, $id)
    {
    	LabelModel::beginTrans();
        $data = Util::postMore([
            'name',
            'cate_id',
        ], $request);
        $label = LabelModel::get($id);
        if (!$data['name']) return Json::fail('请输入标签名称');
        if (is_null($data['cate_id'])) return Json::fail('请选择标签分类');
        if (LabelModel::be(['name'=>$data['name'],'status'=>1,'type'=>1]) && $label->name != $data['name']) return Json::fail('此标签名已存在，请换个标签名'); //标签名是否重复
        $cate_id = $data['cate_id'];
        $data['cate_id'] = !is_null($data['cate_id']) ? implode(',',  $data['cate_id']) : '';
        $res = LabelModel::edit($data, $id);
        $cate_res = LabelCate::where('label_id', $id)->delete();
       if ($res && $cate_res) {
       	    foreach ($cate_id as $cid) {
	            LabelCate::insert(['label_id' => $id, 'cate_id' => $cid, 'add_time' => time()]);
	        }
			LabelModel::commitTrans();
			return Json::successful('修改标签成功!');
        }else{
			LabelModel::rollbackTrans();
			return Json::successful('修改标签失败!');
        }
    }

    /**
     * 删除指定资源
     *
     * @param int $id
     * @return \think\Response
     */
    public function delete($id)
    {
        if (!LabelModel::delLabel($id))
            return Json::fail(LabelModel::getErrorInfo('删除失败,请稍候再试!'));
        else
            return Json::successful('删除成功!');
    }
}
