sae<?php
/*
	看控制起
 */

namespace app\admin\controller\evaluation;

use app\admin\controller\AuthController;
use crmeb\services\{
    JsonService,
    FormBuilder as Form,
    UtilService as Util,
    JsonService as Json
};
use crmeb\basic\BaseModel;
use app\admin\model\user\User;
use think\facade\Route as Url;
use app\admin\model\evaluation\StoreKanban as KanbanModel;
use app\admin\model\evaluation\StoreCategory as CategoryModel;
use app\admin\model\evaluation\StoreDimension as DimensionModel;
use app\admin\model\store\{StoreCategory,
    StoreDescription,
    StoreProductAttr,
    StoreProductAttrResult,
    StoreProduct as ProductModel,
    StoreProductAttrValue};
/**
 * 评测看板
 * Class Kanban
 * @package app\admin\controller\evaluation
 */
class Kanban extends AuthController
{
    public function index()
    {
        $this->assign([
            'year' => get_month(),
            'status' => $this->request->param('status', ''),
            'KanbanCount' => json_encode(['name']),
            'evaluationCategory'=>json_encode(CategoryModel::getTierList(null, 0)),
        ]);
        return $this->fetch();
    }

    /**
     * 获取看列表
     * return json
     */
    public function kanban_list()
    {
        $where = Util::getMore([
            ['status', ''],
            ['data', ''],
            ['cate_id', []], //看分类
            ['founder', $this->request->param('founder', '')], //创建人
            ['subject_name', $this->request->param('subject_name', '')], //主题名称
            ['page', 1],
            ['limit', 20],
        ]);
        return Json::successlayui(KanbanModel::KanbanList($where));
    }

    /**
     * 活动详情
     * @return form-builder
     */
    public function kanban_info($id = '')
    {
        if (!$id || !($kanbanInfo = KanbanModel::get($id)))
            return $this->failed('看不存在!');
        $userInfo = User::getUserInfos($kanbanInfo['uid']);
        if ($userInfo['spread_uid']) {
            $spread = User::where('uid', $userInfo['spread_uid'])->value('nickname');
        } else {
            $spread = '';
        }
        $this->assign(compact('kanbanInfo', 'userInfo', 'spread'));
        return $this->fetch();
    }

    /**
     * 显示创建活动单页.
     * @return \think\Response
     */
    public function create($id = 0)
    {
        $this->assign('id', (int)$id);
        return $this->fetch();
    }

    /**
     * 获取看详细信息
     * @param int $id
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function get_kanban_info($id = 0)
    {
        $list = CategoryModel::getTierList(null, 1);
        $menus = [];
        $storelists = [];
        foreach ($list as $menu) {
            $menus[] = ['value' => $menu['id'], 'label' => $menu['html'] . $menu['cate_name'], 'disabled' => $menu['pid'] == 0 ? 1 : 1];//,'disabled'=>$menu['pid']== 0];
        }
        $data['cateList'] = $menus;
        $data['kanbanInfo'] = [];
        if ($id) {
            $kanbanInfo = KanbanModel::get($id);
            if (!$kanbanInfo) {
                return Json::fail('修改的看不存在');
            }
            $kanbanInfo['cate_id'] = explode(',', $kanbanInfo['cate_id']);
            $kanbanInfo['product_id'] = explode(',', $kanbanInfo['product_id']);
            $kanbanInfo['dimension_id'] = explode(',', $kanbanInfo['dimension_id']);
            $kanbanInfo['slider_image'] = is_string($kanbanInfo['slider_image']) ? json_decode($kanbanInfo['slider_image'], true) : [];
            //创建人信息
            $kanbanInfo['avatar'] = User::where('uid', $kanbanInfo['uid'])->value('avatar');
            //关联商品
            $product_image=[];
            if (is_array($kanbanInfo['product_id'])) {
                foreach ($kanbanInfo['product_id'] as $key => $id) {
                    $product_image[] =  StoreProductAttrValue::where('product_id', $id)->value('image');
                }
            }
             $kanbanInfo['product_image'] = count($product_image) >= 1 ? $product_image : [];
            //维度相关
            $dimensionName = [];
            if (is_array($kanbanInfo['dimension_id'])) {
                foreach ($kanbanInfo['dimension_id'] as $key => $id) {
                    $dimensionName[] =  DimensionModel::where('id', $id)->value('name');
                }
                $dimensionNameObeject =  DimensionModel::where('id','In', $kanbanInfo['dimension_id'])->select()->toArray();
                foreach ($dimensionNameObeject as $key=>$value) {
                    $dimensionNameObeject[$key]['dimension'] = 100/count($dimensionNameObeject);
                }
            }
            $kanbanInfo['dimensionName'] = count($dimensionName) >= 1 ? $dimensionName : [];
            $kanbanInfo['dimension'] = $dimensionNameObeject;
            $kanbanInfo['dimensionTotal'] = count($dimensionName) >= 1 ? 100 : 100/count($dimensionNameObeject);
            $data['kanbanInfo'] = $kanbanInfo;
        }
        return JsonService::successful($data);
    }



    /**
     * 保存新建的资源
     *
     *
     */
    public function save($id)
    {
        $data = Util::postMore([
            ['cate_id', []], //看分类
            'main_title',
            'sub_title',
            ['image', []],
            ['slider_image', []],
            'description',
            ['product_id', []], //关联商品id
            ['dimension_id', []], //关联评测维度id
        ]);
        //设置基础信息
        if (count($data['cate_id']) < 1) return Json::fail('请选择看分类');
        if (!$data['main_title']) return Json::fail('请填写看主标题');
        if (!$data['sub_title']) return Json::fail('请填写看主标题');
        if (!$data['description']) return Json::fail('请填写看简介');
        if (count($data['image']) < 1) return Json::fail('请上传看封面图');
        if (count($data['slider_image']) < 1) return Json::fail('请上传看导览图');
        if (count($data['product_id']) < 1) return Json::fail('请选择关联商品');
        if (count($data['dimension_id']) < 1) return Json::fail('请选择关联评测维度');
        $cate_id = $data['cate_id'];
        $data['cate_id'] = implode(',', $data['cate_id']);
        $product_id = $data['product_id'];
        $data['product_id'] = implode(',', $data['product_id']);
        $dimension_id = $data['dimension_id'];
        $data['dimension_id'] = implode(',', $data['dimension_id']);
        $data['image'] = $data['image'][0];
        $data['slider_image'] = json_encode($data['slider_image']);
        KanbanModel::beginTrans();
        if ($id) {
            $res = KanbanModel::edit($data, $id);
            if ($res) {
                KanbanModel::commitTrans();
                return Json::success('修改看成功!');
            }else{
                KanbanModel::rollbackTrans();
                return Json::fail('修改看失败');
            }
        }else{
            //新增
            $data['add_time'] = time();
            $data['status'] = 1;
            $res = KanbanModel::create($data);
            if ($res) {
                KanbanModel::commitTrans();
                return Json::success('添加看成功!');
            }else{
                KanbanModel::rollbackTrans();
                return Json::fail('添加看失败');
            }
        }
    }


    /**
     * 删除指定资源
     *
     * @param int $id
     * @return \think\Response
     */
    public function destroy($id)
    {

        if (!$id) return $this->failed('数据不存在');
        if (!KanbanModel::be(['id' => $id])) return $this->failed('看数据不存在');
        if (KanbanModel::be(['id' => $id, 'is_del' => 1])) {
            return Json::successful('看数据已被删除!');
        } else {
            $res1 = false;
            if ($res1) {
                return Json::fail(KanbanModel::getErrorInfo('该看已参加活动，无法删除!'));
            } else {
                $data['is_del'] = 1;
                $data['is_system_del'] = 1;
                $data['del_time'] = time();
                if (!KanbanModel::edit($data, $id))
                    return Json::fail('删除失败,请稍候再试!');
                else
                    return Json::successful('成功删除看!');
            }
        }
    }

    /**
     * 审核状态
     * @param $id
     */
    public function review($id)
    {
        KanbanModel::beginTrans();
        $kanbanInfo = KanbanModel::where('id', $id)->find();
        if (!$kanbanInfo) return Json::fail('审核看不存在！');
        $kanbanInfo->status = 0;
        if ($kanbanInfo->save()) {
            KanbanModel::commitTrans();
            return Json::successful('审核成功！');
        } else {
            KanbanModel::rollbackTrans();
            return Json::fail('审核失败');
        }
    }

    /**
     * 发布看
     * @param $id
     */
    public function release()
    {
        $post = Util::postMore([
            ['ids', []]
        ]);
        if (empty($post['ids'])) {
            return Json::fail('请选择需要发布的看');
        } else {
            $res = KanbanModel::where('id', 'in', $post['ids'])->update(['status' => 1]);
            if ($res)
                return Json::successful('发布成功');
            else
                return Json::fail('发布失败');
        }
    }
}
