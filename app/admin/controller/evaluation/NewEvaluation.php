<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2020-12-14 18:45:59
 * @Last Modified time: 2021-07-13 15:45:19
 */
namespace app\admin\controller\evaluation;

use app\admin\controller\AuthController;
use crmeb\services\{
    JsonService,
    FormBuilder as Form,
    UtilService as Util,
    JsonService as Json
};
use app\admin\model\store\{
    StoreProduct as ProductModel,
};
use think\Request;
use think\facade\Route as Url;
use app\admin\model\store\StoreCategory;
use app\admin\model\evaluation\StoreNewEvaluation as NewEvaluationModel;

/**
 * 新评推荐
 * Class NewEvaluation
 * @package app\admin\controller\evaluation
 */
class NewEvaluation extends AuthController
{
    public function index()
    {
        return $this->fetch();
    }

    /**
     * 获取测评推表
     * return json
     */
    public function new_evaluation_list()
    {
        $where = Util::getMore([
            ['page', 1],
            ['limit', 100],
        ]);
        return Json::successlayui(NewEvaluationModel::EvaluationsList($where));
    }

    /**
     * 活动获取评测商品列表
     * @return string
     * @throws \Exception
     */
    public function productList()
    {
        $cate = StoreCategory::getTierList(null, 1);
        $this->assign('cate', $cate);
        return $this->fetch();
    }

    /**
     * 显示创建活动单页.
     * @return \think\Response
     */
    public function create($id = 0)
    {
        $f = [];
        $f[] = Form::input('title', '标题');
        $f[] = Form::checkbox('is_new_title', '勾选使用新标题，否则用产品自身标题')->options([['label' => '', 'value' => 1]]);
        $f[] = Form::frameImageOne('product', '关联测评产品', Url::buildUrl('productList', array('fodder' => 'product')))->icon('plus')->width('100%')->height('500px');
        $f[] = Form::frameImageOne('image', '图片(360*130)', Url::buildUrl('widget.images/index', array('fodder' => 'image')))->icon('image')->width('100%')->height('500px');
        $f[] = Form::checkbox('is_new_image', '勾选使用新配图，否则用产品自身图片')->options([['label' => '', 'value' => 1]]);
        $f[] = Form::input('mp_url', '外部地址');
        $f[] = Form::radio('is_show', '状态', 1)->options([['label' => '显示', 'value' => 1], ['label' => '隐藏', 'value' => 0]]);
        $f[] = Form::number('sort', '排序');
        $f[] = Form::hidden('related_id', '');
        $form = Form::make_post_form('添加测评推荐', $f, Url::buildUrl('save'));
        $this->assign(compact('form'));
        return $this->fetch('public/form-builder');
        return $this->fetch();
    }

    /**
     * 显示编辑资源表单页.
     *
     * @param int $id
     * @return \think\Response
     */
    public function edit($id)
    {
        $c = NewEvaluationModel::get($id);
        if (!$c) return Json::fail('数据不存在!');
        $image =  $c->related_id != 0 ? ProductModel::where('id',$c->related_id)->value('image')  : '';
        $field = [
            Form::input('title', '标题', $c->getData('title')),
            Form::checkbox('is_new_title', '勾选使用新标题，否则用产品自身标题',[$c->getData('is_new_title')])->options([['label' => '', 'value' => 1]]),
            Form::frameImageOne('product', '关联测评产品', Url::buildUrl('widget.images/index', array('fodder' => 'image')), $image)->icon('image')->width('100%')->height('500px'),
            Form::frameImageOne('image', '图片(360*130)', Url::buildUrl('widget.images/index', array('fodder' => 'image')), $c->getData('image'))->icon('image')->width('100%')->height('500px'),
            Form::checkbox('is_new_image', '勾选使用新配图，否则用产品自身图片',[$c->getData('is_new_image')])->options([['label' => '', 'value' => 1]]),
            Form::input('mp_url', '外部地址', $c->getData('mp_url')),
            Form::radio('is_show', '状态', $c->getData('is_show'))->options([['label' => '显示', 'value' => 1], ['label' => '隐藏', 'value' => 0]]),
            Form::number('sort', '排序',$c->getData('sort')),
            Form::hidden('related_id', $c->getData('related_id'))
        ];
        $form = Form::make_post_form('编辑测评推荐', $field, Url::buildUrl('update', array('id' => $id)), 2);

        $this->assign(compact('form'));
        return $this->fetch('public/form-builder');
    }

    /**
     * 保存新建的资源
     *
     * @param \think\Request $request
     * @return \think\Response
     */
    public function save(Request $request)
    {
        $data = Util::postMore([
            'title',
            'related_id',
            'image',
            'mp_url',
            'sort',
            'is_new_title',
            'is_new_image',
            'is_show',
        ], $request);
        if (!$data['title']) return Json::fail('请填写测评推荐标题');
        if (!$data['related_id']) return Json::fail('请选择测评推荐关联的产品ID');
        if (!$data['mp_url']) return Json::fail('请填写外部跳转的地址');
        $data['status'] = 1;
        $data['add_time'] = time();
        $data['updated_time'] = time();
        $data['is_new_title'] = isset($data['is_new_title']) && !isset($data['is_new_title']['label']) ? $data['is_new_title'][0] : 0;
        $data['is_new_image'] = isset($data['is_new_image']) && !isset($data['is_new_image']['label'])  ? $data['is_new_image'][0] : 0;
        NewEvaluationModel::create($data);
        return Json::successful('添加新评推荐成功!');
    }

    /**
     * 保存更新的资源
     *
     * @param \think\Request $request
     * @param int $id
     * @return \think\Response
     */
    public function update(Request $request, $id)
    {
        $data = Util::postMore([
            'title',
            'related_id',
            'image',
            'mp_url',
            'sort',
            'is_new_title',
            'is_new_image',
            'is_show',
        ], $request);
        if (!$data['title']) return Json::fail('请填写测评推荐标题');
        if (!$data['related_id']) return Json::fail('请选择测评推荐关联的产品ID');
        if (!$data['mp_url']) return Json::fail('请填写外部跳转的地址');
        $data['is_new_title'] = isset($data['is_new_title']) && $data['is_new_title'] !="" ? $data['is_new_title'][0] : 0;
        $data['is_new_image'] = isset($data['is_new_image']) && $data['is_new_image'] !=""  ? $data['is_new_image'][0] : 0;
        $data['updated_time'] = time();
        NewEvaluationModel::edit($data, $id);
        return Json::successful('修改成功!');
    }

    /**
     * 删除指定资源
     *
     * @param int $id
     * @return \think\Response
     */
    public function destroy($id)
    {
        if (!$id) return $this->failed('数据不存在');
        if (!NewEvaluationModel::be(['id' => $id])) return $this->failed('新评推荐数据不存在');
        if (NewEvaluationModel::be(['id' => $id, 'is_system_del' => 1])) {
            return Json::successful('新评推荐数据已被删除!');
        } else {
            $data['is_system_del'] = 1;
            $data['del_time'] = time();
            if (!NewEvaluationModel::edit($data, $id)){
                return Json::fail('删除失败,请稍候再试!');
            }else{
                return Json::successful('成功删除新评推荐!');
            }
        }
    }

    /**
     * 快速编辑
     * @param string $field
     * @param string $id
     * @param string $value
     */
    public function set_field($field = '', $id = '', $value = '')
    {
        $field == '' || $id == '' || $value == '' && Json::fail('缺少参数');
        if (NewEvaluationModel::where('id', $id)->update([$field => $value]))
            return Json::successful('保存成功');
        else
            return Json::fail('保存失败');
    }
    /**
     * 设置资源上架|下架
     * @param string $is_show
     * @param string $id
     */
    public function set_show($is_show = '', $id = '')
    {
        ($is_show == '' || $id == '') && Json::fail('缺少参数');
        if (NewEvaluationModel::setEvaluationShow($id, (int)$is_show)) {
            return Json::successful($is_show == 1 ? '显示成功' : '隐藏成功');
        } else {
            return Json::fail(NewEvaluationModel::getErrorInfo($is_show == 1 ? '显示失败' : '隐藏失败'));
        }
    }
}