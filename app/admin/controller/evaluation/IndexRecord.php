<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2021-01-05 15:31:14
 * @Last Modified time: 2021-01-07 17:04:07
 */
namespace app\admin\controller\evaluation;

use think\facade\Db;
use think\Request;
use think\facade\Route as Url;
use app\admin\model\evaluation\StoreIndexExecutionRecord;
use app\admin\controller\AuthController;
use crmeb\services\{FormBuilder as Form, JsonService as Json, UtilService as Util};
use app\command\model\{
    StoreProduct,StoreYwIndex,StoreYwIndexLabel,StoreLabel,StoreEvaluation,StoreYwIndexExecutionRecord
};
/**
 * 源未指数统计程序运行记录
 * Class Activity
 * @package app\admin\controller\evaluation
 */
class IndexRecord extends AuthController
{
    /**
     * 显示资源列表
     *
     * @return \think\Response
     */
    public function index()
    {
        $this->assign([
            'year' => get_month(),
            'status' => $this->request->param('status', ''),
            'type' => $this->request->param('type', ''),
            'IndexCount' => 0,
        ]);
        return $this->fetch();
    }

    /**
     * 获取指数统计日志列表
     * return json
     */
    public function record_list()
    {
        $where = Util::getMore([
            ['status', ''],
            ['type', ''],
            ['data', ''],
            ['keywords', $this->request->param('keywords', '')], //关键字
            ['page', 1],
            ['limit', 20],
        ]);
        return Json::successlayui(StoreIndexExecutionRecord::RecordList($where));
    }
    
    /**
     * 手动执行任务
     * @return form-builder
     */
    public function task( )
    {
        Db::startTrans(); //开启事物
        try {
            // success 
            $success_number = 0;
            $add_number = 0;
            $update_number = 0;
            $new_product_id = [];
            $products = StoreProduct::getEvaluationProductList(); //所有标记为评测产品的产品
            $first_count = StoreYwIndex::where('status',1)->count();  //计算是否是第一次，运算
            foreach ($products as &$product) {
                //查找对应的测评产品的打分数
                $user_rating = StoreEvaluation::getUserEvaluationList($product['id']) ?: "0.0";
                $official_rating = StoreEvaluation::where('product_id',$product['id'])->where('status',1)->where('is_del',0)->where('type',2)->order('id desc')->value('score') ?: "0.0";
                $previous_user_rating = StoreYwIndex::where('product_id',$product['id'])->order('id desc')->value('this_user_rating') ?: $official_rating;
                if ($official_rating > 0 && $user_rating > 0) {
                    $number = bcadd($user_rating, $official_rating,1);
                    $yw_index = bcdiv((string)$number, "2", 1);
                }else{
                    $yw_index = $official_rating > 0 && $user_rating == 0 ? $official_rating : $user_rating;
                }
                if ($yw_index > 0 && $yw_index < 6) {
                    $res =  StoreYwIndex::add($product['id'],$yw_index,$official_rating, $user_rating, $previous_user_rating,2);
                    $success_number++;
                    $new_product_id[] = $product['id'];
                    $labels = StoreLabel::getLabels($product['id']);
                    $labelData = [];
                    foreach ($labels as $label) {
                        $labelData[] = ['index_id'=>$res['id'],'product_id' => $product['id'],'name'=>$label['name'],'cate_name'=>$label['cate_name'], 'this_rating' => $label['score'],'type'=>2,'add_time' => time()];
                    }
                    StoreYwIndexLabel::insertAll($labelData);
                }
            }
            if ($first_count > 0) { 
                $update_number = StoreYwIndex::where('product_id','IN',$new_product_id)->where('status',1)->group('product_id')->count();  //更新
                $add_number =   bcsub((string)count($new_product_id),(string)$update_number);
            }else{
                $add_number = 0;
                $update_number = 0;
            }
            //获得执行结果
            $info = '成功计算'.$success_number.'个产品，新增测评'.$add_number.'个,更新指数'.$update_number.'个';
            $attributes['success'] = $success_number;
            $attributes['add'] = $add_number;
            $attributes['update'] = $update_number;
            $attributes['result'] = $info;
            $attributes['error_info'] = '';
            $attributes['type'] = 2;
            $attributes['status'] = 1;
            $attributes['add_time'] = time();
            StoreYwIndexExecutionRecord::create($attributes);
            Db::commit(); // 提交事务
            return Json::successful('执行成功');
        } catch (\Exception $e) {
            //error
            Db::rollback(); //开始回滚
            //并向数据库写入服务器执行错误的记录
            $attributes['result'] = '程序运行错误';
            $attributes['error_info'] = json_encode($e->getMessage());
            $attributes['type'] = 1;
            $attributes['status'] = 0;
            $attributes['add_time'] = time();
            StoreYwIndexExecutionRecord::create($attributes);
            return Json::fail('执行失败');
        }
    }
}