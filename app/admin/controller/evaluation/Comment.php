<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2020-09-11 10:20:53
 * @Last Modified time: 2020-12-10 14:09:07
 */
namespace app\admin\controller\evaluation;

use app\admin\controller\AuthController;
use think\Request;
use think\facade\Route as Url;
use app\admin\model\system\SystemAttachment;
use app\admin\model\evaluation\{
    StoreComment as CommentModel,
    StoreActivity as ActivityModel,
    StoreActivityReviews as ActivityReviewsModel
};
use crmeb\services\{FormBuilder as Form, JsonService as Json, UtilService as Util};

/**
 * 评测看版-评论
 * Class Activity
 * @package app\admin\controller\evaluation
 */
class Comment extends AuthController
{
    /**
     * 显示资源列表
     *
     * @return \think\Response
     */
    public function index()
    {
        $this->assign([
            'year' => get_month(),
            'status' => $this->request->param('status', ''),
            'commentCount' => CommentModel::CommentCount(),
        ]);
        return $this->fetch();
    }

    /**
     * 获取评论列表
     * return json
     */
    public function comment_list()
    {
        $where = Util::getMore([
            ['status', ''],
            ['data', ''],
            ['keywords', $this->request->param('keywords', '')], //关键字
            ['page', 1],
            ['limit', 20],
        ]);
        return Json::successlayui(CommentModel::CommentList($where));
    }

    /**
     * 显示创建评论单页.
     * @return \think\Response
     */
    public function create($id = 0)
    {
        $f = [];
        $f[] = Form::select('pid', '评论级数')->setOptions(function () {
                $list = CommentModel::select()->toArray();
                $menus = [['value' => 0, 'label' => '一级评论']];
                foreach ($list as $menu) {
                    $menus[] = ['value' => $menu['id'], 'label' => '回复：' . $menu['comment']];
                }
                return $menus;
        })->filterable(1);
        $f[] = Form::select('evaluation_activity_reviews_id', '评价打分看版活动')->setOptions(function () {
                $list = ActivityReviewsModel::select()->toArray();
                foreach ($list as $menu) {
                    $menus[] = ['value' => $menu['id'], 'label' => '关联：' .  mb_substr($menu['comment'], 0, 30)];
                }
                return $menus;
        })->filterable(1);
        $f[] = Form::hidden('uid', '');
        $f[] = Form::frameImageOne('avatar', '用户信息', Url::buildUrl('admin/user.user/userListes', array('fodder' => 'avatar')))->icon('plus')->width('100%')->height('500px');
        $f[] = Form::textarea('comment', '评论内容');
        $form = Form::make_post_form('添加评论', $f, Url::buildUrl('save'));
        $this->assign(compact('form'));
        return $this->fetch('public/form-builder');
    }


     /**
     * 保存新建的资源
     *
     *
     */
    public function save()
    {
        $data = Util::postMore([
            'uid', //用户id
            'pid', //用户id
            'comment',
        ]);
        //设置基础信息
        if (!$data['evaluation_activity_reviews_id']) return Json::fail('关联的活动');
        if (!$data['uid']) return Json::fail('请选择回复人的信息');
        if (!$data['comment']) return Json::fail('请填写评语');
        $data['add_time'] = time();
        $data['status'] = 1;
        $data['top_id'] = 0;
        $pid = $data['pid'];
        if ($pid) {
            // 计算层级
            $pcomment = CommentModel::find($pid);
            $data['top_id'] = $pcomment->pid == 0 ? $pcomment->id : $pcomment->top_id;
            $level = $pcomment->level;
            $data['level'] = $level + 1;
            $data['evaluation_activity_id'] = $pcomment->evaluation_activity_id;
        }else{
            $data['evaluation_activity_id'] = ActivityReviewsModel::where('id',$data['evaluation_activity_reviews_id'])->value('evaluation_activity_id');
        }
        CommentModel::beginTrans();
        $res = CommentModel::create($data);
        if ($res) {
            CommentModel::commitTrans();
            return Json::success('添加用户评论成功!');
        }else{
            CommentModel::rollbackTrans();
            return Json::fail('添加用户评论失败');
        }
    }

    /**
     * 设置分组
     * @param int $uid
     */
    public function set_status($id = 0)
    {
        if (!$id) return $this->failed('缺少参数');
        $field[] = Form::select('status', '状态分组')->setOptions(function (){
            $menus = [];
            $menus[0] = ['value' => 1, 'label' => '通过'];
            $menus[1] = ['value' => 3, 'label' => '屏蔽'];
            return $menus;
        })->filterable(1);
        $form = Form::make_post_form('设置状态', $field, Url::buildUrl('save_set_status', ['id' => $id]), 2);
        $this->assign(compact('form'));
        return $this->fetch('public/form-builder');
    }

    public function save_set_status($id = 0)
    {
        if (!$id) return Json::fail('缺少参数');
        list($status) = Util::postMore([
            ['status', 0],
        ], $this->request, true);
        $ids = explode(',',$id);
        $data = $status == 1 ? ['status' => $status] : ['status' => $status,'is_del'=>1,'del_time'=>time()];
        $res = CommentModel::whereIn('id', $ids)->update($data);
        if ($res) {
            return Json::successful('设置成功');
        } else {
            return Json::successful('设置失败');
        }
    }
    /**
     * 恢复状态
     * @param int $uid
     */
    public function restore($id = 0)
    {
        $data['status'] = 1;
        $data['is_del'] = 0;
        $data['del_time'] = null;
        if (!CommentModel::edit($data,$id))
            return Json::fail(CommentModel::getErrorInfo('恢复失败,请稍候再试!'));
        else
            return Json::successful('恢复成功!');
    }

    /**
     * 审核指定资源
     * @param $id
     */
    public function review($id)
    {
        CommentModel::beginTrans();
        $commentlInfo = CommentModel::where('id', $id)->find();
        if (!$commentlInfo) return Json::fail('审核评论不存在！');
            $commentlInfo->status = 1;
        if ($commentlInfo->save()) {
            CommentModel::commitTrans();
            return Json::successful('审核成功！');
        } else {
            CommentModel::rollbackTrans();
            return Json::fail('审核失败');
        }
    }

    /**
     * 删除指定资源
     *
     * @param int $id
     * @return \think\Response
     */
    public function destroy($id)
    {   
        $data['status'] = 3;
        $data['is_del'] = 1;
        $data['del_time'] = date('Y-m-d H:i:s',time());
        if (!CommentModel::edit($data,$id))
            return Json::fail(CommentModel::getErrorInfo('删除失败,请稍候再试!'));
        else
            return Json::successful('删除成功!');
    }
}