<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2020-09-11 10:20:53
 * @Last Modified time: 2021-01-27 14:59:31
 */
namespace app\admin\controller\evaluation;

use app\admin\controller\AuthController;
use think\Request;
use think\facade\Route as Url;
use app\admin\model\system\SystemAttachment;
use app\admin\model\system\SystemStore;
use app\admin\model\evaluation\{
    StoreEvaluation as EvaluationModel,
    StoreActivity as ActivityModel,
    StoreActivityReviews as ActivityReviewsModel,
    StoreScoringItems
};
use app\admin\model\user\User;
use app\admin\model\store\StoreCategory;
use app\admin\model\store\StoreProduct;
use crmeb\services\{FormBuilder as Form, JsonService as Json, UtilService as Util};

/**
 * 评测管理-评测
 * Class Activity
 * @package app\admin\controller\evaluation
 */
class Management extends AuthController
{
    /**
     * 显示资源列表
     *
     * @return \think\Response
     */
    public function index()
    {
        $this->assign([
            'year' => get_month(),
            'status' => $this->request->param('status', ''),
            'evaluationCount' => EvaluationModel::EvaluationCount(),
        ]);
        return $this->fetch();
    }

    /**
     * 获取评测列表
     * return json
     */
    public function evaluation_list()
    {
        $where = Util::getMore([
            ['status', ''],
            ['data', ''],
            ['type', ''],
            ['keywords', $this->request->param('keywords', '')], //关键字
            ['page', 1],
            ['limit', 20],
        ]);
        return Json::successlayui(EvaluationModel::EvaluationList($where));
    }

    /**
     * 活动获取评测商品列表
     * @return string
     * @throws \Exception
     */
    public function productList()
    {
        $cate = StoreCategory::getTierList(null, 1);
        $this->assign('cate', $cate);
        return $this->fetch();
    }

    /**
     * 活动获取所有列表
     * @return string
     * @throws \Exception
     */
    public function storeList()
    {
        $cate = StoreCategory::getTierList(null, 1);
        $this->assign('cate', $cate);
        return $this->fetch();
    }

    /**
     * 显示创建评测单页.
     * @return \think\Response
     */
    public function create($id = 0)
    {
        $this->assign('id', (int)$id);
        return $this->fetch();
    }
     /**
     * 保存新建的资源
     *
     *
     */
    public function save($id)
    {
        $data = Util::postMore([
            'uid', //用户id
            'product_id', //选择产品Id
            'store_id', //选择门店Id
            'url', //官评文章地址
            'content', //文章内容
            ['images',[]],
            'score',
            'labelCheckList',
            'type',
            'status',
        ]);
        EvaluationModel::beginTrans();
        // 设置基础信息
        if (!$data['product_id']) return Json::fail('请填写产品ID');
        $type = $data['type'];
        if ($type == 2) { //官评
            if (!$data['url']) return Json::fail('请填写官方文章地址');
        }
        if ($type == 1) { //用户评测
            if (!$data['uid']) return Json::fail('请选择关联的用户ID');
            if (count($data['images']) < 1) return Json::fail('请上传评测图');
            if (!$data['content']) return Json::fail('请填写评语');
        }
        if (!$data['score']) return Json::fail('请填写评测分');
        if (!$data['status']) return Json::fail('请填写评测状态');
        $data['image'] = json_encode($data['images']);
        $data['content'] = json_encode($data['content']);
        $scoring = $data['labelCheckList'];
        if ($id) {
            $res = EvaluationModel::edit($data, $id);
            StoreScoringItems::where('evaluation_scoring_id', $id)->delete();
            $scoreData = [];
            if (count($scoring) > 0) {
                foreach ($scoring as &$score) {
                    $type = isset($score['diy']) ? 1 : 0;
                    $scoreData[] = ['evaluation_scoring_id' => $id,'type'=>$type,'cate_id'=>$score['id'], 'cate_name' => $score['cate_name'], 'name'=>$score['name'], 'score'=>$score['score'],'add_time' => time()];
                }
                StoreScoringItems::insertAll($scoreData);
            }
            if ($res) {
                EvaluationModel::commitTrans();
                return Json::success('修改成功!');
            } else {
                EvaluationModel::rollbackTrans();
                return Json::fail(EvaluationModel::getErrorInfo());
            }
        }else{
            $data['source'] = 2;
            $data['add_time'] = time();
            $res = EvaluationModel::create($data);
            $scoreData = [];
            if (count($scoring) > 0) {
                foreach ($scoring as $score) {
                    $type = isset($score['diy']) ? 1 : 0;
                    $scoreData[] = ['evaluation_scoring_id' => $res['id'],'type'=>$type,'cate_id'=>$score['id'], 'cate_name' => $score['cate_name'], 'name'=>$score['name'], 'score'=>$score['score'],'add_time' => time()];
                }
                StoreScoringItems::insertAll($scoreData);
            }
            if ($res) {
                EvaluationModel::commitTrans();
                return Json::success('添加评测成功!');
            }else{
                EvaluationModel::rollbackTrans();
                return Json::fail('添加评测失败');
            }
        }
    }

    /**
     * 显示编辑资源表单页.
     *
     * @param int $id
     * @return \think\Response
     */
    public function edit($id)
    {
        $c = EvaluationModel::get($id);
        if (!$c) return Json::fail('数据不存在!');
        $field = [
            Form::input('name', '项目名称', $c->getData('name')),
            Form::input('position', '位置', $c->getData('position')),
            Form::input('date', '时间', $c->getData('date')),
            Form::input('desc', '简介', $c->getData('desc')),
            Form::input('date', '分类名称', $c->getData('date')),
            Form::frameImages('images', '产品轮播图(640*640px)', Url::buildUrl('widget.images/index', array('fodder' => 'slider_image')), json_decode($c->getData('image'), 1) ?: [])->maxLength(5)->icon('images')->width('100%')->height('500px'),
        ];
        $form = Form::make_post_form('编辑评测', $field, Url::buildUrl('update', array('id' => $id)), 2);

        $this->assign(compact('form'));
        return $this->fetch('public/form-builder');
    }


    /**
     * 保存更新的资源
     *
     * @param \think\Request $request
     * @param int $id
     * @return \think\Response
     */
    public function update(Request $request, $id)
    {
        $data = Util::postMore([
            'name', //用户id
            'position',
            'date',
            'desc',
            'images'
        ], $request);
        if (!$data['name']) return Json::fail('请填写想测项目名称');
        if (!$data['position']) return Json::fail('请填写地址');
        if (!$data['date']) return Json::fail('请填写时间');
        $data['image'] = json_encode($data['images']);
        EvaluationModel::edit($data, $id);
        return Json::successful('修改成功!');
    }

    /**
     * 获取评测详细信息
     * @param int $id
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function get_evaluation_info($id = 0)
    {
        $data['evaluationInfo'] = [];
        if ($id) {
            $evaluationInfo = EvaluationModel::get($id);
            if (!$evaluationInfo) {
                return Json::fail('修改的评测不存在');
            }
            $evaluationInfo['avatar'] = User::where('uid', $evaluationInfo['uid'])->value('avatar');
            $evaluationInfo['product_image'] = StoreProduct::where('id', $evaluationInfo['product_id'])->value('image');
            $evaluationInfo['store_image'] = SystemStore::where('id', $evaluationInfo['store_id'])->value('image');
            $evaluationInfo['images'] = is_string($evaluationInfo['image']) && $evaluationInfo['image'] != "" ? json_decode($evaluationInfo['image'], true) : [];
            $evaluationInfo['content'] = is_string($evaluationInfo['content']) && $evaluationInfo['content'] != "" ? json_decode($evaluationInfo['content'], true) : [];
            $evaluationInfo['labelCheckList'] = [];
            $scoringItems = StoreScoringItems::where('evaluation_scoring_id', $id)->select()->toArray();
            foreach ($scoringItems as &$scoringitem) {
                $scoringitem['diy'] = $scoringitem['type'] ? true : false;
            }
            if (!empty($scoringItems)) {
                $evaluationInfo['labelCheckList'] =$scoringItems;
            }
            $evaluationInfo['score'] = "3";
            $data['evaluationInfo'] = $evaluationInfo;
        }
        return Json::successful($data);
    }


    public function evaluation_info($wid = '')
    {
        if (!$wid || !($evaluationInfo = EvaluationModel::get($wid)))
            return $this->failed('评测不存在!');
        $images = is_string($evaluationInfo['image']) ? json_decode($evaluationInfo['image'], true) : [];
        $this->assign(compact('evaluationInfo','images'));
        return $this->fetch();
    }

    /**
     * 设置分组
     * @param int $uid
     */
    public function save_set_status(Request $request, $id)
    {
        $data = Util::postMore([
            'status', //用户id
        ], $request);
        EvaluationModel::edit($data, $id);
        return Json::successful('修改成功!');
    }

    /**
     * 审核指定资源
     * @param $id
     */
    public function set_status($id)
    {
        $c = EvaluationModel::get($id);
        if (!$c) return Json::fail('数据不存在!');
        $field = [
            Form::radio('status', '选择状态',$c->getData('status'))->options([['label' =>'通过','value' => 1],['label' =>'待审核','value' => 2],['label' =>'拒绝','value' => 3]]),
        ];
        $form = Form::make_post_form('审核评测状态', $field, Url::buildUrl('save_set_status', array('id' => $id)), 2);
        $this->assign(compact('form'));
        return $this->fetch('public/form-builder');
    }

    /**
     * 删除指定资源
     *
     * @param int $id
     * @return \think\Response
     */
    public function destroy($id)
    {   
        $data['status'] = 3;
        $data['is_del'] = 1;
        $data['is_system_del'] = 1;
        $data['del_time'] = time();
        if (!EvaluationModel::edit($data,$id))
            return Json::fail(EvaluationModel::getErrorInfo('删除失败,请稍候再试!'));
        else
            return Json::successful('删除成功!');
    }
}