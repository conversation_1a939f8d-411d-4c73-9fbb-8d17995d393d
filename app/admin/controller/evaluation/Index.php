<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2020-09-11 10:20:53
 * @Last Modified time: 2020-12-27 16:07:15
 */
namespace app\admin\controller\evaluation;

use think\Request;
use think\facade\Route as Url;
use app\admin\model\store\{
    StoreProduct as ProductModel,
};
use app\admin\model\evaluation\StoreIndexStatistics;
use app\admin\model\evaluation\StoreIndexLabelStatistics;
use app\admin\controller\AuthController;
use crmeb\services\{FormBuilder as Form, JsonService as Json, UtilService as Util};
use app\command\model\{
    StoreProduct,StoreYwIndex,StoreYwIndexLabel,StoreLabel,StoreEvaluation
};
/**
 * 源未指数统计日志
 * Class Activity
 * @package app\admin\controller\evaluation
 */
class Index extends AuthController
{
    /**
     * 显示资源列表
     *
     * @return \think\Response
     */
    public function index()
    {
        $this->assign([
            'year' => get_month(),
            'status' => $this->request->param('status', ''),
            'type' => $this->request->param('type', ''),
            'IndexCount' => StoreIndexStatistics::IndexCount(),
        ]);
        return $this->fetch();
    }

    /**
     * 获取指数统计日志列表
     * return json
     */
    public function index_list()
    {
        $where = Util::getMore([
            ['status', ''],
            ['type', ''],
            ['data', ''],
            ['keywords', $this->request->param('keywords', '')], //关键字
            ['page', 1],
            ['limit', 20],
        ]);
        return Json::successlayui(StoreIndexStatistics::IndexList($where));
    }

    /**
     * 详情
     * @return form-builder
     */
    public function index_info($id = '')
    {
        if (!$id || !($IndexInfo = StoreIndexStatistics::get($id))) return $this->failed('指数不存在!');
        $IndexInfo->title = ProductModel::where('id',$IndexInfo['product_id'])->value('store_name')  ?: '';
        $labelInfo = [];
        $this->assign(compact('id','IndexInfo', 'labelInfo'));
        return $this->fetch();
    }


    /**
     * 活动详情
     * @return form-builder
     */
    public function getLabelList($id, $page = 1, $limit = 20)
    {
        return Json::successful(StoreIndexLabelStatistics::getLabelList((int)$id, (int)$page, (int)$limit));
    }

    /**
     * 手动执行任务
     * @return form-builder
     */
    public function task( )
    {
         //查找所有评测产品、是否拥有评测记录 
        $products = StoreProduct::getEvaluationProductList(); //所有官方评测
        foreach ($products as &$product) {
            //查找对应的测评产品的打分数
            $user_rating = StoreEvaluation::getUserEvaluationList($product['id']) ?: 0;
            $official_rating = StoreEvaluation::where('product_id',$product['id'])->where('status',1)->where('is_del',0)->where('type',2)->order('id desc')->value('score') ?: 0;
            $previous_user_rating = StoreYwIndex::where('product_id',$product['id'])->order('id desc')->value('this_user_rating') ?: $official_rating;
            $this_user_rating = number_format($user_rating,1);
            if ($official_rating > 0 && $this_user_rating > 0) {
                $number = bcadd($user_rating, $official_rating,1);
                $yw_index =   bcdiv($number, 2, 1);
            }else{
                $yw_index = $official_rating > 0 && $this_user_rating == 0 ? $official_rating : $this_user_rating;
            }
            if ($yw_index > 0 && $yw_index < 6) {
                $res =  StoreYwIndex::add($product['id'],$yw_index,$official_rating, $this_user_rating, $previous_user_rating,2);
                $labels = StoreLabel::getLabels($product['id']);
                $labelData = [];
                foreach ($labels as $label) {
                    $labelData[] = ['index_id'=>$res['id'],'product_id' => $product['id'],'name'=>$label['name'], 'this_rating' => $label['score'],'type'=>2,'add_time' => time()];
                }
                StoreYwIndexLabel::insertAll($labelData);
            }
        }
        return Json::successful('执行成功');
    }
}