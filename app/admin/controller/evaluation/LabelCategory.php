<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2020-12-10 14:13:06
 * @Last Modified time: 2021-01-27 09:29:01
 */
namespace app\admin\controller\evaluation;

use app\admin\controller\AuthController;
use think\Request;
use think\facade\Route as Url;
use app\admin\model\system\SystemAttachment;
use app\admin\model\evaluation\StoreLabelCategory as CategoryModel;
use crmeb\services\{FormBuilder as Form, JsonService as Json, UtilService as Util};


use app\admin\model\evaluation\{
    StoreComment as CommentModel,
    StoreActivity as ActivityModel,
    StoreActivityReviews as ActivityReviewsModel
};


/**
 * 标签分类控制器
 * Class LabelCategory
 * @package app\admin\controller\evaluation
 */
class LabelCategory extends AuthController
{
    /**
     * 显示资源列表
     *
     * @return \think\Response
     */
    public function index()
    {
    	$this->assign([
            'year' => get_month(),
            'status' => $this->request->param('status', ''),
            'commentCount' => CommentModel::CommentCount(),
        ]);
        return $this->fetch();
    }

    /*
     *  异步获取分类列表
     *  @return json
     */
    public function category_list()
    {
        $where = Util::getMore([
            ['title', ''],
            ['page', 1],
            ['limit', 20],
        ]);
        return Json::successlayui(CategoryModel::CategoryList($where));
    }

    /*
     *  异步获取分类列表
     *  @return json
     */
    public function get_category()
    {
        return Json::successful(CategoryModel::getTierLabel());
    }


    /*
     *  异步获取分类列表
     *  @return json
     */
    public function get_category_default($id = 0)
    {
        return Json::successful(CategoryModel::getTierProductLabel($id));
    }


    /*
     *  异步获取分类列表
     *  @return json
     */
    public function label_list()
    {
        $where = Util::getMore([
            ['title', ''],
            ['page', 1],
            ['limit', 20],
        ]);
        return Json::successlayui(CategoryModel::CategoryList($where));
    }

    /**
     * 快速编辑
     * @param string $field
     * @param string $id
     * @param string $value
     */
    public function set_category($field = '', $id = '', $value = '')
    {
        $field == '' || $id == '' || $value == '' && Json::fail('缺少参数');
        if (CategoryModel::where('id', $id)->update([$field => $value]))
            return Json::successful('保存成功');
        else
            return Json::fail('保存失败');
    }

    /**
     * 显示创建资源表单页.
     *
     * @return \think\Response
     */
    public function create()
    {
        $field = [
            Form::input('title', '分类名称'),
            Form::input('long_title', '分类长名称'),
            Form::checkbox('is_single', '是否单选')->options([['label' =>'','value' => 1]]),
            Form::checkbox('is_required', '是否必选')->options([['label' =>'','value' => 1]]),
            Form::checkbox('is_scoring', '是否打分')->options([['label' =>'','value' => 1]]),
            Form::checkbox('is_show', '是否显示')->options([['label' =>'','value' => 1]]),
            Form::checkbox('is_other', '是否允许添加其他')->options([['label' =>'','value' => 1]]),
            Form::checkbox('is_recommend', '是否推荐至搜索')->options([['label' =>'','value' => 1]]),
            Form::textarea('remarks', '备注')
        ];
        $form = Form::make_post_form('添加分类标签', $field, Url::buildUrl('save'), 2);
        $this->assign(compact('form'));
        return $this->fetch('public/form-builder');
    }

    /**
     * 保存新建的资源
     *
     * @param \think\Request $request
     * @return \think\Response
     */
    public function save(Request $request)
    {
        $attributes = Util::postMore([
            'title',
            'long_title',
            'remarks',
            'is_single',
            'is_required',
            'is_scoring',
            'is_show',
            'is_other',
            'is_recommend'
        ], $request);
        if ($attributes['title'] == '') return Json::fail('请填写分类名称');
        if ($attributes['long_title'] == '') return Json::fail('请填写分类长名称');
        if ($attributes['title'] == '我的自建') return Json::fail('此分类名严禁添加，请换个名称');
        if (CategoryModel::be(['title'=>$attributes['title'],'status'=>1,'is_show'=>1])) return Json::fail('此分类已存在，请换个分类名'); //分类名是否重复
        if (!$attributes['remarks']) return Json::fail('请输入分类备注');
        $data['is_single'] = isset($attributes['is_single']) && $attributes['is_single'] !="" ? 1 : 0;
        $data['is_required'] = isset($attributes['is_required']) && $attributes['is_required'] !="" ? 1 : 0;
        $data['is_scoring'] = isset($attributes['is_scoring']) && $attributes['is_scoring'] !="" ? 1 : 0;
        $data['is_show'] = isset($attributes['is_show']) && $attributes['is_show'] !="" ? 1 : 0;
        $data['is_other'] = isset($attributes['is_other']) && $attributes['is_other'] !="" ? 1 : 0;
        $data['is_recommend'] = isset($attributes['is_recommend']) && $attributes['is_recommend'] !="" ? 1 : 0;
        $data['title'] = $attributes['title'];
        $data['long_title'] = $attributes['long_title'];
        $data['remarks'] = $attributes['remarks'];
        $data['status'] = 1;
        $data['add_time'] = time();
        CategoryModel::create($data);
        return Json::successful('添加分类成功!');
    }

    /**
     * 显示编辑资源表单页.
     *
     * @param int $id
     * @return \think\Response
     */
    public function edit($id)
    {
        $c = CategoryModel::get($id);
        if (!$c) return Json::fail('数据不存在!');
        $field = [
            Form::input('title', '分类名称', $c->getData('title')),
            Form::input('long_title', '分类长名称', $c->getData('long_title')),
            Form::checkbox('is_single', '是否单选',[$c->getData('is_single')])->options([['label' =>'','value' => 1]]),
            Form::checkbox('is_required', '是否必选',[$c->getData('is_required')])->options([['label' =>'','value' => 1]]),
            Form::checkbox('is_scoring', '是否打分',[$c->getData('is_scoring')])->options([['label' =>'','value' => 1]]),
            Form::checkbox('is_show', '是否显示',[$c->getData('is_show')])->options([['label' =>'','value' => 1]]),
            Form::checkbox('is_other', '是否允许添加其他',[$c->getData('is_other')])->options([['label' =>'','value' => 1]]),
            Form::checkbox('is_recommend', '是否推荐至搜索',[$c->getData('is_recommend')])->options([['label' =>'','value' => 1]]),
           	Form::input('remarks', '分类备注', $c->getData('remarks')),
        ];
        $form = Form::make_post_form('编辑标签分类', $field, Url::buildUrl('update', array('id' => $id)), 2);
        $this->assign(compact('form'));
        return $this->fetch('public/form-builder');
    }

    /**
     * 保存更新的资源
     *
     * @param \think\Request $request
     * @param int $id
     * @return \think\Response
     */
    public function update(Request $request, $id)
    {
        $data = Util::postMore([
            'title',
            'long_title',
            'remarks',
            ['is_single',''],
            ['is_required',''],
            ['is_scoring',''],
            ['is_show',''],
            ['is_other',''],
            ['is_recommend',''],
        ], $request);
        $category = CategoryModel::get($id);
        if ($data['title'] == '') return Json::fail('请填写分类名称');
        if (CategoryModel::be(['title'=>$data['title'],'status'=>1,'is_show'=>1]) && $category->title != $data['title']) return Json::fail('此分类已存在，请换个分类名'); //分类名是否重复
        if ($data['long_title'] == '') return Json::fail('请填写分类长名称');
        if (!$data['remarks']) return Json::fail('请输入分类备注');
        $data['is_single'] = isset($data['is_single']) && $data['is_single'] !="" ? $data['is_single'][0] : 0;
        $data['is_required'] = isset($data['is_required']) && $data['is_required'] !="" ? $data['is_required'][0] : 0;
        $data['is_scoring'] = isset($data['is_scoring']) && $data['is_scoring'] !="" ? $data['is_scoring'][0] : 0;
        $data['is_show'] = isset($data['is_show']) && $data['is_show'] !="" ? $data['is_show'][0] : 0;
        $data['is_other'] = isset($data['is_other']) && $data['is_other'] !="" ? $data['is_other'][0] : 0;
        $data['is_recommend'] = isset($data['is_recommend']) && $data['is_recommend'] !="" ? $data['is_recommend'][0] : 0;
        CategoryModel::edit($data, $id);
        return Json::successful('修改成功!');
    }

    /**
     * 删除指定资源
     *
     * @param int $id
     * @return \think\Response
     */
    public function delete($id)
    {
        if (!CategoryModel::delCategory($id))
            return Json::fail(CategoryModel::getErrorInfo('删除失败,请稍候再试!'));
        else
            return Json::successful('删除成功!');
    }
}
