<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2021-10-12 11:32:43
 * @Last Modified time: 2022-04-27 10:44:34
 */
namespace app\admin\controller\widget;

use app\admin\controller\AuthController;
use crmeb\services\JsonService;
use crmeb\services\upload\Upload;
use think\facade\Config;

class Audio extends AuthController
{


    /**
     * 上传类型
     * @var int
     */
    protected $uploadInfo;

    /**
     * 获取配置信息
     * Audio constructor.
     */
    public function initialize()
    {
        parent::initialize();
        $this->uploadInfo['accessKey'] = sys_config('accessKey');
        $this->uploadInfo['secretKey'] = sys_config('secretKey');
        $this->uploadInfo['uploadUrl'] = sys_config('documentUploadUrl');  //音视频 documentUploadUrl 普通 uploadUrl
        $this->uploadInfo['storageName'] = sys_config('document_storage_name'); //音视频 document_storage_name 普通 document_storage_name
        $this->uploadInfo['storageRegion'] = sys_config('storage_region');
        $this->uploadInfo['uploadType'] = sys_config('upload_type');
    }

    /**
     * 获取密钥签名
     */
    public function get_signature()
    {
        if ($this->uploadInfo['uploadType'] == 1) {
            if (!$this->uploadInfo['accessKey'] || !$this->uploadInfo['secretKey']) {
                return JsonService::fail('音频上传需要上传到云端,默认使用阿里云OSS上传请配置!');
            } else {
                $this->uploadInfo['uploadType'] = 3;
            }
        }
        if ($this->uploadInfo['uploadType'] == 2) {
            $upload = new Upload('Qiniu', $this->uploadInfo);
            $res = $upload->getSystem();
            $this->uploadInfo['uploadToken'] = $res['token'];
            $this->uploadInfo['domain'] = $res['domain'];
            $this->uploadInfo['uploadType'] = 'QINIU';
        } elseif ($this->uploadInfo['uploadType'] == 3) {
            $this->uploadInfo['uploadType'] = 'OSS';
            if (($leng = strpos($this->uploadInfo['storageRegion'], 'aliyuncs.com')) !== false) {
                $this->uploadInfo['storageRegion'] = substr($this->uploadInfo['storageRegion'], 0, $leng - 1);
            }
        } elseif ($this->uploadInfo['uploadType'] == 4) {
            $this->uploadInfo['uploadType'] = 'COS';
        }
        return JsonService::successful($this->uploadInfo);
    }

    public function index($fodder = '')
    {
        $this->assign(compact('fodder'));
        return $this->fetch();
    }

    /**
     * 音频代理方法，解决CORS跨域问题（流式传输版本）
     * @return \think\response\Json
     */
    public function proxy_audio()
    {
        // 临时增加内存限制以处理大型音频文件
        $originalMemoryLimit = ini_get('memory_limit');
        ini_set('memory_limit', '512M');
        
        $audioUrl = $this->request->post('audio_url', '');
        $range = $this->request->header('Range', ''); // 支持HTTP Range请求
        
        if (empty($audioUrl)) {
            ini_set('memory_limit', $originalMemoryLimit);
            return JsonService::fail('音频URL不能为空');
        }
        
        try {
            // 验证URL格式
            if (!filter_var($audioUrl, FILTER_VALIDATE_URL)) {
                ini_set('memory_limit', $originalMemoryLimit);
                return JsonService::fail('无效的音频URL');
            }
            
            // 首先获取文件头信息
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $audioUrl);
            curl_setopt($ch, CURLOPT_NOBODY, true);
            curl_setopt($ch, CURLOPT_HEADER, true);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36');
            
            $headers = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
            $contentLength = curl_getinfo($ch, CURLINFO_CONTENT_LENGTH_DOWNLOAD);
            $error = curl_error($ch);
            curl_close($ch);
            
            if ($headers === false || !empty($error)) {
                ini_set('memory_limit', $originalMemoryLimit);
                return JsonService::fail('获取音频文件信息失败: ' . $error);
            }
            
            if ($httpCode !== 200) {
                ini_set('memory_limit', $originalMemoryLimit);
                return JsonService::fail('音频文件请求失败，HTTP状态码: ' . $httpCode);
            }
            
            // 处理Range请求
            $rangeStart = 0;
            $rangeEnd = $contentLength - 1;
            $isPartialContent = false;
            
            if (!empty($range) && $contentLength > 0) {
                if (preg_match('/bytes=(\d+)-(\d*)/', $range, $matches)) {
                    $rangeStart = intval($matches[1]);
                    $rangeEnd = !empty($matches[2]) ? intval($matches[2]) : $contentLength - 1;
                    $isPartialContent = true;
                }
            }
            
            // 设置响应头
            if ($isPartialContent) {
                http_response_code(206); // Partial Content
                header('Content-Range: bytes ' . $rangeStart . '-' . $rangeEnd . '/' . $contentLength);
                header('Content-Length: ' . ($rangeEnd - $rangeStart + 1));
            } else {
                header('Content-Type: ' . ($contentType ?: 'audio/mpeg'));
                if ($contentLength > 0) {
                    header('Content-Length: ' . $contentLength);
                }
            }
            
            header('Access-Control-Allow-Origin: *');
            header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
            header('Access-Control-Allow-Headers: Content-Type, Range');
            header('Accept-Ranges: bytes');
            
            // 流式传输音频数据
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $audioUrl);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 120); // 增加超时时间
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10); // 连接超时
            curl_setopt($ch, CURLOPT_BUFFERSIZE, 65536); // 设置cURL缓冲区大小为64KB
            curl_setopt($ch, CURLOPT_TCP_NODELAY, true); // 禁用Nagle算法，减少延迟
            curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36');
            $headers = [
                'Accept: audio/mpeg,audio/*,*/*',
                'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8',
                'Cache-Control: no-cache',
                'Pragma: no-cache'
            ];
            
            // 如果是Range请求，添加Range头
            if ($isPartialContent) {
                $headers[] = 'Range: bytes=' . $rangeStart . '-' . $rangeEnd;
            }
            
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            
            // 优化的写入回调函数，使用缓冲区减少系统调用
            $bufferSize = 0;
            $maxBufferSize = 65536; // 64KB缓冲区
            $totalBytes = 0;
            $startTime = microtime(true);
            
            curl_setopt($ch, CURLOPT_WRITEFUNCTION, function($ch, $data) use (&$bufferSize, $maxBufferSize, &$totalBytes) {
                echo $data;
                $dataLength = strlen($data);
                $bufferSize += $dataLength;
                $totalBytes += $dataLength;
                
                // 只有当缓冲区达到阈值时才刷新，减少系统调用
                if ($bufferSize >= $maxBufferSize) {
                    ob_flush();
                    flush();
                    $bufferSize = 0;
                }
                
                return $dataLength;
            });
            
            // 执行流式传输
            $result = curl_exec($ch);
            $error = curl_error($ch);
            curl_close($ch);
            
            // 确保最后的缓冲区数据被刷新
            if ($bufferSize > 0) {
                ob_flush();
                flush();
            }
            
            // 计算传输性能指标
            $endTime = microtime(true);
            $transferTime = $endTime - $startTime;
            $transferSpeed = $totalBytes > 0 ? ($totalBytes / 1024 / 1024) / $transferTime : 0; // MB/s
            
            if ($result === false || !empty($error)) {
                 // 如果流式传输失败，记录错误但不返回JSON（因为可能已经开始输出数据）
                 error_log(sprintf('音频流式传输失败: %s | 已传输: %.2fMB | 耗时: %.2fs', 
                     $error, $totalBytes / 1024 / 1024, $transferTime));
             } else {
                 // 记录成功的传输性能
                 error_log(sprintf('音频流式传输完成: %.2fMB | 耗时: %.2fs | 速度: %.2fMB/s', 
                     $totalBytes / 1024 / 1024, $transferTime, $transferSpeed));
             }
             
             // 恢复原始内存限制
             ini_set('memory_limit', $originalMemoryLimit);
             exit;
             
        } catch (\Exception $e) {
            ini_set('memory_limit', $originalMemoryLimit);
            return JsonService::fail('代理请求失败: ' . $e->getMessage());
        }
    }
}