<?php
/**
 * Created by PhpStorm.
 * User: wuh<PERSON><PERSON>
 * Date: 2020-02-24
 * Time: 17:57
 */

namespace app\admin\controller\widget;

use think\facade\Config;
use app\admin\controller\AuthController;
use think\facade\Route as Url;
use app\admin\model\system\{
    SystemAttachment as SystemAttachmentModel, SystemAttachmentCategory as Category
};
use crmeb\services\{JsonService as Json, JsonService, UtilService as Util, FormBuilder as Form, minishop\Shop as Shop };
use crmeb\services\upload\Upload;

class File extends AuthController
{
    /**
     * 上传类型
     * @var int
     */
    protected $uploadInfo;

    /**
     * 获取配置信息
     * File constructor.
     */
    public function initialize()
    {
        parent::initialize();
        $this->uploadInfo['accessKey'] = sys_config('accessKey');
        $this->uploadInfo['secretKey'] = sys_config('secretKey');
        $this->uploadInfo['uploadUrl'] = sys_config('uploadUrl');  //音视频 documentUploadUrl 普通 uploadUrl
        $this->uploadInfo['storageName'] = sys_config('storage_name'); //音视频 document_storage_name 普通 document_storage_name
        $this->uploadInfo['storageRegion'] = sys_config('storage_region');
        $this->uploadInfo['uploadType'] = sys_config('upload_type');
    }

    /**
     * 获取密钥签名
     */
    public function get_signature()
    {
        $private =  $this->request->post('is_private',0);
        if ($this->uploadInfo['uploadType'] == 1) {
            if (!$this->uploadInfo['accessKey'] || !$this->uploadInfo['secretKey']) {
                return JsonService::fail('视频上传需要上传到云端,默认使用阿里云OSS上传请配置!');
            } else {
                $this->uploadInfo['uploadType'] = 3;
            }
        }
        if ($this->uploadInfo['uploadType'] == 2) {
            $upload = new Upload('Qiniu', $this->uploadInfo);
            $res = $upload->getSystem();
            $this->uploadInfo['uploadToken'] = $res['token'];
            $this->uploadInfo['domain'] = $res['domain'];
            $this->uploadInfo['uploadType'] = 'QINIU';
        } elseif ($this->uploadInfo['uploadType'] == 3) {
            $this->uploadInfo['uploadType'] = 'OSS';
            if (($leng = strpos($this->uploadInfo['storageRegion'], 'aliyuncs.com')) !== false) {
                $this->uploadInfo['storageRegion'] = substr($this->uploadInfo['storageRegion'], 0, $leng - 1);
                if ($private == 1) {
                    $this->uploadInfo['storageName'] = sys_config('document_storage_name');
                    $this->uploadInfo['uploadUrl'] =  sys_config('documentUploadUrl');
                }
            }
        } elseif ($this->uploadInfo['uploadType'] == 4) {
            $this->uploadInfo['uploadType'] = 'COS';
        }
        return JsonService::successful($this->uploadInfo);
    }



    /**获取图片列表
     *
     */
    public function get_file_list()
    {
        $where = Util::getMore([
            ['page', 1],
            ['limit', 18],
            ['pid', 0]
        ]);
        return Json::successful(SystemAttachmentModel::getImageList($where));
    }
    /**
     * 附件列表
     * @return \think\response\Json
     */
    public function index()
    {
        $pid = request()->param('pid');
        if ($pid === NULL) {
            $pid = session('pid') ? session('pid') : 0;
        }
        session('pid', $pid);
        $this->assign('pid', $pid);
        return $this->fetch('widget/file');
    }

    
    public function indexs($fodder = '')
    {
        $this->assign('uploadUrl',sys_config('uploadUrl'));
        $this->assign(compact('fodder'));
        return $this->fetch();
    }
}