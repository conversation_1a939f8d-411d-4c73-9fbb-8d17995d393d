<?php
/**
 *
 * @author: xaboy<<EMAIL>>
 * @day: 2018/01/17
 */

namespace app\admin\controller\ump;

use app\admin\controller\AuthController;
use think\facade\Route as Url;
use crmeb\traits\CurdControllerTrait;
use app\admin\model\wechat\WechatUser as UserModel;
use crmeb\services\{JsonService, FormBuilder as Form, UtilService as Util};
use app\admin\model\ump\{StoreCoupon as CouponModel,StoreCouponIssue as CouponIssueModel, StoreCouponIssueUser,StoreCouponUser as CouponUserModel};

class StoreCouponIssue extends AuthController
{
    use CurdControllerTrait;

    protected $bindModel = CouponIssueModel::class;

    public function index()
    {
        $where = Util::getMore([
            ['status', ''],
            ['coupon_title', ''],
            ['type','']
        ]);
        $this->assign(CouponIssueModel::stsypage($where));
        $this->assign('where', $where);
        return $this->fetch();
    }

    public function indexs()
    {
        $where = Util::getMore([
            ['status', ''],
            ['coupon_title', ''],
            ['type',1],
            ['page',1],
            ['limit',20],
        ]);
        return JsonService::successlayui(CouponIssueModel::getList($where));
    }


    public function delete($id = '')
    {
        if (!$id) return JsonService::fail('参数有误!');
        if (CouponIssueModel::edit(['is_del' => 1], $id, 'id'))
            return JsonService::successful('删除成功!');
        else
            return JsonService::fail('删除失败!');
    }

    public function edit($id = '')
    {
        if (!$id) return JsonService::fail('参数有误!');
        $issueInfo = CouponIssueModel::get($id);
        if (-1 == $issueInfo['status'] || 1 == $issueInfo['is_del']) return $this->failed('状态错误,无法修改');
        $f = [Form::radio('status', '是否开启', $issueInfo['status'])->options([['label' => '开启', 'value' => 1], ['label' => '关闭', 'value' => 0]])];
        $form = Form::make_post_form('状态修改', $f, Url::buildUrl('change_field', array('id' => $id, 'field' => 'status')));
        $this->assign(compact('form'));
        return $this->fetch('public/form-builder');
    }

    public function issue_log($id = '')
    {
        if (!$id) return JsonService::fail('参数有误!');
        $this->assign(StoreCouponIssueUser::systemCouponIssuePage($id));
        return $this->fetch();
    }



    /**
     * 选择用户
     * @param int $id
     */
    public function select_user()
    {
        return $this->fetch();
    }


    /**
     * 店员添加
     * @param int $id
     * @return string
     */
    public function gift_user_coupon($id)
    {
        $field = [
            Form::frameImageOne('image', '要赠送优惠券的用户', Url::buildUrl('ump.storeCouponIssue/select_user', array('fodder' => 'image')))->icon('plus')->width('100%')->height('500px'),
            Form::hidden('uid', 0),
            Form::hidden('id', $id),
        ];
        $form = Form::make_post_form('添加评论', $field, Url::buildUrl('grant'), 2);
        $this->assign(compact('form'));
        return $this->fetch('public/form-builder');
    }


    /**
     * @param $id
     */
    public function grant()
    {
        $data = Util::getMore([
            ['id', 0],
            ['uid', 0],
        ]);
        if (!$data['id']) return JsonService::fail('数据不存在!');
        $coupon = CouponModel::get($data['id'])->toArray();
        if (!$coupon) return JsonService::fail('数据不存在!');
        $user = explode(',', $data['uid']);
        if (!CouponUserModel::setCoupon($coupon, $user))
            return JsonService::fail(CouponUserModel::getErrorInfo('发放失败,请稍候再试!'));
        else
            return JsonService::successful('发放成功!');
    }
}