<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2021-06-23 14:06:49
 * @Last Modified time: 2021-08-16 13:42:31
 */
namespace app\admin\controller\ump;

use app\admin\controller\AuthController;
use think\facade\Route as Url;
use app\admin\model\ump\{
    StoreCustomCouponList as CustomCouponListModel, 
    StoreCustomCoupon as CustomCouponModel
};
use crmeb\services\{FormBuilder as Form, UtilService as Util, JsonService as Json};

/**
 * 卡券控制器
 * Class StoreCustomCoupon
 * @package app\admin\controller\ump
 */
class StoreCustomCoupon extends AuthController
{

    /**
     * @return mixed
     */
    public function index()
    {
        $this->assign([
            'status' => $this->request->param('status', ''),
        ]);
        return $this->fetch();
    }


    /**
     * 获取卡券列表
     * return json
     */
    public function coupon_list()
    {
        $where = Util::getMore([
            ['status', ''],
            ['keywords', ''],
            ['page', 1],
            ['limit', 20]
        ], $this->request);
        return Json::successlayui(CustomCouponModel::getCouponList($where));
    }

    /**
     * @return mixed
     */
    public function create()
    {
        $field = [
            Form::input('title', '名称'),
            Form::number('number', '发放个数'),
            Form::date('valid_time', '有效时间'),
            Form::textarea('remarks', '备注')
        ];
        $form = Form::make_post_form('添加卡券', $field, Url::buildUrl('save'), 2);
        $this->assign(compact('form'));
        return $this->fetch('public/form-builder');
    }

    /**
     * 保存
     */
    public function save()
    {
        $data = Util::postMore([
            'title',
            'remarks',
            'valid_time',
            ['number', 0]
        ]);
        if (!$data['title']) return Json::fail('请输入卡券名称');
        if (!$data['valid_time']) return Json::fail('请输入卡券有效期限');
        if (!$data['number'] && $data['number'] == 0) return Json::fail('请输入卡券发放个数');
        $number = $data['number'];
        $data['status'] = 1;
        $data['total_count'] = $number;
        $data['remain_count'] = $number;
        $data['add_time'] = time();
        $data['valid_time'] = strtotime($data['valid_time']);
        //按照发放个数，生成唯一卡券
        unset($data['number']);
        $res =  CustomCouponModel::create($data);
        if (!$res) {
            return Json::fail('添加失败');
        }
        $coupons = [];
        for ($i=0; $i < $number; $i++) { 
           $coupons[$i]['cid'] = $res['id'];
           $coupons[$i]['code'] = make_coupon_card();
           $coupons[$i]['pwd'] = md5(make_coupon_card());
           $coupons[$i]['end_time'] = $data['valid_time'];
           $coupons[$i]['add_time'] = time();
        }
        CustomCouponListModel::insertAll($coupons);
        return Json::successful('添加卡券成功!');
    }

    /**
     * 显示编辑资源表单页.
     * @param $id
     * @return string|void
     * @throws \FormBuilder\exception\FormBuilderException
     */
    public function edit($id)
    {
        $code = CustomCouponModel::get($id);
        if (!$code) return Json::fail('数据不存在!');
        $f = [];
        $f[] = Form::input('title', '名称', $code->getData('title'));
        $f[] = Form::date('valid_time', '有效期限', date('Y-m-d',$code->getData('valid_time')));
        $f[] = Form::textarea('remarks', '备注', $code->getData('remarks'));
        $f[] = Form::radio('status', '状态', $code->getData('status'))->options([['label' => '开启', 'value' => 1], ['label' => '关闭', 'value' => 0]]);
        $form = Form::make_post_form('编辑卡券', $f, Url::buildUrl('update', array('id' => $id)));
        $this->assign(compact('form'));
        return $this->fetch('public/form-builder');
    }


    /**
     * 保存更新的资源
     *
     * @param $id
     */
    public function update($id)
    {
        $data = Util::postMore([
            'title',
            'remarks',
            'valid_time',
        ]);
        if (!$data['title']) return Json::fail('请输入卡券名称');
        if (!$data['valid_time']) return Json::fail('请输入卡券有效期限');
        CustomCouponModel::edit($data, $id);
        return Json::successful('修改成功!');
    }

    /**
     * 保存更新的资源
     *
     * @param $id
     */
    public function export($id)
    {
        $res = CustomCouponModel::exportCode($id);
        if ($res)
            return Json::successful('导出成功');
        else
            return Json::fail('导出失败');
        
    }


    /**
     * 删除指定资源
     *
     * @param int $id
     * @return \think\Response
     */
    public function delete($id)
    {
        if (!$id) return Json::fail('数据不存在!');
        $data['is_del'] = 1;
        if (!CustomCouponModel::editIsDel($id,1))
            return Json::fail(CustomCouponModel::getErrorInfo('删除失败,请稍候再试!'));
        else
            return Json::successful('删除成功!');
    }
}
