<?php

namespace app\admin\controller\ump;

use app\admin\model\special\Special;
use app\admin\model\store\StoreCategory;
use app\admin\controller\AuthController;
use app\admin\model\special\SpecialActivityInvitationRecord;
use app\admin\model\special\SpecialActivity as SpecialActivityModel;
use crmeb\services\{
  FormBuilder as Form,
  UtilService,
  JsonService as Json
};
use app\admin\model\ump\{StoreCoupon as CouponModel, StoreCouponIssue};

/**
 * 限时活动  控制器
 * Class StoreSpecial
 * @package app\admin\controller\ump
 */
class StoreSpecial extends AuthController
{
  protected $bindModel = SpecialActivityModel::class;

  /**
   * 显示资源列表
   *
   * @return \think\Response
   */
  public function index()
  {
    return $this->fetch();
  }

  /**
   * 异步获取砍价数据
   */
  public function get_seckill_list()
  {
    $where = UtilService::getMore([
      ['page', 1],
      ['limit', 20],
      ['status', ''],
      ['store_name', '']
    ]);
    $seckillList = SpecialActivityModel::systemPage($where);
    if (is_object($seckillList['list'])) $seckillList['list'] = $seckillList['list']->toArray();
    $data = $seckillList['list']['data'];
    foreach ($data as $k => $v) {
      $start_time = $v['start_time'] ? date('Y/m/d H:i', $v['start_time']) : '';
      $data[$k]['_start_time'] = $start_time;
      $end_time = $v['stop_time'] ? date('Y/m/d H:i', $v['stop_time']) : '';
      $data[$k]['_stop_time'] = $end_time;
    }
    return Json::successlayui(['count' => $seckillList['list']['total'], 'data' => $data]);
  }

  /**
   * 添加活动活动课程
   * @return form-builder
   */
  public function create($id = 0)
  {
    if ($id) {
      $special_info = SpecialActivityModel::where('id', $id)->find();
      if (!$special_info) {
        return Json::fail('修改的活动不存在');
      }
      // 活动时间 
      $special_ids = $special_info['participate_user_source_id'] ? json_decode($special_info['participate_user_source_id']) : [];
      $special_list = Special::alias('a')->field('a.id,a.title as title,a.image')->whereIn('a.id', $special_ids)->select()->toArray();
      foreach ($special_list as $k => $special) {
        $special_list[$k]['sort'] = 0;
        $special_list[$k]['LAY_CHECKED'] = true;
        $special_list[$k]['LAY_TABLE_INDEX'] = 0;
        $special_list[$k]['file_type'] = 1;
      }
      $buy_special_ids = $special_info['purchased_special_id'] ? explode(",", $special_info['purchased_special_id']) : [];
      $buy_special_list = Special::alias('a')->field('a.id,a.title as title,a.image')->whereIn('a.id', $buy_special_ids)->select()->toArray();
      foreach ($special_list as $k => $special) {
        $buy_special_list[$k]['sort'] = 0;
        $buy_special_list[$k]['LAY_CHECKED'] = true;
        $buy_special_list[$k]['LAY_TABLE_INDEX'] = 0;
        $buy_special_list[$k]['file_type'] = 1;
      }
      $specialCheckList = $special_list ? $special_list : [];
      $specialBuyCheckList = $buy_special_list ? $buy_special_list : [];
      $this->assign('specialCheckList', json_encode($specialCheckList));
      $this->assign('specialBuyCheckList', json_encode($specialBuyCheckList));
    }
    $this->assign('id', (int)$id);
    return $this->fetch();
  }

  /**
   * 保存活动活动课程
   * @param int $id
   */
  public function save($id = 0)
  {
    $data = UtilService::postMore([
      ['type', 0],
      'title',
      'image',
      'live_id',
      'special_id',
      'new_user_coupon_id',
      'info',
      'description',
      'join_condition_text',
      'kf_info',
      'share_info',
      'activity_start_end',
      ['check_special_sure', ''],
      ['check_buy_special_sure', ''],
      ['award_num', 0],
      ['receive_condition', 0],
      ['couponArr', []],
      ['sort', 0],
      ['status', 0],
      ['live_status', 0],
      ['is_receive', 1],
    ]);
    if (!$data['title']) return Json::fail('请输入活动课程标题');
    if ($data['type'] == 0 && !$data['special_id']) return Json::fail('目标促销课程ID：不能为空');
    if ($data['type'] == 1 && !$data['special_id']) return Json::fail('直播促销课程ID：不能为空');
    if ($data['receive_condition'] == 3 && empty($data['check_buy_special_sure'])) return Json::fail('已购指定课来源：不能为空');
    if ($data['type'] == 1 && !$data['new_user_coupon_id']) return Json::fail('指定用户领取优惠券ID：不能为空');
    $data['activity_start_end'] = is_string($data['activity_start_end']) ? explode('/', $data['activity_start_end']) : $data['activity_start_end'];
    $data['start_time'] = strtotime($data['activity_start_end'][0]);
    $data['stop_time'] = strtotime($data['activity_start_end'][1]);
    $data['participate_user_source_id'] =  json_encode(array_column($data['check_special_sure'], 'id'));
    $data['purchased_special_id'] =  implode(',', array_column($data['check_buy_special_sure'], 'id'));
    $data['reward_coupon_list'] =  json_encode(array_column($data['couponArr'], 'id'));
    unset($data['activity_start_end'], $data['check_special_sure'], $data['check_buy_special_sure'], $data['couponArr']);
    if ($id) {
      SpecialActivityModel::edit($data, $id);
      return Json::successful('编辑成功!');
    } else {
      if (SpecialActivityModel::be(['type' => $data['type'], 'special_id' => $data['special_id'], 'status' => 1, 'is_show' => 1, 'is_del' => 0])) return Json::fail('添加失败，存在相同的目标课程活动');
      $data['add_time'] = time();
      $res = SpecialActivityModel::create($data);
      return Json::successful('添加成功!');
    }
  }


  /**
   * 获取活动详细信息
   * @param int $id
   * @throws \think\db\exception\DataNotFoundException
   * @throws \think\db\exception\DbException
   * @throws \think\db\exception\ModelNotFoundException
   */
  public function get_activity_info($id = 0)
  {
    $menus = [];
    $data['cateList'] = $menus;
    if ($id) {
      $special_info = SpecialActivityModel::where('id', $id)->find();
      if (!$special_info) {
        return Json::fail('修改的活动不存在');
      }
      if ($special_info['special_id'] != 0) {
        $special_info['special_image'] = Special::where('id', $special_info['special_id'])->value('image');
      }
      if ($special_info['new_user_coupon_id'] != 0) {
        $name  = StoreCouponIssue::alias('a')->join('store_coupon b', 'b.id=a.cid')->where('a.id', $special_info['new_user_coupon_id'])->value('b.title');
        $special_info['new_user_coupon_name'] = $name ? $name : '';
        $special_info['new_user_coupon_image'] = 'https://cshop.arthorize.com/attach/2021/01/e2d63202101111057244240.png';
      }
      // 活动时间 
      $special_info['activity_start_end'] = date('Y-m-d H:i:s', $special_info['start_time']) . ' / ' . date('Y-m-d H:i:s', $special_info['stop_time']);
      $special_ids = $special_info['participate_user_source_id'] ? json_decode($special_info['participate_user_source_id']) : [];
      $special_list = Special::alias('a')->field('a.id,a.title,a.image')->whereIn('a.id', $special_ids)->select()->toArray();
      $specialCheckList = $special_list ? $special_list : [];
      $special_info['special_tmp_list'] = $specialCheckList;
      $special_info['check_special_sure'] = $specialCheckList;
      // 领取目标课 
      $special_buy_ids = $special_info['receive_condition'] == 3 && $special_info['purchased_special_id'] ? explode(",", $special_info['purchased_special_id']) : [];
      $special_list = Special::alias('a')->field('a.id,a.title,a.image')->whereIn('a.id', $special_buy_ids)->select()->toArray();
      $specialBuyCheckList = $special_list ? $special_list : [];
      $special_info['special_buy_tmp_list'] = $specialBuyCheckList;
      $special_info['check_buy_special_sure'] = $specialBuyCheckList;
      // 老用户累计奖励
      $special_info['couponArr'] = [];
      if ($special_info['award_num'] != 0) {
        $reward_coupon_ids = $special_info['reward_coupon_list'] ? json_decode($special_info['reward_coupon_list']) : [];
        $coupon_list = [];
        foreach ($reward_coupon_ids as $k => $reward_coupon_id) {
          $coupon_list[$k]['id'] = $reward_coupon_id;
          $coupon_list[$k]['content'] = StoreCouponIssue::alias('a')->join('store_coupon b', 'b.id=a.cid')->where('a.id', $reward_coupon_id)->value('b.title');
          $coupon_list[$k]['source_image'] = 'https://cshop.arthorize.com/attach/2021/01/e2d63202101111057244240.png';
        }
        $special_info['couponArr'] = $coupon_list;
      }

      $data['activityInfo'] = $special_info;
    }
    return Json::successful($data);
  }



  /**
   * 设置资源上架|下架
   * @param string $is_show
   * @param string $id
   */
  public function set_status($status = '', $id = '')
  {
    ($status == '' || $id == '') && Json::fail('缺少参数');
    if (SpecialActivityModel::setSpecialActivityStatus($id, (int)$status)) {
      return Json::successful($status == 1 ? '开启成功' : '关闭成功');
    } else {
      return Json::fail(BannerModel::getErrorInfo($status == 1 ? '开启失败' : '关闭失败'));
    }
  }


  /**
   * 活动属性选择页面
   * @param $id
   * @return string|void
   * @throws \think\db\exception\DataNotFoundException
   * @throws \think\db\exception\DbException
   * @throws \think\db\exception\ModelNotFoundException
   */
  public function roster($id)
  {
    $this->assign('id', $id);
    return $this->fetch();
  }


  /*
     * 异步获取参与活动参与记录列表
     * @param int $vip_id 会员id
     * @param int $page 分页
     * @param int $limit 显示条数
     * @return json
     * */
  public function get_roster_list($id = 0)
  {

    $where['id'] = $id;
    list($page, $limit, $keywords) = UtilService::getMore([
      ['page', 1],
      ['limit', 10],
      ['keywords', '']
    ], $this->request, true);
    $where['keywords'] = $keywords;
    return Json::successlayui(SpecialActivityInvitationRecord::getRecordList($where, (int)$page, (int)$limit));
  }
  /**
   * 删除指定资源
   *
   * @param int $id
   * @return \think\Response
   */
  public function delete($id)
  {
    if (!$id) return $this->failed('数据不存在');
    $product = SpecialActivityModel::get($id);
    if (!$product) return Json::fail('数据不存在!');
    if ($product['is_del']) return Json::fail('已删除!');
    $data['is_del'] = 1;
    if (!SpecialActivityModel::edit($data, $id))
      return Json::fail(SpecialActivityModel::getErrorInfo('删除失败,请稍候再试!'));
    else
      return Json::successful('删除成功!');
  }


  /**
   * 获取课程推荐列表
   * @return string
   * @throws \Exception
   */
  public function specialList()
  {
    return $this->fetch();
  }

  /**
   * 获取课程推荐列表
   * @return string
   * @throws \Exception
   */
  public function userList()
  {
    return $this->fetch();
  }


  /**
   * 获取课程推荐列表
   * @return string
   * @throws \Exception
   */
  public function couponList()
  {
    return $this->fetch();
  }


  /**
   * 获取课程推荐列表
   * @return string
   * @throws \Exception
   */
  public function newUserCouponList()
  {
    return $this->fetch();
  }

  /**
   * 获取课程推荐列表
   * @return string
   * @throws \Exception
   */
  public function findUserCouponList()
  {
    return $this->fetch();
  }
}
