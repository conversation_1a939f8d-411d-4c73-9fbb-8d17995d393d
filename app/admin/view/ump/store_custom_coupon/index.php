{extend name="public/container"}
{block name="head_top"}

{/block}
{block name="content"}
<style>
    .btn-outline{
        border:none;
    }
    .btn-outline:hover{
        background-color: #0e9aef;
        color: #fff;
    }
    .layui-form-item .layui-btn {
        margin-top: 5px;
        margin-right: 10px;
    }
    .layui-btn-primary{
        margin-right: 10px;
        margin-left: 0!important;
    }
    label{
        margin-bottom: 0!important;
        margin-top: 4px;
    }
</style>
<div class="layui-fluid">
    <div class="layui-row layui-col-space15" id="app">
        <!--搜索条件-->
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">搜索条件</div>
                <div class="layui-card-body">
                    <div class="layui-carousel layadmin-carousel layadmin-shortcut" lay-anim="" lay-indicator="inside"
                         lay-arrow="none" style="background:none">
                        <div class="layui-card-body">
                            <div class="layui-row layui-col-space10 layui-form-item">
                                <div class="layui-col-lg12">
                                    <label class="layui-form-label">关键字:</label>
                                    <div class="layui-input-block">
                                        <input type="text" name="keywords" style="width: 50%" v-model="where.keywords"
                                               placeholder="请输入卡券中关键字信息" class="layui-input">
                                    </div>
                                </div>
                                <div class="layui-col-lg12">
                                    <div class="layui-input-block">
                                        <button @click="search" type="button"
                                                class="layui-btn layui-btn-sm layui-btn-normal">
                                            <i class="layui-icon layui-icon-search"></i>搜索
                                        </button>
                                        <button @click="refresh" type="reset"
                                                class="layui-btn layui-btn-primary layui-btn-sm">
                                            <i class="layui-icon layui-icon-refresh"></i>刷新
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--列表-->
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">卡券列表</div>
                <div class="layui-card-body">
                    <div class="layui-btn-container" id="container-action">
                        <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" onclick="$eb.createModalFrame(this.innerText,'{:Url('create')}?type=0')">添加卡券</button>
                    </div>
                    <table class="layui-hide" id="List" lay-filter="List"></table>
                    <!--类型-->
                    <script type="text/html" id="type_name">
                        {{d.type_name}}
                    </script>
                    <!-- 发布数量 -->
                    <script type="text/html" id="number">
                        <b style="color: #0a6aa1">发布:{{d.total_count}}</b>
                        <br/>
                        <b style="color:#ff0000;">剩余:{{d.remain_count}}</b>
                    </script>
                    <!-- 操作 -->
                    <script type="text/html" id="act">
                        <button type="button" class="layui-btn layui-btn-xs" onclick="dropdown(this)">操作 <span class="caret"></span></button>
                        <ul class="layui-nav-child layui-anim layui-anim-upbit">
                            {{# if( d.status == 1 |  d.is_del == 0 ){ }}
                                {{# if( d.remain_count > 0 ){ }}
                                    <li>
                                        <a href="javascript:void(0);" lay-event='export'>
                                            <i class="fa fa-floppy-o"></i> 导出
                                        </a>
                                    </li>
                                {{# } }}
                                <li>
                                    <a href="javascript:void(0);" lay-event='deleted'>
                                        <i class="fa fa-edit"></i> 删除
                                    </a>
                                </li>
                            {{# } }}
                        </ul>
                    </script>
                </div>
            </div>
        </div>
    </div>
    <!--end-->
</div>
<script src="{__ADMIN_PATH}js/layuiList.js"></script>
{/block}
{block name="script"}
<script>
    layList.tableList('List', "{:Url('coupon_list')}", function () {
        return [
            {type: 'checkbox'},
            {field: 'id', title: 'ID',width: '10%',align: 'center'},
            {field: 'title', title: '名称', width: '15%', align: 'center'},
            {field: 'number', title: '发布信息', width: '10%',align: 'center',templet: "#number"},
            {field: 'status_name', title: '状态', width: '10%', align: 'center'},
            {field: 'add_time', title: '创建时间', sort: true, align: 'center'},
            {field: 'right', title: '操作', align: 'center', toolbar: '#act'},
        ];
    });
    layList.tool(function (event, data, obj) {
        switch (event) {
            case 'deleted':
                var url = layList.U({c: 'ump.storeCustomCoupon', a: 'delete', p: {id: data.id}});
                $eb.$swal('delete', function () {
                    $eb.axios.get(url).then(function (res) {
                        if (res.status == 200 && res.data.code == 200) {
                            $eb.$swal('success', res.data.msg);
                        } else
                            return Promise.reject(res.data.msg || '屏蔽失败')
                    }).catch(function (err) {
                        $eb.$swal('error', err);
                    });
                }, {'title': '您确定要删除卡券吗？', 'text': '删除后用户将无法兑换激活,请谨慎操作！', 'confirm': '是的，我要删除'})
                break;
            case 'export':
                location.href = layList.U({c: 'ump.storeCustomCoupon', a: 'export', p: {id: data.id}});
                break;
        }
    })
    $('#container-action').find('button').each(function () {
        $(this).on('click', function () {
            var act = $(this).data('type');
            action[act] && action[act]();
        });
    })
    //下拉框
    $(document).click(function (e) {    
        $('.layui-nav-child').hide();
    })
    //自定义方法
    var action={
        refresh:function () {
            layList.reload();
        }
    };

    function dropdown(that) {
        var oEvent = arguments.callee.caller.arguments[0] || event;
        oEvent.stopPropagation();
        var offset = $(that).offset();
        var top = offset.top - $(window).scrollTop();
        var index = $(that).parents('tr').data('index');
        $('.layui-nav-child').each(function (key) {
            if (key != index) {
                $(this).hide();
            }
        })
        if ($(document).height() < top + $(that).next('ul').height()) {
            $(that).next('ul').css({
                'padding': 10,
                'top': -($(that).parents('td').height() / 2 + $(that).height() + $(that).next('ul').height() / 2),
                'min-width': 'inherit',
                'position': 'absolute'
            }).toggle();
        } else {
            $(that).next('ul').css({
                'padding': 10,
                'top': $(that).parents('td').height() / 2 + $(that).height(),
                'min-width': 'inherit',
                'position': 'absolute'
            }).toggle();
        }
    }
    var  status =<?=$status ? $status : "''"?>;
    require(['vue'], function (Vue) {
        new Vue({
            el: "#app",
            data: {
                where: {
                    keywords: '',
                },
                showtime: false,
            },
            watch: {
                'where.keywords': function () {
                    layList.reload(this.where, true);
                }
            },
            methods: {
                search: function () {       
                    layList.reload(this.where, true);
                },
                refresh: function () {
                    layList.reload();
                },
                init: function () {
                var that = this;
                that.$nextTick(function () {
                    layui.use(['form','element'], function () {
                        that.form = layui.form;
                        that.form.render();
                        layui.element.on('tab(docTabBrief)', function(){
                            that.layTabId = this.getAttribute('lay-id');
                        });
                    });
                })
            },
            },
            mounted: function () {
                this.init();
                var that = this;
                window.formReload = this.search;
                layList.laydate.render({
                    elem: this.$refs.date_time,
                    trigger: 'click',
                    eventElem: this.$refs.time,
                    range: true,
                    change: function (value) {
                        that.where.data = value;
                    }
                });
            }
        })
    });
</script>
{/block}