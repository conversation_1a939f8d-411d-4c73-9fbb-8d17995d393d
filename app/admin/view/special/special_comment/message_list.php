{extend name="public/container"}
{block name="content"}
<style type="text/css">
    .form-add{position: fixed;left: 0;bottom: 0;width:100%;}
    .form-add .sub-btn{border-radius: 0;width: 100%;padding: 6px 0;font-size: 14px;outline: none;border: none;color: #fff;background-color: #2d8cf0;}
</style>
<div class="layui-fluid">
    <div class="layui-row layui-col-space15"  id="app">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">搜索条件</div>
                <div class="layui-card-body">
                    <form class="layui-form layui-form-pane" action="">
                        <div class="layui-inline">
                            <label class="layui-form-label">姓名编号</label>
                            <div class="layui-input-block">
                                <input type="text" name="keywords" class="layui-input" placeholder="请输入留言名称，地址等关键字">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <div class="layui-input-inline">
                                <button class="layui-btn layui-btn-sm layui-btn-normal" lay-submit="search" lay-filter="search">
                                    <i class="layui-icon layui-icon-search"></i>搜索</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-body">
                    <table class="layui-hide" id="List" lay-filter="List"></table>
                    <!--图片-->
                    <script type="text/html" id="image">
                        <img style="cursor: pointer" lay-event="open_image" src="{{d.avatar}}">
                    </script>
                   <script type="text/html" id="userinfo">
                        <div class="layui-table-cell laytable-cell-1-0-1"> <img style="cursor: pointer" lay-event="open_image" src="{{ d.avatar }}"> </div>
                        {{d.nickname==null ? '暂无信息':d.nickname}}/{{d.uid}}
                    </script>
                    <!--精华-->
                    <script type="text/html" id="checkrefining">
                        {{d.is_refining== 1  ? '是': '否'}}
                    </script>
                    <script type="text/html" id="status">
                        {{d.status_name}}
                    </script>
                    <!--操作-->
                    <script type="text/html" id="act">
                        <button type="button" class="layui-btn layui-btn-normal layui-btn-sm select" lay-event='select'>选择</button>
                    </script>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="{__ADMIN_PATH}js/layuiList.js"></script>
{/block}
{block name='script'}
<script>
    var parentinputname = '{$Request.param.fodder}';
    layList.form.render();
    //加载列表
    layList.tableList('List',"{:Url('special.specialMessage/message_list',['type'=>1])}",function (){
        return [
            {field: 'id', title: 'ID',width: '5%',align: 'center'},
            {field: 'type_name', title: '类型', width: '6%', align: 'center'},
            {field: 'user', title: '用户', templet: '#userinfo', width: '10%', align: 'center'},
            {field: 'title', title: '标题', width: '15%', align: 'center'},
            {field: 'browse', title: '浏览次数', width: '8%', align: 'center'},
            {field: 'is_refining', title: '是否精华',templet:"#checkrefining",width:'9%',align: 'center'},
            {field: 'status', title: '状态', templet: '#status', width: '10%', align: 'center'},
            {field: 'right', title: '操作',align:'center',toolbar:'#act'}
        ]
    });
    //点击事件绑定
    layList.tool(function (event,data) {
        switch (event) {
            case 'select':
                parent.$f.changeField('message',data.message_cover);
                parent.$f.changeField('message_id',data.id);
                parent.$f.changeField('special_id',data.special_id);
                parent.$f.changeField('source_id',data.source_id);
                parent.$f.closeModal(parentinputname);
                break;
        }
    })
    //查询
    layList.search('search',function(where){
        layList.reload(where);
    });
</script>
{/block}