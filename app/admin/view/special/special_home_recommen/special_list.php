{extend name="public/container"}
{block name="content"}
<style type="text/css">
    .form-add{position: fixed;left: 0;bottom: 0;width:100%;}
    .form-add .sub-btn{border-radius: 0;width: 100%;padding: 6px 0;font-size: 14px;outline: none;border: none;color: #fff;background-color: #2d8cf0;}
</style>
<div class="layui-fluid">
    <div class="layui-row layui-col-space15"  id="app">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">搜索条件</div>
                <div class="layui-card-body">
                    <form class="layui-form layui-form-pane" action="">
                        <div class="layui-inline">
                            <label class="layui-form-label">课程专题名称</label>
                            <div class="layui-input-block">
                                <input type="text" name="store_name" class="layui-input" placeholder="请输入课程专题名称,关键字" style="width: 120%;">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <div class="layui-input-inline">
                                <button class="layui-btn layui-btn-sm layui-btn-normal" lay-submit="search" lay-filter="search" style="margin-left: 32px;">
                                    <i class="layui-icon layui-icon-search"></i>搜索</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-body">
                    <table class="layui-hide" id="List" lay-filter="List"></table>
                    <!--图片-->
                    <script type="text/html" id="image">
                        <img style="cursor: pointer" lay-event="open_image" src="{{d.image}}">
                    </script>
                    <!--类型-->
                    <script type="text/html" id="type">
                       {{# if(d.type == 1){ }}
                            <span class="layui-badge layui-bg-blue">图文专题</span>
                        {{# }else if(d.type == 2){ }}
                            <span class="layui-badge layui-bg-blue">视频专题</span>
                        {{# }else if(d.type == 3){ }}
                            <span class="layui-badge layui-bg-blue">音频专题</span>
                        {{# } }}
                    </script>
                    <!--操作-->
                    <script type="text/html" id="act">
                        <button type="button" class="layui-btn layui-btn-normal layui-btn-sm select" lay-event='select'>选择</button>
                    </script>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="{__ADMIN_PATH}js/layuiList.js"></script>
{/block}
{block name='script'}
<script>
    var parentinputname = '{$Request.param.fodder}';
    layList.form.render();
    //加载列表
    layList.tableList('List',"{:Url('special.special_type/all_ist')}",function (){
        return [
            {field: 'id', title: 'ID', sort: true,event:'id',width:'8%'},
            {field: 'type', title: '类型',templet:'#type',width:'12%'},
            {field: 'image', title: '课程封面图',templet:'#image',width:'12%'},
            {field: 'title', title: '标题',templet:'#store_name',width:'40%'},
            {field: 'right', title: '操作',align:'center',toolbar:'#act'}
        ]
    });
    //点击事件绑定
    layList.tool(function (event,data) {
        switch (event) {
            case 'select':
                parent.getSelectSpecial(data);    
                parent.layer.close(parent.layer.getFrameIndex(window.name));  
        }
    })
    //查询
    layList.search('search',function(where){
        layList.reload(where);
    });
</script>
{/block}