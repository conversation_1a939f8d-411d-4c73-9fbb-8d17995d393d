{extend name="public/container"}
{block name="content"}
<div class="layui-fluid">
    <div class="layui-row layui-col-space15"  id="app">
        <div class="layui-col-md15">
            <div class="layui-card">
                <div class="layui-card-header">搜索条件</div>
                <div class="layui-card-body">
                    <form class="layui-form layui-form-pane" action="">
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">渠道</label>
                                <div class="layui-input-block">
                                    <select name="type">
                                        <option value="">全部</option>
                                            <option value="1">视频号</option>
                                            <!-- <option value="2">抖音</option> -->
                                            <option value="3">小红书</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">商品名</label>
                                <div class="layui-input-block">
                                    <input type="text" name="title" class="layui-input" placeholder="请输入商品名">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">渠道商品Id</label>
                                <div class="layui-input-block">
                                    <input type="text" name="keywords" class="layui-input" placeholder="请输入skuid或商品id">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">选择时间：</label>
                                <div class="layui-input-inline">
                                    <input type="text" class="layui-input time-w" name="add_time" lay-verify="add_time"  id="add_time" placeholder="时间范围">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <div class="layui-input-inline">
                                    <button class="layui-btn layui-btn-sm layui-btn-normal" lay-submit="search" lay-filter="search">
                                        <i class="layui-icon layui-icon-search"></i>搜索</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <!--产品列表-->
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">渠道课程商品配置</div>
                <div class="layui-card-body">
                	<div class="layui-btn-container">
	                    <button type="button" class="layui-btn layui-btn-sm" onclick="$eb.createModalFrame(this.innerText,'{:Url('create')}')">添加渠道商品</button>
	                    <button type="button" class="layui-btn layui-btn-sm" onclick="$eb.createModalFrame(this.innerText,'{:Url('create_sku')}')">查找skuId</button>
	                </div>
                    <table class="layui-hide" id="List" lay-filter="List"></table>
                    <script type="text/html" id="productInfo">
                        {{d.title==null ? '':d.title}}
                    </script>
                    <script type="text/html" id="act">
	                    <button class="layui-btn btn-danger layui-btn-xs" lay-event='delstor'>
	                        <i class="fa fa-times"></i> 删除
	                    </button>
                    </script>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="{__ADMIN_PATH}js/layuiList.js"></script>
{/block}
{block name="script"}
<script>
    setTimeout(function () {
        $('.alert-info').hide();
    },3000);
    //实例化form
    layList.form.render();
    //加载列表
    layList.tableList('List',"{:Url('channel_product_list')}",function (){
        return [
            {field: 'id', title: 'Id', sort: true,event:'id',width:'5%',align:'center'},
            {field: 'type', title: '渠道',align:'center',width:'5%'},
            {field: 'title', title: '商品名',align:'center',width:'20%'},
            {field: 'product_id', title: '着调儿Id', width: '5%', align: 'center'},
            {field: 'channel_product_id', title: '渠道商品Id',align:'center',width:'20%'},
            {field: 'channel_product_sku_id', title: '渠道商品skuId', width: '20%', align: 'center'},
            {field: 'add_time', title: '添加时间',align: 'center',width:'15%'},
            {field: 'right', title: '操作',align:'center',toolbar:'#act'},
        ];
    });
    //查询
    layList.search('search',function(where){
        layList.reload(where,true);
    });
    layList.date('add_time');
    //监听并执行排序
    layList.sort(['id','sort'],true);
    //点击事件绑定
    layList.tool(function (event,data,obj) {
        switch (event) {
            case 'delstor':
                var url=layList.U({c:'special.channel_product',a:'delete',q:{id:data.id}});
                $eb.$swal('delete',function(){
                    $eb.axios.get(url).then(function(res){
                        if(res.status == 200 && res.data.code == 200) {
                            $eb.$swal('success',res.data.msg);
                            obj.del();
                        }else
                            return Promise.reject(res.data.msg || '删除失败')
                    }).catch(function(err){
                        $eb.$swal('error',err);
                    });
                })
                break;
            case 'open_image':
                $eb.openImage(data.pic);
                break;
        }
    })
</script>
{/block}