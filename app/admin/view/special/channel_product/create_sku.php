{extend name="public/container"}
{block name="content"}
<div class="layui-fluid">
    <div class="layui-row layui-col-space15"  id="app">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">查找skuId</div>
                <div class="layui-card-body">
                    渠道:
                    <div class="layui-inline">
                        <select name="status"  class="form-control input-sm" id="demoReloadType">
                            <option value="">请选择渠道</option>
                            <option value="1">视频号</option>
                            <!-- <option value="2">抖音</option> -->
                            <option value="3">小红书</option>
                        </select>
                    </div>
                    商品ID：
                    <div class="layui-inline">
                        <input class="layui-input" name="channel_product_id" id="demoReload" placeholder="商品Id从渠道获取">
                    </div>
                    <button class="layui-btn layui-btn-normal layui-btn-sm" data-type="reload">搜索</button>
                </div>
            </div>
        </div>
        <!--产品列表-->
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">渠道商品列表</div>
                <div class="layui-card-body">
                    <div class="layui-btn-container">
                        <button class="layui-btn layui-btn-normal layui-btn-sm" onclick="window.location.reload()"><i class="layui-icon layui-icon-refresh"></i> 刷新</button>
                    </div>
                    <table class="layui-hide" id="List" lay-filter="List"></table>
                    <script type="text/html" id="image">
                        <img style="cursor: pointer;width: 80px;height: 40px;" lay-event='open_image' src="{{d.image}}">
                    </script>
                    <script type="text/html" id="skus">
                        {{#  layui.each(d.skus, function(index, item){ }}
                        <div>
                            <span>
                                <img style="width: 30px;height: 30px;margin:0;cursor: pointer;"
                                     src="{{item.thumb_img}}">
                            </span>
                            <span>{{item.sku_id}}&nbsp;</span>
                        </div>
                        {{#  }); }}
                    </script>
                    <!--操作-->
                    <script type="text/html" id="act">
                        <button type="button" class="layui-btn layui-btn-normal layui-btn-sm" data-type='copy'>复制</button>
                    </script>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="{__ADMIN_PATH}js/layuiList_v3.js"></script>
{/block}
{block name="script"}
<script>
    var related_id = <?=isset($id) ? $id : ""?>;
    var table_date=new Array();//用于保存当前页数据
    var ids=new Array();    //用于保存选中的数据
    //实例化form
    layList.form.render();
    var table = layui.table;
    table.render({
        elem: '#List'
        ,url:"{:Url('channel_product_sku_list')}"
        ,toolbar: '#toolbarDemo' //开启头部工具栏，并为其绑定左侧模板
        ,defaultToolbar: ['filter', 'exports', 'print', { //自定义头部工具栏右侧图标。如无需自定义，去除该参数即可
            title: '提示'
            ,layEvent: 'LAYTABLE_TIPS'
            ,icon: 'layui-icon-tips'
        }]
        ,cols: [[
            {type: 'checkbox'},
            {field: 'name', title: '渠道商品名'},
            {field: 'id', title: '商品id', sort: true,event:'id'},
            {field: 'skus', title: 'sku组',templet:'#skus'},
            {field: 'right', title: '操作',align:'center',toolbar:'#act'}
        ]]
        ,id: 'testReload'
        ,page: true
        ,done:function (res,curr,count) {
            table_date=res.data;
        }
    });
    var $ = layui.$, active = {
        reload: function(){
            var demoReloadType = $('#demoReloadType');
            var demoReload = $('#demoReload');
            //执行重载
            table.reload('testReload', {
                page: {
                    curr: 1 //重新从第 1 页开始
                }
                ,where: {
                    type: demoReloadType.val(),
                    channel_product_id: demoReload.val(),
                }
            }, 'data');
        }
    };
    $('.layui-btn').on('click', function(){
        var type = $(this).data('type');
        active[type] ? active[type].call(this) : '';
    });
    //点击事件绑定
    layList.tool(function (event,data) {
        alert(1111);
    })
    //自定义方法
    var action= {
        //打开新添加页面
        open_add: function (url,title) {
            layer.open({
                type: 2 //Page层类型
                ,area: ['100%', '100%']
                ,title: title
                ,shade: 0.6 //遮罩透明度
                ,maxmin: true //允许全屏最小化
                ,anim: 1 //0-6的动画形式，-1不开启
                ,content: url
                ,end:function() {
                    location.reload();
                }
            });
        }
    }
</script>
{/block}
