{extend name="public/container"}
{block name="head_top"}

{/block}
{block name="content"}
<style>
    .btn-outline{
        border:none;
    }
    .btn-outline:hover{
        background-color: #0e9aef;
        color: #fff;
    }
    .layui-form-item .layui-btn {
        margin-top: 5px;
        margin-right: 10px;
    }
    .layui-btn-primary{
        margin-right: 10px;
        margin-left: 0!important;
    }
    label{
        margin-bottom: 0!important;
        margin-top: 4px;
    }
</style>
<div class="layui-fluid">
    <div class="layui-row layui-col-space15" id="app">
        <!--搜索条件-->
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">搜索条件</div>
                <div class="layui-card-body">
                    <div class="layui-carousel layadmin-carousel layadmin-shortcut" lay-anim="" lay-indicator="inside"
                         lay-arrow="none" style="background:none">
                        <div class="layui-card-body layui-form">
                            <div class="layui-row layui-col-space10 layui-form-item">
                                <div class="layui-col-lg12">
                                    <label class="layui-form-label">状态:</label>
                                    <div class="layui-input-block" v-cloak="">
                                        <button class="layui-btn layui-btn-sm"
                                                :class="{'layui-btn-primary':where.status!==item.value}"
                                                @click="where.status = item.value" type="button"
                                                v-for="item in status">{{item.name}}
                                            <span v-if="item.count!=undefined"
                                                  :class="item.class!=undefined ? 'layui-badge': 'layui-badge layui-bg-gray' ">{{item.count}}</span>
                                        </button>
                                    </div>
                                </div>
                                <div class="layui-col-lg12">
                                    <label class="layui-form-label">类型:</label>
                                    <div class="layui-input-block" v-cloak="">
                                        <button class="layui-btn layui-btn-sm"
                                                :class="{'layui-btn-primary':where.message_type!==item.value}"
                                                @click="where.message_type = item.value" message_type="button"
                                                v-for="item in messageTypeList">{{item.name}}
                                            <span v-if="item.count!=undefined"
                                                  :class="item.class!=undefined ? 'layui-badge': 'layui-badge layui-bg-gray' ">{{item.count}}</span>
                                        </button>
                                    </div>
                                </div>
                                <div class="layui-col-lg12">
                                    <label class="layui-form-label">内容类型:</label>
                                    <div class="layui-input-block" v-cloak="">
                                        <button class="layui-btn layui-btn-sm"
                                                :class="{'layui-btn-primary':where.type!==item.value}"
                                                @click="where.type = item.value" type="button"
                                                v-for="item in TypeList">{{item.name}}
                                            <span v-if="item.count!=undefined"
                                                  :class="item.class!=undefined ? 'layui-badge': 'layui-badge layui-bg-gray' ">{{item.count}}</span>
                                        </button>
                                    </div>
                                </div>
                                <div class="layui-col-lg12">
                                    <div class="layui-form-item">
                                      <label class="layui-form-label">课程名称:</label>
                                      <div class="layui-input-block" style="width: 496px">
                                           <select name="special_id" v-model="where.special_id" lay-filter="special_id"   lay-search="">
                                                <option value="">全部</option>
                                                {volist name='specialList' id='vo'}
                                                    <option value="{$vo.id}">{$vo.title}</option>
                                                {/volist}
                                            </select>     
                                      </div>
                                    </div>
                                </div>
                                <div class="layui-col-lg12">
                                    <label class="layui-form-label">创建时间:</label>
                                    <div class="layui-input-block" data-type="data" v-cloak="">
                                        <button class="layui-btn layui-btn-sm" type="button" v-for="item in dataList"
                                                @click="setData(item)"
                                                :class="{'layui-btn-primary':where.data!=item.value}">{{item.name}}
                                        </button>
                                        <button class="layui-btn layui-btn-sm" type="button" ref="time"
                                                @click="setData({value:'zd',is_zd:true})"
                                                :class="{'layui-btn-primary':where.data!='zd'}">自定义
                                        </button>
                                        <button type="button" class="layui-btn layui-btn-sm layui-btn-primary"
                                                v-show="showtime==true" ref="date_time">{$year.0} - {$year.1}
                                        </button>
                                    </div>
                                </div>
                                <div class="layui-col-lg12">
                                    <label class="layui-form-label">留言关键字:</label>
                                    <div class="layui-input-block">
                                        <input type="text" name="keywords" style="width: 50%" v-model="where.keywords"
                                               placeholder="请输入留言单名称，地址等关键字" class="layui-input">
                                    </div>
                                </div>
                                <div class="layui-col-lg12">
                                    <div class="layui-input-block">
                                        <button @click="search" type="button"
                                                class="layui-btn layui-btn-sm layui-btn-normal">
                                            <i class="layui-icon layui-icon-search"></i>搜索
                                        </button>
                                        <button @click="refresh" type="reset"
                                                class="layui-btn layui-btn-primary layui-btn-sm">
                                            <i class="layui-icon layui-icon-refresh"></i>刷新
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--列表-->
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">留言列表</div>
                <div class="layui-card-body">
                    <div class="layui-btn-container" id="container-action">
                        <button class="layui-btn layui-btn-sm" class="btn btn-w-m btn-primary" onclick="$eb.createModalFrame(this.innerText,'{:Url('create')}',{w:1100,h:760})">添加虚拟留言</button>
                        <!-- <button class="layui-btn layui-btn-sm" data-type="save_set_status">批量操作</button> -->
                    </div>
                    <table class="layui-hide" id="List" lay-filter="List"></table>
                    <!--用户信息-->
                    <script type="text/html" id="userinfo">
                        <div class="layui-table-cell laytable-cell-1-0-1"> <img style="cursor: pointer" lay-event="open_image" src="{{ d.avatar }}"> </div>
                        {{d.nickname==null ? '暂无信息':d.nickname}}/{{d.uid}}<br>
                        {{# if( d.is_user_get_easter_eggs == 1){ }}
                            已解锁本次课程彩蛋
                        {{# } }}
                    </script>
                    <!--留言状态-->
                    <script type="text/html" id="status">
                        {{d.status_name}}
                    </script>
                    <script type="text/html" id="show_comment">
                        <a href="{:Url('special.specialComment/index')}?pid={{d.id}}">查看</a>
                    </script>
                    <!--显示|隐藏-->
                    <script type="text/html" id="checkrefining">
                        <input type='checkbox' name='id' lay-skin='switch' value="{{d.id}}" lay-filter='is_refining' lay-text='取消精华|设为精华'  {{ d.is_refining == 1 ? 'checked' : '' }}>
                    </script>
                    <script type="text/html" id="act">
                            {{# if( d.is_del == 1 || d.status == 3){ }}
                                <button type="button" class="layui-btn layui-btn-xs layui-btn-normal" lay-event='show'>
                                   <a href="javascript:void(0);"  style="color: #fff;" lay-event='review_restore'>
                                            <i class="fa"></i> 恢复
                                    </a>
                                </button>
                            {{# } }}
                             {{# if( d.status == 2 && d.is_del == 0 ){ }}
                                <button type="button" class="layui-btn layui-btn-xs layui-btn-normal" lay-event='show'>
                                   <a href="javascript:void(0);"  style="color: #fff;" lay-event='review'>
                                            <i class="fa"></i> 审核
                                    </a>
                                </button>
                                {{# if( d.is_special_easter_eggs == 1  ){ }}
                                    <button type="button" class="layui-btn layui-btn-xs layui-btn-normal" lay-event='show'>
                                       <a href="javascript:void(0);"  style="color: #fff;" lay-event='review_egg'>
                                                <i class="fa"></i> 审核通过并解锁彩蛋 
                                        </a>
                                    </button>
                                {{# } }}
                            {{# } }}
                        </button>
                        <button type="button" class="layui-btn layui-btn-xs" onclick="dropdown(this)">操作 <span class="caret"></span></button>
                        <ul class="layui-nav-child layui-anim layui-anim-upbit">
                            <li>
                                <a href="javascript:void(0);" lay-event="message_info">
                                    <i class="fa fa-file-text"></i> 详情
                                </a>
                            </li>
                            <li>
                                <a href="javascript:void(0);" lay-event="edit">
                                    <i class="fa fa-edit"></i> 编辑
                                </a>
                            </li>
                            {{# if( d.message_type == 0){ }}
                                <li>
                                    <a href="javascript:void(0);" lay-event='change_type_work'>
                                        <i class="fa fa-edit"></i> 转作业
                                    </a>
                                </li>
                            {{# } }}
                            {{# if( d.message_type == 1){ }}
                                <li>
                                    <a href="javascript:void(0);" lay-event='change_type_default'>
                                        <i class="fa fa-edit"></i> 转普通评论
                                    </a>
                                </li>
                            {{# } }}
                            {{# if( d.message_type == 1 && d.public_status == 0 ){ }}
                                <li>
                                    <a href="javascript:void(0);" lay-event='yes_work_public_status'>
                                        <i class="fa fa-edit"></i> 作业公开 
                                    </a>
                                </li>
                            {{# } }}
                            {{# if( d.message_type == 1 && d.public_status == 1 ){ }}
                                <li>
                                    <a href="javascript:void(0);" lay-event='no_work_public_status'>
                                        <i class="fa fa-edit"></i> 作业取消
                                    </a>
                                </li>
                            {{# } }}
                            <li>
                                <a href="javascript:void(0);" lay-event="reject">
                                    <i class="fa fa-user-times"></i> 拒绝
                                </a>
                            </li>
                            <li>
                                <a href="javascript:void(0);" lay-event="deleted">
                                    <i class="fa fa-times"></i> 删除
                                </a>
                            </li>
                        </ul>
                    </script>
                </div>
            </div>
        </div>
    </div>
    <!--end-->
</div>
<script src="{__ADMIN_PATH}js/layuiList.js"></script>
<script src="/static/plug/layui/layui.js"></script>
{/block}
{block name="script"}
<script>
    layList.tableList('List', "{:Url('message_list')}", function () {
        return [
            {type: 'checkbox'},
            {field: 'id', title: 'ID',width: '5%',align: 'center'},
            {field: 'type_name', title: '类型', width: '6%', align: 'center'},
            {field: 'user', title: '用户', templet: '#userinfo', width: '10%', align: 'center'},
            {field: 'title', title: '标题', width: '15%', align: 'center'},
            {field: 'browse', title: '浏览次数', width: '8%', align: 'center'},
            {field: 'pid', title: '查看回复',templet:'#show_comment',align:'center',width:'8%'},
            {field: 'is_refining', title: '是否精华',templet:"#checkrefining",width:'9%',align: 'center'},
            {field: 'status', title: '状态', templet: '#status', width: '10%', align: 'center'},
            {field: 'add_time', title: '提交时间', width: '15%', sort: true, align: 'center'},
            {field: 'right', title: '操作', align: 'center', toolbar: '#act', width: '11%'},
        ];
    });
    layList.tool(function (event, data, obj) {
        switch (event) {
            case 'edit':
                location.href = layList.U({a:'create',q:{id:data.id}});
                break;
            case 'deleted':
                var url = layList.U({c: 'special.specialMessage', a: 'destroy', p: {id: data.id}});
                $eb.$swal('delete', function () {
                    $eb.axios.get(url).then(function (res) {
                        if (res.status == 200 && res.data.code == 200) {
                            $eb.$swal('success', res.data.msg);
                        } else
                            return Promise.reject(res.data.msg || '删除失败')
                    }).catch(function (err) {
                        $eb.$swal('error', err);
                    });
                }, {'title': '您确定要删除留言吗？', 'text': '删除后将无法恢复,请谨慎操作！', 'confirm': '是的，我要删除'})
                break;
            case 'reject':
                var url = layList.U({c: 'special.specialMessage', a: 'reject', p: {id: data.id}});
                $eb.$swal('delete', function () {
                    $eb.axios.get(url).then(function (res) {
                        if (res.status == 200 && res.data.code == 200) {
                            $eb.$swal('success', res.data.msg);
                        } else
                            return Promise.reject(res.data.msg || '删除失败')
                    }).catch(function (err) {
                        $eb.$swal('error', err);
                    });
                }, {'title': '您确定要拒绝此留言吗？', 'text': '拒绝后将影响用户浏览,请谨慎操作！', 'confirm': '是的，我要拒绝'})
                break;

            case 'change_type_default':
                var url = layList.U({c: 'special.specialMessage', a: 'set_field', p: {id: data.id,field: 'message_type',values: 0}});
                $eb.$swal('delete', function () {
                    $eb.axios.get(url).then(function (res) {
                        if (res.status == 200 && res.data.code == 200) {
                            $eb.$swal('success', res.data.msg);
                        } else
                            return Promise.reject(res.data.msg || '设置失败')
                    }).catch(function (err) {
                        $eb.$swal('error', err);
                    });
                }, {'title': '您确定要将此作业设为普通评论类型吗？', 'text': '设置后，此作业将在课程评论区展示！', 'confirm': '是的，我要设置'})
                break;
            case 'change_type_work':
                var url = layList.U({c: 'special.specialMessage', a: 'set_field', p: {id: data.id,field: 'message_type',values: 1}});
                $eb.$swal('delete', function () {
                    $eb.axios.get(url).then(function (res) {
                        if (res.status == 200 && res.data.code == 200) {
                            $eb.$swal('success', res.data.msg);
                        } else
                            return Promise.reject(res.data.msg || '设置失败')
                    }).catch(function (err) {
                        $eb.$swal('error', err);
                    });
                }, {'title': '您确定要将此作业设为学员作业类型吗？', 'text': '设置后，此评论将在课程作业区展示！', 'confirm': '是的，我要设置'})
                break;
            case 'review':
                var url = layList.U({c: 'special.specialMessage', a: 'review', p: {id: data.id,status: data.status}});
                $eb.$swal('delete', function () {
                    $eb.axios.get(url).then(function (res) {
                        if (res.status == 200 && res.data.code == 200) {
                            $eb.$swal('success', res.data.msg);
                        } else
                            return Promise.reject(res.data.msg || '审核失败')
                    }).catch(function (err) {
                        $eb.$swal('error', err);
                    });
                }, {'title': '您确定审核该留言吗？', 'text': '审核后，留言将在前台显示！', 'confirm': '是的，我要审核'})
                break;
            case 'review_egg':
                var url = layList.U({c: 'special.specialMessage', a: 'review', p: {id: data.id,status: data.status, is_open_egg: 1}});
                $eb.$swal('delete', function () {
                    $eb.axios.get(url).then(function (res) {
                        if (res.status == 200 && res.data.code == 200) {
                            $eb.$swal('success', res.data.msg);
                        } else
                            return Promise.reject(res.data.msg || '审核失败')
                    }).catch(function (err) {
                        $eb.$swal('error', err);
                    });
                }, {'title': '您确定审核该作业后并赠送彩蛋吗？', 'text': '审核后，作业将在前台显示！', 'confirm': '是的，我要审核'})
                break;
            case 'review_restore':
                var url = layList.U({c: 'special.specialMessage', a: 'save_set_status', p: {id: data.id,field: 'is_del',values: 0}});
                $eb.$swal('delete', function () {
                    $eb.axios.get(url).then(function (res) {
                        if (res.status == 200 && res.data.code == 200) {
                            $eb.$swal('success', res.data.msg);
                        } else
                            return Promise.reject(res.data.msg || '恢复失败')
                    }).catch(function (err) {
                        $eb.$swal('error', err);
                    });
                }, {'title': '您确定恢复留言为已正常状态吗？', 'text': '恢复后，留言状态为已正常状态！', 'confirm': '是的，我要恢复'})
                break;
            case 'yes_work_public_status':
                var url = layList.U({c: 'special.specialMessage', a: 'set_field', p: {id: data.id,field: 'public_status',values: 1}});
                $eb.$swal('delete', function () {
                    $eb.axios.get(url).then(function (res) {
                        if (res.status == 200 && res.data.code == 200) {
                            $eb.$swal('success', res.data.msg);
                        } else
                            return Promise.reject(res.data.msg || '设置失败')
                    }).catch(function (err) {
                        $eb.$swal('error', err);
                    });
                }, {'title': '您确定要将此作业设为公开显示状态吗？', 'text': '设置后，此贴将会展示给课程下的所有学员！', 'confirm': '是的，我要设置'})
                break;
            case 'yes_work_public_status':
                var url = layList.U({c: 'special.specialMessage', a: 'set_field', p: {id: data.id,field: 'public_status',values: 0}});
                $eb.$swal('delete', function () {
                    $eb.axios.get(url).then(function (res) {
                        if (res.status == 200 && res.data.code == 200) {
                            $eb.$swal('success', res.data.msg);
                        } else
                            return Promise.reject(res.data.msg || '设置失败')
                    }).catch(function (err) {
                        $eb.$swal('error', err);
                    });
                }, {'title': '您确定要将此作业设为非公开显示状态吗？', 'text': '设置后，此贴将会不展示给课程下的所有学员！', 'confirm': '是的，我要设置'})
                break;
            case 'message_info':
                $eb.createModalFrame(data.title + '留言详情', layList.U({a: 'message_info', q: {id: data.id}}));
                break;
            case 'open_image':
                $eb.openImage(data.avatar);
                break;
        }
    })
    $('#container-action').find('button').each(function () {
        $(this).on('click', function () {
            var act = $(this).data('type');
            action[act] && action[act]();
        });
    })
    //下拉框
    $(document).click(function (e) {
        $('.layui-nav-child').hide();
    })
     //设置活动收费免费
    layList.switch('is_refining',function (odj,value) {
        if(odj.elem.checked==true){
            layList.baseGet(layList.Url({c:'special.specialMessage',a:'set_is_refining',p:{is_refining:1,id:value}}),function (res) {
                layList.msg(res.msg, function () {
                    layList.reload();
                });
            });
        }else{
            layList.baseGet(layList.Url({c:'special.specialMessage',a:'set_is_refining',p:{is_refining:0,id:value}}),function (res) {
                layList.msg(res.msg, function () {
                    layList.reload();
                });
            });
        }
    });
    //自定义方法
    var action={
        show:function(){
            var ids=layList.getCheckData().getIds('id');
            if(ids.length){
                layList.basePost(layList.Url({c:'special.specialMessage',a:'release'}),{ids:ids},function (res) {
                    layList.msg(res.msg);
                    layList.reload();
                });
            }else{
                layList.msg('请选择要审核的留言');
            }
        },
        save_set_status:function () {
            var ids=layList.getCheckData().getIds('id');
            if(ids.length){
                var str = ids.join(',');
                console.log(str);
                $eb.createModalFrame('批量设置状态',layList.Url({a:'save_set_status',p:{id:str}}),{w:500,h:300});
            }else{
                layList.msg('请选择要批量设置状态的留言');
            }
        },
        refresh:function () {
            layList.reload();
        }
    };
    function dropdown(that) {
        var oEvent = arguments.callee.caller.arguments[0] || event;
        oEvent.stopPropagation();
        var offset = $(that).offset();
        var top = offset.top - $(window).scrollTop();
        var index = $(that).parents('tr').data('index');
        $('.layui-nav-child').each(function (key) {
            if (key != index) {
                $(this).hide();
            }
        })
        if ($(document).height() < top + $(that).next('ul').height()) {
            $(that).next('ul').css({
                'padding': 10,
                'top': -($(that).parents('td').height() / 2 + $(that).height() + $(that).next('ul').height() / 2),
                'min-width': 'inherit',
                'position': 'absolute'
            }).toggle();
        } else {
            $(that).next('ul').css({
                'padding': 10,
                'top': $(that).parents('td').height() / 2 + $(that).height(),
                'min-width': 'inherit',
                'position': 'absolute'
            }).toggle();
        }
    }
    var messageCount =<?=json_encode($messageCount)?>,status =<?=$status ? $status : "''"?>;
    var specialList =<?=json_encode($specialList)?>;
    require(['vue'], function (Vue) {
        new Vue({
            el: "#app",
            data: {
                badge: [],
                checkList: [0],
                status: [
                    {name: '全部', value: ''},
                    {name: '已审核', value: 1, count: messageCount.reviewed},
                    {name: '待审核', value: 2, count: messageCount.unreviewed, class: true},
                    {name: '已拒绝', value: 3, count: messageCount.rejected, class: true},
                    {name: '已删除', value: 4, count: messageCount.deleted, class: true}
                ],
                TypeList: [
                    {name: '全部', value: ''},
                    {name: '图文', value: 0, count: messageCount.texts},
                    {name: '视频', value: 1, count: messageCount.videos},
                    {name: '音频', value: 2, count: messageCount.audios},
                ],
                messageTypeList: [
                    {name: '全部', value: ''},
                    {name: '评论', value: 0, count: messageCount.comments},
                    {name: '作业', value: 1, count: messageCount.works},
                ],
                dataList: [
                    {name: '全部', value: ''},
                    {name: '今天', value: 'today'},
                    {name: '昨天', value: 'yesterday'},
                    {name: '最近7天', value: 'lately7'},
                    {name: '最近30天', value: 'lately30'},
                    {name: '本月', value: 'month'},
                    {name: '本年', value: 'year'},
                ],
                specialList: specialList,
                where: {
                    data: '',
                    type: '',
                    message_type: '',
                    special_id: '',
                    status: '',
                    keywords: '',
                },
                showtime: false,
            },
            watch: {
                'where.type': function () {
                    layList.reload(this.where, true);
                },
                'where.message_type': function () {
                    layList.reload(this.where, true);
                },
                'where.status': function () {
                    layList.reload(this.where, true);
                },
                'where.data': function () {
                    layList.reload(this.where, true);
                },
                'where.keywords': function () {
                    layList.reload(this.where, true);
                },
                // 'where.special_id': function () {
                //     this.where.excel = 0;
                //     layList.reload(this.where, true);
                // }
            },
            methods: {
                setData: function (item) {
                    var that = this;
                    if (item.is_zd == true) {
                        that.showtime = true;
                        this.where.data = this.$refs.date_time.innerText;
                    } else {
                        this.showtime = false;
                        this.where.data = item.value;
                    }
                },
                search: function () {		
                    layList.reload(this.where, true);
                },
                refresh: function () {
                    layList.reload();
                },
                handleBtn: function (item,index) {
                    var that = this;
                   if(item == 0){
                        this.checkList = [0];
                        that.where.special_id = [];
                        that.where.excel = 0;
                        layList.reload(that.where, true);
                   }else {
                        if (this.checkList == [0]) {
                            this.checkList.splice(this.checkList.indexOf(0), 1); //取消
                        }
                        if (this.checkList.indexOf(item) !== -1) {
                            this.checkList.splice(this.checkList.indexOf(item), 1); //取消
                            that.where.special_id = that.checkList;
                            that.where.excel = 0;
                            layList.reload(that.where, true);
                        } else {
                          this.checkList.push(item);//选中添加到数组里
                            that.where.special_id = that.checkList;
                            that.where.excel = 0;
                            layList.reload(that.where, true);
                        }
                   }
                },
                init: function () {
                var that = this;
                that.$nextTick(function () {
                    layui.use(['form','element'], function () {
                        that.form = layui.form;
                        that.form.render();
                        layui.element.on('tab(docTabBrief)', function(){
                            that.layTabId = this.getAttribute('lay-id');
                        });
                    });
                })
            },
            },
            mounted: function () {
                this.init();
                var that = this;
                window.formReload = this.search;
                layList.laydate.render({
                    elem: this.$refs.date_time,
                    trigger: 'click',
                    eventElem: this.$refs.time,
                    range: true,
                    change: function (value) {
                        that.where.data = value;
                    }
                });
                layui.use('form', function(){
                  var form = layui.form;
                  //监听提交
                  form.on('select(special_id)', function(data){
                    that.where.special_id = data.value;
                    that.where.excel = 0;
                    layList.reload(that.where, true);
                  });
                });
            }
        })
    });
</script>
{/block}