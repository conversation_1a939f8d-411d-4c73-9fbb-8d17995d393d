{extend name="public/container"}
{block name="content"}
<style type="text/css">
    video{
        width: 24%!important;
        margin-right: 4px;
        margin-top: 10px;
        margin-left: 10px;
        margin-bottom: -1px;
    }
    audio {
        width: 198px;
        height: 54px;
    }
    .image {
        width: 291px;
        height: 450px;
        margin-right: -3px;
        padding-top: 6px;
        margin-bottom: 5px;
        margin-left: 7px;
    }
</style>
<div class="ibox-content order-info">
    <div class="row">
        <div class="col-sm-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    留言详情
                </div>
                <div class="panel-body">
                    <div class="row show-grid">
                        <div class="col-xs-12">留言标题: {$messageInfo.title}</div>
                        <div class="col-xs-12">留言时间: {$messageInfo.add_time|date="Y-m-d H:i:s"}</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-sm-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    文字简介
                </div>
                <div class="panel-body">
                    <div class="row show-grid">
                        <div class="col-xs-6" >{if $messageInfo.comment}{$messageInfo.comment}{else}暂无文字信息{/if}</div>
                    </div>
                </div>
            </div>
        </div>
         <div class="col-sm-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    图片
                </div>
                <div class="panel-body">
                    <div class="row show-grid">
                        {volist name='messageInfo.image' id='vo'}
	                        <img src="{$vo}" class="image">
	                    {/volist}
                    </div>
                </div>
            </div>
        </div>
        <div class="col-sm-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    视频
                </div>
                <div class="panel-body">
                    <div class="row show-grid">
                        {volist name='messageInfo.video' id='vo'}
                            <video  controls id="message-video">
                              <source src="{$vo}" type="video/mp4">
                              您的浏览器不支持 HTML5 video 标签。
                            </video>
                        {/volist}
                    </div>
                </div>
            </div>
        </div>
        <div class="col-sm-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    音频
                </div>
                <div class="panel-body">
                    <div class="row show-grid">
                        {volist name='messageInfo.audio' id='vo'}
                            <audio controls style="margin-right: 9px;padding-top: 10px;margin-bottom: 5px;margin-left: 5px;"> 
                                  <source src="{$vo}" type="audio/mpeg" >
                                  您的浏览器不支持 audio 元素。
                            </audio>
                        {/volist}
                    </div>
                </div>
            </div>
        </div>
        <div class="col-sm-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    留言区域
                </div>
                <div class="panel-body">
                    <div class="row show-grid">
                        {volist name='messageInfo.commentList' id='vo'}
                            <div class="col-xs-12" >
                                {if $vo.type_name == '一级评论'}
                                    评论类型: {$vo.type_name}<br>
                                    评论ID: {$vo.id}<br>
                                    评论用户ID：{$vo.uid}<br>
                                    评论内容：{$vo.comment}<br>
                                    评论时间：{$vo.add_time|date="Y-m-d H:i:s"}
                                }{else}
                                    类型: {$vo.type_name}<br>
                                    上级评论ID: {$vo.id}<br>
                                    上级评论内容: {$vo.parant_comment}<br>
                                    上级评论时间: {$vo.parent_add_time|date="Y-m-d H:i:s"}<br>
                                    本次回复评论用户ID：{$vo.uid}<br>
                                    本次回复评论内容：{$vo.comment}<br>
                                    本次回复评论时间：{$vo.add_time|date="Y-m-d H:i:s"}
                                {/if}
                            </div>
                        {/volist}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="{__FRAME_PATH}js/content.min.js?v=1.0.0"></script>
{/block}
{block name="script"}

{/block}
