{extend name="public/container"}
{block name="content"}
<div class="layui-fluid">
    <div class="layui-row layui-col-space15"  id="app">
        <div class="layui-col-md13">
            <div class="layui-card">
                <div class="layui-card-header">
                    <div style="font-weight: bold;">{$special_title}</div>
                </div>
                <div class="layui-card-body">
                    <form class="layui-form layui-form-pane" action="">
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">专题搜索</label>
                                <div class="layui-input-block">
                                    <input type="text" name="store_name" class="layui-input" placeholder="请输入专题名称、简介、短语、编号">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">专题分类</label>
                                <div class="layui-input-block">
                                    <select name="subject_id">
                                        <option value="">全部</option>
                                        {volist name="cate" id="vo"}
                                            <option value="{$vo.id}">{$vo.html}{$vo.name}</option>
                                        {/volist}
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">显示状态</label>
                                <div class="layui-input-block">
                                    <select name="is_show">
                                        <option value="">全部</option>
                                        <option value="1">显示</option>
                                        <option value="0">不显示</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">时间范围</label>
                                <div class="layui-input-inline" style="width: 260px;">
                                    <input type="text" name="datetime" class="layui-input" id="datetime" placeholder="时间范围">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <div class="layui-input-inline">
                                    <button class="layui-btn layui-btn-sm layui-btn-normal" lay-submit="search" lay-filter="search">
                                        <i class="layui-icon layui-icon-search"></i>搜索</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <!--产品列表-->
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">分类列表</div>
                <div class="layui-card-body">
                    <div class="layui-btn-container">
                        <button type="button" class="layui-btn layui-btn-sm" onclick="$eb.createModalFrame(this.innerText,'{:Url('create',array('special_type'=>$special_type))}')">添加{$special_title}</button>
                    </div>
                    <table class="layui-hide" id="List" lay-filter="List"></table>
                    <script type="text/html" id="recommend">
                    <div class="layui-btn-container">
                        {{#  layui.each(d.recommend, function(index, item){ }}
                        <button type="button" class="layui-btn  layui-btn-normal layui-btn-xs" data-type="recommend" data-id="{{item.id}}" data-pid="{{d.id}}">{{item.title}}</button>
                        {{#  }); }}
                        </div>
                    </script>
                    <script type="text/html" id="is_pink">
                        {{# if(d.is_pink){ }}
                        <span class="layui-badge layui-bg-blue">开启</span>
                        {{# }else{ }}
                        <span class="layui-badge">关闭</span>
                        {{# } }}
                    </script>
                    <script type="text/html" id="is_live_goods">
                        <input type='checkbox' name='is_live_goods' lay-skin='switch' value="{{d.id}}" lay-filter='is_live_goods' lay-text='是|否'  {{ d.is_live_goods == 1 ? 'checked' : '' }}>
                    </script>
                    <script type="text/html" id="is_show">
                        <input type='checkbox' name='id' lay-skin='switch' value="{{d.id}}" lay-filter='is_show' lay-text='上架|下架'  {{ d.is_show == 1 ? 'checked' : '' }}>
                    </script>
                    <script type="text/html" id="image">
                        <img style="cursor: pointer;" height="50" lay-event='open_image' src="{{d.image}}">
                    </script>
                    <script type="text/html" id="act">
                        {{# if(d.is_pollster && d.pollster_id != 0){ }}
                        <!-- <button type="button" class="layui-btn layui-btn-xs" onclick="$eb.createModalFrame('{{d.title}}-填写名单','{:Url('roster')}?id={{d.id}}',{h:1000,w:1400});"><i class="layui-icon layui-icon-group"></i>填写名单</button> -->
                        {{# } }}
                        <button type="button" class="layui-btn layui-btn-normal layui-btn-xs" onclick="dropdown(this)">
                            <i class="layui-icon">&#xe625;</i>操作
                        </button>
                        <ul class="layui-nav-child layui-anim layui-anim-upbit">
                            <li>
                                <a href="javascript:void(0)" onclick="$eb.createModalFrame(this.innerText,'{:Url('create')}?id={{d.id}}&special_type={$special_type}','编辑专题')" >
                                    <i class="fa fa-paste"></i> 编辑专题
                                </a>
                            </li>
    <!--                         <li>
                                <a href="javascript:void(0)" onclick="$eb.createModalFrame('{{d.title}}-推荐管理','{:Url('recommend')}?special_id={{d.id}}',{h:300,w:400})">
                                    <i class="fa fa-check-circle"></i> 推荐至首页
                                </a>
                            </li> -->
                            <li>
                                <a lay-event='destroy' href="javascript:void(0)">
                                    <i class="fa fa-trash"></i> 删除专题
                                </a>
                            </li>
                        </ul>
                    </script>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="{__ADMIN_PATH}js/layuiList.js"></script>
{/block}
{block name="script"}
<script>
    setTimeout(function () {
        $('.alert-info').hide();
    },3000);
    //实例化form
    layList.form.render();
    layList.date({
        elem: '#datetime',
        theme: '#0092DC',
        type: 'datetime',
        range: '~'
    });
    //加载列表
    layList.tableList('List',"{:Url('list',['special_type'=>$special_type])}",function (){
        return [
            {field: 'id', title: '编号',width:60,align: 'center'},
            {field: 'title', title: '名称',align: 'center'},
            {field: 'subject_name', title: '分类',align: 'center',width:80},
            {field: 'image', title: '封面',templet:'#image',align: 'center'},
            // {field: 'recommend', title: '推荐',templet:'#recommend',align: 'center'},
            {field: 'money', title: '价格',align: 'center',width:70},
            {field: 'task_count', title: '专题数量',align: 'center',width:70},
            {field: 'sales', title: '销量',align: 'center',width:70},
            {field: 'sort', title: '排序',sort: true,event:'sort',edit:'sort',align: 'center',width:60},
            {field: 'is_show', title: '状态',templet:'#is_show',align: 'center',width:80},
            {field: 'right', title: '操作',align:'center',toolbar:'#act',width:165},
        ];
    });
    //自定义方法
    var action= {
        set_category: function (field, id, value) {
            layList.baseGet(layList.Url({
                c: 'special.special_type',
                a: 'set_category',
                q: {field: field, id: id, value: value}
            }), function (res) {
                layList.msg(res.msg);
            });
        },
    }
    //查询
    layList.search('search',function(where){
        layList.reload(where,true);
    });
    layList.switch('is_show',function (odj,value) {
        if(odj.elem.checked==true){
            layList.baseGet(layList.Url({c:'special.special_type',a:'set_show',p:{is_show:1,id:value}}),function (res) {
                layList.msg(res.msg);
            });
        }else{
            layList.baseGet(layList.Url({c:'special.special_type',a:'set_show',p:{is_show:0,id:value}}),function (res) {
                layList.msg(res.msg);
            });
        }
    });
    //下拉框
    $(document).click(function (e) {
        $('.layui-nav-child').hide();
    })
    function dropdown(that){
        var oEvent = arguments.callee.caller.arguments[0] || event;
        oEvent.stopPropagation();
        var offset = $(that).offset();
        var top=offset.top-$(window).scrollTop();
        var index = $(that).parents('tr').data('index');
        $('.layui-nav-child').each(function (key) {
            if (key != index) {
                $(this).hide();
            }
        })
        if($(document).height() < top+$(that).next('ul').height()){
            $(that).next('ul').css({
                'padding': 10,
                'top': - ($(that).parent('td').height() / 2 + $(that).height() + $(that).next('ul').height()/2),
                'min-width': 'inherit',
                'position': 'absolute'
            }).toggle();
        }else{
            $(that).next('ul').css({
                'padding': 10,
                'top':$(that).parent('td').height() / 2 + $(that).height(),
                'min-width': 'inherit',
                'position': 'absolute'
            }).toggle();
        }
    }
    //快速编辑
    layList.edit(function (obj) {
        var id=obj.data.id,value=obj.value;
        switch (obj.field) {
            case 'title':
                action.set_category('title',id,value);
                break;
            case 'sort':
                action.set_category('sort',id,value);
                break;
        }
    });
    //监听并执行排序
    layList.sort(['id','sort'],true);
    //点击事件绑定
    layList.tool(function (event,data,obj) {
        switch (event) {
            case 'destroy':
                var url=layList.U({c:'special.special_type',a:'delete',q:{id:data.id,model_type:'special'}});
                $eb.$swal('delete',function(){
                    $eb.axios.get(url).then(function(res){
                        if(res.status == 200 && res.data.code == 200) {
                            $eb.$swal('success',res.data.msg);
                            obj.del();
                        }else
                            return Promise.reject(res.data.msg || '删除失败')
                    }).catch(function(err){
                        $eb.$swal('error',err);
                    });
                })
                break;
            case 'open_image':
                $eb.openImage(data.pic);
                break;
        }
    })
</script>
{/block}
