{extend name="public/container"}
{block name="content"}
<div class="layui-fluid">
  <div class="layui-row layui-col-space15" id="app">
    <div class="layui-col-md15">
      <div class="layui-card">
        <div class="layui-card-header">搜索条件</div>
        <div class="layui-card-body">
          <form class="layui-form layui-form-pane" action="">
            <div class="layui-form-item">
              <div class="layui-inline">
                <label class="layui-form-label">所有专题</label>
                <div class="layui-input-block">
                  <select name="special_id">
                    <option value="">全部</option>
                    {volist name="special_list" id="vo"}
                    <option value="{$vo.id}">{$vo.title}</option>
                    {/volist}
                  </select>
                </div>
              </div>
              <div class="layui-inline">
                <label class="layui-form-label">邀请码</label>
                <div class="layui-input-block">
                  <input type="text" name="code" class="layui-input" placeholder="请输入邀请码">
                </div>
              </div>
              <!-- <div class="layui-inline">
                <label class="layui-form-label">领取人搜索</label>
                <div class="layui-input-block">
                  <input type="text" name="keywords" class="layui-input" placeholder="请输入领取用户的UID,手机号、昵称" style="width: 220px;">
                </div>
              </div> -->
              <div class="layui-inline">
                <label class="layui-form-label">选择时间：</label>
                <div class="layui-input-inline">
                  <input type="text" class="layui-input time-w" name="add_time" lay-verify="add_time" id="add_time" placeholder="时间范围">
                </div>
              </div>
              <div class="layui-inline">
                <div class="layui-input-inline">
                  <button class="layui-btn layui-btn-sm layui-btn-normal" lay-submit="search" lay-filter="search">
                    <i class="layui-icon layui-icon-search"></i>搜索</button>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
    <!--用户课程权益领取记录-->
    <div class="layui-col-md12">
      <div class="layui-card">
        <div class="layui-card-header">用户课程权益领取记录</div>
        <div class="layui-card-body">
          <table class="layui-hide" id="List" lay-filter="List"></table>
          <!--用户信息-->
          <script type="text/html" id="userInfo">
          {{# if( d.receive_uid ){ }}
            <div>
              <span><img style="width: 100px;height: 100px;cursor: pointer;"
                  src="{{d.receive_avatar}}"></span>
            </div>
            {{d.receive_nickname==null ? '暂无信息':d.receive_nickname}}/{{d.receive_uid}} <br>
            手机号：{{d.receive_phone==null ? '暂未绑定':d.receive_phone}}
            {{# } }}
          </script>

          <script type="text/html" id="codeInfo">
            类型： {{d.grant_type== 1 ? '一码多人':'一人一码'}} <br>
            邀请码：{{d.grant_code }}<br>
            创建时间：{{d.grant_add_time }}
          </script>
          <script type="text/html" id="grantUserInfo">
            <div>
              <span><img style="width: 100px;height: 100px;cursor: pointer;"
                  src="{{d.grant_avatar}}"></span>
            </div>
            {{d.grant_nickname==null ? '暂无信息':d.grant_nickname}}/{{d.grant_uid}}
          </script>
          <script type="text/html" id="status">
          {{d.status_name}}
          </script>
          <script type="text/html" id="act">
            <button class="layui-btn layui-btn-xs" type="button" lay-event="receive_info">
              <i class="fa fa-calendar"></i> 领取详情
            </button>
            {{# if( d.is_del == 0 ){ }}
              <button class="layui-btn btn-danger layui-btn-xs" lay-event='delstor'>
                <i class="fa fa-times"></i> 删除用户权益
              </button>
              {{# } }}
          </script>
        </div>
      </div>
    </div>
  </div>
</div>
<script src="{__ADMIN_PATH}js/layuiList.js"></script>
{/block}
{block name="script"}
<script>
  setTimeout(function() {
    $('.alert-info').hide();
  }, 3000);
  //实例化form
  layList.form.render();
  //加载列表
  layList.tableList('List', "{:Url('list')}", function() {
    return [{
        field: 'id',
        title: 'ID',
        sort: true,
        event: 'id',
        width: '4%',
        align: 'center'
      },
      {
        field: 'title',
        title: '课程',
        align: 'center'
      },
      {
        field: 'codeInfo',
        title: '邀请码信息',
        templet: '#codeInfo',
        align: 'center'
      },
      {
        field: 'order_id',
        title: '发放人',
        templet: '#grantUserInfo',
        align: 'center'
      },
      {
        field: 'userInfo',
        title: '领取人',
        templet: '#userInfo',
        align: 'center'
      },
      {
        field: 'status',
        title: '状态',
        templet: '#status',
        width: '10%',
        align: 'center'
      },
      {
        field: 'receive_add_time',
        title: '领取时间',
        align: 'center'
      },
      {
        field: 'right',
        title: '操作',
        align: 'center',
        toolbar: '#act',
        width: '15%',
        align: 'center'
      },
    ];
  });
  //查询
  layList.search('search', function(where) {
    layList.reload(where, true);
  });
  //excel下载
  layList.search('export', function(where) {
    where.excel = 1;
    location.href = layList.U({
      c: 'special.receive_benefits',
      a: 'list',
      q: where
    });
  })
  layList.date('add_time');
  //监听并执行排序
  layList.sort(['id', 'sort'], true);
  //点击事件绑定
  layList.tool(function(event, data, obj) {
    switch (event) {
      case 'delstor':
        var url = layList.U({
          c: 'special.ReceiveBenefits',
          a: 'delete',
          q: {
            id: data.id
          }
        });
        $eb.$swal('delete', function() {
          $eb.axios.get(url).then(function(res) {
            if (res.status == 200 && res.data.code == 200) {
              $eb.$swal('success', res.data.msg);
              obj.del();
            } else
              return Promise.reject(res.data.msg || '删除失败')
          }).catch(function(err) {
            $eb.$swal('error', err);
          });
        })
        break;
      case 'receive_info':
        $eb.createModalFrame(data.nickname + '领取权益详情', layList.U({
          a: 'receive_info',
          q: {
            id: data.id
          }
        }));
        break;
      case 'open_image':
        $eb.openImage(data.pic);
        break;
    }
  })
</script>
{/block}