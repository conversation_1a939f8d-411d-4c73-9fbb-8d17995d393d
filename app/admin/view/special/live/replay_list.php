{extend name="public/container"}
{block name="content"}
<div class="layui-fluid">
    <div class="layui-row layui-col-space15"  id="app">
        <!--产品列表-->
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">小程序直播回放</div>
                <div class="layui-card-body">
                    <table class="layui-hide" id="List" lay-filter="List"></table>
                    <script type="text/html" id="productInfo">
                        {{d.title==null ? '':d.title}}
                    </script>
                    <script type="text/html" id="stopTime">
                        创建时间：{{ d.create_time }}<br>
                        过期时间：{{ d.expire_time }}
                    </script>
                    <script type="text/html" id="act">
                        <button class="layui-btn layui-btn-xs" lay-event='download'>
                           <i class="fa fa-eye"></i> 下载回放
                         </button>
                    </script>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="{__ADMIN_PATH}js/layuiList.js"></script>
{/block}
{block name="script"}
<script>
    var id = '<?=$id?>';
    var live_id = '<?=$live_id?>';
    setTimeout(function () {
        $('.alert-info').hide();
    },3000);
    //实例化form
    layList.form.render();
    //加载列表
    layList.tableList('List',"{:Url('get_replay_list',['room_id'=>$live_id])}",function () {
        return [
            {field: 'add_time', title: '回放视频时间',align: 'center',toolbar: '#stopTime'},
            {field: 'media_url', title: '回放视频链接', align: 'center'},
            {field: 'right', title: '操作',align:'center',toolbar:'#act'},
        ];
    });
    //点击事件绑定
    layList.tool(function (event,data,obj) {
        switch (event) {
            case 'download':
                location.href = layList.U({c:'special.live',a:'download',q:{id:id,url:data.media_url}});
                break;
        }
    })
</script>
{/block}