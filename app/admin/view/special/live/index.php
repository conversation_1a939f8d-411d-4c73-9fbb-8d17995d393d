{extend name="public/container"}
{block name="content"}
<div class="layui-fluid">
    <div class="layui-row layui-col-space15"  id="app">
        <div class="layui-col-md15">
            <div class="layui-card">
                <div class="layui-card-header">搜索条件</div>
                <div class="layui-card-body">
                    <form class="layui-form layui-form-pane" action="">
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">活动名称</label>
                                <div class="layui-input-block">
                                    <input type="text" name="title" class="layui-input" placeholder="请输入活动名称">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">直播房间号</label>
                                <div class="layui-input-block">
                                    <input type="text" name="keywords" class="layui-input" placeholder="请输入直播房间号">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">选择时间：</label>
                                <div class="layui-input-inline">
                                    <input type="text" class="layui-input time-w" name="add_time" lay-verify="add_time"  id="add_time" placeholder="时间范围">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <div class="layui-input-inline">
                                    <button class="layui-btn layui-btn-sm layui-btn-normal" lay-submit="search" lay-filter="search">
                                        <i class="layui-icon layui-icon-search"></i>搜索</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <!--产品列表-->
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">小程序直播历史</div>
                <div class="layui-card-body">
<!--                 	<div class="layui-btn-container">
	                    <button type="button" class="layui-btn layui-btn-sm" onclick="$eb.createModalFrame(this.innerText,'{:Url('create')}')">添加房间</button>
	                </div> -->
                    <table class="layui-hide" id="List" lay-filter="List"></table>
                    <script type="text/html" id="productInfo">
                        {{d.title==null ? '':d.title}}
                    </script>
                    <script type="text/html" id="stopTime">
                        开始时间：{{ d.start_time }}<br>
                        结束时间：{{ d.stop_time }}
                    </script>
                    <script type="text/html" id="act">
<!--                    <button class="layui-btn layui-btn-xs" onclick="$eb.createModalFrame('编辑','{:Url('edit')}?id={{d.id}}')">
                           <i class="fa fa-edit"></i> 编辑
                         </button>
	                    <button class="layui-btn btn-danger layui-btn-xs" lay-event='delstor'>
	                        <i class="fa fa-times"></i> 删除
	                    </button> -->
                        {{# if(d.status_name == '直播已结束') { }}
                        <button class="layui-btn layui-btn-xs" onclick="$eb.createModalFrame('编辑','{:Url('replay_list')}?id={{d.id}}&live_id={{d.live_id}}')">
                           <i class="fa fa-eye"></i> 查看回放
                         </button>
                        {{# }else{ }}
                        <button class="layui-btn layui-btn-xs layui-btn-disabled">
                           <i class="fa fa-ban"></i> 暂不操作
                         </button>
                        {{# } }}
                    </script>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="{__ADMIN_PATH}js/layuiList.js"></script>
{/block}
{block name="script"}
<script>
    setTimeout(function () {
        $('.alert-info').hide();
    },3000);
    //实例化form
    layList.form.render();
    //加载列表
    layList.tableList('List',"{:Url('live_list')}",function (){
        return [
            {field: 'id', title: 'Id', sort: true,event:'id',width:'10%',align:'center'},
            {field: 'title', title: '活动名称',align:'center',width:'20%'},
            {field: 'live_id', title: '房间号', width: '20%', align: 'center'},
            {field: 'add_time', title: '直播时间',align: 'center',toolbar: '#stopTime'},
            {field: 'status_name', title: '直播状态',align: 'center',width:'20%'},
            {field: 'right', title: '操作',align:'center',toolbar:'#act'},
        ];
    });
    //查询
    layList.search('search',function(where){
        layList.reload(where,true);
    });
    layList.date('add_time');
    //监听并执行排序
    layList.sort(['id','sort'],true);
    //点击事件绑定
    layList.tool(function (event,data,obj) {
        switch (event) {
            case 'delstor':
                var url=layList.U({c:'special.live',a:'delete',q:{id:data.id}});
                $eb.$swal('delete',function(){
                    $eb.axios.get(url).then(function(res){
                        if(res.status == 200 && res.data.code == 200) {
                            $eb.$swal('success',res.data.msg);
                            obj.del();
                        }else
                            return Promise.reject(res.data.msg || '删除失败')
                    }).catch(function(err){
                        $eb.$swal('error',err);
                    });
                })
                break;
            case 'open_image':
                $eb.openImage(data.pic);
                break;
            case 'download':
                location.href = layList.U({c:'special.live',a:'download',q:{id:data.id}});
                break;
        }
    })
</script>
{/block}