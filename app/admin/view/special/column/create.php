<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link href="{__FRAME_PATH}css/font-awesome.min.css" rel="stylesheet">
    <link href="{__ADMIN_PATH}plug/umeditor/themes/default/css/umeditor.css" type="text/css" rel="stylesheet">
    <script type="text/javascript" src="{__ADMIN_PATH}plug/umeditor/third-party/jquery.min.js"></script>
    <script type="text/javascript" src="{__ADMIN_PATH}plug/umeditor/third-party/template.min.js"></script>
    <script type="text/javascript" charset="utf-8" src="{__ADMIN_PATH}plug/umeditor/umeditor.config.js"></script>
    <script type="text/javascript" charset="utf-8" src="{__ADMIN_PATH}plug/umeditor/umeditor.min.js"></script>
    <script type="text/javascript" src="{__ADMIN_PATH}plug/umeditor/lang/zh-cn/zh-cn.js"></script>
    <link rel="stylesheet" href="/static/plug/layui/css/layui.css">
    <script src="/static/plug/layui/layui.js"></script>
    <script src="{__PLUG_PATH}vue/dist/vue.min.js"></script>
    <script src="/static/plug/axios.min.js"></script>
    <script src="/static/plug/iview/dist/iview.min.js"></script>
    <script src="{__MODULE_PATH}widget/aliyun-oss-sdk-4.4.4.min.js"></script>
    <script src="{__MODULE_PATH}widget/cos-js-sdk-v5.min.js"></script>
    <script src="{__MODULE_PATH}widget/qiniu-js-sdk-2.5.5.js"></script>
    <script src="{__MODULE_PATH}widget/plupload.full.min.js"></script>
    <script src="{__MODULE_PATH}widget/videoUpload.js"></script>
    <script type="text/javascript">
        $eb = parent._mpApi;
        window.controlle="<?php echo strtolower(trim(preg_replace("/[A-Z]/", "_\\0", app('request')->controller()), "_"));?>";
        window.module="6GqvmHa8HRGHoQEQ";
    </script>
    <style>
        .layui-form-item {
            margin-bottom: 0px;
        }

        .layui-form-item .special-label {
            width: 50px;
            float: left;
            height: 30px;
            line-height: 38px;
            margin-left: 10px;
            margin-top: 5px;
            border-radius: 5px;
            background-color: #0092DC;
            text-align: center;
        }

        .layui-form-item .special-label i {
            display: inline-block;
            width: 18px;
            height: 18px;
            font-size: 18px;
            color: #fff;
        }

        .layui-form-item .label-box {
            border: 1px solid;
            border-radius: 10px;
            position: relative;
            padding: 10px;
            height: 30px;
            color: #fff;
            background-color: #393D49;
            text-align: center;
            cursor: pointer;
            display: inline-block;
            line-height: 10px;
        }

        .layui-form-item .label-box p {
            line-height: inherit;
        }

        .pictrueBox {
            display: inline-block !important;
        }

        .pictrue {
            width: 60px;
        height: 60px;
            border: 1px dotted rgba(0, 0, 0, 0.1);
            margin-right: 15px;
            display: inline-block;
            position: relative;
            cursor: pointer;
        }

        .pictrue img {
            width: 100%;
            height: 100%;
        }

        .upLoad {
            width: 58px;
            height: 58px;
            line-height: 58px;
            border: 1px dotted rgba(0, 0, 0, 0.1);
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.02);
            cursor: pointer;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .rulesBox {
            display: flex;
            flex-wrap: wrap;
            margin-left: 10px;
        }

        .layui-tab-content {
            margin-top: 15px;
        }

        .ml110 {
            margin: 18px 0 4px 110px;
        }

        .rules {
            display: flex;
        }

        .rules-btn-sm {
            height: 30px;
            line-height: 30px;
            font-size: 12px;
            width: 109px;
        }

        .rules-btn-sm input {
            width: 79% !important;
            height: 84% !important;
            padding: 0 10px;
        }

        .ml10 {
            margin-left: 10px !important;
        }

        .ml40 {
            margin-left: 40px !important;
        }

        .closes {
            position: absolute;
            left: 86%;
            top: -18%;
        }
        .red {
            color: red;
        }
        .layui-input-block .layui-video-box{
            width: 22%;
            height: 180px;
            border-radius: 10px;
            background-color: #707070;
            margin-top: 10px;
            position: relative;
            overflow: hidden;
        }
        .layui-input-block .layui-video-box i{
            color: #fff;
            line-height: 180px;
            margin: 0 auto;
            width: 50px;
            height: 50px;
            display: inherit;
            font-size: 50px;
        }
        .layui-input-block .layui-video-box .mark{
            position: absolute;
            width: 100%;
            height: 30px;
            top: 0;
            background-color: rgba(0,0,0,.5);
            text-align: center;
        }
        .layui-form-select dl {
            z-index: 1015;
        }
        .store_box{
            display: flex;
        }
        .ivu-input{
             border-width: 0px !important;
             width: 100%;
             height: 36px;
         }
         .ivu-select-dropdown{
             z-index: 999;
             background: #fff;
         }
         .ivu-cascader-menu .ivu-cascader-menu-item-active {
             background-color: #f3f3f3;
             color: #2d8cf0;
         }
         .ivu-cascader-menu .ivu-cascader-menu-item {
             position: relative;
             padding-right: 24px;
             -webkit-transition: all .2s ease-in-out;
             transition: all .2s ease-in-out;
         }
         .ivu-cascader .ivu-cascader-menu-item {
             margin: 0;
             line-height: normal;
             padding: 7px 16px;
             clear: both;
             color: #495060;
             font-size: 12px!important;
             white-space: nowrap;
             list-style: none;
             cursor: pointer;
             -webkit-transition: background .2s ease-in-out;
             transition: background .2s ease-in-out;
         }
         .ivu-cascader-menu:last-child {
             border-right-color: transparent;
             margin-right: -1px;
         }
         .ivu-cascader-menu {
             display: inline-block;
             min-width: 100px;
             height: 180px;
             margin: 0;
             padding: 5px 0!important;
             vertical-align: top;
             list-style: none;
             border-right: 1px solid #e9eaec;
             overflow: auto;
                 }
        .info{
            color: #c9c9c9;
            padding-left: 10px;
            line-height: 30px;
        }
    </style>
</head>
<script type="text/javascript">
    window.controlle="<?php echo strtolower(trim(preg_replace("/[A-Z]/", "_\\0", app('request')->controller()), "_"));?>";
    window.module="6GqvmHa8HRGHoQEQ";
</script>
<body>
<div class="layui-fluid">
    <div class="layui-row layui-col-space15"  id="app" v-cloak="">
        <div class="layui-card">
            <div class="layui-card-header">
                <span class="">{{id ? '专题修改': '专题添加' }}</span>
                <button style="margin-left: 20px" type="button" class="layui-btn layui-btn-primary layui-btn-xs" @click="goBack">返回列表</button>
            </div>
            <div class="layui-card-body">
                <form class="layui-form" action="" v-cloak="">
                    <div class="layui-tab layui-tab-brief" lay-filter="docTabBrief">
                        <ul class="layui-tab-title">
                            <li class="layui-this" lay-id='1'>基本设置</li>
                            <li lay-id='2'>素材选择</li>
                            <li lay-id='3'>营销设置</li>
                            <li lay-id='4'>价格设置</li>
                        </ul>
                        <div class="layui-tab-content">
                            <div class="layui-tab-item layui-show">
                                <div class="layui-row layui-col-space15">
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">课程分类<i class="red">*</i></label>
                                                <div class="layui-input-block" id="subject_id">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">专题标题<i class="red">*</i></label>
                                                <div class="layui-input-block">
                                                    <input type="text" name="title" lay-verify="title" autocomplete="off"
                                                           placeholder="请输入专题标题" class="layui-input" v-model="formData.title" maxlength="100">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item layui-form-text">
                                                <label class="layui-form-label">专题简介<i class="red">*</i></label>
                                                <div class="layui-input-block">
                                                    <textarea name="abstract" v-model="formData.abstract"
                                                              placeholder="请输入专题简介" class="layui-textarea"></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item layui-form-text">
                                                <label class="layui-form-label">专题短语<i class="red">*</i></label>
                                                <div class="layui-input-block">
                                                    <textarea name="info" v-model="formData.phrase"
                                                              placeholder="请输入专题短语" class="layui-textarea"></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
<!--                                     <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item layui-form-text">
                                                <label class="layui-form-label">专题关键词<i class="red">*</i></label>
                                                <div class="layui-input-block">
                                                    <textarea name="keyword" v-model="formData.keyword"
                                                              placeholder="请输入专题关键词" class="layui-textarea"></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div> -->
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">专题封面(710*400px)<i class="red">*</i></label>
                                                <div class="pictrueBox">
                                                    <div class="pictrue" v-if="formData.image" @click="uploadImage('image')">
                                                        <img :src="formData.image"></div>
                                                    <div class="upLoad" @click="uploadImage('image')" v-else>
                                                        <i class="layui-icon layui-icon-camera" class="iconfont"
                                                           style="font-size: 26px;"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">专题banner(710*400px)<i class="red">*</i></label>
                                                <div class="pictrueBox pictrue" v-for="(item,index) in formData.banner">
                                                    <img :src="item">
                                                    <i class="layui-icon closes" @click="deleteImage('banner',index)">&#x1007</i>
                                                </div>
                                                <div class="pictrueBox">
                                                    <div class="upLoad" @click="uploadImage('banner')"
                                                         v-if="formData.banner.length <= rule.banner.maxLength">
                                                        <i class="layui-icon layui-icon-camera" class="iconfont"
                                                           style="font-size: 26px;"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">推广海报((690*590px))<i class="red">*</i></label>
                                                <div class="pictrueBox">
                                                    <div class="pictrue" v-if="formData.poster_image" @click="uploadImage('poster_image')">
                                                        <img :src="formData.poster_image"></div>
                                                    <div class="upLoad" @click="uploadImage('poster_image')" v-else>
                                                        <i class="layui-icon layui-icon-camera" class="iconfont"
                                                           style="font-size: 26px;"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">客服二维码((200*200px))<i class="red">*</i></label>
                                                <div class="pictrueBox">
                                                    <div class="pictrue" v-if="formData.service_code" @click="uploadImage('service_code')">
                                                        <img :src="formData.service_code"></div>
                                                    <div class="upLoad" @click="uploadImage('service_code')" v-else>
                                                        <i class="layui-icon layui-icon-camera" class="iconfont"
                                                           style="font-size: 26px;"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">专题标签：</label>
                                                <div class="layui-input-block">
                                                    <input type="text" style="width:50%;display:inline-block;margin-right: 10px;" name="label" lay-verify="label" autocomplete="off"
                                                           placeholder="请输入专题标签" class="layui-input" v-model="label" placeholder="最多10个字" autocomplete="off" maxlength="10" class="layui-input">
                                                             <button type="button" class="layui-btn layui-btn-normal" @click="addLabrl" >
                                                    <i class="layui-icon">&#xe654;</i>
                                                </button><sapn style="margin-left: 10px;color: red;">输入标签名称后点击”+“号按钮添加；最多写入10个字；点击标签即可删除</span>
                                                </div>
                                            </div>
                                            <div v-if="formData.label.length" class="layui-form-item" style="margin-top: 5px;">
                                                <div class="layui-input-block">
                                                    <button v-for="(item,index) in formData.label" :key="index" type="button" class="layui-btn layui-btn-normal layui-btn-sm" @click="delLabel(index)">{{item}}</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-form-item submit">
                                        <label class="layui-form-label">插入视频</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="link_key" v-model="videoLink" style="width:50%;display:inline-block;margin-right: 10px;" autocomplete="off" placeholder="请输入视频链接" class="layui-input">
                                            <button type="button" @click="uploadVideo" class="layui-btn layui-btn-sm layui-btn-normal">{{videoLink ? '确认添加' : '上传视频'}}</button>
                                            <input ref="videoFilElem" type="file" style="display: none">
                                        </div>
                                        <div class="layui-input-block video_show" style="width: 30%;margin-top: 20px;" v-if="upload.videoIng">
                                            <div class="layui-progress" style="margin-bottom: 10px">
                                                <div class="layui-progress-bar layui-bg-blue" :style="'width:'+progress+'%'"></div>
                                            </div>
                                            <button type="button" class="layui-btn layui-btn-sm layui-btn-danger percent">{{progress}}%</button>
                                        </div>
                                        <div class="layui-input-block" v-if="formData.video_link">
                                            <div class="layui-video-box" v-if="formData.video_link">
                                                <video style="width:100%;height: 100%!important;border-radius: 10px;" :src="formData.video_link" controls="controls">
                                                    您的浏览器不支持 video 标签。
                                                </video>
                                                <div class="mark" @click="delVideo">
                                                    <span class="layui-icon layui-icon-delete" style="font-size: 30px; color: #1E9FFF;"></span>
                                                </div>

                                            </div>
                                            <div class="layui-video-box" v-else>
                                                <i class="layui-icon layui-icon-play"></i>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">专题详情<i class="red">*</i></label>
                                                <div class="layui-input-block">
                                                    <textarea type="text/plain" name="content" id="myEditor" style="width:100%;">{{formData.content}}</textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">字节专题详情<i class="red">*</i></label>
                                                <div class="layui-input-block">
                                                    <textarea type="text/plain" name="byte_content" id="myByteEditor" style="width:100%;">{{formData.byte_content}}</textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm4 layui-col-md4">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">是否配置常见问题<i class="red">*</i></label>
                                                <div class="layui-input-block">
                                                    <input type="radio" name="is_problems" lay-filter="is_problems" value="1" title="是"
                                                           :checked="formData.is_problems == 1 ? true : false">
                                                    <input type="radio" name="is_problems" lay-filter="is_problems" value="0" title="否"
                                                           :checked="formData.is_problems == 0 ? true : false">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12" v-show="formData.is_problems == 1">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">常见问题<i class="red">*</i></label>
                                                <div class="layui-input-block">
                                                    <textarea type="text/plain" name="common_problem" id="myQuestionEditor" style="width:100%;">{{formData.common_problem}}</textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">排序：</label>
                                                <div class="layui-input-block">
                                                    <input type="number" name="title" lay-verify="sort" autocomplete="off"
                                                            class="layui-input" v-model="formData.sort">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-tab-item">
                                    <div class="layui-row layui-col-space15">
                                    <div class="layui-col-xs12 layui-col-sm4 layui-col-md4">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">是否多期<i class="red">*</i></label>
                                                <div class="layui-input-block">
                                                    <input type="radio" name="is_multi_period" lay-filter="is_multi_period" value="0" title="否"
                                                           :checked="formData.is_multi_period == 0 ? true : false">
                                                    <input type="radio" name="is_multi_period" lay-filter="is_multi_period" value="1" title="是"
                                                           :checked="formData.is_multi_period == 1 ? true : false">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-row layui-col-space15" v-show="formData.is_multi_period == 1">
                                    <div class="layui-col-xs12 layui-col-sm4 layui-col-md4">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">总期数<i class="red">*</i></label>
                                                <div class="layui-input-block">
                                                    <input type="text" name="total_period_number" lay-verify="total_period_number" autocomplete="off"
                                                           placeholder="请输入总期数" class="layui-input" v-model="formData.total_period_number" maxlength="100">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-row layui-col-space15">
                                    <div class="layui-col-xs12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">选择素材：<i class="red">*</i></label>
                                                <div class="layui-input-block">
                                                    <input type="hidden" id="check_source_tmp" name="check_source_tmp"/>
                                                    <button type="button" class="layui-btn layui-btn-normal" @click='search_task'>
                                                        选择素材
                                                    </button>
                                                    <div class="layui-form-mid layui-word-aux" style="float: none;display: inline-block;">如果素材列表中没有你要的素材，请：<a @click='add_source' style="color: #0093dd;">前往添加素材</a></div>
                                                </div>
                                            </div>
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">素材展示：</label>
                                                <div class="layui-input-block">
                                                    <input type="hidden" id="check_source_sure" name="check_source_sure"/>
                                                    <table class="layui-hide" id="showSourceList" lay-filter="showSourceList"></table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-row layui-col-space15">
                                    <div class="layui-col-xs12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">选择商品：</label>
                                                <div class="layui-input-block">
                                                    <input type="hidden" id="check_product_tmp" name="check_product_tmp"/>
                                                    <button type="button" class="layui-btn layui-btn-normal" @click='search_product'>
                                                        选择商品
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">商品展示：</label>
                                                <div class="layui-input-block">
                                                    <input type="hidden" id="check_product_sure" name="check_product_sure"/>
                                                    <table class="layui-hide" id="showProductList" lay-filter="showProductList"></table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-row layui-col-space15">
                                    <div class="layui-col-xs12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">选择课程：</label>
                                                <div class="layui-input-block">
                                                    <input type="hidden" id="check_special_tmp" name="check_special_tmp"/>
                                                    <button type="button" class="layui-btn layui-btn-normal" @click='search_special'>
                                                        选择课程
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">合集展示：</label>
                                                <div class="layui-input-block">
                                                    <input type="hidden" id="check_special_sure" name="check_special_sure"/>
                                                    <table class="layui-hide" id="showSpecialList" lay-filter="showSpecialList"></table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                </div>
                            </div>
                            <div class="layui-tab-item">
                                <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                    <div class="grid-demo grid-demo-bg1">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">老带新<i class="red">*</i></label>
                                            <div class="layui-input-block">
                                                <input type="radio" name="is_activity" lay-filter="is_activity" value="1" title="是"
                                                       :checked="formData.is_activity == 1 ? true : false">
                                                <input type="radio" name="is_activity" lay-filter="is_activity" value="0" title="否"
                                                       :checked="formData.is_activity == 0 ? true : false">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-row layui-col-space15">
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">选择直播：</label>
                                                <div class="layui-input-block">
                                                    <input type="hidden" id="check_live_tmp" name="check_live_tmp"/>
                                                    <button type="button" class="layui-btn layui-btn-normal" @click='search_live'>
                                                        选择直播
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">直播展示：</label>
                                                <div class="layui-input-block">
                                                    <input type="hidden" id="check_live_sure" name="check_live_sure"/>
                                                    <table class="layui-hide" id="showLiveList" lay-filter="showLiveList"></table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                    <div class="grid-demo grid-demo-bg1">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">跑马灯提示语开关<i class="red">*</i></label>
                                            <div class="layui-input-block">
                                                <input type="radio" name="is_live_info" lay-filter="is_live_info" value="1" title="是"
                                                       :checked="formData.is_live_info == 1 ? true : false">
                                                <input type="radio" name="is_live_info" lay-filter="is_live_info" value="0" title="否"
                                                       :checked="formData.is_live_info == 0 ? true : false">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-row layui-col-space15" v-show="formData.is_live_info == 1">
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">跑马灯提示语<i class="red">*</i></label>
                                                <div class="layui-input-block">
                                                    <input type="text" name="live_info" lay-verify="live_info" autocomplete="off"
                                                           placeholder="请输入跑马灯提示语" class="layui-input" v-model="formData.live_info" maxlength="100">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>                   
                                <div class="layui-row layui-col-space15" v-show="formData.is_live_info == 1">
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">预告直播时间文字<i class="red">*</i></label>
                                                <div class="layui-input-block">
                                                    <input type="text" name="notice_time" lay-verify="notice_time" autocomplete="off"
                                                           placeholder="请输入预告直播时间文字" class="layui-input" v-model="formData.notice_time" maxlength="100">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-tab-item">
                                <div class="layui-row layui-col-space15">
                                    <div class="layui-col-xs12 layui-col-sm4 layui-col-md4">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">付费方式<i class="red">*</i></label>
                                                <div class="layui-input-block">
                                                    <input type="radio" name="pay_type" lay-filter="pay_type" value="1" title="付费"
                                                           :checked="formData.pay_type == 1 ? true : false">
                                                    <input type="radio" name="pay_type" lay-filter="pay_type" value="0" title="免费"
                                                           :checked="formData.pay_type == 0 ? true : false">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-row layui-col-space15" v-show="formData.pay_type == 1">
                                    <div class="layui-col-xs12 layui-col-sm4 layui-col-md4">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">购买金额<i class="red">*</i></label>
                                                <div class="layui-input-block">
                                                    <input type="text" name="money" lay-verify="money" autocomplete="off"
                                                           placeholder="请输入购买金额" class="layui-input" v-model="formData.money" maxlength="100">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-tab-content">
                            <div class="layui-row layui-col-space15">
                                <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                    <div class="grid-demo grid-demo-bg1">
                                        <div class="layui-form-item" v-if="id">
                                            <button class="layui-btn layui-btn-primary layui-btn-sm" id="submit" type="button" @click="handleSubmit()">保存</button>
                                            <button class="layui-btn layui-btn-primary layui-btn-sm" type="button" @click="back" v-if="layTabId != 1">上一步</button>
                                            <button class="layui-btn layui-btn-normal layui-btn-sm" type="button" v-if="layTabId != 4" @click="next">下一步</button>
                                        </div>
                                        <div class="layui-form-item" v-else>
                                            <button class="layui-btn layui-btn-primary layui-btn-sm" type="button" @click="back" v-if="layTabId != 1">上一步</button>
                                            <button class="layui-btn layui-btn-normal layui-btn-sm" type="button" @click="next" v-if="layTabId != 4">下一步</button>
                                            <button class="layui-btn layui-btn-normal layui-btn-sm" id="submit" type="button" v-if="layTabId == 4" @click="handleSubmit()">提交</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<script src="{__PLUG_PATH}city.js"></script>
<script>
    var id = {$id};
    sourceCheckList = <?= isset($sourceCheckList) ? $sourceCheckList : "{}"?>;
    productCheckList = <?= isset($productCheckList) ? $productCheckList : "{}"?>;
    specialCheckList = <?= isset($specialCheckList) ? $specialCheckList : "{}"?>;
    liveCheckList = <?= isset($liveCheckList) ? $liveCheckList : "{}"?>;
    $.each(city,function (key,item) {
       city[key].value = item.label;
       if(item.children && item.children.length){
           $.each(item.children,function (i,v) {
               city[key].children[i].value=v.label;
               if(v.children && v.children.length){
                   $.each(v.children,function (k,val) {
                       city[key].children[i].children[k].value=val.label;
                   });
               }
           });
       }
   });
    new Vue({
        el: '#app',
        data: {
            id:id,
            //分类列表
            cateList: [],
            subject_list: [],
            source_list: [],
            source_tmp_list: [],//用于子页父业选中素材传值的临时变量
            live_tmp_list: [],//用于子页父业选中素材传值的临时变量
            product_tmp_list: [],//用于子页父业选中素材传值的临时变量
            special_tmp_list: [],//用于子页父业选中素材传值的临时变量
            lecturer_list: [],
            upload:{
                videoIng:false
            },
            addresData: city,
            formData: {
                subjectIds: '',
                phrase: '',
                label: [],
                abstract: '',
                keyword:'',
                live_info:'',
                notice_time:'',
                title: '',
                subject_id:  [],
                lecturer_id: 0,
                task_id: 0,
                image: '',
                banner: [],
                poster_image: '',
                service_code: '',
                money: 0.00,
                pink_money: 0.00,
                pink_number:  0,
                pink_strar_time: '',
                pink_end_time:'',
                fake_pink_number: 0,
                sort: 0,
                is_pink: 0,
                fake_sales: 0,
                browse_count: 0,
                pink_time: 0,
                content: '',
                byte_content: '',
                common_problem: '',
                pay_type: 0,
                is_live: 0,
                is_problems:0,
                is_activity:0,
                is_live_info:0,
                is_multi_period:0,
                total_period_number:0,
                check_source_sure: sourceCheckList ? sourceCheckList : "",
                check_product_sure: productCheckList ? productCheckList : [],
                check_special_sure: sourceCheckList ? sourceCheckList : [],
                check_live_sure: liveCheckList ? liveCheckList : [],
                member_pay_type: 0,
                member_money: 0.00,
                banner: []
            },
            videoLink:'',
            //多属性header头
            formHeader:[],
            radioRule: ['pay_type','is_live','is_live_info','is_multi_period','is_problems','is_activity'],//radio 当选规则
            radioLabel: ['pay_type','is_live','is_live_info','is_problems','is_activity'],//radio 当选规则
            rule: { //多图选择规则
                banner: {
                        maxLength: 5
                }
            },
            label: '',
            ruleList:[],
            ruleIndex:-1,
            progress: 0,
            um: null,//编译器实例化
            form: null,//layui.form
            layTabId: 1,
            ruleBool: id ? true : false,
        },
        watch:{
        },
        methods: {
            back:function(){
                var that = this;
                layui.use(['element'], function () {
                    layui.element.tabChange('docTabBrief', that.layTabId == 1 ? 1 : parseInt(that.layTabId) - 1);
                });
            },
            next:function(){
                var that = this;
                layui.use(['element'], function () {
                    layui.element.tabChange('docTabBrief', that.layTabId == 5 ? 5 : parseInt(that.layTabId) + 1);
                });
            },
            goBack:function(){

            },
            U: function (opt) {
                var m = opt.m || 'admin', c = opt.c || window.controlle || '', a = opt.a || 'index', q = opt.q || '',
                    p = opt.p || {};
                var params = Object.keys(p).map(function (key) {
                    return key + '/' + p[key];
                }).join('/');
                var gets = Object.keys(q).map(function (key) {
                    return key+'='+ q[key];
                }).join('&');

                return '/' + m + '/' + c + '/' + a + (params == '' ? '' : '/' + params) + (gets == '' ? '' : '?' + gets);
            },
            /**
             * 提示
             * */
            showMsg: function (msg, success) {
                $('#submit').removeAttr('disabled').text('提交');
                layui.use(['layer'], function () {
                    layui.layer.msg(msg, success);
                });
            },
            /**
             * 获取课程专题信息
             * */
            getActivityInfo: function () {
                var that = this;
                that.requestGet(that.U({m:window.module,c:"special.special_type",a:'get_special_info',q:{id:id}})).then(function (res) {
                    that.$set(that,'cateList',res.data.cateList);
                    var specialInfo = res.data.specialInfo || {};
                    if(specialInfo.id && that.id){
                        that.$set(that,'formData',specialInfo);
                    }
                    that.init();
                }).catch(function (res) {
                    that.showMsg(res.msg);
                })
            },
            /**
             * 删除图片
             * */
            deleteImage: function (key, index) {
                var that = this;
                if (index != undefined) {
                    that.formData[key].splice(index, 1);
                    that.$set(that.formData, key, that.formData[key]);
                } else {
                    that.$set(that.formData, key, '');
                }
            },
            createFrame: function (title, src, opt) {
                opt === undefined && (opt = {});
                var h = 0;
                if (window.innerHeight < 800 && window.innerHeight >= 700) {
                    h = window.innerHeight - 50;
                } else if (window.innerHeight < 900 && window.innerHeight >= 800) {
                    h = window.innerHeight - 100;
                } else if (window.innerHeight < 1000 && window.innerHeight >= 900) {
                    h = window.innerHeight - 150;
                } else if (window.innerHeight >= 1000) {
                    h = window.innerHeight - 200;
                } else {
                    h = window.innerHeight;
                }
                var area = [(opt.w || window.innerWidth / 2) + 'px', (!opt.h || opt.h > h ? h : opt.h) + 'px'];
                layui.use('layer',function () {
                    return layer.open({
                        type: 2,
                        title: title,
                        area: area,
                        fixed: false, //不固定
                        maxmin: true,
                        moveOut: false,//true  可以拖出窗外  false 只能在窗内拖
                        anim: 5,//出场动画 isOutAnim bool 关闭动画
                        offset: 'auto',//['100px','100px'],//'auto',//初始位置  ['100px','100px'] t[ 上 左]
                        shade: 0,//遮罩
                        resize: true,//是否允许拉伸
                        content: src,//内容
                        move: '.layui-layer-title'
                    });
                });
            },
            changeIMG: function (name, value) {
                if (this.getRule(name).maxLength !== undefined) {
                    var that = this;
                    value.map(function (v) {
                        that.formData[name].push(v);
                    });
                    this.$set(this.formData, name, this.formData[name]);
                } else {
                    if(name == 'batchAttr.pic'){
                        this.batchAttr.pic = value;
                    } else {
                        if (name.indexOf('.') !== -1) {
                            var key = name.split('.');
                            if (key.length == 2){
                                this.formData[key[0]][key[1]] = value;
                            } else if(key.length == 3){
                                this.formData[key[0]][key[1]][key[2]] = value;
                            } else if(key.length == 4){
                                this.$set(this.formData[key[0]][key[1]][key[2]],key[3],value)
                            }
                        } else {
                            this.formData[name] = value;
                        }
                    }
                }
            },
            getRule: function (name) {
                return this.rule[name] || {};
            },
            uploadImage: function (name) {
                return this.createFrame('选择图片',this.U({m:window.module,c:"widget.images",a:'index',p:{fodder:name}}),{h:545,w:900});
            },
            delLabel: function (index) {
                    this.formData.label.splice(index, 1);
                    this.$set(this.formData, 'label', this.formData.label);
            },
            addLabrl: function () {
                if (this.label) {
                    if (this.label.length > 10) return this.showMsg('您输入的标签字数太长');
                    var length = this.formData.label.length;
                    if (length >= 8) return this.showMsg('标签最多添加2个');
                    for (var i = 0; i < length; i++) {
                        if (this.formData.label[i] == this.label) return this.showMsg('请勿重复添加');
                    }
                    this.formData.label.push(this.label);
                    this.$set(this.formData, 'label', this.formData.label);
                    this.label = '';
                }else{
                    return this.showMsg('请输入正确标签值');
                }
            },
            search_task: function () {
                    var that = this;
                    var url = "{:Url('special.special_type/searchs_task')}?special_id=" + id + "&special_type={$special_type}";
                    var title = '选择素材';
                    that.searchTask = true;
                    layer.open({
                        type: 2 //Page层类型
                        , area: ['80%', '90%']
                        , title: title
                        , shade: 0.6 //遮罩透明度
                        , maxmin: true //允许全屏最小化
                        , anim: 1 //0-6的动画形式，-1不开启
                        , content: url,
                        btn: '确定',
                        btnAlign: 'c', //按钮居中
                        closeBtn: 1,
                        yes: function () {
                            layer.closeAll();
                            var source_tmp = $("#check_source_tmp").val();
                            that.source_tmp_list = JSON.parse(source_tmp);
                            that.formData.check_source_sure = JSON.parse(source_tmp);
                            that.show_source_list();
                        }
                    });
                },
            search_product: function () {
                var that = this;
                var url = "{:Url('special.special_type/searchs_product')}?id=" + id + "&type=1";
                var title = '选择商品';
                that.searchTask = true;
                layer.open({
                    type: 2 //Page层类型
                    , area: ['80%', '90%']
                    , title: title
                    , shade: 0.6 //遮罩透明度
                    , maxmin: true //允许全屏最小化
                    , anim: 1 //0-6的动画形式，-1不开启
                    , content: url,
                    btn: '确定',
                    btnAlign: 'c', //按钮居中
                    closeBtn: 1,
                    yes: function () {
                        layer.closeAll();
                        var product_tmp = $("#check_product_tmp").val();
                        that.product_tmp_list = JSON.parse(product_tmp);
                        that.formData.check_product_sure = JSON.parse(product_tmp);
                        that.show_product_list();
                    }
                });
            },
            search_special: function () {
                var that = this;
                var url = "{:Url('special.special_type/searchs_special')}?id=" + id + "&type=1";
                var title = '选择课程合集';
                that.searchSpecial = true;
                layer.open({
                    type: 2 //Page层类型
                    , area: ['80%', '90%']
                    , title: title
                    , shade: 0.6 //遮罩透明度
                    , maxmin: true //允许全屏最小化
                    , anim: 1 //0-6的动画形式，-1不开启
                    , content: url,
                    btn: '确定',
                    btnAlign: 'c', //按钮居中
                    closeBtn: 1,
                    yes: function () {
                        layer.closeAll();
                        var special_tmp = $("#check_special_tmp").val();
                        that.special_tmp_list = JSON.parse(special_tmp);
                        that.formData.check_special_sure = JSON.parse(special_tmp);
                        that.show_special_list();
                    }
                });
            },
            search_live: function () {
                var that = this;
                var url = "{:Url('special.special_type/searchs_live')}?id=" + id + "&type=1";
                var title = '选择课程合集';
                that.searchSpecial = true;
                layer.open({
                    type: 2 //Page层类型
                    , area: ['80%', '90%']
                    , title: title
                    , shade: 0.6 //遮罩透明度
                    , maxmin: true //允许全屏最小化
                    , anim: 1 //0-6的动画形式，-1不开启
                    , content: url,
                    btn: '确定',
                    btnAlign: 'c', //按钮居中
                    closeBtn: 1,
                    yes: function () {
                        layer.closeAll();
                        var live_tmp = $("#check_live_tmp").val();
                        that.live_tmp_list = JSON.parse(live_tmp);
                        that.formData.check_live_sure = JSON.parse(live_tmp);
                        that.show_live_list();
                    }
                });
            },
            add_source: function (name) {
                return this.createFrame('添加素材',this.U({m:window.module,c:"special.special_type",a:'addSources',p:{fodder:name}}),{h:545,w:900});
            },
            show_source_list: function () {
                    var that = this;
                    var table = layui.table;
                    var form = layui.form;
                    console.log(layui.table);
                    table.render({
                        elem: '#showSourceList',
                        cols: [[
                            {field: 'id', title: '编号', align: 'center',width:110},
                            {field: 'title', title: '素材标题', edit: 'title', align: 'center',width:800},
                            {
                                field: 'image',
                                title: '封面',
                                templet: '<div><img src="{{ d.image }}" style="width: 100%;"></div>',
                                align: 'center',
                                width:300,

                            },
                            {field: 'sort', title: '排序',edit:'sort',align: 'center',width: 150},
                            {
                                field: 'right', title: '操作', align: 'center',width: 300, templet: function (d) {
                                    return '<div><a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del"><i class="layui-icon">&#xe640;</i> 移除</a></div>';
                                }
                            },
                        ]],
                        data: (Object.keys(that.formData.check_source_sure).length > 0) ? that.formData.check_source_sure : [],
                        page: {
                          theme: '#0092DC'
                      },
                        id: 'table'
                    });
                    table.on('tool(showSourceList)', function(obj){
                        var data = obj.data;
                        if(obj.event === 'del'){
                            if (that.formData.check_source_sure) {
                                for(var i=0;i<that.formData.check_source_sure.length;i++){
                                    if(that.formData.check_source_sure[i].id==data.id){
                                        that.formData.check_source_sure.splice(i,1);
                                    }
                                }
                                that.formData.check_source_sure=that.formData.check_source_sure;
                                that.show_source_list();
                            }
                        }
                    });
                    //监听单元格编辑
                    table.on('edit(showSourceList)', function(obj){
                        var id=obj.data.id,values=obj.value;
                        switch (obj.field) {
                            case 'sort':
                                if (that.formData.check_source_sure) {
                                    $.each(that.formData.check_source_sure, function(index, value){
                                        if(value.id == id){
                                            that.formData.check_source_sure[index].sort = values;
                                        }
                                    })
                                }
                                break;
                        }
                    });
                    //监听素材是否删除
                    form.on('switch(delect)', function (obj) {
                        if (that.formData.check_source_sure) {
                            for (var i = 0; i < that.formData.check_source_sure.length; i++) {
                                if (that.formData.check_source_sure[i].id == obj.value) {
                                    that.formData.check_source_sure.splice(i, 1);
                                }
                            }
                            that.formData.check_source_sure = that.formData.check_source_sure;
                            that.show_source_list();
                        }
                    });
            },
            show_product_list: function () {
                    var that = this;
                    var table = layui.table;
                    var form = layui.form;
                    table.render({
                        elem: '#showProductList',
                        cols: [[
                            {field: 'id', title: '编号', align: 'center',width:110},
                            {field: 'store_name', title: '商品名', edit: 'store_name', align: 'center',width:800},
                            {
                                field: 'image',
                                title: '封面',
                                templet: '<div><img src="{{ d.image }}" style="width: 100%;"></div>',
                                align: 'center',
                                width:300
                            },
                            {field: 'sort', title: '排序',edit:'sort',align: 'center',width: 150},
                            {
                                field: 'right', title: '操作', align: 'center',width: 300, templet: function (d) {
                                    return '<div><a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del"><i class="layui-icon">&#xe640;</i> 移除</a></div>';
                                }
                            },
                        ]],
                        data: (Object.keys(that.formData.check_product_sure).length > 0) ? that.formData.check_product_sure : [],
                        page: {
                          theme: '#0092DC'
                      },
                        id: 'table'
                    });
                    table.on('tool(showProductList)', function(obj){
                        var data = obj.data;
                        if(obj.event === 'del'){
                            if (that.formData.check_product_sure) {
                                for(var i=0;i<that.formData.check_product_sure.length;i++){
                                    if(that.formData.check_product_sure[i].id==data.id){
                                        that.formData.check_product_sure.splice(i,1);
                                    }
                                }
                                that.formData.check_product_sure=that.formData.check_product_sure;
                                that.show_product_list();
                            }
                        }
                    });
                    //监听单元格编辑
                    table.on('edit(showProductList)', function(obj){
                        var id=obj.data.id,values=obj.value;
                        switch (obj.field) {
                            case 'sort':
                                if (that.formData.check_product_sure) {
                                    $.each(that.formData.check_product_sure, function(index, value){
                                        if(value.id == id){
                                            that.formData.check_product_sure[index].sort = values;
                                        }
                                    })
                                }
                                break;
                        }
                    });
                    //监听素材是否删除
                    form.on('switch(delect)', function (obj) {
                        if (that.formData.check_product_sure) {
                            for (var i = 0; i < that.formData.check_product_sure.length; i++) {
                                if (that.formData.check_product_sure[i].id == obj.value) {
                                    that.formData.check_product_sure.splice(i, 1);
                                }
                            }
                            that.formData.check_product_sure = that.formData.check_product_sure;
                            that.show_product_list();
                        }
                    });
            },
            show_special_list: function () {
                    var that = this;
                    var table = layui.table;
                    var form = layui.form;
                    console.log(layui.table);
                    table.render({
                        elem: '#showSpecialList',
                        cols: [[
                            {field: 'id', title: '编号', align: 'center',width:110},
                            {field: 'title', title: '合集名', edit: 'title', align: 'center',width:800},
                            {
                                field: 'image',
                                title: '封面',
                                templet: '<div><img src="{{ d.image }}" style="width: 100%;"></div>',
                                align: 'center',
                                width:300
                            },
                            {field: 'sort', title: '排序',edit:'sort',align: 'center',width: 150},
                            {
                                field: 'right', title: '操作', align: 'center',width: 300, templet: function (d) {
                                    return '<div><a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del"><i class="layui-icon">&#xe640;</i> 移除</a></div>';
                                }
                            },
                        ]],
                        data: (Object.keys(that.formData.check_special_sure).length > 0) ? that.formData.check_special_sure : [],
                        page: {
                          theme: '#0092DC'
                      },
                        id: 'table'
                    });
                    table.on('tool(showSpecialList)', function(obj){
                        var data = obj.data;
                        if(obj.event === 'del'){
                            if (that.formData.check_special_sure) {
                                for(var i=0;i<that.formData.check_special_sure.length;i++){
                                    if(that.formData.check_special_sure[i].id==data.id){
                                        that.formData.check_special_sure.splice(i,1);
                                    }
                                }
                                that.formData.check_special_sure=that.formData.check_special_sure;
                                that.show_special_list();
                            }
                        }
                    });
                    //监听单元格编辑
                    table.on('edit(showSpecialList)', function(obj){
                        var id=obj.data.id,values=obj.value;
                        switch (obj.field) {
                            case 'sort':
                                if (that.formData.check_special_sure) {
                                    $.each(that.formData.check_special_sure, function(index, value){
                                        if(value.id == id){
                                            that.formData.check_special_sure[index].sort = values;
                                        }
                                    })
                                }
                                break;
                        }
                    });
                    //监听素材是否免费操作
                    form.on('switch(pay_status)', function (obj) {
                        if (that.formData.check_special_sure) {
                            $.each(that.formData.check_special_sure, function (index, value) {
                                if (value.id == obj.value) {
                                    that.formData.check_special_sure[index].pay_status = obj.elem.checked == true ? 0 : 1;
                                }
                            })
                        }
                    });
                    //监听素材是否删除
                    form.on('switch(delect)', function (obj) {
                        if (that.formData.check_special_sure) {
                            for (var i = 0; i < that.formData.check_special_sure.length; i++) {
                                if (that.formData.check_special_sure[i].id == obj.value) {
                                    that.formData.check_special_sure.splice(i, 1);
                                }
                            }
                            that.formData.check_special_sure = that.formData.check_special_sure;
                            that.show_special_list();
                        }
                    });
            },
            show_live_list: function () {
                    var that = this;
                    var table = layui.table;
                    var form = layui.form;
                    console.log(layui.table);
                    table.render({
                        elem: '#showLiveList',
                        cols: [[
                            {field: 'id', title: '编号', align: 'center',width:110},
                            {field: 'title', title: '活动名', edit: 'title', align: 'center',width:800},
                            {
                                field: 'image',
                                title: '封面',
                                templet: '<div><img src="{{ d.image }}" style="width: 100%;"></div>',
                                align: 'center',
                                width:300,
                            },
                            {field: 'sort', title: '排序',edit:'sort',align: 'center',width: 150},
                            {
                                field: 'right', title: '操作', align: 'center',width: 300, templet: function (d) {
                                    return '<div><a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del"><i class="layui-icon">&#xe640;</i> 移除</a></div>';
                                }
                            },
                        ]],
                        data: (Object.keys(that.formData.check_live_sure).length > 0) ? that.formData.check_live_sure : [],
                        page: {
                          theme: '#0092DC'
                      },
                        id: 'table'
                    });
                    table.on('tool(showLiveList)', function(obj){
                        var data = obj.data;
                        if(obj.event === 'del'){
                            if (that.formData.check_live_sure) {
                                for(var i=0;i<that.formData.check_live_sure.length;i++){
                                    if(that.formData.check_live_sure[i].id==data.id){
                                        that.formData.check_live_sure.splice(i,1);
                                    }
                                }
                                that.formData.check_live_sure=that.formData.check_live_sure;
                                that.show_live_list();
                            }
                        }
                    });
                    //监听单元格编辑
                    table.on('edit(showLiveList)', function(obj){
                        var id=obj.data.id,values=obj.value;
                        switch (obj.field) {
                            case 'sort':
                                if (that.formData.check_live_sure) {
                                    $.each(that.formData.check_live_sure, function(index, value){
                                        if(value.id == id){
                                            that.formData.check_live_sure[index].sort = values;
                                        }
                                    })
                                }
                                break;
                        }
                    });
                    //监听素材是否免费操作
                    form.on('switch(pay_status)', function (obj) {
                        if (that.formData.check_live_sure) {
                            $.each(that.formData.check_live_sure, function (index, value) {
                                if (value.id == obj.value) {
                                    that.formData.check_live_sure[index].pay_status = obj.elem.checked == true ? 0 : 1;
                                }
                            })
                        }
                    });
                    //监听素材是否删除
                    form.on('switch(delect)', function (obj) {
                        if (that.formData.check_live_sure) {
                            for (var i = 0; i < that.formData.check_live_sure.length; i++) {
                                if (that.formData.check_live_sure[i].id == obj.value) {
                                    that.formData.check_live_sure.splice(i, 1);
                                }
                            }
                            that.formData.check_live_sure = that.formData.check_live_sure;
                            that.show_live_list();
                        }
                    });
            },
            uploadVideo: function () {
                if (this.videoLink) {
                    this.formData.video_link = this.videoLink;
                } else {
                    $(this.$refs.filElem).click();
                }
            },
            openWindows: function(title, url, opt) {
                return this.createFrame(title, url, opt);
            },
            delVideo: function () {
                var that = this;
                that.$set(that.formData, 'video_link', '');
            },
            insertEditor: function (list) {
                this.um.execCommand('insertimage', list);
            },
            insertEditorVideo: function (src) {
                UM.getEditor('myEditor').focus();
               UM.getEditor('myEditor').execCommand('inserthtml','<div><video style="width: 99%" src="'+src+'" class="video-ue" controls="controls" width="100"><source src="'+src+'"></source></video></div><br>');
            },
            insertEditorAudio: function (src) {
                UM.getEditor('myEditor').focus();
                UM.getEditor('myEditor').execCommand('inserthtml','<div><audio style="width: 99%" src="'+src+'" class="audio-ue" controls="controls" width="100"><source src="'+src+'"></source></audio></div><br>');
            },
            getContent: function () {
                return this.um.getContent();
            },
            insertByteEditor: function (list) {
                this.um.execCommand('insertimage', list);
            },
            insertByteEditorVideo: function (src) {
                UM.getEditor('myByteEditor').focus();
               UM.getEditor('myByteEditor').execCommand('inserthtml','<div><video style="width: 99%" src="'+src+'" class="video-ue" controls="controls" width="100"><source src="'+src+'"></source></video></div><br>');
            },
            insertByteEditorAudio: function (src) {
                UM.getEditor('myByteEditor').focus();
                UM.getEditor('myByteEditor').execCommand('inserthtml','<div><audio style="width: 99%" src="'+src+'" class="audio-ue" controls="controls" width="100"><source src="'+src+'"></source></audio></div><br>');
            },
            getByteContent: function () {
                return this.ums.getContent();
            },
            insertQuestionEditor: function (list) {
                this.umss.execCommand('insertimage', list);
            },
            insertQuestionEditorVideo: function (src) {
               UMS.getEditor('myQuestionEditor').focus();
               UMS.getEditor('myQuestionEditor').execCommand('inserthtml','<div><video style="width: 99%" src="'+src+'" class="video-ue" controls="controls" width="100"><source src="'+src+'"></source></video></div><br>');
            },
            insertQuestionAudio: function (src) {
                UMS.getEditor('myQuestionEditor').focus();
                UMS.getEditor('myQuestionEditor').execCommand('inserthtml','<div><audio style="width: 99%" src="'+src+'" class="audio-ue" controls="controls" width="100"><source src="'+src+'"></source></audio></div><br>');
            },
            getQuestionContent: function () {
                return this.umss.getContent();
            },
            /**
             * 监听radio字段
             */
            eeventRadio: function () {
                var that = this;
                that.radioRule.map(function (val) {
                    that.form.on('radio(' + val + ')', function (res) {
                        that.formData[val] = res.value;
                    });
                })
            },
            init: function () {
                var that = this;
                window.UMEDITOR_CONFIG.toolbar = [
                    // 加入一个 test
                    'source | undo redo | bold italic underline strikethrough | superscript subscript | forecolor backcolor | removeformat |',
                    'insertorderedlist insertunorderedlist | selectall cleardoc paragraph | fontfamily fontsize',
                    '| justifyleft justifycenter justifyright justifyjustify |',
                    'link unlink | emotion selectimgs video  | map',
                    '| horizontal print preview fullscreen', 'drafts', 'formula'
                ];
                UM.registerUI('selectimgs', function (name) {
                    var me = this;
                    var $btn = $.eduibutton({
                        icon: 'image',
                        click: function () {
                            that.createFrame('选择图片', "{:Url('widget.images/index',['fodder'=>'editor'])}");
                        },
                        title: '选择图片'
                    });

                    this.addListener('selectionchange', function () {
                        //切换为不可编辑时，把自己变灰
                        var state = this.queryCommandState(name);
                        $btn.edui().disabled(state == -1).active(state == 1)
                    });
                    return $btn;

                });
                UM.registerUI('video', function (name) {
                    var me = this;
                    var $btn = $.eduibutton({
                        icon: 'video',
                        click: function () {
                            that.createFrame('选择音视频', "{:Url('widget.video/indexs',['fodder'=>'video'])}");
                        },
                        title: '选择音视频'
                    });

                    this.addListener('selectionchange', function () {
                        //切换为不可编辑时，把自己变灰
                        var state = this.queryCommandState(name);
                        $btn.edui().disabled(state == -1).active(state == 1)
                    });
                    return $btn;

                });
                //实例化编辑器
                this.um = UM.getEditor('myEditor', {initialFrameWidth: '99%', initialFrameHeight: 400});
                this.um.setContent(that.formData.content);
                this.ums = UM.getEditor('myByteEditor', {initialFrameWidth: '99%', initialFrameHeight: 400});
                this.ums.setContent(that.formData.byte_content);
                this.umss = UM.getEditor('myQuestionEditor', {initialFrameWidth: '99%', initialFrameHeight: 400});
                this.umss.setContent(that.formData.common_problem);
                that.$nextTick(function () {
                    layui.use(['form','element','table','laydate'], function () {
                        that.form = layui.form;
                        that.laydate = layui.laydate;
                        that.form.render();
                        that.form.on('select(labelIndex)', function (data) {
                                that.setLabelTable(parseInt(data.value),false)
                        });
                        that.form.on('select(rule_index)', function (data) {
                            that.ruleIndex = data.value;
                        });
                        layui.element.on('tab(docTabBrief)', function(){
                            that.layTabId = this.getAttribute('lay-id');
                        });
                        that.eeventRadio();
                        that.show_source_list();
                        that.show_product_list();
                        that.show_special_list();
                        that.show_live_list();
                    });
                      layui.config({
                        base : '/static/plug/layui/'
                    }).extend({
                        selectN: './selectN',
                    }).use('selectM',function () {
                        var selectM = layui.selectM;
                        selectM({
                            //元素容器【必填】
                            elem: '#subject_id'
                            //候选数据【必填】
                            ,data: that.cateList
                            //默认值
                            ,selected: that.formData.subject_id || []
                            //最多选中个数，默认5
                            ,max : 1
                            ,name: 'subject_id'
                            ,model: 'formData.subject_id'
                            //值的分隔符
                            ,delimiter: ','
                            //候选项数据的键名
                            ,field: {idName:'id',titleName:'label',statusName:'disabled'}
                        });
                    });
                })
            },
            requestPost: function (url, data) {
                return new Promise(function (resolve, reject) {
                    axios.post(url, data).then(function (res) {
                        if (res.status == 200 && res.data.code == 200) {
                            resolve(res.data)
                        } else {
                            reject(res.data);
                        }
                    }).catch(function (err) {
                        reject({msg:err})
                    });
                })
            },
            requestGet: function (url) {
                return new Promise(function (resolve, reject) {
                    axios.get(url).then(function (res) {
                        if (res.status == 200 && res.data.code == 200) {
                            resolve(res.data)
                        } else {
                            reject(res.data);
                        }
                    }).catch(function (err) {
                        reject({msg:err})
                    });
                })
            },
            handleSubmit:function () {
                var that = this;
                subject_id = $('input[name="subject_id"]').val();
                if (subject_id != '') {
                    this.formData.subject_id = subject_id.split(',');
                }else{
                    this.formData.subject_id = [];
                }
                if (!that.formData.subject_id.length) {
                    return that.showMsg('请选择课程分类');
                }
                if (!that.formData.title) {
                    return that.showMsg('请填写课程专题标题');
                }
                if (!that.formData.abstract) {
                    return that.showMsg('请填写课程专题简介');
                }
                if (!that.formData.phrase) {
                    return that.showMsg('请填写课程专题短语');
                }
                // if (!that.formData.keyword) {
                //     return that.showMsg('请填写课程专题关键词');
                // }
                if (!that.formData.image) {
                    return that.showMsg('请填写课程专题封面');
                }
                if (!that.formData.banner.length) {
                    return that.showMsg('请选择课程专题banner');
                }
                if (!that.formData.poster_image) {
                    return that.showMsg('请填写课程专题推广海报');
                }
                if (!that.formData.service_code) {
                    return that.showMsg('请填写客服二维码');
                }
                // if (!that.formData.label.length) {
                //     return that.showMsg('请选择课程专题专题标签');
                // }
                that.formData.content = that.getContent();
                if (!that.formData.content) {
                    return that.showMsg('请填写课程专题专题详情');
                }
              that.formData.byte_content = that.getByteContent();
                if (!that.formData.byte_content) {
                    return that.showMsg('请填写字节课程专题专题详情');
                }
                if (that.formData.is_problems == 1 ) {
                    that.formData.common_problem = that.getQuestionContent();
                    if (!that.formData.content) {
                        return that.showMsg('请填写课程专题专题详情');
                    }
                }
                // if(Object.keys(that.formData.check_source_sure).length == 0) return that.showMsg('请选择素材');
                that.formData.subjectIds = JSON.stringify(that.formData.check_source_sure);
                $('#submit').attr('disabled', 'disabled').text('保存中...');
                that.requestPost(that.U({m:window.module,c:'special.special_type',a:'save_special',p:{id:that.id,special_type:'{$special_type}'}}),that.formData).then(function (res) {
                    that.confirm();
                }).catch(function (res) {
                    that.showMsg(res.msg);
                });
            },
            confirm: function(){
                var that = this;
                layui.use(['layer'], function () {
                    var layer = layui.layer;
                    layer.confirm(that.id ? '修改成功是否返回课程专题列表' : '添加成功是否返回课程专题列表', {
                        btn: ['返回列表',that.id ? '继续修改' : '继续添加'] //按钮
                    }, function(){
                        location.href = that.U({m:window.module,c:'special.special_type',a:'index',p:{special_type:'{$special_type}'}});
                    }, function(){
                        location.reload();
                    });
                });
            },
            render:function(){
                this.$nextTick(function(){
                    layui.use(['form'], function () {
                        layui.form.render('select');
                    });
                })
            },
            // 移动
            handleDragStart (e, item) {
                this.dragging = item;
            },
            handleDragEnd (e, item) {
                this.dragging = null
            },
            handleDragOver (e) {
                e.dataTransfer.dropEffect = 'move'
            },
            handleDragEnter (e, item) {
                e.dataTransfer.effectAllowed = 'move'
                if (item === this.dragging) {
                    return
                }
                var newItems = [...this.formData.activity];
                var src = newItems.indexOf(this.dragging);
                var dst = newItems.indexOf(item);
                newItems.splice(dst, 0, ...newItems.splice(src, 1))
                this.formData.activity = newItems;
            },
        },
        mounted: function () {
            var that = this;
            axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';
            that.getActivityInfo();
            window.$vm = that;
            window.changeIMG = that.changeIMG;
            window.insertEditor = that.insertEditor;
            window.insertEditorVideo = that.insertEditorVideo;
            window.insertEditorAudio = that.insertEditorAudio;
            window.insertByteEditor = that.insertByteEditor;
            window.insertByteEditorVideo = that.insertByteEditorVideo;
            window.insertByteEditorAudio = that.insertByteEditorAudio;
            window.insertQuestionEditor = that.insertQuestionEditor;
            window.insertQuestionEditorVideo = that.insertQuestionEditorVideo;
            window.insertQuestionEditorAudio = that.insertQuestionEditorAudio;
            $(that.$refs.filElem).change(function () {
                var inputFile = this.files[0];
                that.requestPost(that.U({m:window.module,c:"widget.video",a:'get_signature'})).then(function (res) {
                    AdminUpload.upload(res.data.uploadType,{
                        token: res.data.uploadToken || '',
                        file: inputFile,
                        accessKeyId: res.data.accessKey || '',
                        accessKeySecret: res.data.secretKey || '',
                        bucketName: res.data.storageName || '',
                        region: res.data.storageRegion || '',
                        domain: res.data.domain || '',
                        uploadIng:function (progress) {
                            that.upload.videoIng = true;
                            that.progress = progress;
                        }
                    }).then(function (res) {
                        //成功
                        that.$set(that.formData, 'video_link', res.url);
                        that.progress = 0;
                        that.upload.videoIng = false;
                        return that.showMsg('上传成功');
                    }).catch(function (err) {
                        //失败
                        console.info(err);
                        return that.showMsg('上传错误请检查您的配置');
                    });
                }).catch(function (res) {
                    return that.showMsg(res.msg || '获取密钥失败,请检查您的配置');
                });
            })
        }
    });
</script>
</body>
</html>
