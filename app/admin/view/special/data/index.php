{extend name="public/container"}
{block name="head_top"}

{/block}
{block name="content"}
<style>
    .btn-outline{
        border:none;
    }
    .btn-outline:hover{
        background-color: #0e9aef;
        color: #fff;
    }
    .layui-form-item .layui-btn {
        margin-top: 5px;
        margin-right: 10px;
    }
    .layui-btn-primary{
        margin-right: 10px;
        margin-left: 0!important;
    }
    label{
        margin-bottom: 0!important;
        margin-top: 4px;
    }
</style>
<div class="layui-fluid">
    <div class="layui-row layui-col-space15" id="app">
        <!--搜索条件-->
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">搜索条件</div>
                <div class="layui-card-body">
                    <div class="layui-carousel layadmin-carousel layadmin-shortcut" lay-anim="" lay-indicator="inside"
                         lay-arrow="none" style="background:none">
                        <div class="layui-card-body layui-form">
                            <div class="layui-row layui-col-space10 layui-form-item">
                                <div class="layui-form-item">
                                  <label class="layui-form-label">专题名称:</label>
                                  <div class="layui-input-block" style="width: 496px">
                                       <select name="special_id" v-model="where.special_id" lay-filter="special_id"   lay-search="">
                                            <option value="">全部</option>
                                            {volist name='specialList' id='vo'}
                                                <option value="{$vo.id}">{$vo.store_name}</option>
                                            {/volist}
                                        </select>     
                                  </div>
                                </div>
                                <div class="layui-form-item">
                                  <label class="layui-form-label">免费视频:</label>
                                  <div class="layui-input-block" style="width: 496px">
                                       <select name="source_id" v-model="where.source_id" lay-filter="source_id"   lay-search="">
                                            <option value="">全部</option>
                                            {volist name='sourceList' id='vo'}
                                                <option value="{$vo.id}">{$vo.store_name}</option>
                                            {/volist}
                                        </select>     
                                  </div>
                                </div>
                                <div class="layui-col-lg12">
                                    <label class="layui-form-label">评论数量:</label>
                                    <div class="layui-input-block" data-type="comment" v-cloak="">
                                        <button class="layui-btn layui-btn-sm" type="button" v-for="item in commentList"
                                                @click="setCommentData(item)"
                                                :class="{'layui-btn-primary':where.comment!=item.value}">{{item.name}}
                                        </button>
                                    </div>
                                </div>
                                <div class="layui-col-lg12">
                                    <label class="layui-form-label">观看时长:</label>
                                    <div class="layui-input-block" data-type="time" v-cloak="">
                                        <button class="layui-btn layui-btn-sm" type="button" v-for="item in timeList"
                                                @click="setWachData(item)"
                                                :class="{'layui-btn-primary':where.time!=item.value}">{{item.name}}
                                        </button>
                                    </div>
                                </div>
                                <div class="layui-col-lg12">
                                    <label class="layui-form-label">消费金额:</label>
                                    <div class="layui-input-block" data-type="spend" v-cloak="">
                                        <button class="layui-btn layui-btn-sm" type="button" v-for="item in spendList"
                                                @click="setSpendData(item)"
                                                :class="{'layui-btn-primary':where.spend!=item.value}">{{item.name}}
                                        </button>
                                    </div>
                                </div>
                                <div class="layui-col-lg12">
                                    <div class="layui-input-block">
                                        <button @click="search" type="button"
                                                class="layui-btn layui-btn-sm layui-btn-normal" lay-filter="formDemo">
                                            <i class="layui-icon layui-icon-search"></i>搜索
                                        </button>
                                        <button @click="excel" type="button"
                                                class="layui-btn layui-btn-warm layui-btn-sm export" type="button">
                                            <i class="fa fa-floppy-o" style="margin-right: 3px;"></i>导出
                                        </button>
                                        <button @click="refresh" type="reset"
                                                class="layui-btn layui-btn-primary layui-btn-sm">
                                            <i class="layui-icon layui-icon-refresh"></i>刷新
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--列表-->
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">统计列表</div>
                <div class="layui-card-body">
                    <div class="layui-btn-container" id="container-action">
                    </div>
                    <table class="layui-hide" id="List" lay-filter="List"></table>
                    <!--订单-->
                    <script type="text/html" id="order_id">
                        {{d.order_id}}<br/>
                        {{d.deposit_order_id}}<br/>
                        {{d.final_payment_order_id}}<br/>
                        {{# if(d.store_name){ }}
                            {{d.store_name}}<br/>
                        {{# } }}
                        <span style="color: {{d.color}};">{{d.pink_name}}</span><br/>　
                        {{#  if(d.is_cancel == 1 ){ }}
                            <span style="color: {{d.color}};">订单已取消</span>
                        {{#  }else{ }}
                            {{#  if(d.is_del == 1){ }}<span style="color: {{d.color}};">用户已删除</span>{{# } }}　
                        {{# } }}
                    </script>
                    <!--用户信息-->
                    <script type="text/html" id="userinfo">
                        {{d.nickname==null ? '暂无信息':d.nickname}}/{{d.uid}}
                    </script>
                    <script type="text/html" id="invite_info">
                        {{#  if(d.invite_nickname !=null ){ }}
                            {{d.invite_nickname==null ? '暂无信息':d.invite_nickname}}/{{d.uid}}
                        {{#  }else{ }}
                            暂无信息
                        {{# } }}
                        <br>
                        {{#  if(d.invite_order_id !=null ){ }}
                            关联的订单ID：{{d.invite_order_id}}
                        {{# } }}
                    </script>
                    <!--订单状态-->
                    <script type="text/html" id="status">
                        {{d.status_name}}
                    </script>
                    <!--活动信息-->
                    <script type="text/html" id="info">
                        {{#  layui.each(d._info, function(index, item){ }}
                        {{#  if(item.cart_info.activityInfo.attrInfo!=undefined){ }}
                        <div>
                            <span>
                                <img style="width: 30px;height: 30px;margin:0;cursor: pointer;"
                                     src="{{item.cart_info.activityInfo.attrInfo.image}}">
                            </span>
                            <span>{{item.cart_info.activityInfo.store_name}}&nbsp;{{item.cart_info.activityInfo.attrInfo.suk}}</span>
                            <span> | ￥{{item.cart_info.truePrice}}×{{item.cart_info.cart_num}}</span>
                        </div>
                        {{#  }else{ }}
                        <div>
                            <span><img style="width: 30px;height: 30px;margin:0;cursor: pointer;"
                                       src="{{item.cart_info.activityInfo.image}}"></span>
                            <span>{{item.cart_info.activityInfo.store_name}}</span><span> | ￥{{item.cart_info.truePrice}}×{{item.cart_info.cart_num}}</span>
                        </div>
                        {{# } }}
                        {{#  }); }}
                    </script>
                    <script type="text/html" id="act">
                        <button type="button" class="layui-btn layui-btn-xs" onclick="dropdown(this)">操作 <span
                                    class="caret"></span></button>
                        <ul class="layui-nav-child layui-anim layui-anim-upbit">
                            <li>
                                <a href="javascript:void(0);" lay-event='order_info'>
                                    <i class="fa fa-file-text"></i> 订单详情
                                </a>
                            </li>
                            <li>
                                <a href="javascript:void(0);"
                                   onclick="$eb.createModalFrame('修改订单','{:Url('edit')}?id={{d.id}}')">
                                    <i class="fa fa-edit"></i> 修改订单
                                </a>
                            </li>
                            <li>
                                <a lay-event='marke' href="javascript:void(0);">
                                    <i class="fa fa-edit"></i> 订单备注
                                </a>
                            </li>
                            <li>
                                <a href="javascript:void(0);"
                                   onclick="$eb.createModalFrame('订单记录','{:Url('order_status')}?oid={{d.id}}')">
                                    <i class="fa fa-newspaper-o"></i> 订单记录
                                </a>
                            </li>
                        </ul>
                    </script>
                </div>
            </div>
        </div>
    </div>
    <!--end-->
</div>
<script src="{__ADMIN_PATH}js/layuiList.js"></script>
    <script src="/static/plug/layui/layui.js"></script>
{/block}
{block name="script"}
<script>
    //实例化form
    layList.tableList('List', "{:Url('data_list')}", function () {
        return [
            {type: 'checkbox'},
            {field: 'order_id', title: '手机号',  width: '14%'},
            {field: 'nickname', title: '用户信息', templet: '#userinfo', width: '10%', align: 'center'},
            {field: 'info', title: '身份', templet: "#info", height: 'full-20'},
            {field: 'info', title: '城市', templet: "#info", height: 'full-20'},
            {field: 'pay_type', title: '年龄段', templet: '#pay_type_name', width: '8%', align: 'center'},
            {field: 'pay_price', title: '购买内容', width: '8%', align: 'center',templet: '#payinfo'},
            {field: 'status', title: '来源', templet: '#status', width: '8%', align: 'center'},
            {field: 'status', title: '用户评论总数', templet: '#status', width: '8%', align: 'center'},
            {field: 'status', title: '用户观看时长', templet: '#status', width: '8%', align: 'center'},
        ];
    });
    layList.tool(function (event, data, obj) {
        switch (event) {
            case 'data_info':
                $eb.createModalFrame(data.nickname + '订单详情', layList.U({a: 'order_info', q: {oid: data.id}}));
                break;
        }
    })
    var action = {
    };
    $('#container-action').find('button').each(function () {
        $(this).on('click', function () {
            var act = $(this).data('type');
            action[act] && action[act]();
        });
    })
    //下拉框
    $(document).click(function (e) {
        $('.layui-nav-child').hide();
    })
    function dropdown(that) {
        var oEvent = arguments.callee.caller.arguments[0] || event;
        oEvent.stopPropagation();
        var offset = $(that).offset();
        var top = offset.top - $(window).scrollTop();
        var index = $(that).parents('tr').data('index');
        $('.layui-nav-child').each(function (key) {
            if (key != index) {
                $(this).hide();
            }
        })
        if ($(document).height() < top + $(that).next('ul').height()) {
            $(that).next('ul').css({
                'padding': 10,
                'top': -($(that).parents('td').height() / 2 + $(that).height() + $(that).next('ul').height() / 2),
                'min-width': 'inherit',
                'position': 'absolute'
            }).toggle();
        } else {
            $(that).next('ul').css({
                'padding': 10,
                'top': $(that).parents('td').height() / 2 + $(that).height(),
                'min-width': 'inherit',
                'position': 'absolute'
            }).toggle();
        }
    }
    var specialList =<?=json_encode($specialList)?>,  sourceList =<?=json_encode($sourceList)?>;
    require(['vue'], function (Vue) {
        new Vue({
            el: "#app",
            data: {
                commentList: [
                    {name: '全部', value: ''},
                    {name: '10条', value: '1'},
                    {name: '20条', value: '2'},
                    {name: '30条', value: '3'},
                    {name: '40条', value: '4'},
                    {name: '50条', value: '5'},
                    {name: '60条', value: '6'},
                ],
                timeList: [
                    {name: '全部', value: ''},
                    {name: '5分钟', value: '1'},
                    {name: '10分钟', value: '2'},
                    {name: '15分钟', value: '3'},
                    {name: '20分钟', value: '4'},
                    {name: '30分钟', value: '5'},
                    {name: '50分钟', value: '6'},
                ],
                spendList: [
                    {name: '全部', value: ''},
                    {name: '100元', value: '1'},
                    {name: '200元', value: '2'},
                    {name: '300元', value: '3'},
                    {name: '400元', value: '4'},
                    {name: '500元', value: '5'},
                    {name: '600元', value: '6'},
                ],
                specialList: specialList,
                sourceList: sourceList,
                where: {
                    special_id:0,
                    source_id:0,
                    comment: '',
                    time: '',
                    spend: '',
                    excel: 0,
                },
                showtime: false,
            },
            watch: {
                'where.comment': function () {
                    this.where.excel = 0;
                    layList.reload(this.where, true);
                },
                'where.time': function () {
                    this.where.excel = 0;
                    layList.reload(this.where, true);
                },
                'where.spend': function () {
                    this.where.excel = 0;
                    layList.reload(this.where, true);
                },
            },
            methods: {
                setCommentData: function (item) {
                    var that = this;
                    this.where.comment = item.value;
                },
                setWachData: function (item) {
                    var that = this;
                    this.where.time = item.value;
                },
                setSpendData: function (item) {
                    var that = this;
                    this.where.spend = item.value;
                },
                search: function () {
                    this.where.excel = 0;
                    layList.reload(this.where, true);
                },
                refresh: function () {
                    layList.reload();
                },
                excel: function () {
                    this.where.excel = 1;
                    location.href = layList.U({c: 'market.store_order', a: 'order_list', q: this.where});
                    this.where.excel = 0;
                },
                handleBtn: function (item,index) {
                    var that = this;
                },
            },
            mounted: function () {
                var that = this;
                window.formReload = this.search;
                layList.laydate.render({
                    elem: this.$refs.date_time,
                    trigger: 'click',
                    eventElem: this.$refs.time,
                    range: true,
                    change: function (value) {
                        that.where.data = value;
                    }
                });
                layui.use('form', function(){
                  var form = layui.form;
                  //监听提交
                  form.on('select(special_id)', function(data){
                    that.where.special_id = data.value;
                    that.where.excel = 0;
                    layList.reload(that.where, true);
                  });
                  form.on('select(source_id)', function(data){
                    that.where.source_id = data.value;
                    that.where.excel = 0;
                    layList.reload(that.where, true);
                  });
                });
            }
        })
    });
</script>
{/block}