{extend name="public/container"}
{block name="content"}
<div class="layui-fluid">
    <div class="layui-row layui-col-space15"  id="app">
        <div class="layui-col-md15">
            <div class="layui-card">
                <div class="layui-card-header">搜索条件</div>
                <div class="layui-card-body">
                    <form class="layui-form layui-form-pane" action="">
                        <div class="layui-form-item">
                        	<div class="layui-inline">
                                <label class="layui-form-label">所有课程</label>
                                <div class="layui-input-block">
                                    <select name="special_id">
                                        <option value="">全部</option>
                                        {volist name="special_list" id="vo"}
                                            <option value="{$vo.id}">{$vo.title}</option>
                                        {/volist}
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">来源</label>
                                <div class="layui-input-block">
                                    <select name="type">
                                        <option value="">全部</option>
                                            <option value="1">视频号小店</option>
                                            <option value="2">抖音小店</option>
                                            <option value="3">小红书小店</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline">
                            <label class="layui-form-label">是否登录</label>
                                <div class="layui-input-block">
                                    <select name="is_uid">
                                        <option value="">全部</option>
                                            <option value="1">未登录</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">关键字搜索</label>
                                <div class="layui-input-block">
                                    <input type="text" name="keywords" class="layui-input" placeholder="请输入订单号、手机号">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">选择时间：</label>
                                <div class="layui-input-inline">
                                    <input type="text" class="layui-input time-w" name="add_time" lay-verify="add_time"  id="add_time" placeholder="时间范围">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <div class="layui-input-inline">
                                    <button class="layui-btn layui-btn-sm layui-btn-normal" lay-submit="search" lay-filter="search">
                                        <i class="layui-icon layui-icon-search"></i>搜索</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <!--产品列表-->
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">第三方渠道购买记录</div>
                <div class="layui-card-body">
                    <table class="layui-hide" id="List" lay-filter="List"></table>
                    <!--用户信息-->
                    <script type="text/html" id="userInfo">
                    	{{# if( d.avatar){ }}
                            <div>
	                            <span><img style="width: 100px;height: 100px;cursor: pointer;"
	                                src="{{d.avatar}}"></span>
	                        </div>
                        {{# } }}
                        {{d.nickname==null ? '暂无用户授权信息':d.nickname}}
                        {{# if( d.uid){ }}
                            /{{d.uid}}
                        {{# } }}
                    </script>
                    <script type="text/html" id="specialInfo">
                        <div>
                            <span><img
                                src="{{d.image}}"></span>
                        </div>
                        {{d.title==null ? '':d.title}}
                    </script>
                    <script type="text/html" id="orderInfo">
                        收货人：{{d.user_name==null ? '':d.user_name}}<br>
                        联系电话：{{d.user_phone==null ? '':d.user_phone}}<br>
						用户备注：{{d.user_mark==null ? '':d.user_mark}}<br>
                        商家备注：{{d.mer_mark==null ? '':d.mer_mark}} <br>
                        {{# if( d.type == '小红书小店' && d.user_name && d.user_phone){ }}
                            {{# if( d.sms_status == 1){ }}
                                发送短信：已发送<br>
                                发送状态：发送成功
                            {{# } }}
                            {{# if( d.sms_status == 2 ){ }}
                                发送短信：已发送<br>
                                发送状态：发送失败<br>
                                失败原因：{{d.sms_err_msg}} 
                            {{# } }}
                        {{# } }}
                    </script>
                    <script type="text/html" id="status">
                        {{d.status_name}}
                    </script>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="{__ADMIN_PATH}js/layuiList.js"></script>
{/block}
{block name="script"}
<script>
    setTimeout(function () {
        $('.alert-info').hide();
    },3000);
    //实例化form
    layList.form.render();
    //加载列表
    layList.tableList('List',"{:Url('list')}",function (){
        return [
            {field: 'id', title: 'ID', sort: true,event:'id',width:'4%',align:'center'},
            {field: 'title', title: '课程',templet: '#specialInfo',align:'center',width:'20%'},
            {field: 'userInfo', title: '订阅用户', templet: '#userInfo',width:'15%', align:'center'},
            {field: 'type', title: '来源',align:'center',width:'10%'},
            {field: 'order_id', title: '订单号',align:'center',width:'11.3%'},
            {field: 'orderInfo', title: '收货信息',templet: '#orderInfo', width: '20%', align: 'center'},
            {field: 'status_name', title: '订单状态',align:'center',width:'10%'},
            {field: 'add_time', title: '记录时间',align: 'center',width:'10%'},
        ];
    });
    //查询
    layList.search('search',function(where){
        layList.reload(where,true);
    });
    layList.date('add_time');
    //监听并执行排序
    layList.sort(['id','sort'],true);
    //点击事件绑定
    layList.tool(function (event,data,obj) {})
</script>
{/block}