{extend name="public/container"}
{block name="content"}
<div class="layui-fluid">
    <div class="layui-row layui-col-space15"  id="app">
        <!--产品列表-->
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-body">
                    <div class="layui-btn-container">
                        <button type="button" class="layui-btn layui-btn-sm" onclick="$eb.createModalFrame(this.innerText,'{:Url('create')}')">添加Banner</button>
                    </div>
                    <table class="layui-hide" id="List" lay-filter="List"></table>
                    <script type="text/html" id="image">
                        {{# if( d.image ){ }}
                         <img style="cursor: pointer" lay-event='open_image' src="{{d.image}}">
                        {{# } }}
                    </script>
                    <!-- 是否可用 -->
                    <script type="text/html" id="position">
                        {{d.position == 1 ? '首页': '心愿页'}} 
                    </script>
                    <script type="text/html" id="type">
                        {{# if( d.type == 0 ){ }}
                            内部跳转<br>
                            内部链接：{{d.routine_url}}
                        {{# } }}
                        {{# if( d.type == 1 ){ }}
                            公众号跳转<br>
                            公众号链接：{{d.mp_url}}
                        {{# } }}                        
                        {{# if( d.type == 2 ){ }}
                            视频号跳转<br>
                            视频号ID：{{d.finderUserName}}<br>
                            视频feedId：{{d.feedId}}
                            公众号链接：{{d.mp_url}}
                        {{# } }}
                        {{# if( d.type == 3 ){ }}
                            视频号直播跳转<br>
                            视频号ID：{{d.finderUserName}}
                            公众号链接：{{d.mp_url}}
                        {{# } }}
                    </script>
                    <!-- 是否可用 -->
                    <script type="text/html" id="is_show">
                        <input type='checkbox' name='is_show' lay-skin='switch' value="{{d.id}}" lay-filter='is_show' lay-text='显|隐'  {{ d.is_show == 1 ? 'checked' : '' }}>
                    </script>
                    <!-- 是否字节可用 -->
                    <script type="text/html" id="is_bytedance_show">
                        <input type='checkbox' name='is_bytedance_show' lay-skin='switch' value="{{d.id}}" lay-filter='is_bytedance_show' lay-text='显|隐'  {{ d.is_bytedance_show == 1 ? 'checked' : '' }}>
                    </script>
                    <!--日期信息-->
                    <script type="text/html" id="time">
                        <div>
                            <span>创建时间：{{d.add_time}}</span><br>
                            <span>最后更新时间：{{d.updated_time}}</span>
                        </div>
                    </script>
                    <script type="text/html" id="act">
                        <button class="layui-btn layui-btn-xs" onclick="$eb.createModalFrame('编辑','{:Url('create')}?id={{d.id}}')">
                            <i class="fa fa-edit"></i> 编辑
                        </button>
                        <button class="layui-btn btn-danger layui-btn-xs" lay-event='delstor'>
                            <i class="fa fa-times"></i> 删除
                        </button>
                    </script>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="{__ADMIN_PATH}js/layuiList.js"></script>
{/block}
{block name="script"}
<script>
    setTimeout(function () {
        $('.alert-info').hide();
    },3000);
    //实例化form
    layList.form.render();
    //加载列表
    layList.tableList('List',"{:Url('banner_list')}",function (){
        return [
            {field: 'id', title: 'ID', sort: true,event:'id',width:'4%',align:'center'},
            {field: 'title', title: '标题',align:'center'},
            {field: 'image', title: '图片',templet:'#image',align:'center'},
            {field: 'position', title: '位置',templet:'#position',align:'center'},
            {field: 'type', title: '跳转方式',align:'center',templet:'#type'},
            {field: 'sort', title: '排序',edit:'sort',sort: true,width:'6%','align':'center'},
            {field: 'is_show', title: '是否可用',templet:'#is_show',align:'center'},
            {field: 'is_bytedance_show', title: '是否字节可用',templet:'#is_bytedance_show',align:'center'},
            {field: 'add_time', title: '提交时间',align:'center', templet: "#time"},
            {field: 'right', title: '操作',align:'center',toolbar:'#act',width:'10%',align:'center'},
        ];
    });
    //自定义方法
    var action= {
        set_field: function (field, id, value) {
            layList.baseGet(layList.Url({
                c: 'article.home',
                a: 'set_field',
                q: {field: field, id: id, value: value}
            }), function (res) {
                layList.msg(res.msg);
            });
        },
    }
    //查询
    layList.search('search',function(where){
        layList.reload(where,true);
    });
    layList.switch('is_show',function (odj,value) {
        if(odj.elem.checked==true){
            layList.baseGet(layList.Url({c:'article.home',a:'set_show',p:{is_show:1,id:value}}),function (res) {
                layList.msg(res.msg);
            });
        }else{
            layList.baseGet(layList.Url({c:'article.home',a:'set_show',p:{is_show:0,id:value}}),function (res) {
                layList.msg(res.msg);
            });
        }
    });    
    layList.switch('is_bytedance_show',function (odj,value) {
        if(odj.elem.checked==true){
            layList.baseGet(layList.Url({c:'article.home',a:'set_bytedance_show',p:{is_bytedance_show:1,id:value}}),function (res) {
                layList.msg(res.msg);
            });
        }else{
            layList.baseGet(layList.Url({c:'article.home',a:'set_bytedance_show',p:{is_bytedance_show:0,id:value}}),function (res) {
                layList.msg(res.msg);
            });
        }
    });
    //快速编辑
    layList.edit(function (obj) {
        var id=obj.data.id,value=obj.value;
        switch (obj.field) {
            case 'sort':
                action.set_field('sort',id,value);
                break;
        }
    });
    //监听并执行排序
    layList.sort(['id','sort'],true);
    //点击事件绑定
    layList.tool(function (event,data,obj) {
        switch (event) {
            case 'delstor':
                var url=layList.U({c:'article.home',a:'destroy',q:{id:data.id}});
                $eb.$swal('delete',function(){
                    $eb.axios.get(url).then(function(res){
                        if(res.status == 200 && res.data.code == 200) {
                            $eb.$swal('success',res.data.msg);
                            obj.del();
                        }else
                            return Promise.reject(res.data.msg || '删除失败')
                    }).catch(function(err){
                        $eb.$swal('error',err);
                    });
                })
                break;
            case 'open_image':
                $eb.openImage(data.image);
                break;
        }
    })
</script>
{/block}
