{extend name="public/container"}
{block name="head_top"}

{/block}
{block name="content"}
<style>
    .btn-outline{
        border:none;
    }
    .btn-outline:hover{
        background-color: #0e9aef;
        color: #fff;
    }
    .layui-form-item .layui-btn {
        margin-top: 5px;
        margin-right: 10px;
    }
    .layui-btn-primary{
        margin-right: 10px;
        margin-left: 0!important;
    }
    label{
        margin-bottom: 0!important;
        margin-top: 4px;
    }
</style>
<div class="layui-fluid">
    <div class="layui-row layui-col-space15" id="app">
        <!--搜索条件-->
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">搜索条件</div>
                <div class="layui-card-body">
                    <div class="layui-carousel layadmin-carousel layadmin-shortcut" lay-anim="" lay-indicator="inside"
                         lay-arrow="none" style="background:none">
                        <div class="layui-card-body">
                            <div class="layui-row layui-col-space10 layui-form-item">
                                <div class="layui-col-lg12">
                                    <label class="layui-form-label">状态:</label>
                                    <div class="layui-input-block" v-cloak="">
                                        <button class="layui-btn layui-btn-sm"
                                                :class="{'layui-btn-primary':where.status!==item.value}"
                                                @click="where.status = item.value" type="button"
                                                v-for="item in billboardStatus">{{item.name}}
                                            <span v-if="item.count!=undefined"
                                                  :class="item.class!=undefined ? 'layui-badge': 'layui-badge layui-bg-gray' ">{{item.count}}</span>
                                        </button>
                                    </div>
                                </div>
                                <div class="layui-col-lg12">
                                    <label class="layui-form-label">创建时间:</label>
                                    <div class="layui-input-block" data-type="data" v-cloak="">
                                        <button class="layui-btn layui-btn-sm" type="button" v-for="item in dataList"
                                                @click="setData(item)"
                                                :class="{'layui-btn-primary':where.data!=item.value}">{{item.name}}
                                        </button>
                                        <button class="layui-btn layui-btn-sm" type="button" ref="time"
                                                @click="setData({value:'zd',is_zd:true})"
                                                :class="{'layui-btn-primary':where.data!='zd'}">自定义
                                        </button>
                                        <button type="button" class="layui-btn layui-btn-sm layui-btn-primary"
                                                v-show="showtime==true" ref="date_time">{$year.0} - {$year.1}
                                        </button>
                                    </div>
                                </div>
                                <div class="layui-col-lg12">
                                    <label class="layui-form-label">关键字:</label>
                                    <div class="layui-input-block">
                                        <input type="text" name="keywords" style="width: 50%" v-model="where.keywords"
                                               placeholder="请输入评论内容，评论人用户信息" class="layui-input">
                                    </div>
                                </div>
                                <div class="layui-col-lg12">
                                    <div class="layui-input-block">
                                        <button @click="search" type="button"
                                                class="layui-btn layui-btn-sm layui-btn-normal">
                                            <i class="layui-icon layui-icon-search"></i>搜索
                                        </button>
                                        <button @click="refresh" type="reset"
                                                class="layui-btn layui-btn-primary layui-btn-sm">
                                            <i class="layui-icon layui-icon-refresh"></i>刷新
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--end-->
        <!-- 中间详细信息-->
        <div :class="item.col!=undefined ? 'layui-col-sm'+item.col+' '+'layui-col-md'+item.col:'layui-col-sm6 layui-col-md3'"
             v-for="item in badge" v-cloak="" v-if="item.count > 0">
            <div class="layui-card">
                <div class="layui-card-header">
                    {{item.name}}
                    <span class="layui-badge layuiadmin-badge" :class="item.background_color">{{item.field}}</span>
                </div>
                <div class="layui-card-body">
                    <p class="layuiadmin-big-font">{{item.count}}</p>
                    <p v-show="item.content!=undefined">
                        {{item.content}}
                        <span class="layuiadmin-span-color">{{item.sum}}<i :class="item.class"></i></span>
                    </p>
                </div>
            </div>
        </div>
        <!--enb-->
    </div>
    <!--列表-->
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">评论列表</div>
                <div class="layui-card-body">
                    <div class="layui-btn-container" id="container-action">
                        <button class="layui-btn layui-btn-sm" data-type="set_status">批量处理</button>
                    </div>
                    <table class="layui-hide" id="List" lay-filter="List"></table>
                    <!--用户信息-->
                    <script type="text/html" id="userinfo">
                        <div class="layui-table-cell laytable-cell-1-0-1"> <img style="cursor: pointer" lay-event="open_image" src="{{ d.avatar }}"> </div>
                        {{d.nickname==null ? '暂无信息':d.nickname}}/{{d.uid}}
                    </script>
                    <!--类型-->
                    <script type="text/html" id="type_name">
                        {{d.type_name}}
                    </script>
                    <!--评论状态-->
                    <script type="text/html" id="status">
                        {{d.status_name}}
                    </script>
                    <script type="text/html" id="act">
                        <button type="button" class="layui-btn layui-btn-xs" onclick="dropdown(this)">操作 <span class="caret"></span></button>
                        <ul class="layui-nav-child layui-anim layui-anim-upbit">
                            <li>
                                <a href="javascript:void(0);" lay-event='comment_info'>
                                    <i class="fa fa-file-text"></i> 评论详情
                                </a>
                            </li>
                            {{# if( d.status == 1 |  d.is_del == 0 ){ }}
                                <li>
                                    <a href="javascript:void(0);" lay-event='deleted'>
                                        <i class="fa fa-edit"></i> 屏蔽
                                    </a>
                                </li>
                            {{# } }}
                            {{# if( d.status == 2  ||  d.is_del == 0){ }}
                                <li>
                                    <a href="javascript:void(0);" lay-event='review'>
                                        <i class="fa fa-edit"></i> 审核
                                    </a>
                                </li>
                            {{# } }}
                            {{# if( d.status == 3  ||  d.is_del == 1){ }}
                                <li>
                                    <a href="javascript:void(0);" lay-event='restore'>
                                        <i class="fa fa-edit"></i> 恢复显示
                                    </a>
                                </li>
                            {{# } }}
                    </script>
                </div>
            </div>
        </div>
    </div>
    <!--end-->
</div>
<script src="{__ADMIN_PATH}js/layuiList.js"></script>
{/block}
{block name="script"}
<script>
    layList.tableList('List', "{:Url('comment_list')}", function () {
        return [
            {type: 'checkbox'},
            {field: 'id', title: 'ID',width: '5%',align: 'center'},
            {field: 'user', title: '用户', templet: '#userinfo', width: '15%', align: 'center'},
            {field: 'type_name', title: '内容类型', templet: "#type_name", width: '8%',align: 'center'},
            {field: 'comment', title: '评论内容' , width: '25%',align: 'center'},
            {field: 'status', title: '状态', templet: '#status', width: '10%', align: 'center'},
            {field: 'add_time', title: '提交时间', width: '15.2%', sort: true, align: 'center'},
            {field: 'right', title: '操作', align: 'center', toolbar: '#act', width: '20%'},
        ];
    });
    layList.tool(function (event, data, obj) {
        switch (event) {
            case 'deleted':
                var url = layList.U({c: 'market.store_comment', a: 'destroy', p: {id: data.id}});
                $eb.$swal('delete', function () {
                    $eb.axios.get(url).then(function (res) {
                        if (res.status == 200 && res.data.code == 200) {
                            $eb.$swal('success', res.data.msg);
                        } else
                            return Promise.reject(res.data.msg || '屏蔽失败')
                    }).catch(function (err) {
                        $eb.$swal('error', err);
                    });
                }, {'title': '您确定要屏蔽评论吗？', 'text': '屏蔽后将无法恢复,请谨慎操作！', 'confirm': '是的，我要屏蔽'})
                break;
            case 'restore':
                var url = layList.U({c: 'market.store_comment', a: 'restore', p: {id: data.id,status: data.status}});
                $eb.$swal('delete', function () {
                    $eb.axios.get(url).then(function (res) {
                        if (res.status == 200 && res.data.code == 200) {
                            $eb.$swal('success', res.data.msg);
                        } else
                            return Promise.reject(res.data.msg || '恢复失败')
                    }).catch(function (err) {
                        $eb.$swal('error', err);
                    });
                }, {'title': '您确定恢复该评论吗？', 'text': '恢复后，评论状态为发布状态！', 'confirm': '是的，我要恢复'})
                break;
            case 'review':
                var url = layList.U({c: 'market.store_comment', a: 'restore', p: {id: data.id,status: data.status}});
                $eb.$swal('delete', function () {
                    $eb.axios.get(url).then(function (res) {
                        if (res.status == 200 && res.data.code == 200) {
                            $eb.$swal('success', res.data.msg);
                        } else
                            return Promise.reject(res.data.msg || '恢复失败')
                    }).catch(function (err) {
                        $eb.$swal('error', err);
                    });
                }, {'title': '您确定审核该评论吗？', 'text': '审核后，评论状态为发布状态！', 'confirm': '是的，我要审核'})
                break;
            case 'comment_info':
                $eb.createModalFrame('评论详情', layList.U({a: 'comment_info', q: {cid: data.id}}));
                break;
            case 'open_image':
                $eb.openImage(data.avatar);
                break;
        }
    })
    $('#container-action').find('button').each(function () {
        $(this).on('click', function () {
            var act = $(this).data('type');
            action[act] && action[act]();
        });
    })
    //下拉框
    $(document).click(function (e) {
        $('.layui-nav-child').hide();
    })
    //自定义方法
    var action={
        show:function(){
            var ids=layList.getCheckData().getIds('id');
            if(ids.length){
                layList.basePost(layList.Url({c:'market.store_comment',a:'release'}),{ids:ids},function (res) {
                    layList.msg(res.msg);
                    layList.reload();
                });
            }else{
                layList.msg('请选择要审核的评论');
            }
        },
        set_status:function () {
            var ids=layList.getCheckData().getIds('id');
            if(ids.length){
                var str = ids.join(',');
                console.log(str);
                $eb.createModalFrame('批量设置状态',layList.Url({a:'set_status',p:{id:str}}),{w:500,h:300});
            }else{
                layList.msg('请选择要批量设置状态的评论');
            }
        },
        refresh:function () {
            layList.reload();
        }
    };

    function dropdown(that) {
        var oEvent = arguments.callee.caller.arguments[0] || event;
        oEvent.stopPropagation();
        var offset = $(that).offset();
        var top = offset.top - $(window).scrollTop();
        var index = $(that).parents('tr').data('index');
        $('.layui-nav-child').each(function (key) {
            if (key != index) {
                $(this).hide();
            }
        })
        if ($(document).height() < top + $(that).next('ul').height()) {
            $(that).next('ul').css({
                'padding': 10,
                'top': -($(that).parents('td').height() / 2 + $(that).height() + $(that).next('ul').height() / 2),
                'min-width': 'inherit',
                'position': 'absolute'
            }).toggle();
        } else {
            $(that).next('ul').css({
                'padding': 10,
                'top': $(that).parents('td').height() / 2 + $(that).height(),
                'min-width': 'inherit',
                'position': 'absolute'
            }).toggle();
        }
    }
    var commentCount =<?=json_encode($commentCount)?>,status =<?=$status ? $status : "''"?>;
    require(['vue'], function (Vue) {
        new Vue({
            el: "#app",
            data: {
                badge: [],
                billboardStatus: [
                    {name: '全部', value: ''},
                    {name: '已通过', value: 1, count: commentCount.passed},
                    {name: '待审核', value: 2, count: commentCount.pending, class: true},
                    {name: '已屏蔽', value: 3, count: commentCount.deleted},
                ],
                dataList: [
                    {name: '全部', value: ''},
                    {name: '今天', value: 'today'},
                    {name: '昨天', value: 'yesterday'},
                    {name: '最近7天', value: 'lately7'},
                    {name: '最近30天', value: 'lately30'},
                    {name: '本月', value: 'month'},
                    {name: '本年', value: 'year'},
                ],
                where: {
                    data: '',
                    status: '',
                    keywords: '',
                },
                showtime: false,
            },
            watch: {
                'where.status': function () {
                    layList.reload(this.where, true);
                },
                'where.data': function () {
                    layList.reload(this.where, true);
                },
                'where.keywords': function () {
                    layList.reload(this.where, true);
                }
            },
            methods: {
                setData: function (item) {
                    var that = this;
                    if (item.is_zd == true) {
                        that.showtime = true;
                        this.where.data = this.$refs.date_time.innerText;
                    } else {
                        this.showtime = false;
                        this.where.data = item.value;
                    }
                },
                search: function () {		
                    layList.reload(this.where, true);
                },
                refresh: function () {
                    layList.reload();
                },
                init: function () {
                var that = this;
                that.$nextTick(function () {
                    layui.use(['form','element'], function () {
                        that.form = layui.form;
                        that.form.render();
                        layui.element.on('tab(docTabBrief)', function(){
                            that.layTabId = this.getAttribute('lay-id');
                        });
                    });
                })
            },
            },
            mounted: function () {
                this.init();
                var that = this;
                window.formReload = this.search;
                layList.laydate.render({
                    elem: this.$refs.date_time,
                    trigger: 'click',
                    eventElem: this.$refs.time,
                    range: true,
                    change: function (value) {
                        that.where.data = value;
                    }
                });
            }
        })
    });
</script>
{/block}