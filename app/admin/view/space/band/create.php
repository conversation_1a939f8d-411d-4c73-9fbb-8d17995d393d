<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="referrer" content="never">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link href="{__FRAME_PATH}css/font-awesome.min.css" rel="stylesheet">
    <link href="{__ADMIN_PATH}plug/umeditor/themes/default/css/umeditor.css" type="text/css" rel="stylesheet">
    <script type="text/javascript" src="{__ADMIN_PATH}plug/umeditor/third-party/jquery.min.js"></script>
    <script type="text/javascript" src="{__ADMIN_PATH}plug/umeditor/third-party/template.min.js"></script>
    <script type="text/javascript" charset="utf-8" src="{__ADMIN_PATH}plug/umeditor/umeditor.config.js"></script>
    <script type="text/javascript" charset="utf-8" src="{__ADMIN_PATH}plug/umeditor/umeditor.min.js"></script>
    <script type="text/javascript" src="{__ADMIN_PATH}plug/umeditor/lang/zh-cn/zh-cn.js"></script>
    <link rel="stylesheet" href="/static/plug/layui/css/layui.css">
    <script src="/static/plug/layui/layui.js"></script>
    <script src="{__PLUG_PATH}vue/dist/vue.min.js"></script>
    <script src="/static/plug/axios.min.js"></script>
    <script src="/static/plug/iview/dist/iview.min.js"></script>
    <script src="{__MODULE_PATH}widget/aliyun-oss-sdk-4.4.4.min.js"></script>
    <script src="{__MODULE_PATH}widget/cos-js-sdk-v5.min.js"></script>
    <script src="{__MODULE_PATH}widget/qiniu-js-sdk-2.5.5.js"></script>
    <script src="{__MODULE_PATH}widget/plupload.full.min.js"></script>
    <script src="{__MODULE_PATH}widget/videoUpload.js"></script>
    <script type="text/javascript">
        $eb = parent._mpApi;
        window.controlle="<?php echo strtolower(trim(preg_replace("/[A-Z]/", "_\\0", app('request')->controller()), "_"));?>";
        window.module="6GqvmHa8HRGHoQEQ";
    </script>
    <style>
        .layui-form-item {
            margin-bottom: 0px;
        }

        .layui-form-item .special-label {
            width: 50px;
            float: left;
            height: 30px;
            line-height: 38px;
            margin-left: 10px;
            margin-top: 5px;
            border-radius: 5px;
            background-color: #0092DC;
            text-align: center;
        }

        .layui-form-item .special-label i {
            display: inline-block;
            width: 18px;
            height: 18px;
            font-size: 18px;
            color: #fff;
        }

        .layui-form-item .label-box {
            border: 1px solid;
            border-radius: 10px;
            position: relative;
            padding: 10px;
            height: 30px;
            color: #fff;
            background-color: #393D49;
            text-align: center;
            cursor: pointer;
            display: inline-block;
            line-height: 10px;
        }

        .layui-form-item .label-box p {
            line-height: inherit;
        }

        .pictrueBox {
            display: inline-block !important;
        }

        .pictrue {
            width: 60px;
        height: 60px;
            border: 1px dotted rgba(0, 0, 0, 0.1);
            margin-right: 15px;
            display: inline-block;
            position: relative;
            cursor: pointer;
        }

        .pictrue img {
            width: 100%;
            height: 100%;
        }

        .upLoad {
            width: 58px;
            height: 58px;
            line-height: 58px;
            border: 1px dotted rgba(0, 0, 0, 0.1);
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.02);
            cursor: pointer;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .rulesBox {
            display: flex;
            flex-wrap: wrap;
            margin-left: 10px;
        }

        .layui-tab-content {
            margin-top: 15px;
        }

        .ml110 {
            margin: 18px 0 4px 110px;
        }

        .rules {
            display: flex;
        }

        .rules-btn-sm {
            height: 30px;
            line-height: 30px;
            font-size: 12px;
            width: 109px;
        }

        .rules-btn-sm input {
            width: 79% !important;
            height: 84% !important;
            padding: 0 10px;
        }

        .ml10 {
            margin-left: 10px !important;
        }

        .ml40 {
            margin-left: 40px !important;
        }

        .closes {
            position: absolute;
            left: 86%;
            top: -18%;
        }
        .red {
            color: red;
        }
        .layui-input-block .layui-video-box{
            width: 22%;
            height: 180px;
            border-radius: 10px;
            background-color: #707070;
            margin-top: 10px;
            position: relative;
            overflow: hidden;
        }
        .layui-input-block .layui-video-box i{
            color: #fff;
            line-height: 180px;
            margin: 0 auto;
            width: 50px;
            height: 50px;
            display: inherit;
            font-size: 50px;
        }
        .layui-input-block .layui-video-box .mark{
            position: absolute;
            width: 100%;
            height: 30px;
            top: 0;
            background-color: rgba(0,0,0,.5);
            text-align: center;
        }
        .layui-input-block .layui-audio-box{
            width: 50%;
            height: 54px;
            border-radius: 10px;
            background-color: #707070;
            margin-top: 10px;
            position: relative;
            overflow: hidden;
        }
        .layui-input-block .layui-audio-box i{
            color: #fff;
            line-height: 180px;
            margin: 0 auto;
            width: 50px;
            height: 50px;
            display: inherit;
            font-size: 50px;
        }
        .layui-input-block .layui-audio-box .mark{
            width: 11%;
            height: 30px;
            top: 0px;
            background-color: rgba(0,0,0,.5);
            text-align: center;
            float: right;
            margin-right: 10px;
            margin-top: 11px;
        }
        .layui-form-select dl {
            z-index: 1015;
        }
        .store_box{
            display: flex;
        }
        .ivu-input{
             border-width: 0px !important;
             width: 100%;
             height: 36px;
         }
         .ivu-select-dropdown{
             z-index: 999;
             background: #fff;
         }
         .ivu-cascader-menu .ivu-cascader-menu-item-active {
             background-color: #f3f3f3;
             color: #2d8cf0;
         }
         .ivu-cascader-menu .ivu-cascader-menu-item {
             position: relative;
             padding-right: 24px;
             -webkit-transition: all .2s ease-in-out;
             transition: all .2s ease-in-out;
         }
         .ivu-cascader .ivu-cascader-menu-item {
             margin: 0;
             line-height: normal;
             padding: 7px 16px;
             clear: both;
             color: #495060;
             font-size: 12px!important;
             white-space: nowrap;
             list-style: none;
             cursor: pointer;
             -webkit-transition: background .2s ease-in-out;
             transition: background .2s ease-in-out;
         }
         .ivu-cascader-menu:last-child {
             border-right-color: transparent;
             margin-right: -1px;
         }
         .ivu-cascader-menu {
             display: inline-block;
             min-width: 100px;
             height: 180px;
             margin: 0;
             padding: 5px 0!important;
             vertical-align: top;
             list-style: none;
             border-right: 1px solid #e9eaec;
             overflow: auto;
                 }
        .info{
            color: #c9c9c9;
            padding-left: 10px;
            line-height: 30px;
        }

        .video_wrap{
           width: 600px;position: relative;
        }
        .jiqu{
           position: absolute;
           top: 50%;
           z-index: 999;top: 10px;right:10px;width: 60px;text-align: center;
           background: rgb(0,0,0,0.8);
           color: #fff;
           font-size: 20px;
           height: 60px;
           line-height: 60px;
           cursor: pointer;
           /* display: none; */
        }
        .video_wrap:hover .jiqu{
            display: block;
        }
    </style>
</head>
<script type="text/javascript">
    window.controlle="<?php echo strtolower(trim(preg_replace("/[A-Z]/", "_\\0", app('request')->controller()), "_"));?>";
    window.module="6GqvmHa8HRGHoQEQ";
</script>
<body>
<div class="layui-fluid">
    <div class="layui-row layui-col-space15"  id="app" v-cloak="">
        <div class="layui-card">
            <div class="layui-card-header">
                <span class="">{{id ? '编辑乐队': '添加乐队' }}</span>
                <button style="margin-left: 20px" type="button" class="layui-btn layui-btn-primary layui-btn-xs" @click="goBack">返回列表</button>
            </div>
            <div class="layui-card-body">
                <form class="layui-form" action="" v-cloak="">
                    <div class="layui-tab layui-tab-brief" lay-filter="docTabBrief">
                        <ul class="layui-tab-title">
                            <li class="layui-this" lay-id='1'>基本设置</li>
                            <li lay-id='2'>空间管理</li>
                            <li lay-id='3'>帖子管理</li>
                        </ul>
                        <div class="layui-tab-content">
                            <div class="layui-tab-item layui-show">
                                <div class="layui-row layui-col-space15">
                                    <div class="layui-row layui-col-space15">
                                        <div class="layui-col-xs12 layui-col-sm4 layui-col-md4">
                                            <div class="grid-demo grid-demo-bg1">
                                                <div class="layui-form-item">
                                                    <label class="layui-form-label">类型<i class="red">*</i></label>
                                                    <div class="layui-input-block">
                                                        <input type="radio" name="type" lay-filter="type" value="1" title="乐队"
                                                               :checked="formData.type == 1 ? true : false">
                                                        <input type="radio" name="type" lay-filter="type" value="2" title="课程"
                                                               :checked="formData.type == 2 ? true : false">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">乐队名<i class="red">*</i></label>
                                                <div class="layui-input-block">
                                                    <input type="text" name="store_name" lay-verify="store_name" autocomplete="off"
                                                           placeholder="请输入乐队名" class="layui-input" v-model="formData.store_name" maxlength="100">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">限定成员数<i class="red">*</i></label>
                                                <div class="layui-input-block">
                                                    <input type="number" name="limit_band_size" lay-verify="limit_band_size" autocomplete="off"
                                                           placeholder="请输入限定成员人数" class="layui-input" v-model="formData.limit_band_size" maxlength="100">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">关联报名活动<i class="red">*</i></label>
                                                <div class="layui-input-block" id="activity_id">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">乐队城市<i class="red">*</i></label>
                                                <div class="layui-input-block" id="city_id">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item layui-form-text">
                                                <label class="layui-form-label">地点说明</label>
                                                <div class="layui-input-block">
                                                    <textarea name="detailed_address" v-model="formData.detailed_address"
                                                              placeholder="请输入地点说明" class="layui-textarea"></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item layui-form-text">
                                                <label class="layui-form-label">时间说明</label>
                                                <div class="layui-input-block">
                                                    <textarea name="introduction" v-model="formData.introduction"
                                                              placeholder="请输入时间说明" class="layui-textarea"></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">排序：</label>
                                                <div class="layui-input-block">
                                                    <input type="number" name="sort" lay-verify="sort" autocomplete="off"
                                                            class="layui-input" v-model="formData.sort">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">时间：</label>
                                                <div class="layui-input-block">
                                                    <input type="text" name="add_time" id="add_time"  v-model="formData.add_time" required
                                             lay-verify="required" placeholder="时间" class="layui-input" readonly="readonly">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-tab-item">
                                <div class="layui-row layui-col-space15">
                                    <div class="layui-col-xs12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">选择老师：</label>
                                                <div class="layui-input-block">
                                                    <input type="hidden" id="check_instructor_tmp" name="check_instructor_tmp"/>
                                                    <button type="button" class="layui-btn layui-btn-normal" @click='search_teacher'>
                                                        选择指导老师
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">老师展示：</label>
                                                <div class="layui-input-block">
                                                    <input type="hidden" id="check_assistant_sure" name="check_assistant_sure"/>
                                                    <table class="layui-hide" id="showAssistantList" lay-filter="showAssistantList"></table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-row layui-col-space15">
                                    <div class="layui-col-xs12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">选择助理：</label>
                                                <div class="layui-input-block">
                                                    <input type="hidden" id="check_assistant_tmp" name="check_assistant_tmp"/>
                                                    <button type="button" class="layui-btn layui-btn-normal" @click='search_assistant'>
                                                        选择助理
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">助理展示：</label>
                                                <div class="layui-input-block">
                                                    <input type="hidden" id="check_instructor_sure" name="check_instructor_sure"/>
                                                    <table class="layui-hide" id="showInstructorList" lay-filter="showInstructorList"></table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-tab-item">
                                <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                    <div class="grid-demo grid-demo-bg1">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">标签：<i class="red">*</i></label>
                                            <div class="layui-input-block">
                                                <input type="text" style="width:50%;display:inline-block;margin-right: 10px;" name="label" lay-verify="label" autocomplete="off"
                                                       placeholder="请输入标签" class="layui-input" v-model="label" placeholder="最多15个字" autocomplete="off" maxlength="15" class="layui-input">
                                                         <button type="button" class="layui-btn layui-btn-normal" @click="addLabrl" >
                                                <i class="layui-icon">&#xe654;</i>
                                            </button><sapn style="margin-left: 10px;color: red;">输入关键字名称后点击”+“号按钮添加；最多写入3个字；点击关键字即可删除</span>
                                            </div>
                                        </div>
                                        <div v-if="formData.label.length" class="layui-form-item" style="margin-top: 5px;">
                                            <div class="layui-input-block">
                                                <button v-for="(item,index) in formData.label" :key="index" type="button" class="layui-btn layui-btn-normal layui-btn-sm" @click="delLabel(index)">{{item}}</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            </div>
                            <div class="layui-tab-content">
                                <div class="layui-row layui-col-space15">
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item" v-if="id">
                                                <button class="layui-btn layui-btn-primary layui-btn-sm" id="submit" type="button" @click="handleSubmit()">保存</button>
                                                <button class="layui-btn layui-btn-primary layui-btn-sm" type="button" @click="back" v-if="layTabId != 1">上一步</button>
                                                <button class="layui-btn layui-btn-normal layui-btn-sm" type="button" v-if="layTabId != 3" @click="next">下一步</button>
                                            </div>
                                            <div class="layui-form-item" v-else>
                                                <button class="layui-btn layui-btn-primary layui-btn-sm" type="button" @click="back" v-if="layTabId != 1">上一步</button>
                                                <button class="layui-btn layui-btn-normal layui-btn-sm" type="button" @click="next" v-if="layTabId != 3">下一步</button>
                                                <button class="layui-btn layui-btn-normal layui-btn-sm" id="submit" type="button" v-if="layTabId == 3" @click="handleSubmit()">提交</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<script src="{__PLUG_PATH}city.js"></script>
<script>
    var id = {$id};
    // 新增
    instructorCheckList = <?= isset($productCheckList) ? $productCheckList : "{}"?>;
    assistantCheckList = <?= isset($sourceCheckList) ? $sourceCheckList : "{}"?>;
    server_time  = "<?php echo date("Y-m-d H:i:s",time()); ?>";
    $.each(city,function (key,item) {
       city[key].value = item.label;
       if(item.children && item.children.length){
           $.each(item.children,function (i,v) {
               city[key].children[i].value=v.label;
               if(v.children && v.children.length){
                   $.each(v.children,function (k,val) {
                       city[key].children[i].children[k].value=val.label;
                   });
               }
           });
       }
   });
    new Vue({
        el: '#app',
        data: {
            id:id,
            //分类列表
            cityList: [],
            activityList: [],
            instructor_tmp_list: [],//用于子页父业选中指导老师临时变量
            assistant_tmp_list: [],//用于子页父业选中管理员临时变量
            upload:{
                videoIng:false,
                audioIng:false,
            },
            formData: {
                type: 1,
                limit_band_size:0,
                store_name: '',
                detailed_address:'',
                city_id: 0,
                activity_id: 0,
                // check_product_sure: productCheckList ? productCheckList : [],
                // check_special_sure: specialCheckList ? specialCheckList : [],
                check_instructor_sure: instructorCheckList ? instructorCheckList : [], //指导老师
                check_assistant_sure: assistantCheckList ? assistantCheckList : [], //助理
                label: ['公告','精华','精华'],
                sort: 0,
                add_time:server_time ? server_time : '',
            },
            formVideoData: {
                link: 0,
                time: 0,
                width:1420,
                height:800,
            },
            videoTime:0,
            videoImage:null,
            clickIdx:0,
            demand_switch:0,
            link:'',
            audioLink:'',
            videoLink:'',
            //多属性header头
            formHeader:[],
            attr: [],//临时属性
            label: '',
            radioRule: ['type'],//radio 当选规则
            radioLabel: ['type'],//radio 当选规则
            progress: 0,
            audio_progress: 0,
             searchTask:false,
            um: null,//编译器实例化
            form: null,//layui.form
            layTabId: 1,
            ruleBool: id ? true : false,
        },
        watch:{
            'formData.type':function (n) {
                if (n) {
                    this.render();
                }
            }
        },
        methods: {
            back:function(){
                var that = this;
                layui.use(['element'], function () {
                    layui.element.tabChange('docTabBrief', that.layTabId == 1 ? 1 : parseInt(that.layTabId) - 1);
                });
            },
            next:function(){
                var that = this;
                layui.use(['element'], function () {
                    layui.element.tabChange('docTabBrief', that.layTabId == 5 ? 5 : parseInt(that.layTabId) + 1);
                });
            },
            goBack:function(){
                location.href = this.U({m:window.module,c:'special.special_type',a:'sources_index'});
            },
            U: function (opt) {
                var m = opt.m || 'admin', c = opt.c || window.controlle || '', a = opt.a || 'index', q = opt.q || '',
                    p = opt.p || {};
                var params = Object.keys(p).map(function (key) {
                    return key + '/' + p[key];
                }).join('/');
                var gets = Object.keys(q).map(function (key) {
                    return key+'='+ q[key];
                }).join('&');

                return '/' + m + '/' + c + '/' + a + (params == '' ? '' : '/' + params) + (gets == '' ? '' : '?' + gets);
            },
            /**
             * 提示
             * */
            showMsg: function (msg, success) {
                $('#submit').removeAttr('disabled').text('提交');
                layui.use(['layer'], function () {
                    layui.layer.msg(msg, success);
                });
            },
            /**
             * 获取素材信息
             * */
            /**
             * 获取素材信息
             * */
            getActivityInfo: function () {
                var that = this;
                that.requestGet(that.U({m:window.module,c:"space.band",a:'get_band_info',q:{id:that.id}})).then(function (res) {
                    var specialInfo = res.data.specialInfo || {};
                    if(specialInfo.id && that.id){
                        that.$set(that,'formData',specialInfo);
                    }
                    that.$set(that, 'activityList', res.data.activityList);
                    that.$set(that, 'cityList', res.data.cityList);
                    that.init();
                }).catch(function (res) {
                    console.log(res);
                    that.showMsg(res.msg || res);

                })
            },
            get_city_list: function () {
                var that = this;
                that.requestGet(that.U({m:window.module,c:"space.band",a:'get_city_list'})).then(function (res) {
                    that.$set(that, 'cityList', res.data);
                });
            },
            get_activity_list: function () {
                var that = this;
                that.requestGet(that.U({m:window.module,c:"space.band",a:'get_activity_list'})).then(function (res) {
                    that.$set(that, 'activityList', res.data);
                });
            },
            getCurrentTime() {
                //获取当前时间并打印
                var _this = this;
            　　let yy = new Date().getFullYear();
            　　let mm = new Date().getMonth()+1;
            　　let dd = new Date().getDate();
            　　let hh = new Date().getHours();
            　　let mf = new Date().getMinutes()<10 ? '0'+new Date().getMinutes() : new Date().getMinutes();
            　　let ss = new Date().getSeconds()<10 ? '0'+new Date().getSeconds() : new Date().getSeconds();
            　　_this.gettime = yy+'/'+mm+'/'+dd+' '+hh+':'+mf+':'+ss;
            　　console.log(_this.gettime)  
            },
            /**
             * 删除图片
             * */
            deleteImage: function (key, index) {
                var that = this;
                if (index != undefined) {
                    that.formData[key].splice(index, 1);
                    that.$set(that.formData, key, that.formData[key]);
                } else {
                    that.$set(that.formData, key, '');
                }
            },
            createFrame: function (title, src, opt) {
                var that = this;
                opt === undefined && (opt = {});
                var h = 0;
                if (window.innerHeight < 800 && window.innerHeight >= 700) {
                    h = window.innerHeight - 50;
                } else if (window.innerHeight < 900 && window.innerHeight >= 800) {
                    h = window.innerHeight - 100;
                } else if (window.innerHeight < 1000 && window.innerHeight >= 900) {
                    h = window.innerHeight - 150;
                } else if (window.innerHeight >= 1000) {
                    h = window.innerHeight - 200;
                } else {
                    h = window.innerHeight;
                }
                var area = [(opt.w || window.innerWidth / 2) + 'px', (!opt.h || opt.h > h ? h : opt.h) + 'px'];
                layui.use('layer',function () {
                    return layer.open({
                        type: 2,
                        title: title,
                        area: area,
                        fixed: false, //不固定
                        maxmin: true,
                        moveOut: false,//true  可以拖出窗外  false 只能在窗内拖
                        anim: 5,//出场动画 isOutAnim bool 关闭动画
                        offset: 'auto',//['100px','100px'],//'auto',//初始位置  ['100px','100px'] t[ 上 左]
                        shade: 0,//遮罩
                        resize: true,//是否允许拉伸
                        content: src,//内容
                        move: '.layui-layer-title'
                    });
                });
                if (title == '选择图片') {
                    that.$set(that.formData, 'video_data', {});
                    that.$set(that.formData, 'is_snapshot', false);
                }
            },
            changeIMG: function (name, value) {
                if (this.getRule(name).maxLength !== undefined) {
                    var that = this;
                    value.map(function (v) {
                        that.formData[name].push(v);
                    });
                    this.$set(this.formData, name, this.formData[name]);
                } else {
                    if(name == 'batchAttr.pic'){
                        this.batchAttr.pic = value;
                    } else {
                        if (name.indexOf('.') !== -1) {
                            var key = name.split('.');
                            if (key.length == 2){
                                this.formData[key[0]][key[1]] = value;
                            } else if(key.length == 3){
                                this.formData[key[0]][key[1]][key[2]] = value;
                            } else if(key.length == 4){
                                this.$set(this.formData[key[0]][key[1]][key[2]],key[3],value)
                            }
                        } else {
                            this.formData[name] = value;
                        }
                    }
                }
            },
            getRule: function (name) {
                return name || {};
            },
            uploadImage: function (name) {
                return this.createFrame('选择图片',this.U({m:window.module,c:"widget.images",a:'index',p:{fodder:name}}),{h:545,w:900});
            },
            uploadVideo: function () {
                if (this.videoLink) {
                    this.formData.video_link = this.videoLink;
                } else {
                    $(this.$refs.videoFilElem).click();
                }
            },
            uploadAudio: function () {
                if (this.audioLink) {
                    this.formData.audio_link = this.audioLink;
                } else {
                    $(this.$refs.audioFilElem).click();
                }
            },
            openWindows: function(title, url, opt) {
                return this.createFrame(title, url, opt);
            },
            delVideo: function () {
                var that = this;
                that.link = '';
                that.upload.videoIng = false;
                that.$set(that.formData, 'video_link', '');
            },
            delAudio: function () {
                var that = this;
                that.link = '';
                that.upload.audioIng = false;
                that.$set(that.formData, 'audio_link', '');
            },
            search_teacher: function (type) {
                var that = this;
                var url = "{:Url('space.band/searchs_teacher')}?id=" + id + "&type=1";
                var title = '选择课程合集';
                that.searchTeacher = true;
                layer.open({
                    type: 2 //Page层类型
                    , area: ['80%', '90%']
                    , title: title
                    , shade: 0.6 //遮罩透明度
                    , maxmin: true //允许全屏最小化
                    , anim: 1 //0-6的动画形式，-1不开启
                    , content: url,
                    btn: '确定',
                    btnAlign: 'c', //按钮居中
                    closeBtn: 1,
                    yes: function () {
                        layer.closeAll();
                        var instructor_tmp = $("#check_instructor_tmp").val();
                        that.instructor_tmp_list = JSON.parse(instructor_tmp);
                        that.formData.check_instructor_sure = JSON.parse(instructor_tmp);
                        that.show_instructor_list();
                    }
                });
            },
            search_assistant: function (type) {
                var that = this;
                var url = "{:Url('space.band/searchs_assistant')}?id=" + id + "&type=2";
                var title = '选择助理';
                that.searchAssistant = true;
                layer.open({
                    type: 2 //Page层类型
                    , area: ['80%', '90%']
                    , title: title
                    , shade: 0.6 //遮罩透明度
                    , maxmin: true //允许全屏最小化
                    , anim: 1 //0-6的动画形式，-1不开启
                    , content: url,
                    btn: '确定',
                    btnAlign: 'c', //按钮居中
                    closeBtn: 1,
                    yes: function () {
                        layer.closeAll();
                        var assistant_tmp = $("#check_assistant_tmp").val();
                        that.assistant_tmp_list = JSON.parse(assistant_tmp);
                        that.formData.check_assistant_sure = JSON.parse(assistant_tmp);
                        that.show_assistant_list();
                    }
                });
            },
            delLabel: function (index) {
                    this.formData.label.splice(index, 1);
                    this.$set(this.formData, 'label', this.formData.label);
            },
            addLabrl: function () {
                if (this.label) {
                    if (this.label.length > 15) return this.showMsg('您输入的关键字字数太长');
                    var length = this.formData.label.length;
                    if (length >= 6) return this.showMsg('关键字最多添加6个');
                    for (var i = 0; i < length; i++) {
                        if (this.formData.label[i] == this.label) return this.showMsg('请勿重复添加');
                    }
                    this.formData.label.push(this.label);
                    this.$set(this.formData, 'label', this.formData.label);
                    this.label = '';
                }else{
                    return this.showMsg('请输入正确关键字值');
                }
            },
           show_instructor_list: function () {
                    var that = this;
                    var table = layui.table;
                    var form = layui.form;
                    table.render({
                        elem: '#showInstructorList',
                        cols: [[
                            {field: 'id', title: '编号', align: 'center',width:150},
                            {field: 'nickname', title: '老师姓名', edit: 'nickname', align: 'center',width:420},
                            {
                                field: 'image',
                                title: '头像',
                                templet: '<div><img src="{{ d.avatar }}" style="width: 100%;"></div>',
                                align: 'center',
                                width:400,
                            },
                            {field: 'sort', title: '排序',edit:'sort',align: 'center',width:300},
                            {
                                field: 'right', title: '操作', align: 'center', templet: function (d) {
                                    return '<div><a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del"><i class="layui-icon">&#xe640;</i> 移除</a></div>';
                                }
                                ,width:300,
                            },
                        ]],
                        data: (Object.keys(that.formData.check_instructor_sure).length > 0) ? that.formData.check_instructor_sure : [],
                        page: {
                          theme: '#0092DC'
                      },
                        id: 'table'
                    });
                    table.on('tool(showInstructorList)', function(obj){
                        var data = obj.data;
                        if(obj.event === 'del'){
                            if (that.formData.check_instructor_sure) {
                                for(var i=0;i<that.formData.check_instructor_sure.length;i++){
                                    if(that.formData.check_instructor_sure[i].id==data.id){
                                        that.formData.check_instructor_sure.splice(i,1);
                                    }
                                }
                                that.formData.check_instructor_sure=that.formData.check_instructor_sure;
                                that.show_instructor_list();
                            }
                        }
                    });
                    //监听单元格编辑
                    table.on('edit(showInstructorList)', function(obj){
                        var id=obj.data.id,values=obj.value;
                        switch (obj.field) {
                            case 'sort':
                                if (that.formData.check_instructor_sure) {
                                    $.each(that.formData.check_instructor_sure, function(index, value){
                                        if(value.id == id){
                                            that.formData.check_instructor_sure[index].sort = values;
                                        }
                                    })
                                }
                                break;
                        }
                    });
                    //监听素材是否删除
                    form.on('switch(delect)', function (obj) {
                        if (that.formData.check_instructor_sure) {
                            for (var i = 0; i < that.formData.check_instructor_sure.length; i++) {
                                if (that.formData.check_instructor_sure[i].id == obj.value) {
                                    that.formData.check_instructor_sure.splice(i, 1);
                                }
                            }
                            that.formData.check_instructor_sure = that.formData.check_instructor_sure;
                            that.show_instructor_list();
                        }
                    });
            },
            show_assistant_list: function () {
                    var that = this;
                    var table = layui.table;
                    var form = layui.form;
                    table.render({
                        elem: '#showAssistantList',
                        cols: [[
                            {field: 'id', title: '编号', align: 'center',width:150},
                            {field: 'nickname', title: '助理姓名', edit: 'nickname', align: 'center',width:420},
                            {
                                field: 'avatar',
                                title: '头像',
                                templet: '<div><img src="{{ d.avatar }}" style="width: 100%;"></div>',
                                align: 'center',
                                width:400,
                            },
                            {field: 'sort', title: '排序',edit:'sort',align: 'center',width:300},
                            {
                                field: 'right', title: '操作', align: 'center', templet: function (d) {
                                    return '<div><a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del"><i class="layui-icon">&#xe640;</i> 移除</a></div>';
                                }
                                ,width:300,
                            },
                        ]],
                        data: (Object.keys(that.formData.check_assistant_sure).length > 0) ? that.formData.check_assistant_sure : [],
                        page: {
                          theme: '#0092DC'
                      },
                        id: 'table'
                    });
                    table.on('tool(showAssistantList)', function(obj){
                        var data = obj.data;
                        if(obj.event === 'del'){
                            if (that.formData.check_assistant_sure) {
                                for(var i=0;i<that.formData.check_assistant_sure.length;i++){
                                    if(that.formData.check_assistant_sure[i].id==data.id){
                                        that.formData.check_assistant_sure.splice(i,1);
                                    }
                                }
                                that.formData.check_assistant_sure=that.formData.check_assistant_sure;
                                that.show_assistant_list();
                            }
                        }
                    });
                    //监听单元格编辑
                    table.on('edit(showAssistantList)', function(obj){
                        var id=obj.data.id,values=obj.value;
                        switch (obj.field) {
                            case 'sort':
                                if (that.formData.check_assistant_sure) {
                                    $.each(that.formData.check_assistant_sure, function(index, value){
                                        if(value.id == id){
                                            that.formData.check_assistant_sure[index].sort = values;
                                        }
                                    })
                                }
                                break;
                        }
                    });
                    //监听素材是否删除
                    form.on('switch(delect)', function (obj) {
                        if (that.formData.check_assistant_sure) {
                            for (var i = 0; i < that.formData.check_assistant_sure.length; i++) {
                                if (that.formData.check_assistant_sure[i].id == obj.value) {
                                    that.formData.check_assistant_sure.splice(i, 1);
                                }
                            }
                            that.formData.check_assistant_sure = that.formData.check_assistant_sure;
                            that.show_assistant_list();
                        }
                    });
            },
            /**
             * 监听radio字段
             */
            eeventRadio: function () {
                var that = this;
                that.radioRule.map(function (val) {
                    that.form.on('radio(' + val + ')', function (res) {
                        that.formData[val] = res.value;
                    });
                })
            },
            init: function () {
                var that = this;
                window.UMEDITOR_CONFIG.toolbar = [
                    // 加入一个 test
                    'source | undo redo | bold italic underline strikethrough | superscript subscript | forecolor backcolor | removeformat |',
                    'insertorderedlist insertunorderedlist | selectall cleardoc paragraph | fontfamily fontsize',
                    '| justifyleft justifycenter justifyright justifyjustify |',
                    'link unlink | emotion selectimgs video | map',
                    '| horizontal print preview fullscreen', 'drafts', 'formula'
                ];
                UM.registerUI('selectimgs', function (name) {
                    var me = this;
                    var $btn = $.eduibutton({
                        icon: 'image',
                        click: function () {
                            that.createFrame('选择图片', "{:Url('widget.images/index',['fodder'=>'editor'])}");
                        },
                        title: '选择图片'
                    });

                    this.addListener('selectionchange', function () {
                        //切换为不可编辑时，把自己变灰
                        var state = this.queryCommandState(name);
                        $btn.edui().disabled(state == -1).active(state == 1)
                    });
                    return $btn;

                });
                UM.registerUI('video', function (name) {
                    var me = this;
                    var $btn = $.eduibutton({
                        icon: 'video',
                        click: function () {
                            that.createFrame('选择音视频', "{:Url('widget.video/indexs',['fodder'=>'video'])}");
                        },
                        title: '选择音视频'
                    });

                    this.addListener('selectionchange', function () {
                        //切换为不可编辑时，把自己变灰
                        var state = this.queryCommandState(name);
                        $btn.edui().disabled(state == -1).active(state == 1)
                    });
                    return $btn;

                });
                //实例化编辑器
                that.$nextTick(function () {
                    layui.use(['form','element','table','laydate'], function () {
                        that.form = layui.form;
                        that.laydate = layui.laydate;
                        that.form.render();
                        that.form.on('select(labelIndex)', function (data) {
                                that.setLabelTable(parseInt(data.value),false)
                        });
                        that.form.on('select(rule_index)', function (data) {
                            that.ruleIndex = data.value;
                        });
                        layui.element.on('tab(docTabBrief)', function(){
                            that.layTabId = this.getAttribute('lay-id');
                        });
                        that.eeventRadio();
                        that.laydate.render({
                            elem: '#add_time', //指定元素
                            type: 'datetime',
                            done: function(value) {
                                that.formData.add_time = value;
                            }
                        });
                        // 管理
                        that.show_instructor_list();
                        that.show_assistant_list();
                    });
                    layui.config({
                        base : '/static/plug/layui/'
                    }).extend({
                        selectN: './selectN',
                    }).use('selectM',function () {
                        var selectM = layui.selectM;
                        var selectM1 = layui.selectM;
                        selectM({
                            //元素容器【必填】
                            elem: '#city_id'
                            //候选数据【必填】
                            ,data: that.cityList
                            //默认值
                            ,selected: that.formData.city_id || 0
                            //最多选中个数，默认5
                            ,max : 1
                            ,name: 'city_id'
                            ,model: 'formData.city_id'
                            //值的分隔符
                            ,delimiter: ','
                            //候选项数据的键名
                            ,field: {idName:'id',titleName:'name',statusName:'disabled'}
                        });
                        selectM1({
                            //元素容器【必填】
                            elem: '#activity_id'
                            //候选数据【必填】
                            ,data: that.activityList
                            //默认值
                            ,selected: that.formData.activity_id || 0
                            //最多选中个数，默认5
                            ,max : 5
                            ,name: 'activity_id'
                            ,model: 'formData.activity_id'
                            //值的分隔符
                            ,delimiter: ','
                            //候选项数据的键名
                            ,field: {idName:'id',titleName:'name',statusName:'disabled'}
                        });
                    });
                })
            },
            requestPost: function (url, data) {
                return new Promise(function (resolve, reject) {
                    axios.post(url, data).then(function (res) {
                        if (res.status == 200 && res.data.code == 200) {
                            resolve(res.data)
                        } else {
                            reject(res.data);
                        }
                    }).catch(function (err) {
                        reject({msg:err})
                    });
                })
            },
            requestGet: function (url) {
                return new Promise(function (resolve, reject) {
                    axios.get(url).then(function (res) {
                        if (res.status == 200 && res.data.code == 200) {
                            resolve(res.data)
                        } else {
                            reject(res.data);
                        }
                    }).catch(function (err) {
                        reject({msg:err})
                    });
                })
            },
            handleSubmit:function () {
                var that = this;
                if (!that.formData.store_name) {
                    return that.showMsg('请填写乐队名');
                }
                that.formData.activity_id = $('input[name="activity_id"]').val();
                if (!that.formData.activity_id) {
                    return that.showMsg('请选择关联活动');
                }
                that.formData.city_id = $('input[name="city_id"]').val();
                if (!that.formData.city_id) {
                    return that.showMsg('请选择关联的城市');
                }
                if (!that.formData.detailed_address) {
                    return that.showMsg('请填写地点说明');
                }
                if (!that.formData.introduction) {
                    return that.showMsg('请填写事件说明');
                }
                $('#submit').attr('disabled', 'disabled').text('保存中...');
                that.requestPost(that.U({m:window.module,c:'space.band',a:'save',p:{id:that.id,special_type:that.formData.type}}),that.formData).then(function (res) {
                    that.confirm();
                }).catch(function (res) {
                    that.showMsg(res.msg);
                });
            },
            clone_form:function () {
                if(parseInt(id) == 0){
                    var that = this;
                    if(that.formData.image) return layList.msg('请先删除上传的图片在尝试取消');
                    parent.layer.closeAll();
                }
                parent.layer.closeAll();
            },
            confirm: function(){
                var that = this;
                layui.use(['layer'], function () {
                    var layer = layui.layer;
                    layer.confirm(that.id ? '修改成功是否返回素材列表' : '添加成功是否返回素材列表', {
                        btn: ['返回列表',that.id ? '继续修改' : '继续添加'] //按钮
                    }, function(){
                        location.href = that.U({m:window.module,c:'special.special_type',a:'sources_index'});
                    }, function(){
                        location.reload();
                    });
                });
            },
            render:function(){
                this.$nextTick(function(){
                    layui.use(['form'], function () {
                        layui.form.render('select');
                    });
                })
            },
            // 移动
            handleDragStart (e, item) {
                this.dragging = item;
            },
            handleDragEnd (e, item) {
                this.dragging = null
            },
            handleDragOver (e) {
                e.dataTransfer.dropEffect = 'move'
            },
            handleDragEnter (e, item) {
                e.dataTransfer.effectAllowed = 'move'
                if (item === this.dragging) {
                    return
                }
                var newItems = [...this.formData.activity];
                var src = newItems.indexOf(this.dragging);
                var dst = newItems.indexOf(item);
                newItems.splice(dst, 0, ...newItems.splice(src, 1))
                this.formData.activity = newItems;
            },
        },
        mounted: function () {
            var that = this;
            axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';
            that.getActivityInfo();
            that.get_city_list();
            that.get_activity_list();
            that.getCurrentTime();
            //获取素材展示
            // that.show_product_list();
            window.$vm = that;
            window.changeIMG = that.changeIMG;
            window.insertEditor = that.insertEditor;
            window.insertEditorVideo = that.insertEditorVideo;
            window.insertEditorAudio = that.insertEditorAudio;
            $(that.$refs.videoFilElem).change(function (type) {
                var inputFile = this.files[0];
                that.requestPost(that.U({m:window.module,c:"widget.video",a:'get_signature'}),{is_private:1}).then(function (res) {
                    AdminUpload.upload(res.data.uploadType,{
                        token: res.data.uploadToken || '',
                        file: inputFile,
                        accessKeyId: res.data.accessKey || '',
                        accessKeySecret: res.data.secretKey || '',
                        bucketName: res.data.storageName || '',
                        region: res.data.storageRegion || '',
                        domain: res.data.domain || '',
                        uploadIng:function (progress) {
                            that.upload.videoIng = true;
                            that.progress = progress;
                        }
                    }).then(function (res) {
                        //成功
                        that.$set(that.formData, 'video_link', res.origin_url);
                        that.$set(that.formVideoData, 'link', res.origin_url);
                        that.$set(that.formVideoData, 'time', 0);
                        that.$set(that.formVideoData, 'width', 0);
                        that.$set(that.formVideoData, 'height', 0);
                        that.$set(that.formData, 'video_signature_link', res.signatureUrl);
                        that.progress = 0;
                        that.upload.videoIng = false;
                        return that.showMsg('上传成功');
                    }).catch(function (err) {
                        //失败
                        return that.showMsg('上传错误请检查您的配置');
                    });
                }).catch(function (res) {
                    return that.showMsg(res.msg || '获取密钥失败,请检查您的配置');
                });
            });
            $(that.$refs.audioFilElem).change(function (type) {
                var inputFile = this.files[0];
                that.requestPost(that.U({m:window.module,c:"widget.audio",a:'get_signature'})).then(function (res) {
                    AdminUpload.upload(res.data.uploadType,{
                        token: res.data.uploadToken || '',
                        file: inputFile,
                        accessKeyId: res.data.accessKey || '',
                        accessKeySecret: res.data.secretKey || '',
                        bucketName: res.data.storageName || '',
                        region: res.data.storageRegion || '',
                        domain: res.data.domain || '',
                        uploadIng:function (progress) {
                            that.upload.audioIng = true;
                            that.audio_progress = progress;
                        }
                    }).then(function (res) {
                        //成功
                        that.$set(that.formData, 'audio_link', res.origin_url);
                        that.$set(that.formData, 'audio_signature_link', res.signatureUrl);
                        that.audio_progress = 0;
                        that.upload.audioIng = false;
                        return that.showMsg('上传成功');
                    }).catch(function (err) {
                        //失败
                        return that.showMsg('上传错误请检查您的配置');
                    });
                }).catch(function (res) {
                    return that.showMsg(res.msg || '获取密钥失败,请检查您的配置');
                });
            });
        }
    });
</script>
</body>
</html>
