{extend name="public/container"}
{block name="content"}
<style type="text/css">
  .layui-table img {
    margin: 0px -3px;
    width: calc(100% + 20px);
    height: calc(100% + 20px);
  }

  .layui-table img {
    max-width: 60px;
  }

  .image {
    box-shadow: 0 0 0 0.25rem #fff;
    border-radius: 50%;
    background-repeat: repeat-x;
  }
</style>
<div class="layui-fluid" style="background: #fff;margin-top: -10px;">
  <div class="layui-tab layui-tab-brief" lay-filter="tab">
    <ul class="layui-tab-title">
      <li lay-id="list" {eq name='type' value='0' }class="layui-this" {/eq}>
        <a href="{eq name='type' value='0'}javascript:;{else}{:Url('index',['type'=>0])}{/eq}">全部({$all})</a>
      </li>
      <li lay-id="list" {eq name='type' value='1' }class="layui-this" {/eq}>
        <a href="{eq name='type' value='1'}javascript:;{else}{:Url('index',['type'=>1])}{/eq}">报名中的乐队({$registered})</a>
      </li>
      <li lay-id="list" {eq name='type' value='2' }class="layui-this" {/eq}>
        <a href="{eq name='type' value='2'}javascript:;{else}{:Url('index',['type'=>2])}{/eq}">进行中的乐队({$processing})</a>
      </li>
      <li lay-id="list" {eq name='type' value='3' }class="layui-this" {/eq}>
        <a href="{eq name='type' value='3'}javascript:;{else}{:Url('index',['type'=>3])}{/eq}">已结束的乐队({$ended})</a>
      </li>
      <li lay-id="list" {eq name='type' value='4' }class="layui-this" {/eq}>
        <a href="{eq name='type' value='4'}javascript:;{else}{:Url('index',['type'=>4])}{/eq}">已取消的乐队({$cancelled})</a>
      </li>
      <li lay-id="list" {eq name='type' value='5' }class="layui-this" {/eq}>
        <a href="{eq name='type' value='5'}javascript:;{else}{:Url('index',['type'=>5])}{/eq}">乐队回收站({$recycle})</a>
      </li>
    </ul>
  </div>
  <div class="layui-row layui-col-space15" id="app">
    <div class="layui-col-md12">
      <div class="layui-card">
        <div class="layui-card-body">
          <form class="layui-form layui-form-pane" action="">
            <div class="layui-form-item">
              <div class="layui-inline">
                <label class="layui-form-label">乐队名称</label>
                <div class="layui-input-block">
                  <input type="text" name="store_name" class="layui-input" placeholder="请输入的乐队名称,关键字,编号">
                  <input type="hidden" name="type" value="{$type}">
                </div>
              </div>
              <div class="layui-inline">
                <div class="layui-input-inline">
                  <button class="layui-btn layui-btn-sm layui-btn-normal" lay-submit="search" lay-filter="search">
                    <i class="layui-icon layui-icon-search"></i>搜索</button>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
    <!--的乐队列表-->
    <div class="layui-col-md12">
      <div class="layui-card">
        <div class="layui-card-body">
          <div class="layui-btn-container">
            <a class="layui-btn layui-btn-sm" href="{:Url('create')}">添加的乐队</a>
            {switch name='type'}
            {case value="5"}
            <button class="layui-btn layui-btn-sm" data-type="show">批量显示</button>
            {/case}
            {/switch}
          </div>
          <table class="layui-hide" id="List" lay-filter="List"></table>
          <!--图片-->
          <script type="text/html" id="image">
            <img style="cursor: pointer" lay-event="open_image" src="{{d.image}}">
          </script>
          <!--显示|隐藏-->
          <script type="text/html" id="checkisshow">
            <input type='checkbox' name='id' lay-skin='switch' value="{{d.id}}" lay-filter='is_show' lay-text='显示|隐藏' {{ d.is_show == 1 ? 'checked' : '' }}>
          </script>
          <!-- 首页推荐 -->
          <script type="text/html" id="checkishomeshow">
            <input type='checkbox' name='id' lay-skin='switch' value="{{d.id}}" lay-filter='is_home_recommend' lay-text='是|否' {{ d.is_home_recommend == 1 ? 'checked' : '' }}>
          </script>
          <!--乐队名称-->
          <script type="text/html" id="name">
            <h4>{{d.store_name}}</h4>
            <p>状态:
              {{# if(d.status == 1 ){ }}
                <font color="red">报名中</font>
                {{# } }}
                  {{# if(d.status == 2 ){ }}
                    <font color="red">进行中</font>
                    {{# } }}
                      {{# if(d.status == 3 ){ }}
                        <font color="red">已结束</font>
                        {{# } }}
                          {{# if(d.status == 4 ){ }}
                            <font color="red">已取消</font>
                            {{# } }}
            </p>
          </script>
          <!--空间要求-->
          <script type="text/html" id="space_conditions">
            <p>所在城市:{{d.city_name}}</p>
            <p>地点说明:{{d.detailed_address}}</p>
            <p>时间说明:{{d.introduction}}</p>
            <p>乐队限制人数:{{d.limit_band_size}}</p>
          </script>
          <!--活动要求-->
          <script type="text/html" id="activity_conditions">
            <p>关联的活动名称:{{d.activity_info.store_name}}</p>
            <p>费用:{{d.activity_info.price}}</p>
            <p>加入要求:{{d.activity_info.info}}</p>
          </script>
          <!--报名进度-->
          <script type="text/html" id="apply_apply">
            <p>乐队限制人数:{{d.limit_band_size}}</p>
            <p>已成团人数:{{d.group_number}}</p>
          </script>
          <!-- 指导信息 -->
          <script type="text/html" id="guideinfo">
            指导老师:
            <div class="avatars">
              <div class="avatars__item">
                <div class="avatars__image">
                  {{# layui.each(d.instructor, function(index, item){ }}
                    <img src="{{item.avatar}}" class="image">
                    {{# }); }}
                </div>
              </div>
            </div>
            助理:
            <div class="avatars">
              <div class="avatars__item">
                <div class="avatars__image">
                  {{# layui.each(d.assistant, function(index, item){ }}
                    <img src="{{item.avatar}}" class="image" id="second">
                    {{# }); }}
                </div>
              </div>
            </div>
          </script>
          <!-- 成团信息 -->
          <script type="text/html" id="groupinfo">
            <p>已成团人数:{{d.group_number}}</p>
            已成团学生列表：<div class="avatars">
              <div class="avatars__item">
                <div class="avatars__image">
                  {{# layui.each(d.groupList, function(index, item){ }}
                    <img src="{{item.avatar}}" class="image" id="second">
                    {{# }); }}
                </div>
              </div>
            </div>
          </script>
          <!--操作-->
          <script type="text/html" id="act">
            <button type="button" class="layui-btn layui-btn-xs layui-btn-normal" lay-event='edit'>
              编辑
            </button>
            <button type="button" class="layui-btn layui-btn-xs" onclick="dropdown(this)">操作 <span class="caret"></span></button>
            <ul class="layui-nav-child layui-anim layui-anim-upbit">
              <li>
                <a href="javascript:void(0);"
                  onclick="$eb.createModalFrame('状态管理','{:Url('band_status')}?id={{d.id}}&type=1',{w:600,h:500})">
                  <i class="fa fa-history"></i> 状态管理
                </a>
              </li>
              {{# if(d.is_del){ }}
                <li>
                  <a href="javascript:void(0);" lay-event='delstor'>
                    <i class="fa fa-trash"></i> 恢复的乐队
                  </a>
                </li>
                {{# }else{ }}
                  <li>
                    <a href="javascript:void(0);" lay-event='delstor'>
                      <i class="fa fa-trash"></i> 移到回收站
                    </a>
                  </li>
                  {{# } }}
            </ul>
          </script>
        </div>
      </div>
    </div>
  </div>
</div>
<script src="{__ADMIN_PATH}js/layuiList.js"></script>
<script>
  var type = <?= $type ?>;
  //实例化form
  layList.form.render();
  //加载列表
  layList.tableList('List', "{:Url('band_ist',['type'=>$type])}", function() {
    var join = new Array();
    switch (parseInt(type)) {
      case 1:
        join = [{
            field: 'id',
            title: 'ID',
            sort: true,
            event: 'id',
            width: '5%'
          },
          {
            field: 'store_name',
            title: '乐队信息',
            templet: '#name',
            width: '15%'
          },
          {
            field: 'limit_band_size',
            title: '活动信息',
            templet: "#activity_conditions",
            width: '22.3%'
          },
          {
            field: 'limit_band_size',
            title: '空间要求',
            templet: "#space_conditions",
            width: '23%'
          },
          {
            field: 'limit_band_size',
            title: '报名进度',
            templet: "#apply_apply",
            width: '10%'
          },
          {
            field: 'sort',
            title: '排序',
            edit: 'sort',
            width: '5%'
          },
          {
            field: 'is_draft',
            title: '草稿状态',
            templet: "#checkisshow",
            width: '5%'
          },
          {
            field: 'is_show',
            title: '显示状态',
            templet: "#checkisshow",
            width: '5%'
          },
          {
            field: 'right',
            title: '操作',
            align: 'center',
            toolbar: '#act',
            width: '10%'
          },
        ];
        break;
      case 2:
        join = [{
            field: 'id',
            title: 'ID',
            sort: true,
            event: 'id',
            width: '6%'
          },
          {
            field: 'store_name',
            title: '乐队信息',
            templet: '#name'
          },
          {
            field: 'guideinfo',
            title: '管理员信息',
            templet: '#guideinfo'
          },
          {
            field: 'groupinfo',
            title: '成团信息',
            templet: '#groupinfo'
          },
          {
            field: 'is_show',
            title: '显示状态',
            templet: "#checkisshow",
            width: '8%'
          },
          {
            field: 'right',
            title: '操作',
            align: 'center',
            toolbar: '#act',
            width: '14%'
          },
        ];
        break;
      case 3:
        join = [{
            field: 'id',
            title: 'ID',
            sort: true,
            event: 'id',
            width: '5%'
          },
          {
            field: 'store_name',
            title: '乐队信息',
            templet: '#name'
          },
          {
            field: 'end_time',
            title: '结束时间'
          },
        ];
        break;
      case 4:
        join = [{
            field: 'id',
            title: 'ID',
            sort: true,
            event: 'id',
            width: '5%'
          },
          {
            field: 'store_name',
            title: '乐队信息',
            templet: '#name'
          },
          {
            field: 'cancel_time',
            title: '取消时间'
          },
          {
            field: 'cancel_mark',
            title: '取消原因'
          },
        ];
        break;
      case 5:
        join = [{
            field: 'id',
            title: 'ID',
            sort: true,
            event: 'id',
            width: '5%'
          },
          {
            field: 'store_name',
            title: '乐队信息',
            templet: '#name'
          },
          {
            field: 'del_time',
            title: '回收时间',
            width: '20%'
          },
          {
            field: 'is_show',
            title: '显示状态',
            templet: "#checkisshow",
            width: '10%'
          },
        ];
        break;
      default:
        join = [{
            field: 'id',
            title: 'ID',
            sort: true,
            event: 'id',
            width: '5%'
          },
          {
            field: 'store_name',
            title: '乐队信息',
            templet: '#name'
          },
          {
            field: 'is_home_recommend',
            title: '首页推荐',
            templet: "#checkishomeshow",
            width: '9.4%'
          },
          {
            field: 'is_draft',
            title: '草稿状态',
            templet: "#checkisshow",
            width: '8%'
          },
          {
            field: 'is_show',
            title: '显示状态',
            templet: "#checkisshow",
            width: '8%'
          },
          {
            field: 'right',
            title: '操作',
            align: 'center',
            toolbar: '#act',
            width: '14%'
          },
        ];
        break;
    }
    return join;
  })
  //excel下载
  layList.search('export', function(where) {
    where.excel = 1;
    location.href = layList.U({
      c: 'space.band',
      a: 'band_ist',
      q: where
    });
  })
  //下拉框
  $(document).click(function(e) {
    $('.layui-nav-child').hide();
  })

  function dropdown(that) {
    var oEvent = arguments.callee.caller.arguments[0] || event;
    oEvent.stopPropagation();
    var offset = $(that).offset();
    var top = offset.top - $(window).scrollTop();
    var index = $(that).parents('tr').data('index');
    $('.layui-nav-child').each(function(key) {
      if (key != index) {
        $(this).hide();
      }
    })
    if ($(document).height() < top + $(that).next('ul').height()) {
      $(that).next('ul').css({
        'padding': 10,
        'top': -($(that).parent('td').height() / 2 + $(that).height() + $(that).next('ul').height() / 2),
        'min-width': 'inherit',
        'position': 'absolute'
      }).toggle();
    } else {
      $(that).next('ul').css({
        'padding': 10,
        'top': $(that).parent('td').height() / 2 + $(that).height(),
        'min-width': 'inherit',
        'position': 'absolute'
      }).toggle();
    }
  }
  //快速编辑
  layList.edit(function(obj) {
    var id = obj.data.id,
      value = obj.value;
    switch (obj.field) {
      case 'price':
        action.set_band('price', id, value);
        break;
      case 'limit_band_size':
        action.set_band('limit_band_size', id, value);
        break;
      case 'sort':
        action.set_band('sort', id, value);
        break;
    }
  });
  //隐藏的乐队
  layList.switch('is_show', function(odj, value) {
    if (odj.elem.checked == true) {
      layList.baseGet(layList.Url({
        c: 'space.band',
        a: 'set_show',
        p: {
          is_show: 1,
          id: value
        }
      }), function(res) {
        layList.msg(res.msg, function() {
          layList.reload();
        });
      });
    } else {
      layList.baseGet(layList.Url({
        c: 'space.band',
        a: 'set_show',
        p: {
          is_show: 0,
          id: value
        }
      }), function(res) {
        layList.msg(res.msg, function() {
          layList.reload();
        });
      });
    }
  });
  //点击事件绑定
  layList.tool(function(event, data, obj) {
    switch (event) {
      case 'delstor':
        var url = layList.U({
          c: 'space.band',
          a: 'delete',
          q: {
            id: data.id
          }
        });
        if (data.is_del) var code = {
          title: "操作提示",
          text: "确定恢复的乐队操作吗？",
          type: 'info',
          confirm: '是的，恢复该的乐队'
        };
        else var code = {
          title: "操作提示",
          text: "确定将该的乐队移入回收站吗？",
          type: 'info',
          confirm: '是的，移入回收站'
        };
        $eb.$swal('delete', function() {
          $eb.axios.get(url).then(function(res) {
            if (res.status == 200 && res.data.code == 200) {
              $eb.$swal('success', res.data.msg);
              obj.del();
              location.reload();
            } else
              return Promise.reject(res.data.msg || '删除失败')
          }).catch(function(err) {
            $eb.$swal('error', err);
          });
        }, code)
        break;
      case 'open_image':
        $eb.openImage(data.image);
        break;
      case 'edit':
        location.href = layList.U({
          c: 'space.band',
          a: 'create',
          q: {
            id: data.id
          }
        });
        break;
      case 'attr':
        $eb.createModalFrame(data.name + '-属性', layList.U({
          c: 'space.band',
          a: 'attr',
          q: {
            id: data.id
          }
        }), {
          h: 600,
          w: 800
        })
        break;
    }
  })
  //排序
  layList.sort(function(obj) {
    var type = obj.type;
    switch (obj.field) {
      case 'id':
        layList.reload({
          order: layList.order(type, 'id')
        }, true, null, obj);
        break;
      case 'sales':
        layList.reload({
          order: layList.order(type, 'sales')
        }, true, null, obj);
        break;
    }
  });
  //查询
  layList.search('search', function(where) {
    layList.reload(where, true);
  });
  //自定义方法
  var action = {
    set_band: function(field, id, value) {
      layList.baseGet(layList.Url({
        c: 'space.band',
        a: 'set_band',
        q: {
          field: field,
          id: id,
          value: value
        }
      }), function(res) {
        layList.msg(res.msg);
      });
    },
    show: function() {
      var ids = layList.getCheckData().getIds('id');
      if (ids.length) {
        layList.basePost(layList.Url({
          c: 'space.band',
          a: 'band_show'
        }), {
          ids: ids
        }, function(res) {
          layList.msg(res.msg);
          layList.reload();
        });
      } else {
        layList.msg('请选择要显示的的乐队');
      }
    }
  };
  //多选事件绑定
  $('.layui-btn-container').find('button').each(function() {
    var type = $(this).data('type');
    $(this).on('click', function() {
      action[type] && action[type]();
    })
  });
</script>
{/block}