<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="referrer" content="never">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link href="{__FRAME_PATH}css/font-awesome.min.css" rel="stylesheet">
    <link href="{__ADMIN_PATH}plug/umeditor/themes/default/css/umeditor.css" type="text/css" rel="stylesheet">
    <script type="text/javascript" src="{__ADMIN_PATH}plug/umeditor/third-party/jquery.min.js"></script>
    <script type="text/javascript" src="{__ADMIN_PATH}plug/umeditor/third-party/template.min.js"></script>
    <script type="text/javascript" charset="utf-8" src="{__ADMIN_PATH}plug/umeditor/umeditor.config.js"></script>
    <script type="text/javascript" charset="utf-8" src="{__ADMIN_PATH}plug/umeditor/umeditor.min.js"></script>
    <script type="text/javascript" src="{__ADMIN_PATH}plug/umeditor/lang/zh-cn/zh-cn.js"></script>
    <link rel="stylesheet" href="/static/plug/layui/css/layui.css">
    <script src="/static/plug/layui/layui.js"></script>
    <script src="{__PLUG_PATH}vue/dist/vue.min.js"></script>
    <script src="/static/plug/axios.min.js"></script>
    <script src="/static/plug/iview/dist/iview.min.js"></script>
    <script src="{__MODULE_PATH}widget/aliyun-oss-sdk-4.4.4.min.js"></script>
    <script src="{__MODULE_PATH}widget/cos-js-sdk-v5.min.js"></script>
    <script src="{__MODULE_PATH}widget/qiniu-js-sdk-2.5.5.js"></script>
    <script src="{__MODULE_PATH}widget/plupload.full.min.js"></script>
    <script src="{__MODULE_PATH}widget/videoUpload.js"></script>
    <script type="text/javascript">
        $eb = parent._mpApi;
        window.controlle="<?php echo strtolower(trim(preg_replace("/[A-Z]/", "_\\0", app('request')->controller()), "_"));?>";
        window.module="6GqvmHa8HRGHoQEQ";
    </script>
    <style>
        .layui-form-item {
            margin-bottom: 0px;
        }

        .layui-form-item .special-label {
            width: 50px;
            float: left;
            height: 30px;
            line-height: 38px;
            margin-left: 10px;
            margin-top: 5px;
            border-radius: 5px;
            background-color: #0092DC;
            text-align: center;
        }

        .layui-form-item .special-label i {
            display: inline-block;
            width: 18px;
            height: 18px;
            font-size: 18px;
            color: #fff;
        }

        .layui-form-item .label-box {
            border: 1px solid;
            border-radius: 10px;
            position: relative;
            padding: 10px;
            height: 30px;
            color: #fff;
            background-color: #393D49;
            text-align: center;
            cursor: pointer;
            display: inline-block;
            line-height: 10px;
        }

        .layui-form-item .label-box p {
            line-height: inherit;
        }

        .pictrueBox {
            display: inline-block !important;
        }

        .pictrue {
            width: 60px;
        height: 60px;
            border: 1px dotted rgba(0, 0, 0, 0.1);
            margin-right: 15px;
            display: inline-block;
            position: relative;
            cursor: pointer;
        }

        .pictrue img {
            width: 100%;
            height: 100%;
        }

        .upLoad {
            width: 58px;
            height: 58px;
            line-height: 58px;
            border: 1px dotted rgba(0, 0, 0, 0.1);
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.02);
            cursor: pointer;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .rulesBox {
            display: flex;
            flex-wrap: wrap;
            margin-left: 10px;
        }

        .layui-tab-content {
            margin-top: 15px;
        }

        .ml110 {
            margin: 18px 0 4px 110px;
        }

        .rules {
            display: flex;
        }

        .rules-btn-sm {
            height: 30px;
            line-height: 30px;
            font-size: 12px;
            width: 109px;
        }

        .rules-btn-sm input {
            width: 79% !important;
            height: 84% !important;
            padding: 0 10px;
        }

        .ml10 {
            margin-left: 10px !important;
        }

        .ml40 {
            margin-left: 40px !important;
        }

        .closes {
            position: absolute;
            left: 86%;
            top: -18%;
        }
        .red {
            color: red;
        }
        .layui-input-block .layui-video-box{
            width: 22%;
            height: 180px;
            border-radius: 10px;
            background-color: #707070;
            margin-top: 10px;
            position: relative;
            overflow: hidden;
        }
        .layui-input-block .layui-video-box i{
            color: #fff;
            line-height: 180px;
            margin: 0 auto;
            width: 50px;
            height: 50px;
            display: inherit;
            font-size: 50px;
        }
        .layui-input-block .layui-video-box .mark{
            position: absolute;
            width: 100%;
            height: 30px;
            top: 0;
            background-color: rgba(0,0,0,.5);
            text-align: center;
        }
        .layui-input-block .layui-audio-box{
            width: 50%;
            height: 54px;
            border-radius: 10px;
            background-color: #707070;
            margin-top: 10px;
            position: relative;
            overflow: hidden;
        }
        .layui-input-block .layui-audio-box i{
            color: #fff;
            line-height: 180px;
            margin: 0 auto;
            width: 50px;
            height: 50px;
            display: inherit;
            font-size: 50px;
        }
        .layui-input-block .layui-audio-box .mark{
            width: 11%;
            height: 30px;
            top: 0px;
            background-color: rgba(0,0,0,.5);
            text-align: center;
            float: right;
            margin-right: 10px;
            margin-top: 11px;
        }
        .layui-form-select dl {
            z-index: 1015;
        }
        .store_box{
            display: flex;
        }
        .ivu-input{
             border-width: 0px !important;
             width: 100%;
             height: 36px;
         }
         .ivu-select-dropdown{
             z-index: 999;
             background: #fff;
         }
         .ivu-cascader-menu .ivu-cascader-menu-item-active {
             background-color: #f3f3f3;
             color: #2d8cf0;
         }
         .ivu-cascader-menu .ivu-cascader-menu-item {
             position: relative;
             padding-right: 24px;
             -webkit-transition: all .2s ease-in-out;
             transition: all .2s ease-in-out;
         }
         .ivu-cascader .ivu-cascader-menu-item {
             margin: 0;
             line-height: normal;
             padding: 7px 16px;
             clear: both;
             color: #495060;
             font-size: 12px!important;
             white-space: nowrap;
             list-style: none;
             cursor: pointer;
             -webkit-transition: background .2s ease-in-out;
             transition: background .2s ease-in-out;
         }
         .ivu-cascader-menu:last-child {
             border-right-color: transparent;
             margin-right: -1px;
         }
         .ivu-cascader-menu {
             display: inline-block;
             min-width: 100px;
             height: 180px;
             margin: 0;
             padding: 5px 0!important;
             vertical-align: top;
             list-style: none;
             border-right: 1px solid #e9eaec;
             overflow: auto;
                 }
        .info{
            color: #c9c9c9;
            padding-left: 10px;
            line-height: 30px;
        }

        .video_wrap{
           width: 600px;position: relative;
        }
        .jiqu{
           position: absolute;
           top: 50%;
           z-index: 999;top: 10px;right:10px;width: 60px;text-align: center;
           background: rgb(0,0,0,0.8);
           color: #fff;
           font-size: 20px;
           height: 60px;
           line-height: 60px;
           cursor: pointer;
           /* display: none; */
        }
        .video_wrap:hover .jiqu{
            display: block;
        }
    </style>
</head>
<script type="text/javascript">
    window.controlle="<?php echo strtolower(trim(preg_replace("/[A-Z]/", "_\\0", app('request')->controller()), "_"));?>";
    window.module="6GqvmHa8HRGHoQEQ";
</script>
<body>
<div class="layui-fluid">
    <div class="layui-row layui-col-space15"  id="app" v-cloak="">
        <div class="layui-card">
            <div class="layui-card-header">
                <span class="">{{id ? '编辑空间资料': '添加空间资料' }}</span>
                <button style="margin-left: 20px" type="button" class="layui-btn layui-btn-primary layui-btn-xs" @click="goBack">返回列表</button>
            </div>
            <div class="layui-card-body">
                <form class="layui-form" action="" v-cloak="">
                    <div class="layui-tab layui-tab-brief" lay-filter="docTabBrief">
                        <ul class="layui-tab-title">
                            <li class="layui-this" lay-id='1'>设置资料</li>
                        </ul>
                        <div class="layui-tab-content">
                            <div class="layui-tab-item layui-show">
                                <div class="layui-row layui-col-space15">
                                    <div class="layui-row layui-col-space15">
                                        <div class="layui-col-xs12 layui-col-sm4 layui-col-md4">
                                            <div class="grid-demo grid-demo-bg1">
                                                <div class="layui-form-item">
                                                    <label class="layui-form-label">资料类型<i class="red">*</i></label>
                                                    <div class="layui-input-block">
                                                        <input type="radio" name="type" lay-filter="type" value="1" title="课程"
                                                               :checked="formData.type == 1 ? true : false">
                                                        <input type="radio" name="type" lay-filter="type" value="2" title="素材"
                                                               :checked="formData.type == 2 ? true : false">
                                                        <input type="radio" name="type" lay-filter="type" value="3" title="下载文件"
                                                               :checked="formData.type == 3 ? true : false">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                    <div class="grid-demo grid-demo-bg1">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">资料分类<i class="red">*</i></label>
                                            <div class="layui-input-block" id="cate_id">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                    <div class="grid-demo grid-demo-bg1">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">乐队空间<i class="red">*</i></label>
                                            <div class="layui-input-block" id="band_space_id">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-row layui-col-space15" v-show="formData.type == 1">
                                    <div class="layui-col-xs12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">选择课程：<i class="red">*</i></label>
                                                <div class="layui-input-block">
                                                    <input type="hidden" id="check_special_tmp" name="check_special_tmp"/>
                                                    <button type="button" class="layui-btn layui-btn-normal" @click='search_special'>
                                                        选择课程
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">专题展示：</label>
                                                <div class="layui-input-block">
                                                    <input type="hidden" id="check_special_sure" name="check_special_sure"/>
                                                    <table class="layui-hide" id="showSpecialList" lay-filter="showSpecialList"></table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-row layui-col-space15" v-show="formData.type == 2">
                                    <div class="layui-col-xs12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">选择素材：<i class="red">*</i></label>
                                                <div class="layui-input-block">
                                                    <input type="hidden" id="check_product_tmp" name="check_product_tmp"/>
                                                    <button type="button" class="layui-btn layui-btn-normal" @click='search_task'>
                                                        选择素材
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">素材展示：</label>
                                                <div class="layui-input-block">
                                                    <input type="hidden" id="check_source_tmp" name="check_source_tmp"/>
                                                    <table class="layui-hide" id="showSourceList" lay-filter="showSourceList"></table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-row layui-col-space15" v-show="formData.type == 3">
                                    <div class="layui-col-xs12 layui-col-sm4 layui-col-md4">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">素材类型<i class="red">*</i></label>
                                                <div class="layui-input-block">
                                                    <input type="radio" name="source_type" lay-filter="source_type" value="1" title="图片"
                                                           :checked="formData.source_type == 1 ? true : false">
                                                    <input type="radio" name="source_type" lay-filter="source_type" value="2" title="视频"
                                                           :checked="formData.source_type == 2 ? true : false">
                                                    <input type="radio" name="source_type" lay-filter="source_type" value="3" title="音频"
                                                           :checked="formData.source_type == 3 ? true : false">
                                                    <input type="radio" name="source_type" lay-filter="source_type" value="4" title="文档"
                                                           :checked="formData.source_type == 4 ? true : false">
                                                    <input type="radio" name="source_type" lay-filter="source_type" value="5" title="zip压缩格式"
                                                           :checked="formData.source_type == 5 ? true : false">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                               <div class="layui-col-xs12 layui-col-sm12 layui-col-md12"  v-show="formData.type == 3 && formData.source_type == 1">
                                    <div class="grid-demo grid-demo-bg1">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">图片(710*400px)<i class="red">*</i></label>
                                            <div class="pictrueBox">
                                                <div class="pictrue" v-if="formData.image" @click="uploadImage('image')">
                                                    <img :src="formData.image"></div>
                                                <div class="upLoad" @click="uploadImage('image')" v-else>
                                                    <i class="layui-icon layui-icon-camera" class="iconfont"
                                                       style="font-size: 26px;"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-form-item submit" v-show="formData.type == 3 && formData.source_type == 2">
                                    <label class="layui-form-label">视频素材<i class="red">*</i></label>
                                    <div class="layui-input-block">
                                        <input type="text" name="title" v-model="videoLink" style="width:50%;display:inline-block;margin-right: 10px;" autocomplete="off" placeholder="请点击上传视频按钮，选择素材上传" class="layui-input">
                                        <button type="button" @click="uploadVideo" class="layui-btn layui-btn-sm layui-btn-normal">{{videoLink ? '确认添加' : '上传视频'}}</button>
                                        <input ref="videoFilElem" type="file" style="display: none">
                                        <input ref="screenShotFilElem" type="text" style="display: none">
                                    </div>
                                    <div class="layui-input-block video_show" style="width: 30%;margin-top: 20px;" v-if="upload.videoIng">
                                        <div class="layui-progress" style="margin-bottom: 10px">
                                            <div class="layui-progress-bar layui-bg-blue" :style="'width:'+progress+'%'"></div>
                                        </div>
                                        <button type="button" class="layui-btn layui-btn-sm layui-btn-danger percent">{{progress}}%</button>
                                    </div>
                                    <div class="layui-input-block" v-if="formData.video_link">

                                        <div class="" style="display: flex;margin-top: 10px;" v-if="formData.video_link">
                                                                           <div style="" class="video_wrap">
                                           <video ref="videoForm" style="width:100%;border-radius: 10px;" :src="formData.video_signature_link" controls="controls">
                                           <!-- <video ref="videoForm" style="width:100%;border-radius: 10px;" src="https://yw-kb.oss-cn-beijing.aliyuncs.com/test/20200318.mp4" controls="controls"> -->
                                               您的浏览器不支持 video 标签。
                                           </video>
                                           <div class="jiqu"  @click="getVideoImage(false)">截取</div>
                                           <div class="mark" @click="delVideo" style="position: absolute;top: 10px;z-index: 999;">
                                               <span class="layui-icon layui-icon-delete" style="font-size: 30px; color: #1E9FFF;"></span>
                                           </div>

                                          </div>
                                          <div style="padding: 0 20px;">
                                           <div style="color:#ccc;padding: 10px;">左侧截取或者输入时间截取</div>
                                           <div  style="display: flex;align-items: center;">
                                            <input type="number" v-model="videoTime" placeholder="输入时间截取" class="layui-input" >
                                            <button type="button" class="layui-btn layui-btn-sm layui-btn-normal" @click="getVideoImage(true)">截取</button>
                                           </div>
                                           <div style="width: 400px;height: 300px;" v-if="videoImage">
                                                <img :src="videoImage" style="width: 100%;height: 100%;object-fit: contain;"></div>
                                           </div>
                                          </div>
                                        </div>
                                    </div>
                            <div class="layui-form-item submit" v-show="formData.type == 3 && formData.source_type == 3">
                                <label class="layui-form-label">音频素材<i class="red">*</i></label>
                                <div class="layui-input-block">
                                    <input type="text" name="title" v-model="formData.audio_link" style="width:50%;display:inline-block;margin-right: 10px;" autocomplete="off" placeholder="请点击上传音频按钮，选择素材上传" class="layui-input">
                                    <button type="button" @click="uploadAudio" class="layui-btn layui-btn-sm layui-btn-normal">{{audioLink ? '确认添加' : '上传音频'}}</button>
                                    <input ref="audioFilElem" type="file" style="display: none">
                                </div>
                                <div class="layui-input-block audio_show" style="width: 30%;margin-top: 20px;" v-if="upload.audioIng">
                                    <div class="layui-progress" style="margin-bottom: 10px">
                                        <div class="layui-progress-bar layui-bg-blue" :style="'width:'+audio_progress+'%'"></div>
                                    </div>
                                    <button type="button" class="layui-btn layui-btn-sm layui-btn-danger percent">{{audio_progress}}%</button>
                                </div>
                                <div class="layui-input-block" v-if="formData.audio_link">
                                    <div class="layui-audio-box" v-if="formData.audio_link">
                                        <audio :src="formData.audio_signature_link" controls="controls">
                                            您的浏览器不支持 audio 标签。
                                        </audio>
                                        <div class="mark" @click="delAudio">
                                            <span class="layui-icon layui-icon-delete" style="font-size: 30px; color: #1E9FFF;"></span>
                                        </div>

                                    </div>
                                    <div class="layui-audio-box" v-else>
                                        <i class="layui-icon layui-icon-play"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-col-xs12 layui-col-sm12 layui-col-md12" v-show="formData.type == 3 && formData.source_type == 4">
                                <label class="layui-form-label">文档<i class="red">*</i></label>
                                <div class="layui-input-block">
                                    <input type="text" name="title" v-model="formData.file_link" style="width:60%;display:inline-block;margin-right: 10px;" autocomplete="off" placeholder="请点击上传文档按钮，选择文档文件上传" class="layui-input">
                                    <button type="button" @click="uploadFile" class="layui-btn layui-btn-sm layui-btn-normal">{{fileLink ? '确认添加' : '上传文档'}}</button>
                                    <input ref="documentFilElem" type="file" style="display: none">
                                </div>
                                <div class="layui-input-block audio_show" style="width: 30%;margin-top: 20px;" v-if="upload.fileIng">
                                    <div class="layui-progress" style="margin-bottom: 10px">
                                        <div class="layui-progress-bar layui-bg-blue" :style="'width:'+file_progress+'%'"></div>
                                    </div>
                                    <button type="button" class="layui-btn layui-btn-sm layui-btn-danger percent">{{file_progress}}%</button>
                                </div>
                            </div>
                            <div class="layui-col-xs12 layui-col-sm12 layui-col-md12" v-show="formData.type == 3 && formData.source_type == 5">
                                <label class="layui-form-label">压缩文件<i class="red">*</i></label>
                                <div class="layui-input-block">
                                    <input type="text" name="title" v-model="formData.zip_link" style="width:60%;display:inline-block;margin-right: 10px;" autocomplete="off" placeholder="请点击上传压缩文件按钮，选择压缩包文件上传" class="layui-input">
                                    <button type="button" @click="uploadZipFile" class="layui-btn layui-btn-sm layui-btn-normal">{{zipLink ? '确认添加' : '上传压缩文件'}}</button>
                                    <input ref="zipFilElem" type="file" style="display: none">
                                </div>
                                <div class="layui-input-block audio_show" style="width: 30%;margin-top: 20px;" v-if="upload.zipIng">
                                    <div class="layui-progress" style="margin-bottom: 10px">
                                        <div class="layui-progress-bar layui-bg-blue" :style="'width:'+zip_file_progress+'%'"></div>
                                    </div>
                                    <button type="button" class="layui-btn layui-btn-sm layui-btn-danger percent">{{zip_file_progress}}%</button>
                                </div>
                            </div>
                            <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                <div class="grid-demo grid-demo-bg1">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">排序：</label>
                                        <div class="layui-input-block">
                                            <input type="number" name="title" lay-verify="sort" autocomplete="off"
                                                    class="layui-input" v-model="formData.sort">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        </div>
                            <div class="layui-tab-content">
                                <div class="layui-row layui-col-space15">
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item" v-if="id">
                                                <button class="layui-btn layui-btn-primary layui-btn-sm" id="submit" type="button" @click="handleSubmit()">保存</button>
                                            </div>
                                            <div class="layui-form-item" v-else>
                                                <button class="layui-btn layui-btn-normal layui-btn-sm" id="submit" type="button" v-if="layTabId == 1" @click="handleSubmit()">提交</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<script src="{__PLUG_PATH}city.js"></script>
<script>
    var id = {$id};
    specialCheckList = <?= isset($specialCheckList) ? $specialCheckList : "{}"?>;
    sourceCheckList = <?= isset($sourceCheckList) ? $sourceCheckList : "{}"?>;
    server_time  = "<?php echo date("Y-m-d H:i:s",time()); ?>";
    $.each(city,function (key,item) {
       city[key].value = item.label;
       if(item.children && item.children.length){
           $.each(item.children,function (i,v) {
               city[key].children[i].value=v.label;
               if(v.children && v.children.length){
                   $.each(v.children,function (k,val) {
                       city[key].children[i].children[k].value=val.label;
                   });
               }
           });
       }
   });
    new Vue({
        el: '#app',
        data: {
            id:id,
            //分类列表
            cateList: [],
            bandList: [],
            special_tmp_list: [],//用于子页父业选中素材传值的临时变量
            source_tmp_list: [],//用于子页父业选中素材传值的临时变量
            upload:{
                videoIng:false,
                audioIng:false,
                fileIng:false,
                zipIng:false,
            },
            formData: {
                type: 1,
                cate_id: 0,
                band_space_id:0,
                source_type: 1,
                image: '',
                file_name:'',
                audio_link:'',
                video_link:'',
                file_link:'',
                zip_link:'',
                video_signature_link:'',
                audio_signature_link:'',
                file_signature_link:'',
                zip_file_signature_link:'',
                check_special_sure: specialCheckList ? specialCheckList : [],
                check_source_sure: sourceCheckList ? sourceCheckList : [],
                sort: 0
            },
            formVideoData: {
                link: 0,
                time: 0,
                width:1420,
                height:800,
            },
            videoTime:0,
            videoImage:null,
            clickIdx:0,
            demand_switch:0,
            link:'',
            audioLink:'',
            videoLink:'',
            fileLink:'',
            zipLink:'',
            //多属性header头
            formHeader:[],
            attr: [],//临时属性
            label: '',
            radioRule: ['type','source_type'],//radio 当选规则
            radioLabel: ['type','source_type'],//radio 当选规则
            progress: 0,
            audio_progress: 0,
            file_progress: 0,
            zip_file_progress: 0,
            searchTask:false,
            um: null,//编译器实例化
            form: null,//layui.form
            layTabId: 1,
            ruleBool: id ? true : false,
        },
        watch:{
            'formData.type':function (n) {
                if (n) {
                    this.render();
                }
            },            
            'formData.source_type':function (n) {
                if (n) {
                    this.render();
                }
            }
        },
        methods: {
            back:function(){
                var that = this;
                layui.use(['element'], function () {
                    layui.element.tabChange('docTabBrief', that.layTabId == 1 ? 1 : parseInt(that.layTabId) - 1);
                });
            },
            next:function(){
                var that = this;
                layui.use(['element'], function () {
                    layui.element.tabChange('docTabBrief', that.layTabId == 5 ? 5 : parseInt(that.layTabId) + 1);
                });
            },
            goBack:function(){
                location.href = this.U({m:window.module,c:'special.special_type',a:'sources_index'});
            },
            U: function (opt) {
                var m = opt.m || 'admin', c = opt.c || window.controlle || '', a = opt.a || 'index', q = opt.q || '',
                    p = opt.p || {};
                var params = Object.keys(p).map(function (key) {
                    return key + '/' + p[key];
                }).join('/');
                var gets = Object.keys(q).map(function (key) {
                    return key+'='+ q[key];
                }).join('&');
                return '/' + m + '/' + c + '/' + a + (params == '' ? '' : '/' + params) + (gets == '' ? '' : '?' + gets);
            },
            /**
             * 提示
             * */
            showMsg: function (msg, success) {
                $('#submit').removeAttr('disabled').text('提交');
                layui.use(['layer'], function () {
                    layui.layer.msg(msg, success);
                });
            },
            /**
             * 获取素材信息
             * */
            getActivityInfo: function () {
                var that = this;
                that.requestGet(that.U({m:window.module,c:"space.band_material",a:'get_material_info',q:{id:that.id}})).then(function (res) {
                    that.$set(that,'cateList',res.data.cateList);
                    that.$set(that,'bandList',res.data.bandList);
                    that.init();
                }).catch(function (res) {
                    console.log(res);
                    that.showMsg(res.msg || res);
                })
            },
            get_cate_list: function () {
                var that = this;
                that.requestGet(that.U({m:window.module,c:"space.band_material_category",a:'get_cate_list'})).then(function (res) {
                    that.$set(that, 'cateList', res.data);
                });
            },
            get_band_list: function () {
                var that = this;
                that.requestGet(that.U({m:window.module,c:"space.band",a:'get_band_list'})).then(function (res) {
                    that.$set(that, 'bandList', res.data);
                });
            },
            getCurrentTime() {
                //获取当前时间并打印
                var _this = this;
            　　let yy = new Date().getFullYear();
            　　let mm = new Date().getMonth()+1;
            　　let dd = new Date().getDate();
            　　let hh = new Date().getHours();
            　　let mf = new Date().getMinutes()<10 ? '0'+new Date().getMinutes() : new Date().getMinutes();
            　　let ss = new Date().getSeconds()<10 ? '0'+new Date().getSeconds() : new Date().getSeconds();
            　　_this.gettime = yy+'/'+mm+'/'+dd+' '+hh+':'+mf+':'+ss;
            　　console.log(_this.gettime)  
            },
            /**
             * 删除图片
             * */
            deleteImage: function (key, index) {
                var that = this;
                if (index != undefined) {
                    that.formData[key].splice(index, 1);
                    that.$set(that.formData, key, that.formData[key]);
                } else {
                    that.$set(that.formData, key, '');
                }
            },
            createFrame: function (title, src, opt) {
                var that = this;
                opt === undefined && (opt = {});
                var h = 0;
                if (window.innerHeight < 800 && window.innerHeight >= 700) {
                    h = window.innerHeight - 50;
                } else if (window.innerHeight < 900 && window.innerHeight >= 800) {
                    h = window.innerHeight - 100;
                } else if (window.innerHeight < 1000 && window.innerHeight >= 900) {
                    h = window.innerHeight - 150;
                } else if (window.innerHeight >= 1000) {
                    h = window.innerHeight - 200;
                } else {
                    h = window.innerHeight;
                }
                var area = [(opt.w || window.innerWidth / 2) + 'px', (!opt.h || opt.h > h ? h : opt.h) + 'px'];
                layui.use('layer',function () {
                    return layer.open({
                        type: 2,
                        title: title,
                        area: area,
                        fixed: false, //不固定
                        maxmin: true,
                        moveOut: false,//true  可以拖出窗外  false 只能在窗内拖
                        anim: 5,//出场动画 isOutAnim bool 关闭动画
                        offset: 'auto',//['100px','100px'],//'auto',//初始位置  ['100px','100px'] t[ 上 左]
                        shade: 0,//遮罩
                        resize: true,//是否允许拉伸
                        content: src,//内容
                        move: '.layui-layer-title'
                    });
                });
                if (title == '选择图片') {
                    that.$set(that.formData, 'video_data', {});
                    that.$set(that.formData, 'is_snapshot', false);
                }
            },
            changeIMG: function (name, value) {
                if (this.getRule(name).maxLength !== undefined) {
                    var that = this;
                    value.map(function (v) {
                        that.formData[name].push(v);
                    });
                    this.$set(this.formData, name, this.formData[name]);
                } else {
                    if(name == 'batchAttr.pic'){
                        this.batchAttr.pic = value;
                    } else {
                        if (name.indexOf('.') !== -1) {
                            var key = name.split('.');
                            if (key.length == 2){
                                this.formData[key[0]][key[1]] = value;
                            } else if(key.length == 3){
                                this.formData[key[0]][key[1]][key[2]] = value;
                            } else if(key.length == 4){
                                this.$set(this.formData[key[0]][key[1]][key[2]],key[3],value)
                            }
                        } else {
                            this.formData[name] = value;
                        }
                    }
                }
            },
            getRule: function (name) {
                return name || {};
            },
            getVideoImage(type,w=120,h=100){
            var that = this;
            let currentTime = 0,videoForm = this.$refs.videoForm;
            if(type){
             if(this.videoTime>videoForm.duration || this.videoTime<0){
                const t =Math.floor(videoForm.duration)
                return this.showMsg(`有效值在0~${t}之间`);
             }
             videoForm.currentTime = this.videoTime;
             videoForm.play();
                currentTime = this.videoTime * 1000
            }else{
                currentTime = Math.floor(videoForm.currentTime*1000)
            }
            that.$set(that.formVideoData, 'time', currentTime);
            that.$set(that.formVideoData, 'width', w);
            that.$set(that.formVideoData, 'height', h);
            var formVideoData = that.formVideoData;
            that.$set(that.formData, 'video_data', formVideoData);
            that.requestPost(that.U({m:window.module,c:'special.special_type',a:'source_video_snapshot',p:{}}),that.formVideoData).then(function (res) {
                if (res && res.code == 200) {
                    that.$set(that.formData, 'image', res.data);
                    that.$set(that.formData, 'is_snapshot', true);
                }
            }).catch(function (res) {
                return that.showMsg(res.msg || '获取截图失败,请稍后');
            });
           },
            uploadImage: function (name) {
                return this.createFrame('选择图片',this.U({m:window.module,c:"widget.images",a:'index',p:{fodder:name}}),{h:545,w:900});
            },
            uploadFile: function (opt) {
                if (this.fileLink) {
                    this.formData.file_link = this.fileLink;
                } else {
                    $(this.$refs.documentFilElem).click();
                }
            },
            uploadZipFile: function (opt) {
                if (this.zipLink) {
                    this.formData.zip_link = this.zipLink;
                } else {
                    $(this.$refs.zipFilElem).click();
                }
            },
            uploadVideo: function () {
                if (this.videoLink) {
                    this.formData.video_link = this.videoLink;
                } else {
                    $(this.$refs.videoFilElem).click();
                }
            },
            uploadAudio: function () {
                if (this.audioLink) {
                    this.formData.audio_link = this.audioLink;
                } else {
                    $(this.$refs.audioFilElem).click();
                }
            },
            openWindows: function(title, url, opt) {
                return this.createFrame(title, url, opt);
            },
            delVideo: function () {
                var that = this;
                that.link = '';
                that.upload.videoIng = false;
                that.$set(that.formData, 'video_link', '');
            },
            delAudio: function () {
                var that = this;
                that.link = '';
                that.upload.audioIng = false;
                that.$set(that.formData, 'audio_link', '');
            },
            getContent: function () {
                return this.um.getContent();
            },
            getDetail: function () {
                return this.ums.getContent();
            },
            search_special: function () {
                var that = this;
                var url = "{:Url('special.special_type/searchs_special')}?id=" + id + "&type=0";
                var title = '选择课程合集';
                that.searchSpecial = true;
                layer.open({
                    type: 2 //Page层类型
                    , area: ['80%', '90%']
                    , title: title
                    , shade: 0.6 //遮罩透明度
                    , maxmin: true //允许全屏最小化
                    , anim: 1 //0-6的动画形式，-1不开启
                    , content: url,
                    btn: '确定',
                    btnAlign: 'c', //按钮居中
                    closeBtn: 1,
                    yes: function () {
                        layer.closeAll();
                        var special_tmp = $("#check_special_tmp").val();
                        that.special_tmp_list = JSON.parse(special_tmp);
                        that.formData.check_special_sure = JSON.parse(special_tmp);
                        that.show_special_list();
                    }
                });
            },
            search_task: function () {
                var that = this;
                var url = "{:Url('special.special_type/searchs_all_task')}?id=" + id + "&type=0";
                var title = '选择素材资料';
                that.searchTask = true;
                layer.open({
                    type: 2 //Page层类型
                    , area: ['80%', '90%']
                    , title: title
                    , shade: 0.6 //遮罩透明度
                    , maxmin: true //允许全屏最小化
                    , anim: 1 //0-6的动画形式，-1不开启
                    , content: url,
                    btn: '确定',
                    btnAlign: 'c', //按钮居中
                    closeBtn: 1,
                    yes: function () {
                        layer.closeAll();
                        var source_tmp = $("#check_source_tmp").val();
                        that.source_tmp_list = JSON.parse(source_tmp);
                        that.formData.check_source_sure = JSON.parse(source_tmp);
                        that.show_source_list();
                    }
                });
            },
            delLabel: function (index) {
                    this.formData.label.splice(index, 1);
                    this.$set(this.formData, 'label', this.formData.label);
            },
            addLabrl: function () {
                if (this.label) {
                    if (this.label.length > 15) return this.showMsg('您输入的关键字字数太长');
                    var length = this.formData.label.length;
                    if (length >= 6) return this.showMsg('关键字最多添加6个');
                    for (var i = 0; i < length; i++) {
                        if (this.formData.label[i] == this.label) return this.showMsg('请勿重复添加');
                    }
                    this.formData.label.push(this.label);
                    this.$set(this.formData, 'label', this.formData.label);
                    this.label = '';
                }else{
                    return this.showMsg('请输入正确关键字值');
                }
            },
            show_source_list: function () {
                var that = this;
                var table = layui.table;
                var form = layui.form;
                table.render({
                    elem: '#showSourceList',
                    cols: [[
                        {field: 'id', title: '编号', align: 'center',width:150},
                        {field: 'store_name', title: '商品名', edit: 'store_name', align: 'center',width:350},
                        {
                            field: 'image',
                            title: '封面',
                            templet: '<div><img src="{{ d.image }}" style="width: 100%;"></div>',
                            align: 'center',
                            width:350,
                        },
                        {field: 'sort', title: '排序',edit:'sort',align: 'center',width:200},
                        {
                            field: 'right', title: '操作', align: 'center', templet: function (d) {
                                return '<div><a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del"><i class="layui-icon">&#xe640;</i> 移除</a></div>';
                            }
                            ,width:200,
                        },
                    ]],
                    data: (Object.keys(that.formData.check_source_sure).length > 0) ? that.formData.check_source_sure : [],
                    page: {
                      theme: '#0092DC'
                  },
                    id: 'table'
                });
                table.on('tool(showSourceList)', function(obj){
                    var data = obj.data;
                    if(obj.event === 'del'){
                        if (that.formData.check_source_sure) {
                            for(var i=0;i<that.formData.check_source_sure.length;i++){
                                if(that.formData.check_source_sure[i].id==data.id){
                                    that.formData.check_source_sure.splice(i,1);
                                }
                            }
                            that.formData.check_source_sure=that.formData.check_source_sure;
                            that.show_source_list();
                        }
                    }
                });
                //监听单元格编辑
                table.on('edit(showSourceList)', function(obj){
                    var id=obj.data.id,values=obj.value;
                    switch (obj.field) {
                        case 'sort':
                            if (that.formData.check_special_sure) {
                                $.each(that.formData.check_special_sure, function(index, value){
                                    if(value.id == id){
                                        that.formData.check_special_sure[index].sort = values;
                                    }
                                })
                            }
                            break;
                    }
                });
                //监听素材是否删除
                form.on('switch(delect)', function (obj) {
                    if (that.formData.check_special_sure) {
                        for (var i = 0; i < that.formData.check_special_sure.length; i++) {
                            if (that.formData.check_special_sure[i].id == obj.value) {
                                that.formData.check_special_sure.splice(i, 1);
                            }
                        }
                        that.formData.check_source_sure = that.formData.check_special_sure;
                        that.show_source_list();
                    }
                });
            },
            show_special_list: function () {
                    var that = this;
                    var table = layui.table;
                    var form = layui.form;
                    console.log(layui.table);
                    table.render({
                        elem: '#showSpecialList',
                        cols: [[
                            {field: 'id', title: '编号', align: 'center',width:150},
                            {field: 'title', title: '合集名', edit: 'title', align: 'center',width:350},
                            {
                                field: 'image',
                                title: '封面',
                                templet: '<div><img src="{{ d.image }}" style="width: 100%;"></div>',
                                align: 'center',
                                width:350,
                            },
                            {field: 'sort', title: '排序',edit:'sort',align: 'center',width:200},
                            {
                                field: 'right', title: '操作', align: 'center', templet: function (d) {
                                    return '<div><a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del"><i class="layui-icon">&#xe640;</i> 移除</a></div>';
                                }
                                ,width:200,
                            },
                        ]],
                        data: (Object.keys(that.formData.check_special_sure).length > 0) ? that.formData.check_special_sure : [],
                        page: {
                          theme: '#0092DC'
                      },
                        id: 'table'
                    });
                    table.on('tool(showSpecialList)', function(obj){
                        var data = obj.data;
                        if(obj.event === 'del'){
                            if (that.formData.check_special_sure) {
                                for(var i=0;i<that.formData.check_special_sure.length;i++){
                                    if(that.formData.check_special_sure[i].id==data.id){
                                        that.formData.check_special_sure.splice(i,1);
                                    }
                                }
                                that.formData.check_special_sure=that.formData.check_special_sure;
                                that.show_special_list();
                            }
                        }
                    });
                    //监听单元格编辑
                    table.on('edit(showSpecialList)', function(obj){
                        var id=obj.data.id,values=obj.value;
                        switch (obj.field) {
                            case 'sort':
                                if (that.formData.check_special_sure) {
                                    $.each(that.formData.check_special_sure, function(index, value){
                                        if(value.id == id){
                                            that.formData.check_special_sure[index].sort = values;
                                        }
                                    })
                                }
                                break;
                        }
                    });
                    //监听素材是否免费操作
                    form.on('switch(pay_status)', function (obj) {
                        if (that.formData.check_special_sure) {
                            $.each(that.formData.check_special_sure, function (index, value) {
                                if (value.id == obj.value) {
                                    that.formData.check_special_sure[index].pay_status = obj.elem.checked == true ? 0 : 1;
                                }
                            })
                        }
                    });
                    //监听素材是否删除
                    form.on('switch(delect)', function (obj) {
                        if (that.formData.check_special_sure) {
                            for (var i = 0; i < that.formData.check_special_sure.length; i++) {
                                if (that.formData.check_special_sure[i].id == obj.value) {
                                    that.formData.check_special_sure.splice(i, 1);
                                }
                            }
                            that.formData.check_special_sure = that.formData.check_special_sure;
                            that.show_special_list();
                        }
                    });
            },
            /**
             * 监听radio字段
             */
            eeventRadio: function () {
                var that = this;
                that.radioRule.map(function (val) {
                    that.form.on('radio(' + val + ')', function (res) {
                        that.formData[val] = res.value;
                    });
                })
            },
            init: function () {
                var that = this;
                window.UMEDITOR_CONFIG.toolbar = [
                    // 加入一个 test
                    'source | undo redo | bold italic underline strikethrough | superscript subscript | forecolor backcolor | removeformat |',
                    'insertorderedlist insertunorderedlist | selectall cleardoc paragraph | fontfamily fontsize',
                    '| justifyleft justifycenter justifyright justifyjustify |',
                    'link unlink | emotion selectimgs video | map',
                    '| horizontal print preview fullscreen', 'drafts', 'formula'
                ];
                UM.registerUI('selectimgs', function (name) {
                    var me = this;
                    var $btn = $.eduibutton({
                        icon: 'image',
                        click: function () {
                            that.createFrame('选择图片', "{:Url('widget.images/index',['fodder'=>'editor'])}");
                        },
                        title: '选择图片'
                    });

                    this.addListener('selectionchange', function () {
                        //切换为不可编辑时，把自己变灰
                        var state = this.queryCommandState(name);
                        $btn.edui().disabled(state == -1).active(state == 1)
                    });
                    return $btn;

                });
                UM.registerUI('video', function (name) {
                    var me = this;
                    var $btn = $.eduibutton({
                        icon: 'video',
                        click: function () {
                            that.createFrame('选择音视频', "{:Url('widget.video/indexs',['fodder'=>'video'])}");
                        },
                        title: '选择音视频'
                    });

                    this.addListener('selectionchange', function () {
                        //切换为不可编辑时，把自己变灰
                        var state = this.queryCommandState(name);
                        $btn.edui().disabled(state == -1).active(state == 1)
                    });
                    return $btn;

                });
                that.$nextTick(function () {
                    layui.use(['form','element','table','laydate'], function () {
                        that.form = layui.form;
                        that.laydate = layui.laydate;
                        that.form.render();
                        that.form.on('select(labelIndex)', function (data) {
                                that.setLabelTable(parseInt(data.value),false)
                        });
                        that.form.on('select(rule_index)', function (data) {
                            that.ruleIndex = data.value;
                        });
                        layui.element.on('tab(docTabBrief)', function(){
                            that.layTabId = this.getAttribute('lay-id');
                        });
                        that.eeventRadio();
                        that.laydate.render({
                            elem: '#add_time', //指定元素
                            type: 'datetime',
                            done: function(value) {
                                that.formData.add_time = value;
                            }
                        });
                        that.show_source_list();
                        that.show_special_list();
                    });
                    layui.config({
                        base : '/static/plug/layui/'
                    }).extend({
                        selectN: './selectN',
                    }).use('selectM',function () {
                        var selectM = layui.selectM;
                        var selectN = layui.selectM;
                        selectM({
                            //元素容器【必填】
                            elem: '#cate_id'
                            //候选数据【必填】
                            ,data: that.cateList
                            //默认值
                            ,selected: that.formData.cate_id || 0
                            //最多选中个数，默认5
                            ,max : 3
                            ,name: 'cate_id'
                            ,model: 'formData.cate_id'
                            //值的分隔符
                            ,delimiter: ','
                            //候选项数据的键名
                            ,field: {idName:'id',titleName:'name',statusName:'disabled'}
                        });
                        selectN({
                            //元素容器【必填】
                            elem: '#band_space_id'
                            //候选数据【必填】
                            ,data: that.bandList
                            //默认值
                            ,selected: that.formData.band_space_id || 0
                            //最多选中个数，默认5
                            ,max : 3
                            ,name: 'band_space_id'
                            ,model: 'formData.band_space_id'
                            //值的分隔符
                            ,delimiter: ','
                            //候选项数据的键名
                            ,field: {idName:'id',titleName:'name',statusName:'disabled'}
                        });
                    });
                })
            },
            requestPost: function (url, data) {
                return new Promise(function (resolve, reject) {
                    axios.post(url, data).then(function (res) {
                        if (res.status == 200 && res.data.code == 200) {
                            resolve(res.data)
                        } else {
                            reject(res.data);
                        }
                    }).catch(function (err) {
                        reject({msg:err})
                    });
                })
            },
            requestGet: function (url) {
                return new Promise(function (resolve, reject) {
                    axios.get(url).then(function (res) {
                        if (res.status == 200 && res.data.code == 200) {
                            resolve(res.data)
                        } else {
                            reject(res.data);
                        }
                    }).catch(function (err) {
                        reject({msg:err})
                    });
                })
            },
            handleSubmit:function () {
                var that = this;
                that.formData.cate_id = $('input[name="cate_id"]').val();
                if (!that.formData.cate_id) {
                    return that.showMsg('请选择资料分类');
                }
                that.formData.band_space_id = $('input[name="band_space_id"]').val();
                if (!that.formData.band_space_id) {
                    return that.showMsg('请选择关联的乐队');
                }
                if (that.formData.type == 1) {
                    if (Object.keys(that.formData.check_special_sure).length === 0) {
                        return that.showMsg('请选择关联的课程专题');
                    }
                }
                if (that.formData.type == 2) {
                    if (Object.keys(that.formData.check_source_sure).length === 0) {
                        return that.showMsg('请选择关联的素材资料');
                    }
                }
                // 用户自主上传
                if (that.formData.type == 3) {
                    if (that.formData.file_type == 1) {
                        if (!that.formData.image) {
                            return that.showMsg('请选择图片文件');
                        }
                    }
                    if (that.formData.file_type == 2) {
                        if (!that.formData.video_link) {
                            return that.showMsg('请上传视频素材');
                        }
                    }

                    if (that.formData.file_type == 2) {
                        if (!that.formData.audio_link) {
                            return that.showMsg('请上传音频素材');
                        }
                    }
                }
                $('#submit').attr('disabled', 'disabled').text('保存中...');
                that.requestPost(that.U({m:window.module,c:'space.band_material',a:'save_material',p:{id:that.id,special_type:that.formData.source_type}}),that.formData).then(function (res) {
                    that.confirm();
                }).catch(function (res) {
                    that.showMsg(res.msg);
                });
            },
            clone_form:function () {
                if(parseInt(id) == 0){
                    var that = this;
                    if(that.formData.image) return layList.msg('请先删除上传的图片在尝试取消');
                    parent.layer.closeAll();
                }
                parent.layer.closeAll();
            },
            confirm: function(){
                var that = this;
                layui.use(['layer'], function () {
                    var layer = layui.layer;
                    layer.confirm(that.id ? '修改成功是否返回素材列表' : '添加成功是否返回素材列表', {
                        btn: ['返回列表',that.id ? '继续修改' : '继续添加'] //按钮
                    }, function(){
                        location.href = that.U({m:window.module,c:'special.special_type',a:'sources_index'});
                    }, function(){
                        location.reload();
                    });
                });
            },
            render:function(){
                this.$nextTick(function(){
                    layui.use(['form'], function () {
                        layui.form.render('select');
                    });
                })
            },
            // 移动
            handleDragStart (e, item) {
                this.dragging = item;
            },
            handleDragEnd (e, item) {
                this.dragging = null
            },
            handleDragOver (e) {
                e.dataTransfer.dropEffect = 'move'
            },
            handleDragEnter (e, item) {
                e.dataTransfer.effectAllowed = 'move'
                if (item === this.dragging) {
                    return
                }
                var newItems = [...this.formData.activity];
                var src = newItems.indexOf(this.dragging);
                var dst = newItems.indexOf(item);
                newItems.splice(dst, 0, ...newItems.splice(src, 1))
                this.formData.activity = newItems;
            },
        },
        mounted: function () {
            var that = this;
            axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';
            that.getActivityInfo();
            that.get_cate_list();
            that.get_band_list();
            that.getCurrentTime();
            window.$vm = that;
            window.changeIMG = that.changeIMG;
            window.insertEditor = that.insertEditor;
            window.insertEditorVideo = that.insertEditorVideo;
            window.insertEditorAudio = that.insertEditorAudio;
            $(that.$refs.videoFilElem).change(function (type) {
                var inputFile = this.files[0];
                that.requestPost(that.U({m:window.module,c:"widget.video",a:'get_signature'}),{is_private:1}).then(function (res) {
                    AdminUpload.upload(res.data.uploadType,{
                        token: res.data.uploadToken || '',
                        file: inputFile,
                        accessKeyId: res.data.accessKey || '',
                        accessKeySecret: res.data.secretKey || '',
                        bucketName: res.data.storageName || '',
                        region: res.data.storageRegion || '',
                        domain: res.data.domain || '',
                        uploadIng:function (progress) {
                            that.upload.videoIng = true;
                            that.progress = progress;
                        }
                    }).then(function (res) {
                        //成功
                        that.$set(that.formData, 'file_name', inputFile.name);
                        that.$set(that.formData, 'video_link', res.origin_url);
                        that.$set(that.formVideoData, 'link', res.origin_url);
                        that.$set(that.formVideoData, 'time', 0);
                        that.$set(that.formVideoData, 'width', 0);
                        that.$set(that.formVideoData, 'height', 0);
                        that.$set(that.formData, 'video_signature_link', res.signatureUrl);
                        that.progress = 0;
                        that.upload.videoIng = false;
                        return that.showMsg('上传成功');
                    }).catch(function (err) {
                        //失败
                        return that.showMsg('上传错误请检查您的配置');
                    });
                }).catch(function (res) {
                    return that.showMsg(res.msg || '获取密钥失败,请检查您的配置');
                });
            });
            $(that.$refs.audioFilElem).change(function (type) {
                var inputFile = this.files[0];
                that.requestPost(that.U({m:window.module,c:"widget.audio",a:'get_signature'})).then(function (res) {
                    AdminUpload.upload(res.data.uploadType,{
                        token: res.data.uploadToken || '',
                        file: inputFile,
                        accessKeyId: res.data.accessKey || '',
                        accessKeySecret: res.data.secretKey || '',
                        bucketName: res.data.storageName || '',
                        region: res.data.storageRegion || '',
                        domain: res.data.domain || '',
                        uploadIng:function (progress) {
                            that.upload.audioIng = true;
                            that.audio_progress = progress;
                        }
                    }).then(function (res) {
                        //成功
                        that.$set(that.formData, 'file_name', inputFile.name);
                        that.$set(that.formData, 'audio_link', res.origin_url);
                        that.$set(that.formData, 'audio_signature_link', res.signatureUrl);
                        that.audio_progress = 0;
                        that.upload.audioIng = false;
                        return that.showMsg('上传成功');
                    }).catch(function (err) {
                        //失败
                        return that.showMsg('上传错误请检查您的配置');
                    });
                }).catch(function (res) {
                    return that.showMsg(res.msg || '获取密钥失败,请检查您的配置');
                });
            });
            $(that.$refs.documentFilElem).change(function (type) {
                var inputFile = this.files[0];
                that.requestPost(that.U({m:window.module,c:"widget.audio",a:'get_signature'})).then(function (res) {
                    AdminUpload.upload(res.data.uploadType,{
                        token: res.data.uploadToken || '',
                        file: inputFile,
                        accessKeyId: res.data.accessKey || '',
                        accessKeySecret: res.data.secretKey || '',
                        bucketName: res.data.storageName || '',
                        region: res.data.storageRegion || '',
                        domain: res.data.domain || '',
                        uploadIng:function (progress) {
                            that.upload.fileIng = true;
                            that.file_progress = progress;
                        }
                    }).then(function (res) {
                        //成功
                        that.$set(that.formData, 'file_name', inputFile.name);
                        that.$set(that.formData, 'file_link', res.origin_url);
                        that.file_progress = 0;
                        that.upload.fileIng = false;
                        return that.showMsg('上传成功');
                    }).catch(function (err) {
                        //失败
                        return that.showMsg('上传错误请检查您的配置');
                    });
                }).catch(function (res) {
                    return that.showMsg(res.msg || '获取密钥失败,请检查您的配置');
                });
            });
            $(that.$refs.zipFilElem).change(function (type) {
                var inputFile = this.files[0];
                that.requestPost(that.U({m:window.module,c:"widget.audio",a:'get_signature'})).then(function (res) {
                    AdminUpload.upload(res.data.uploadType,{
                        token: res.data.uploadToken || '',
                        file: inputFile,
                        accessKeyId: res.data.accessKey || '',
                        accessKeySecret: res.data.secretKey || '',
                        bucketName: res.data.storageName || '',
                        region: res.data.storageRegion || '',
                        domain: res.data.domain || '',
                        uploadIng:function (progress) {
                            that.upload.zipIng = true;
                            that.zip_file_progress = progress;
                        }
                    }).then(function (res) {
                        //成功
                        that.$set(that.formData, 'file_name', inputFile.name);
                        that.$set(that.formData, 'zip_link', res.origin_url);
                        that.zip_file_progress = 0;
                        that.upload.zipIng = false;
                        return that.showMsg('上传成功');
                    }).catch(function (err) {
                        //失败
                        return that.showMsg('上传错误请检查您的配置');
                    });
                }).catch(function (res) {
                    return that.showMsg(res.msg || '获取密钥失败,请检查您的配置');
                });
            })
        }
    });
</script>
</body>
</html>
