{extend name="public/container"}
{block name="head_top"}

{/block}
{block name="content"}
<style>
    .btn-outline{
        border:none;
    }
    .btn-outline:hover{
        background-color: #0e9aef;
        color: #fff;
    }
    .layui-form-item .layui-btn {
        margin-top: 5px;
        margin-right: 10px;
    }
    .layui-btn-primary{
        margin-right: 10px;
        margin-left: 0!important;
    }
    label{
        margin-bottom: 0!important;
        margin-top: 4px;
    }
</style>
<div class="layui-fluid">
    <div class="layui-row layui-col-space15" id="app">
        <!--搜索条件-->
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">搜索条件</div>
                <div class="layui-card-body">
                    <div class="layui-carousel layadmin-carousel layadmin-shortcut" lay-anim="" lay-indicator="inside"
                         lay-arrow="none" style="background:none">
                        <div class="layui-card-body">
                            <div class="layui-row layui-col-space10 layui-form-item">
                                <div class="layui-col-lg12">
                                    <label class="layui-form-label">关键字:</label>
                                    <div class="layui-input-block">
                                        <input type="text" name="keywords" style="width: 50%" v-model="where.keywords"
                                               placeholder="请输入关键字信息" class="layui-input">
                                    </div>
                                </div>
                                <div class="layui-col-lg12">
                                    <div class="layui-input-block">
                                        <button @click="search" type="button"
                                                class="layui-btn layui-btn-sm layui-btn-normal">
                                            <i class="layui-icon layui-icon-search"></i>搜索
                                        </button>
                                        <button @click="refresh" type="reset"
                                                class="layui-btn layui-btn-primary layui-btn-sm">
                                            <i class="layui-icon layui-icon-refresh"></i>刷新
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--列表-->
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">优惠码列表</div>
                <div class="layui-card-body">
                    <div class="layui-btn-container" id="container-action">
                        <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" onclick="$eb.createModalFrame(this.innerText,'{:Url('create')}?type=0')">添加优惠券</button>
                        <button class="layui-btn layui-btn-primary layui-btn-sm export" data-type="export">
                            <i class="fa fa-floppy-o" style="margin-right: 3px;"></i>导出</button>
                    </div>
                    <table class="layui-hide" id="List" lay-filter="List"></table>
                    <!--类型-->
                    <script type="text/html" id="type_name">
                        {{d.type_name}}
                    </script>
                    <script type="text/html" id="act">
                        <button type="button" class="layui-btn layui-btn-xs" onclick="dropdown(this)">操作 <span class="caret"></span></button>
                        <ul class="layui-nav-child layui-anim layui-anim-upbit">
                            {{# if( d.status == 1 |  d.is_del == 0 ){ }}
                                <li>
                                    <a href="javascript:void(0);" lay-event='deleted'>
                                        <i class="fa fa-edit"></i> 屏蔽
                                    </a>
                                </li>
                            {{# } }}
                    </script>
                </div>
            </div>
        </div>
    </div>
    <!--end-->
</div>
<script src="{__ADMIN_PATH}js/layuiList.js"></script>
{/block}
{block name="script"}
<script>
    layList.tableList('List', "{:Url('code_list')}", function () {
        return [
            {type: 'checkbox'},
            {field: 'id', title: 'ID',width: '10%',align: 'center'},
            {field: 'title', title: '名称', width: '15%', align: 'center'},
            {field: 'price', title: '面值', width: '15%',align: 'center'},
            {field: 'number', title: '发布数量', width: '10%',align: 'center'},
            {field: 'valid_time', title: '有效期限', width: '15%',align: 'center'},
            {field: 'status_name', title: '状态', width: '10%', align: 'center'},
            {field: 'add_time', title: '提交时间', width: '12.2%', sort: true, align: 'center'},
            {field: 'right', title: '操作', align: 'center', toolbar: '#act', width: '10%'},
        ];
    });
    layList.tool(function (event, data, obj) {
        switch (event) {
            case 'deleted':
                var url = layList.U({c: 'market.store_promo_code', a: 'destroy', p: {id: data.id}});
                $eb.$swal('delete', function () {
                    $eb.axios.get(url).then(function (res) {
                        if (res.status == 200 && res.data.code == 200) {
                            $eb.$swal('success', res.data.msg);
                        } else
                            return Promise.reject(res.data.msg || '屏蔽失败')
                    }).catch(function (err) {
                        $eb.$swal('error', err);
                    });
                }, {'title': '您确定要屏蔽评论吗？', 'text': '屏蔽后将无法恢复,请谨慎操作！', 'confirm': '是的，我要屏蔽'})
                break;
            case 'restore':
                var url = layList.U({c: 'market.store_promo_code', a: 'restore', p: {id: data.id,status: data.status}});
                $eb.$swal('delete', function () {
                    $eb.axios.get(url).then(function (res) {
                        if (res.status == 200 && res.data.code == 200) {
                            $eb.$swal('success', res.data.msg);
                        } else
                            return Promise.reject(res.data.msg || '恢复失败')
                    }).catch(function (err) {
                        $eb.$swal('error', err);
                    });
                }, {'title': '您确定恢复该评论吗？', 'text': '恢复后，评论状态为发布状态！', 'confirm': '是的，我要恢复'})
                break;
            case 'review':
                var url = layList.U({c: 'market.store_promo_code', a: 'restore', p: {id: data.id,status: data.status}});
                $eb.$swal('delete', function () {
                    $eb.axios.get(url).then(function (res) {
                        if (res.status == 200 && res.data.code == 200) {
                            $eb.$swal('success', res.data.msg);
                        } else
                            return Promise.reject(res.data.msg || '恢复失败')
                    }).catch(function (err) {
                        $eb.$swal('error', err);
                    });
                }, {'title': '您确定审核该评论吗？', 'text': '审核后，评论状态为发布状态！', 'confirm': '是的，我要审核'})
                break;
        }
    })
    $('#container-action').find('button').each(function () {
        $(this).on('click', function () {
            var act = $(this).data('type');
            action[act] && action[act]();
        });
    })
    //下拉框
    $(document).click(function (e) {    
        $('.layui-nav-child').hide();
    })
    //自定义方法
    var action={
        show:function(){
            var ids=layList.getCheckData().getIds('id');
            if(ids.length){
                layList.basePost(layList.Url({c:'market.store_promo_code',a:'release'}),{ids:ids},function (res) {
                    layList.msg(res.msg);
                    layList.reload();
                });
            }else{
                layList.msg('请选择要审核的评论');
            }
        },
        export:function () {
            var ids=layList.getCheckData().getIds('id');
            if(ids.length){
                var str = ids.join(',');
                location.href = layList.U({a: 'export', q: {ids:ids}});
                layList.msg('导出成功');
            }else{
                layList.msg('请选择要导出组的优惠码');
            }
        },
        refresh:function () {
            layList.reload();
        }
    };

    function dropdown(that) {
        var oEvent = arguments.callee.caller.arguments[0] || event;
        oEvent.stopPropagation();
        var offset = $(that).offset();
        var top = offset.top - $(window).scrollTop();
        var index = $(that).parents('tr').data('index');
        $('.layui-nav-child').each(function (key) {
            if (key != index) {
                $(this).hide();
            }
        })
        if ($(document).height() < top + $(that).next('ul').height()) {
            $(that).next('ul').css({
                'padding': 10,
                'top': -($(that).parents('td').height() / 2 + $(that).height() + $(that).next('ul').height() / 2),
                'min-width': 'inherit',
                'position': 'absolute'
            }).toggle();
        } else {
            $(that).next('ul').css({
                'padding': 10,
                'top': $(that).parents('td').height() / 2 + $(that).height(),
                'min-width': 'inherit',
                'position': 'absolute'
            }).toggle();
        }
    }
    var  status =<?=$status ? $status : "''"?>;
    require(['vue'], function (Vue) {
        new Vue({
            el: "#app",
            data: {
                where: {
                    keywords: '',
                },
                showtime: false,
            },
            watch: {
                'where.keywords': function () {
                    layList.reload(this.where, true);
                }
            },
            methods: {
                setData: function (item) {
                    var that = this;
                    if (item.is_zd == true) {
                        that.showtime = true;
                        this.where.data = this.$refs.date_time.innerText;
                    } else {
                        this.showtime = false;
                        this.where.data = item.value;
                    }
                },
                search: function () {       
                    layList.reload(this.where, true);
                },
                refresh: function () {
                    layList.reload();
                },
                init: function () {
                var that = this;
                that.$nextTick(function () {
                    layui.use(['form','element'], function () {
                        that.form = layui.form;
                        that.form.render();
                        layui.element.on('tab(docTabBrief)', function(){
                            that.layTabId = this.getAttribute('lay-id');
                        });
                    });
                })
            },
            },
            mounted: function () {
                this.init();
                var that = this;
                window.formReload = this.search;
                layList.laydate.render({
                    elem: this.$refs.date_time,
                    trigger: 'click',
                    eventElem: this.$refs.time,
                    range: true,
                    change: function (value) {
                        that.where.data = value;
                    }
                });
            }
        })
    });
</script>
{/block}