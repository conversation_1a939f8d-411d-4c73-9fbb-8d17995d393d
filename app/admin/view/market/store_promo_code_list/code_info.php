{extend name="public/container"}
{block name="content"}
<style type="text/css">
    video{
        width: 24%!important;
        margin-right: 4px;
        margin-top: 10px;
        margin-left: 10px;
        margin-bottom: -1px;
    }
    audio {
        width: 198px;
        height: 54px;
    }
</style>
<div class="ibox-content order-info">
    <div class="row">
        <div class="col-sm-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    优惠码-使用详情
                </div>
                <div class="panel-body">
                    <div class="row show-grid">
                        <div class="col-xs-12">优惠码: {$codeInfo.code}</div>
                        <div class="col-xs-12">优惠金额: {$codeInfo.price}</div>
                        <div class="col-xs-12">状态: 
                        	{if condition="$codeInfo['is_fail'] eq 1"}
                             	<font color="red">已失效</font>  
                            {elseif condition="$codeInfo['status'] eq 1 && $codeInfo['is_del'] == 0"/}
                            	已使用 
                            {elseif condition="$codeInfo['status'] eq 0 && $codeInfo['is_del'] == 0"/}
                            	待使用
                            {elseif condition="$codeInfo['is_del'] eq 1"/}
                            	删除
                            {/if}
                        </div>
                        <div class="col-xs-12">创建时间: {$codeInfo.add_time|date="Y-m-d H:i:s"}</div>
                    </div>
                </div>
            </div>
            {if condition="$userInfo"}
	           	<div class="panel panel-default">
	                <div class="panel-heading">
	                    使用者-详情
	                </div>
	                <div class="panel-body">
	                    <div class="row show-grid">
	                        <div class="col-xs-12">用户ID: {$userInfo.uid}</div>
	                        <div class="col-xs-12">用户昵称: {$userInfo.nickname}</div>
	                        <div class="col-xs-12">订单ID: {$orderInfo.order_id}</div>
	                        <div class="col-xs-12">使用时间: {$codeInfo.use_time|date="Y-m-d H:i:s"}</div>
	                    </div>
	                </div>
	            </div>
             {/if}
        </div>
    </div>
</div>
<script src="{__FRAME_PATH}js/content.min.js?v=1.0.0"></script>
{/block}
