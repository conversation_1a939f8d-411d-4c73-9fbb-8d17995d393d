{extend name="public/container"}
{block name="content"}
<div class="row">
    <div class="col-sm-12">
        <div class="ibox">
            <div class="ibox-content">
                <div class="row">
                    <div class="m-b m-l">

                        <form action="" class="form-inline">    
                            <div class="input-group">
                                <input type="text" name="name" value="{$where.name}" placeholder="请输入优惠码名称" class="input-sm form-control"> <span class="input-group-btn">
                                    <button type="submit" class="btn btn-sm btn-primary"> <i class="fa fa-search" ></i>搜索</button> </span>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="table table-striped  table-bordered">
                        <thead>
                        <tr>
                            <th class="text-center">编号</th>
                            <th class="text-center">优惠组</th>
                            <th class="text-center">优惠码</th>
                            <th class="text-center">金额</th>
                            <th class="text-center">时间</th>
                            <th class="text-center">状态</th>
                            <th class="text-center">操作</th>
                        </tr>
                        </thead>
                        <tbody class="">
                        {volist name="list" id="vo"}
                        <tr>
                            <td class="text-center">
                                {$vo.id}
                            </td>
                            <td class="text-center">
                                {$vo.code_group}
                            </td>
                            <td class="text-center">
                                {$vo.code}
                            </td>
                            <td class="text-center">
                                {$vo.price}
                            </td>
                            <td class="text-center">
                                <?php if($vo['fail_time']){ ?>
                                    失效时间：{$vo.fail_time|date='Y-m-d H:i:s'}<br>
                                <?php } ?>
                                <?php if($vo['use_time']){ ?>
                                     使用时间：{$vo.use_time|date='Y-m-d H:i:s'}<br>
                                <?php } ?>
                            </td>
                            <td class="text-center">
                                <?php if($vo['status'] ==0 && $vo['is_fail'] == 0 ){ ?>
                                    <span class="label label-warning">待使用</span>
                                <?php }elseif(1 == $vo['is_fail']){ ?>
                                    <span class="label label-danger">已失效</span>
                                <?php }elseif(1 == $vo['status']){ ?>
                                    <span class="label label-primary">已使用</span>
                                <?php } ?>
                            </td>
                            <td class="text-center">
                                <div class="input-group-btn js-group-btn">
                                    <div class="btn-group">
                                        <?php if(-1 ==$vo['status'] || 0 == $vo['status']){ ?>
                                        <button class="btn btn-danger btn-xs" data-url="{:Url('delete',array('id'=>$vo['id']))}" type="button"><i class="fa fa-times"></i> 删除
                                        </button>
                                        <?php }elseif(1 == $vo['status']){ ?>
                                             <button data-toggle="dropdown" class="btn btn-xs btn-primary"
                                                aria-expanded="false">操作
                                            <span class="caret"></span>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <?php if($vo['is_fail'] == 0 ){ ?>
                                                <li>
                                                    <a href="javascript:void(0);"
                                                       onclick="$eb.createModalFrame('code码使用详情','{:Url('code_info')}?id={{d.id}}')">
                                                        <i class="fa fa-newspaper-o"></i> 查看code使用者
                                                    </a>
                                                </li>
                                                <?php } ?>
                                            </ul>
                                        <?php } ?>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                </div>
                {include file="public/inner_page"}
            </div>
        </div>
    </div>
</div>
<script src="{__ADMIN_PATH}js/layuiList.js"></script>
<script>
    $('.btn-danger').on('click',function(){
        var _this = $(this),url =_this.data('url');
        $eb.$swal('delete',function(){
            $eb.axios.get(url).then(function(res){
                if(res.status == 200 && res.data.code == 200) {
                    $eb.$swal('success',res.data.msg);
                        _this.parents('tr').remove();
                }else
                    return Promise.reject(res.data.msg || '删除失败')
            }).catch(function(err){
                $eb.$swal('error',err);
            });
        },{'title':'您确定要删除发布的优惠码吗？','text':'删除后将无法恢复,请谨慎操作！','confirm':'是的，我要删除'})
    });
</script>
{/block}
