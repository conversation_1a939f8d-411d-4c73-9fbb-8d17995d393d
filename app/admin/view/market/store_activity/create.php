<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link href="{__FRAME_PATH}css/font-awesome.min.css" rel="stylesheet">
    <link href="{__ADMIN_PATH}plug/umeditor/themes/default/css/umeditor.css" type="text/css" rel="stylesheet">
    <script type="text/javascript" src="{__ADMIN_PATH}plug/umeditor/third-party/jquery.min.js"></script>
    <script type="text/javascript" src="{__ADMIN_PATH}plug/umeditor/third-party/template.min.js"></script>
    <script type="text/javascript" charset="utf-8" src="{__ADMIN_PATH}plug/umeditor/umeditor.config.js"></script>
    <script type="text/javascript" charset="utf-8" src="{__ADMIN_PATH}plug/umeditor/umeditor.min.js"></script>
    <script type="text/javascript" src="{__ADMIN_PATH}plug/umeditor/lang/zh-cn/zh-cn.js"></script>
    <link rel="stylesheet" href="/static/plug/layui/css/layui.css">
    <script src="/static/plug/layui/layui.js"></script>
    <script src="{__PLUG_PATH}vue/dist/vue.min.js"></script>
    <script src="/static/plug/axios.min.js"></script>
    <script src="/static/plug/iview/dist/iview.min.js"></script>
    <script src="{__MODULE_PATH}widget/aliyun-oss-sdk-4.4.4.min.js"></script>
    <script src="{__MODULE_PATH}widget/cos-js-sdk-v5.min.js"></script>
    <script src="{__MODULE_PATH}widget/qiniu-js-sdk-2.5.5.js"></script>
    <script src="{__MODULE_PATH}widget/plupload.full.min.js"></script>
    <script src="{__MODULE_PATH}widget/videoUpload.js"></script>
    <script type="text/javascript">
        $eb = parent._mpApi;
        window.controlle="<?php echo strtolower(trim(preg_replace("/[A-Z]/", "_\\0", app('request')->controller()), "_"));?>";
        window.module="6GqvmHa8HRGHoQEQ";
    </script>
    <style>
        .layui-form-item {
            margin-bottom: 0px;
        }

        .layui-form-item .special-label {
            width: 50px;
            float: left;
            height: 30px;
            line-height: 38px;
            margin-left: 10px;
            margin-top: 5px;
            border-radius: 5px;
            background-color: #0092DC;
            text-align: center;
        }

        .layui-form-item .special-label i {
            display: inline-block;
            width: 18px;
            height: 18px;
            font-size: 18px;
            color: #fff;
        }

        .layui-form-item .label-box {
            border: 1px solid;
            border-radius: 10px;
            position: relative;
            padding: 10px;
            height: 30px;
            color: #fff;
            background-color: #393D49;
            text-align: center;
            cursor: pointer;
            display: inline-block;
            line-height: 10px;
        }

        .layui-form-item .label-box p {
            line-height: inherit;
        }


        .pictrueBox {
            display: inline-block !important;
        }

        .pictrue {
            width: 60px;
        height: 60px;
            border: 1px dotted rgba(0, 0, 0, 0.1);
            margin-right: 15px;
            display: inline-block;
            position: relative;
            cursor: pointer;
        }

        .pictrue img {
            width: 100%;
            height: 100%;
        }

        .upLoad {
            width: 58px;
            height: 58px;
            line-height: 58px;
            border: 1px dotted rgba(0, 0, 0, 0.1);
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.02);
            cursor: pointer;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .rulesBox {
            display: flex;
            flex-wrap: wrap;
            margin-left: 10px;
        }

        .layui-tab-content {
            margin-top: 15px;
        }

        .ml110 {
            margin: 18px 0 4px 110px;
        }

        .rules {
            display: flex;
        }

        .rules-btn-sm {
            height: 30px;
            line-height: 30px;
            font-size: 12px;
            width: 109px;
        }

        .rules-btn-sm input {
            width: 79% !important;
            height: 84% !important;
            padding: 0 10px;
        }

        .ml10 {
            margin-left: 10px !important;
        }

        .ml40 {
            margin-left: 40px !important;
        }

        .closes {
            position: absolute;
            left: 86%;
            top: -18%;
        }
        .red {
            color: red;
        }
        .layui-input-block .layui-video-box{
            width: 22%;
            height: 180px;
            border-radius: 10px;
            background-color: #707070;
            margin-top: 10px;
            position: relative;
            overflow: hidden;
        }
        .layui-input-block .layui-video-box i{
            color: #fff;
            line-height: 180px;
            margin: 0 auto;
            width: 50px;
            height: 50px;
            display: inherit;
            font-size: 50px;
        }
        .layui-input-block .layui-video-box .mark{
            position: absolute;
            width: 100%;
            height: 30px;
            top: 0;
            background-color: rgba(0,0,0,.5);
            text-align: center;
        }
        .layui-form-select dl {
            z-index: 1015;
        }
        .store_box{
            display: flex;
        }
        .ivu-input{
             border-width: 0px !important;
             width: 100%;
             height: 36px;
         }
         .ivu-select-dropdown{
             z-index: 999;
             background: #fff;
         }
         .ivu-cascader-menu .ivu-cascader-menu-item-active {
             background-color: #f3f3f3;
             color: #2d8cf0;
         }
         .ivu-cascader-menu .ivu-cascader-menu-item {
             position: relative;
             padding-right: 24px;
             -webkit-transition: all .2s ease-in-out;
             transition: all .2s ease-in-out;
         }
         .ivu-cascader .ivu-cascader-menu-item {
             margin: 0;
             line-height: normal;
             padding: 7px 16px;
             clear: both;
             color: #495060;
             font-size: 12px!important;
             white-space: nowrap;
             list-style: none;
             cursor: pointer;
             -webkit-transition: background .2s ease-in-out;
             transition: background .2s ease-in-out;
         }
         .ivu-cascader-menu:last-child {
             border-right-color: transparent;
             margin-right: -1px;
         }
         .ivu-cascader-menu {
             display: inline-block;
             min-width: 100px;
             height: 180px;
             margin: 0;
             padding: 5px 0!important;
             vertical-align: top;
             list-style: none;
             border-right: 1px solid #e9eaec;
             overflow: auto;
                 }
        .info{
            color: #c9c9c9;
            padding-left: 10px;
            line-height: 30px;
        }
    </style>
</head>
<script type="text/javascript">
    window.controlle="<?php echo strtolower(trim(preg_replace("/[A-Z]/", "_\\0", app('request')->controller()), "_"));?>";
    window.module="6GqvmHa8HRGHoQEQ";
</script>
<body>
<div class="layui-fluid">
    <div class="layui-row layui-col-space15"  id="app" v-cloak="">
        <div class="layui-card">
            <div class="layui-card-header">
                <span class="">{{id ? '活动修改': '活动添加' }}</span>
                <button style="margin-left: 20px" type="button" class="layui-btn layui-btn-primary layui-btn-xs" @click="goBack">返回列表</button>
            </div>
            <div class="layui-card-body">
                <form class="layui-form" action="" v-cloak="">
                    <div class="layui-tab layui-tab-brief" lay-filter="docTabBrief">
                        <ul class="layui-tab-title">
                            <li class="layui-this" lay-id='1'>基础信息</li>
                            <li lay-id='2'>活动详情</li>
                        </ul>
                        <div class="layui-tab-content">
                            <div class="layui-tab-item layui-show">
                                <div class="layui-row layui-col-space15">
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">活动分类<i class="red">*</i></label>
                                                <div class="layui-input-block" id="cate_id">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">活动名称<i class="red">*</i></label>
                                                <div class="layui-input-block">
                                                    <input type="text" name="store_name" lay-verify="title" autocomplete="off"
                                                           placeholder="请输入活动名称" class="layui-input" v-model="formData.store_name" maxlength="100">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item layui-form-text">
                                                <label class="layui-form-label">活动简介<i class="red">*</i></label>
                                                <div class="layui-input-block">
                                                    <textarea name="introduction" v-model="formData.introduction"
                                                              placeholder="请输入活动简介" class="layui-textarea"></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item layui-form-text">
                                                <label class="layui-form-label">使用说明<i class="red">*</i></label>
                                                <div class="layui-input-block">
                                                    <textarea name="info" v-model="formData.info"
                                                              placeholder="请输入使用说明" class="layui-textarea"></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item layui-form-text">
                                                <label class="layui-form-label">提示语<i class="red">*</i></label>
                                                <div class="layui-input-block">
                                                    <textarea name="hint" v-model="formData.hint"
                                                              placeholder="请输入提示语" class="layui-textarea"></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item layui-form-text">
                                                <label class="layui-form-label">结算页备注</label>
                                                <div class="layui-input-block">
                                                    <textarea name="activity_order_notes" v-model="formData.activity_order_notes"
                                                              placeholder="请输入结算页备注" class="layui-textarea"></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">经纬度<i class="red">*</i></label>
                                                <div class="layui-input-block flex">
                                                    <input type="text" name="latlng" autocomplete="off" placeholder="请点击查找位置，选择经纬度" class="layui-input" style="width:50%;display:inline-block;margin-right: 10px;"
                                                     v-model="formData.latlng">
                                                     <button type="button" @click="openWindows('查找位置','{:Url('select_address')}',{w:400,h:700})"  class="layui-btn layui-btn-sm layui-btn-normal">查找位置</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">详细地址<i class="red">*</i></label>
                                                <div class="layui-input-block">
                                                    <input type="text" name="detailed_address" autocomplete="off" placeholder="请输入详细地址" class="layui-input" v-model="formData.detailed_address">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">文字协议<i class="red">*</i></label>
                                                <div class="layui-input-block">
                                                    <textarea type="text/plain" name="text_agreement" id="myEditors" style="width:100%;">{{formData.text_agreement}}</textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">联系方式：文字<i class="red">*</i></label>
                                                <div class="layui-input-block">
                                                    <input type="text" name="contact_info" lay-verify="title" autocomplete="off"
                                                           placeholder="请输入联系方式：如微信号、qq号、电话号码、邮箱" class="layui-input" v-model="formData.contact_info" maxlength="100">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">联系方式：二维码<i class="red">*</i></label>
                                                <div class="pictrueBox">
                                                    <div class="pictrue" v-if="formData.contact_image" @click="uploadImage('contact_image')">
                                                        <img :src="formData.contact_image"></div>
                                                    <div class="upLoad" @click="uploadImage('contact_image')" v-else>
                                                        <i class="layui-icon layui-icon-camera" class="iconfont"
                                                           style="font-size: 26px;"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">联系方式：抖音二维码<i class="red">*</i></label>
                                                <div class="pictrueBox">
                                                    <div class="pictrue" v-if="formData.douyin_contact_image" @click="uploadImage('douyin_contact_image')">
                                                        <img :src="formData.douyin_contact_image"></div>
                                                    <div class="upLoad" @click="uploadImage('douyin_contact_image')" v-else>
                                                        <i class="layui-icon layui-icon-camera" class="iconfont"
                                                           style="font-size: 26px;"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
              <!--                       <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">预报名：微信群二维码<i class="red">*</i></label>
                                                <div class="pictrueBox">
                                                    <div class="pictrue" v-if="formData.wait_wechat_group_image" @click="uploadImage('wait_wechat_group_image')">
                                                        <img :src="formData.wait_wechat_group_image"></div>
                                                    <div class="upLoad" @click="uploadImage('wait_wechat_group_image')" v-else>
                                                        <i class="layui-icon layui-icon-camera" class="iconfont"
                                                           style="font-size: 26px;"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div> -->
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">微信群二维码<i class="red">*</i></label>
                                                <div class="pictrueBox">
                                                    <div class="pictrue" v-if="formData.ok_wechat_group_image" @click="uploadImage('ok_wechat_group_image')">
                                                        <img :src="formData.ok_wechat_group_image"></div>
                                                    <div class="upLoad" @click="uploadImage('ok_wechat_group_image')" v-else>
                                                        <i class="layui-icon layui-icon-camera" class="iconfont"
                                                           style="font-size: 26px;"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
               <!--                      <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">预报名：抖音群二维码<i class="red">*</i></label>
                                                <div class="pictrueBox">
                                                    <div class="pictrue" v-if="formData.wait_douyin_group_image" @click="uploadImage('wait_douyin_group_image')">
                                                        <img :src="formData.wait_douyin_group_image"></div>
                                                    <div class="upLoad" @click="uploadImage('wait_douyin_group_image')" v-else>
                                                        <i class="layui-icon layui-icon-camera" class="iconfont"
                                                           style="font-size: 26px;"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div> -->
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">抖音群二维码<i class="red">*</i></label>
                                                <div class="pictrueBox">
                                                    <div class="pictrue" v-if="formData.ok_douyin_group_image" @click="uploadImage('ok_douyin_group_image')">
                                                        <img :src="formData.ok_douyin_group_image"></div>
                                                    <div class="upLoad" @click="uploadImage('ok_douyin_group_image')" v-else>
                                                        <i class="layui-icon layui-icon-camera" class="iconfont"
                                                           style="font-size: 26px;"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">活动开始/结束时间<i class="red">*</i></label>
                                        <div class="layui-input-block">
                                            <input type="text" name="activity_start_end" id="activity_start_end"  v-model="formData.activity_start_end" required
                                             lay-verify="required" placeholder="活动开始时间/结束时间" class="layui-input" readonly="readonly">
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">开始活动时间：(非必填项，建议输入)</label>
                                                <div class="layui-input-block">
                                                    <input type="text" name="start_activity_time_str" lay-verify="title" autocomplete="off"
                                                           placeholder="请输入活动时间字符串类型，例：20001年01月01日00点01分开始" class="layui-input" v-model="formData.start_activity_time_str" maxlength="100">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">截止活动时间：(非必填项，建议输入)</label>
                                                <div class="layui-input-block">
                                                    <input type="text" name="end_activity_time_str" lay-verify="title" autocomplete="off"
                                                           placeholder="请输入截止活动时间字符串类型，例：20001年01月02日00点02分结束" class="layui-input" v-model="formData.end_activity_time_str" maxlength="100">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">报名开始/结束时间<i class="red">*</i></label>
                                        <div class="layui-input-block">
                                            <input type="text" name="start_end" id="start_end" v-model="formData.start_end" required
                                             lay-verify="required" placeholder="报名开始时间/结束时间" class="layui-input" readonly="readonly">
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">报名限制人数</label>
                                                <div class="layui-input-block">
                                                    <input type="text" name="limit_signup_number" autocomplete="off" placeholder="请输入报名限制人数" class="layui-input" v-model="formData.limit_signup_number">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">预报名开始/结束时间<i v-if="formData.status == 0" class="red">*</i></label>
                                        <div class="layui-input-block">
                                            <input type="text" name="crowdfunding_start_end" id="crowdfunding_start_end" v-model="formData.crowdfunding_start_end" required
                                             lay-verify="required" placeholder="活动预报名开始/结束时间" class="layui-input" readonly="readonly">
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">预报名限制人数</label>
                                                <div class="layui-input-block">
                                                    <input type="number" name="limit_crowdfunding_number" autocomplete="off" placeholder="请输入预报名限制人数" class="layui-input" v-model="formData.limit_crowdfunding_number">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-row layui-col-space15">
                                        <div class="layui-col-xs12 layui-col-sm4 layui-col-md4">
                                            <div class="grid-demo grid-demo-bg1">
                                                <div class="layui-form-item">
                                                    <label class="layui-form-label">是否开启活动优惠<i class="red">*</i></label>
                                                    <div class="layui-input-block">
                                                        <input type="radio" name="is_countdown" lay-filter="is_countdown" value="1" title="显示"
                                                               :checked="formData.is_countdown == 1 ? true : false">
                                                        <input type="radio" name="is_countdown" lay-filter="is_countdown" value="0" title="隐藏"
                                                               :checked="formData.is_countdown == 0 ? true : false">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item layui-form-text">
                                                <label class="layui-form-label">活动优惠报名提示语<i class="red">*</i></label>
                                                <div class="layui-input-block">
                                                     <input type="text" name="offer_notes" lay-verify="title" autocomplete="off"
                                                           placeholder="请输入活动优惠报名提示语" class="layui-input" v-model="formData.offer_notes" maxlength="100">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">活动优惠结束时间<i v-if="formData.status == 0" class="red">*</i></label>
                                        <div class="layui-input-block">
                                            <input type="text" name="end_discount_time" id="end_discount_time" v-model="formData.end_discount_time" required
                                             lay-verify="required" placeholder="活动优惠结束时间" class="layui-input" readonly="readonly">
                                        </div>
                                    </div>
                                    <div class="layui-row layui-col-space15">
                                        <div class="layui-col-xs12 layui-col-sm4 layui-col-md4">
                                            <div class="grid-demo grid-demo-bg1">
                                                <div class="layui-form-item">
                                                    <label class="layui-form-label">活动状态<i class="red">*</i></label>
                                                    <div class="layui-input-block">
                                                        <input type="radio" name="status" lay-filter="status" value="0" title="预报名"
                                                               :checked="formData.status == 0 ? true : false">
                                                        <input type="radio" name="status" lay-filter="status" value="1" title="正式报名"
                                                               :checked="formData.status == 1 ? true : false">
                                                        <input type="radio" name="status" lay-filter="status" value="2" title="开始活动"
                                                                   :checked="formData.status == 2 ? true : false">
                                                        <input type="radio" name="status" lay-filter="status" value="3" title="结束活动"
                                                                   :checked="formData.status == 3 ? true : false">
                                                        <input type="radio" name="status" lay-filter="status" value="4" title="取消"
                                                                   :checked="formData.status == 4 ? true : false">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-row layui-col-space15">
                                        <div class="layui-col-xs12 layui-col-sm4 layui-col-md4">
                                            <div class="grid-demo grid-demo-bg1">
                                                <div class="layui-form-item">
                                                    <label class="layui-form-label">是否开启心愿区<i class="red">*</i></label>
                                                    <div class="layui-input-block">
                                                        <input type="radio" name="is_wish" lay-filter="is_wish" value="1" title="开启"
                                                               :checked="formData.is_wish == 1 ? true : false">
                                                        <input type="radio" name="is_wish" lay-filter="is_wish" value="0" title="关闭"
                                                               :checked="formData.is_wish == 0 ? true : false">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">心愿区标题<i class="red">*</i></label>
                                                <div class="layui-input-block">
                                                    <input type="text" name="wish_title" lay-verify="title" autocomplete="off"
                                                           placeholder="请输入心愿区标题" class="layui-input" v-model="formData.wish_title" maxlength="100">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item layui-form-text">
                                                <label class="layui-form-label">心愿区文案<i class="red">*</i></label>
                                                <div class="layui-input-block">
                                                    <textarea name="wish_desc" v-model="formData.wish_desc"
                                                              placeholder="请输入心愿区说明文案" class="layui-textarea"></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-row layui-col-space15">
                                        <div class="layui-col-xs12 layui-col-sm4 layui-col-md4">
                                            <div class="grid-demo grid-demo-bg1">
                                                <div class="layui-form-item">
                                                    <label class="layui-form-label">是否开启备用联系方式<i class="red">*</i></label>
                                                    <div class="layui-input-block">
                                                        <input type="radio" name="is_spare" lay-filter="is_spare" value="1" title="显示"
                                                               :checked="formData.is_spare == 1 ? true : false">
                                                        <input type="radio" name="is_spare" lay-filter="is_spare" value="0" title="隐藏"
                                                               :checked="formData.is_spare == 0 ? true : false">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-row layui-col-space15">
                                        <div class="layui-col-xs12 layui-col-sm4 layui-col-md4">
                                            <div class="grid-demo grid-demo-bg1">
                                                <div class="layui-form-item">
                                                    <label class="layui-form-label">是否开启活动渠道收集<i class="red">*</i></label>
                                                    <div class="layui-input-block">
                                                        <input type="radio" name="is_channels" lay-filter="is_channels" value="1" title="显示"
                                                               :checked="formData.is_channels == 1 ? true : false">
                                                        <input type="radio" name="is_channels" lay-filter="is_channels" value="0" title="隐藏"
                                                               :checked="formData.is_channels == 0 ? true : false">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12" v-if="formData.is_channels == 1">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">渠道<i class="red">*</i></label>
                                                <div class="layui-input-block">
                                                    <input type="text" style="width:50%;display:inline-block;margin-right: 10px;" name="channels" lay-verify="channels" autocomplete="off"
                                                           placeholder="请输入渠道名称" class="layui-input" v-model="channels" placeholder="最多15个字" autocomplete="off" class="layui-input">
                                                             <button type="button" class="layui-btn layui-btn-normal" @click="addhannels" >
                                                    <i class="layui-icon">&#xe654;</i>
                                                </button><sapn style="margin-left: 10px;color: red;">输入渠道名后点击”+“号按钮添加；点击关键字删除</span>
                                                </div>
                                            </div>
                                            <div v-if="formData.channels.length" class="layui-form-item" style="margin-top: 5px;">
                                                <div class="layui-input-block">
                                                    <button v-for="(item,index) in formData.channels" :key="index" type="button" class="layui-btn layui-btn-normal layui-btn-sm" @click="delLabel(index,'channels')">{{item}}</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
<!--                                     <div class="layui-row layui-col-space15">
                                        <div class="layui-col-xs12 layui-col-sm4 layui-col-md4">
                                            <div class="grid-demo grid-demo-bg1">
                                                <div class="layui-form-item">
                                                    <label class="layui-form-label">是否开启管理员管理<i class="red">*</i></label>
                                                    <div class="layui-input-block">
                                                        <input type="radio" name="is_manage" lay-filter="is_manage" value="1" title="是"
                                                               :checked="formData.is_manage == 1 ? true : false">
                                                        <input type="radio" name="is_manage" lay-filter="is_manage" value="0" title="否"
                                                               :checked="formData.is_manage == 0 ? true : false">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div> -->
                                    <div class="layui-row layui-col-space15">
                                        <div class="layui-col-xs12 layui-col-sm4 layui-col-md4">
                                            <div class="grid-demo grid-demo-bg1">
                                                <div class="layui-form-item">
                                                    <label class="layui-form-label">是否开启学员卡填写<i class="red">*</i></label>
                                                    <div class="layui-input-block">
                                                        <input type="radio" name="is_apply" lay-filter="is_apply" value="1" title="显示"
                                                               :checked="formData.is_apply == 1 ? true : false">
                                                        <input type="radio" name="is_apply" lay-filter="is_apply" value="0" title="隐藏"
                                                               :checked="formData.is_apply == 0 ? true : false">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-row layui-col-space15">
                                        <div class="layui-col-xs12 layui-col-sm4 layui-col-md4">
                                            <div class="grid-demo grid-demo-bg1">
                                                <div class="layui-form-item">
                                                    <label class="layui-form-label">是否学员卡填写：必须要有作品<i class="red">*</i></label>
                                                    <div class="layui-input-block">
                                                        <input type="radio" name="is_must_work" lay-filter="is_must_work" value="1" title="显示"
                                                               :checked="formData.is_must_work == 1 ? true : false">
                                                        <input type="radio" name="is_must_work" lay-filter="is_must_work" value="0" title="隐藏"
                                                               :checked="formData.is_must_work == 0 ? true : false">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
<!--                                     <div class="layui-col-xs12 layui-col-sm12 layui-col-md12" v-if="formData.is_apply == 1">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">职业<i class="red">*</i></label>
                                                <div class="layui-input-block">
                                                    <input type="text" style="width:50%;display:inline-block;margin-right: 10px;" name="careers" lay-verify="careers" autocomplete="off"
                                                           placeholder="请输入职业" class="layui-input" v-model="careers" placeholder="最多15个字" autocomplete="off"  class="layui-input">
                                                             <button type="button" class="layui-btn layui-btn-normal" @click="addCareer" >
                                                    <i class="layui-icon">&#xe654;</i>
                                                </button><sapn style="margin-left: 10px;color: red;">输入职业后点击”+“号按钮添加；点击关键字删除</span>
                                                </div>
                                            </div>
                                            <div v-if="formData.careers.length" class="layui-form-item" style="margin-top: 5px;">
                                                <div class="layui-input-block">
                                                    <button v-for="(item,index) in formData.careers" :key="index" type="button" class="layui-btn layui-btn-normal layui-btn-sm" @click="delLabel(index,'careers')">{{item}}</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div> -->
<!--                                     <div class="layui-col-xs12 layui-col-sm12 layui-col-md12" v-if="formData.is_apply == 1">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">擅长乐器种类：<i class="red">*</i></label>
                                                <div class="layui-input-block">
                                                    <input type="text" style="width:50%;display:inline-block;margin-right: 10px;" name="musical_instruments" lay-verify="musical_instruments" autocomplete="off"
                                                           placeholder="请输入职业" class="layui-input" v-model="musical_instruments" placeholder="最多15个字" autocomplete="off"  class="layui-input">
                                                             <button type="button" class="layui-btn layui-btn-normal" @click="addMusicCate" >
                                                    <i class="layui-icon">&#xe654;</i>
                                                </button><sapn style="margin-left: 10px;color: red;">输入类名后点击”+“号按钮添加；点击关键字删除</span>
                                                </div>
                                            </div>
                                            <div v-if="formData.musical_instruments.length" class="layui-form-item" style="margin-top: 5px;">
                                                <div class="layui-input-block">
                                                    <button v-for="(item,index) in formData.musical_instruments" :key="index" type="button" class="layui-btn layui-btn-normal layui-btn-sm" @click="delLabel(index,'musical_instruments')">{{item}}</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div> -->
                                    <div class="layui-row layui-col-space15">
                                        <div class="layui-col-xs12 layui-col-sm4 layui-col-md4">
                                            <div class="grid-demo grid-demo-bg1">
                                                <div class="layui-form-item">
                                                    <label class="layui-form-label">是否开启调查问卷<i class="red">*</i></label>
                                                    <div class="layui-input-block">
                                                        <input type="radio" name="is_questionnaire" lay-filter="is_questionnaire" value="1" title="显示"
                                                               :checked="formData.is_questionnaire == 1 ? true : false">
                                                        <input type="radio" name="is_questionnaire" lay-filter="is_questionnaire" value="0" title="隐藏"
                                                               :checked="formData.is_questionnaire == 0 ? true : false">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-row layui-col-space15">
                                        <div class="layui-col-xs12 layui-col-sm4 layui-col-md4">
                                            <div class="grid-demo grid-demo-bg1">
                                                <div class="layui-form-item">
                                                    <label class="layui-form-label">是否显示<i class="red">*</i></label>
                                                    <div class="layui-input-block">
                                                        <input type="radio" name="is_show" lay-filter="is_show" value="1" title="显示"
                                                               :checked="formData.is_show == 1 ? true : false">
                                                        <input type="radio" name="is_show" lay-filter="is_show" value="0" title="隐藏"
                                                               :checked="formData.is_show == 0 ? true : false">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-row layui-col-space15">
                                        <div class="layui-col-xs12 layui-col-sm4 layui-col-md4">
                                            <div class="grid-demo grid-demo-bg1">
                                                <div class="layui-form-item">
                                                    <label class="layui-form-label">是否字节显示<i class="red">*</i></label>
                                                    <div class="layui-input-block">
                                                        <input type="radio" name="is_bytedance_show" lay-filter="is_bytedance_show" value="1" title="显示"
                                                               :checked="formData.is_bytedance_show == 1 ? true : false">
                                                        <input type="radio" name="is_bytedance_show" lay-filter="is_bytedance_show" value="0" title="隐藏"
                                                               :checked="formData.is_bytedance_show == 0 ? true : false">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-tab-item">
                                <div class="layui-row layui-col-space15">
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">封面图<i class="red">*</i></label>
                                                <div class="pictrueBox">
                                                    <div class="pictrue" v-if="formData.image" @click="uploadImage('image')">
                                                        <img :src="formData.image"></div>
                                                    <div class="upLoad" @click="uploadImage('image')" v-else>
                                                        <i class="layui-icon layui-icon-camera" class="iconfont"
                                                           style="font-size: 26px;"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">轮播图<i class="red">*</i></label>
                                                <div class="pictrueBox pictrue" v-for="(item,index) in formData.slider_image">
                                                    <img :src="item">
                                                    <i class="layui-icon closes" @click="deleteImage('slider_image',index)">&#x1007</i>
                                                </div>
                                                <div class="pictrueBox">
                                                    <div class="upLoad" @click="uploadImage('slider_image')"
                                                         v-if="formData.slider_image.length <= rule.slider_image.maxLength">
                                                        <i class="layui-icon layui-icon-camera" class="iconfont"
                                                           style="font-size: 26px;"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-form-item submit">
                                        <label class="layui-form-label">主图视频</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="link_key" v-model="videoLink" style="width:50%;display:inline-block;margin-right: 10px;" autocomplete="off" placeholder="请输入视频链接" class="layui-input">
                                            <button type="button" @click="uploadVideo" class="layui-btn layui-btn-sm layui-btn-normal">{{videoLink ? '确认添加' : '上传视频'}}</button>
                                            <input ref="filElem" type="file" style="display: none">
                                        </div>
                                        <div class="layui-input-block video_show" style="width: 30%;margin-top: 20px;" v-if="upload.videoIng">
                                            <div class="layui-progress" style="margin-bottom: 10px">
                                                <div class="layui-progress-bar layui-bg-blue" :style="'width:'+progress+'%'"></div>
                                            </div>
                                            <button type="button" class="layui-btn layui-btn-sm layui-btn-danger percent">{{progress}}%</button>
                                        </div>
                                        <div class="layui-input-block" v-if="formData.video_link">
                                            <div class="layui-video-box" v-if="formData.video_link">
                                                <video style="width:100%;height: 100%!important;border-radius: 10px;" :src="formData.video_link" controls="controls">
                                                    您的浏览器不支持 video 标签。
                                                </video>
                                                <div class="mark" @click="delVideo">
                                                    <span class="layui-icon layui-icon-delete" style="font-size: 30px; color: #1E9FFF;"></span>
                                                </div>

                                            </div>
                                            <div class="layui-video-box" v-else>
                                                <i class="layui-icon layui-icon-play"></i>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md7">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">买票多次总数量限制</label>
                                                <div class="layui-input-block">
                                                    <input type="number" name="many_quota" autocomplete="off" placeholder="请输入买票多次总数量限制" class="layui-input" v-model="formData.many_quota">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md7">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">单次买票限制</label>
                                                <div class="layui-input-block">
                                                    <input type="number" name="single_quota" autocomplete="off" placeholder="请输入单次买票限制" class="layui-input" v-model="formData.single_quota">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">活动规格<i class="red">*</i></label>
                                                <div class="layui-input-block">
                                                    <input type="radio" name="spec_type" value="0" title="单规格"
                                                           lay-filter="spec_type"
                                                           :checked="formData.spec_type == 0 ? true : false">
                                                    <input type="radio" name="spec_type" value="1" title="多规格"
                                                           lay-filter="spec_type"
                                                           :checked="formData.spec_type == 1 ? true : false">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12" v-if="formData.spec_type == 0">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label"></label>
                                                <div class="layui-input-block">
                                                    <table class="layui-table">
                                                        <thead>
                                                        <tr>
                                                            <th>图片<i class="red">*</i></th>
                                                            <th>起售数量<i class="red">*</i></th>
                                                            <th>售价<i class="red">*</i></th>
                                                            <th>定金价</th>
                                                            <th>定金折扣价</th>
                                                            <th>原价<i class="red">*</i></th>
                                                            <th>库存<i class="red">*</i></th>
                                                            <th>活动编号</th>
                                                            <th>重量(KG)</th>
                                                            <th>体积(m³)</th>
                                                        </tr>
                                                        </thead>
                                                        <tr>
                                                            <td>
                                                                <div class="pictrueBox">
                                                                    <div class="pictrue" v-if="formData.attr.pic"
                                                                         @click="uploadImage('attr.pic')"><img
                                                                                :src="formData.attr.pic"></div>
                                                                    <div class="upLoad" @click="uploadImage('attr.pic')"
                                                                         v-else>
                                                                        <i class="layui-icon layui-icon-camera" class="iconfont"
                                                                           style="font-size: 26px;"></i>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td><input type="text" v-model="formData.attr.digits"
                                                                       class="layui-input">
                                                            </td>
                                                            <td><input type="text" v-model="formData.attr.price"
                                                                       class="layui-input"></td>
                                                            <td><input type="text" v-model="formData.attr.dp_price"
                                                                       class="layui-input"></td>
                                                            <td><input type="text" v-model="formData.attr.dpd_price"
                                                                       class="layui-input"></td>
                                                            <td><input type="text" v-model="formData.attr.ot_price"
                                                                       class="layui-input"></td>
                                                            <td><input type="text" v-model="formData.attr.stock"
                                                                       class="layui-input"></td>
                                                            <td><input type="text" v-model="formData.attr.bar_code"
                                                                       class="layui-input"></td>
                                                            <td><input type="text" v-model="formData.attr.weight"
                                                                       class="layui-input"></td>
                                                            <td><input type="text" v-model="formData.attr.volume"
                                                                       class="layui-input"></td>
                                                        </tr>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- 多规格-->
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12" v-if="formData.spec_type == 1">
                                        <div class="layui-col-xs12 layui-col-sm6 layui-col-md6">
                                            <div class="grid-demo grid-demo-bg1">
                                                <div class="layui-form-item">
                                                    <label class="layui-form-label">选择规格<i class="red">*</i></label>
                                                    <div class="layui-input-block selected store_box">
                                                        <select name="ruleIndex"  lay-filter="rule_index">
                                                            <option value="-1">请选择</option>
                                                            <option :value="index" v-for="(item,index) in ruleList" >{{item.rule_name}}</option>
                                                        </select>
                                                        <button type="button" style="height: 38px;border-left: 0;" class="layui-btn layui-btn-sm" @click="allRule">确认</button>
                                                        <button type="button" style="height: 38px;" class="layui-btn layui-btn-sm layui-btn-primary" @click="addRule">添加规则</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                            <div class="grid-demo grid-demo-bg1" v-for="(item,index) in formData.items">
                                                <div class="ml110"><span>{{item.value}}</span><i class="layui-icon"
                                                                                                 @click="deleteItem(index)">&#x1007;</i>
                                                </div>
                                                <div class="layui-form-item rules">
                                                    <label class="layui-form-label"></label>
                                                    <button type="button" class="layui-btn layui-btn-primary layui-btn-sm"
                                                            v-for="(val,inx) in item.detail">
                                                        {{val}}
                                                        <i class="layui-icon layui-icon-close"
                                                           @click="deleteValue(item,inx)"></i>
                                                    </button>
                                                    <div class="rules rulesBox">
                                                        <div class="rules-btn-sm">
                                                            <input type="text" v-model="item.detailValue" name="title"
                                                                   autocomplete="off" placeholder="请输入">
                                                        </div>
                                                        <button class="layui-btn layui-btn-sm" type="button"
                                                                @click="addDetail(item)">添加
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="grid-demo grid-demo-bg1 rules" style="margin-top: 24px;" v-if="newRule">
                                                <div class="layui-form-item layui-form-text rules">
                                                    <label class="layui-form-label">规格：</label>
                                                    <div class="rules-btn-sm">
                                                        <input type="text" name="title" v-model="formDynamic.attrsName"
                                                               autocomplete="off" placeholder="请输入规格">
                                                    </div>
                                                </div>
                                                <div class="layui-form-item layui-form-text rules">
                                                    <label class="layui-form-label">规格值：</label>
                                                    <div class="rules-btn-sm">
                                                        <input type="text" name="title" v-model="formDynamic.attrsVal"
                                                               autocomplete="off" placeholder="请输入规格值">
                                                    </div>
                                                </div>
                                                <button class="layui-btn layui-btn-sm ml40" type="button"
                                                        @click="createAttrName">添加
                                                </button>
                                                <button class="layui-btn layui-btn-sm ml10" type="button"
                                                        @click="newRule = false">取消
                                                </button>
                                            </div>
                                            <div class="grid-demo grid-demo-bg1" style="margin-top: 20px;margin-bottom: 10px;" v-if="newRule == false && ruleBool">
                                                <div class="layui-form-item">
                                                    <label class="layui-form-label"></label>
                                                    <button class="layui-btn layui-btn-sm" type="button" @click="newRule = true">
                                                        添加新规格
                                                    </button>
                                                    <button class="layui-btn layui-btn-sm" type="button" @click="generates">立即生成
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="layui-col-xs12 layui-col-sm12 layui-col-md12"
                                             v-if="formData.attrs.length && formHeader.length">

                                            <div class="grid-demo grid-demo-bg1" style="margin-top: 20px">
                                                <div class="layui-form-item">
                                                    <label class="layui-form-label">批量设置：</label>
                                                    <div class="layui-input-block">
                                                        <table class="layui-table">
                                                            <thead>
                                                            <tr>
                                                                <th>图片<i class="red">*</i></th>
                                                                <th>起售数量<i class="red">*</i></th>
                                                                <th>售价<i class="red">*</i></th>
                                                                <th>定金价</th>
                                                                <th>定金折扣价</th>
                                                                <th>原价<i class="red">*</i></th>
                                                                <th>库存<i class="red">*</i></th>
                                                                <th>活动编号</th>
                                                                <th>重量(KG)</th>
                                                                <th>体积(m³)</th>
                                                                <th width="15%" style="text-align: center;">操作</th>
                                                            </tr>
                                                            </thead>
                                                            <tr>
                                                                <td>
                                                                    <div class="pictrueBox">
                                                                        <div class="pictrue" v-if="batchAttr.pic"
                                                                             @click="uploadImage('batchAttr.pic')"><img
                                                                                    :src="batchAttr.pic"></div>
                                                                        <div class="upLoad" @click="uploadImage('batchAttr.pic')"
                                                                             v-else>
                                                                            <i class="layui-icon layui-icon-camera" class="iconfont"
                                                                               style="font-size: 26px;"></i>
                                                                        </div>
                                                                    </div>
                                                                </td>
                                                                <td>
                                                                    <input type="text" v-model="batchAttr.digits"
                                                                           class="layui-input">
                                                                </td>
                                                                <td><input type="text" v-model="batchAttr.price"
                                                                           class="layui-input"></td>
                                                                <td><input type="text" v-model="batchAttr.dp_price"
                                                                           class="layui-input"></td>
                                                                <td><input type="text" v-model="batchAttr.dpd_price"
                                                                           class="layui-input">
                                                                </td>
                                                                <td><input type="text" v-model="batchAttr.ot_price"
                                                                           class="layui-input"></td>
                                                                <td>
                                                                    <input type="text" v-model="batchAttr.stock"
                                                                           class="layui-input">
                                                                </td>
                                                                <td>
                                                                    <input type="text" v-model="batchAttr.bar_code"
                                                                           class="layui-input">
                                                                </td>
                                                                <td>
                                                                    <input type="text" v-model="batchAttr.weight"
                                                                           class="layui-input">
                                                                </td>
                                                                <td>
                                                                    <input type="text" v-model="batchAttr.volume"
                                                                           class="layui-input">
                                                                </td>
                                                                <td style="text-align: center;">
                                                                    <button class="layui-btn layui-btn-sm" type="button"
                                                                            @click="batchAdd">批量修改
                                                                    </button>
                                                                    <button class="layui-btn layui-btn-sm layui-btn-danger" type="button"
                                                                            @click="batchClear">清空
                                                                    </button>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="grid-demo grid-demo-bg1" style="margin-top: 20px">
                                                <div class="layui-form-item">
                                                    <label class="layui-form-label">活动属性：</label>
                                                    <div class="layui-input-block">
                                                        <table class="layui-table">
                                                            <thead>
                                                            <tr>
                                                                <th v-for="(item,index) in formHeader" v-if="item.align">
                                                                    {{item.title}}
                                                                </th>
                                                            </tr>
                                                            </thead>
                                                            <tr v-for="(item,index) in formData.attrs">
                                                                <td v-for="(n,v) in item.detail">{{n}}</td>
                                                                <td>
                                                                    <div class="pictrueBox">
                                                                        <div class="pictrue" v-if="item.pic"
                                                                             @click="uploadImage('attrs.'+index+'.pic')"><img
                                                                                    :src="item.pic"></div>
                                                                        <div class="upLoad" @click="uploadImage('attrs.'+index+'.pic')"
                                                                             v-else>
                                                                            <i class="layui-icon layui-icon-camera"
                                                                               class="iconfont" style="font-size: 26px;"></i>
                                                                        </div>
                                                                    </div>
                                                                </td>
                                                                <td><input type="number" v-model="item.digits"
                                                                           class="layui-input"></td>
                                                                <td><input type="number" v-model="item.price"
                                                                           class="layui-input"></td>
                                                                <td><input type="number" v-model="item.dp_price"
                                                                           class="layui-input"></td>
                                                                <td><input type="number" v-model="item.dpd_price"
                                                                           class="layui-input"></td>
                                                                <td><input type="number" v-model="item.ot_price"
                                                                           class="layui-input"></td>
                                                                <td><input type="number" v-model="item.stock"
                                                                           class="layui-input"></td>
                                                                <td>
                                                                    <input type="text" v-model="item.bar_code"
                                                                           class="layui-input">
                                                                </td>
                                                                <td>
                                                                    <input type="number" v-model="item.weight"
                                                                           class="layui-input">
                                                                </td>
                                                                <td>
                                                                    <input type="number" v-model="item.volume"
                                                                           class="layui-input">
                                                                </td>
                                                                <td>
                                                                    <button class="layui-btn layui-btn-sm" type="button"
                                                                            @click="deleteAttrs(index)">删除
                                                                    </button>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-row layui-col-space15" style="margin-top: 20px;">
                                        <textarea type="text/plain" name="description" id="myEditor" style="width:100%;">{{formData.description}}</textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-tab-content">
                            <div class="layui-row layui-col-space15">
                                <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                    <div class="grid-demo grid-demo-bg1">
                                        <div class="layui-form-item" v-if="id">
                                            <button class="layui-btn layui-btn-primary layui-btn-sm" id="submit" type="button" @click="handleSubmit()">保存</button>
                                            <button class="layui-btn layui-btn-primary layui-btn-sm" type="button" @click="back" v-if="layTabId != 1">上一步</button>
                                            <button class="layui-btn layui-btn-normal layui-btn-sm" type="button" v-if="layTabId != 2" @click="next">下一步</button>
                                        </div>
                                        <div class="layui-form-item" v-else>
                                            <button class="layui-btn layui-btn-primary layui-btn-sm" type="button" @click="back" v-if="layTabId != 1">上一步</button>
                                            <button class="layui-btn layui-btn-normal layui-btn-sm" type="button" @click="next" v-if="layTabId != 2">下一步</button>
                                            <button class="layui-btn layui-btn-normal layui-btn-sm" id="submit" type="button" v-if="layTabId == 2" @click="handleSubmit()">提交</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<script src="{__PLUG_PATH}city.js"></script>
<script>
    var id = {$id};
    $.each(city,function (key,item) {
       city[key].value = item.label;
       if(item.children && item.children.length){
           $.each(item.children,function (i,v) {
               city[key].children[i].value=v.label;
               if(v.children && v.children.length){
                   $.each(v.children,function (k,val) {
                       city[key].children[i].children[k].value=val.label;
                   });
               }
           });
       }
   });
    new Vue({
        el: '#app',
        data: {
            id:id,
            //分类列表
            cateList: [],
            upload:{
                videoIng:false
            },
            addresData: city,
            formData: {
                cate_id: [],
                store_name: '',
                introduction: '', //活动简介
                activity_order_notes: '', //结算页备注
                info: '',
                hint: '',
                offer_notes: '',
                price: 0,
                start_end:'',
                channels: [],
                careers: [],
                musical_instruments: [],
                start_activity_time_str:'',
                end_activity_time_str:'',
                activity_start_end:'',
                crowdfunding_start_end:'',
                end_discount_time:'',
                limit_signup_number:0,
                limit_crowdfunding_number:0,
                //联系方式
                contact_info: '',
                contact_image: '',
                douyin_contact_image: '',
                wait_wechat_group_image: '',
                ok_wechat_group_image: '',
                wait_douyin_group_image: '',
                ok_douyin_group_image: '',
                many_quota:0,
                single_quota:0,
                attrs: [],
                image: '',
                video_link: '',
                slider_image: [],
                spec_type: 0,
                attr: {
                    pic: '',
                    price: 0,
                    dp_price: 0,
                    dpd_price: 0,
                    ot_price: 0,
                    stock: 0,
                    digits:1,
                    bar_code: '',
                    weight: 0,
                    volume: 0,
                },
                description: '',
                text_agreement: '',
                ficti: 0,
                sort: 0,
                is_show: 0,
                is_spare: 1,
                is_channels: 1,
                is_apply: 1,
                is_must_work:0,
                is_manage:1,
                is_questionnaire: 1,
                is_bytedance_show: 0,
                is_countdown:0,
                is_wish: 0,
                wish_title: '',
                wish_desc: '',
                status:0,
                items: [],
                detailed_address:'',
                latlng:''
            },
            videoLink:'',
            //批量添加属性
            batchAttr:{
                pic: '',
                price: 0,
                dp_price: 0,
                dpd_price: 0,
                ot_price: 0,
                stock: 0,
                digits:1,
                bar_code: '',
                weight: 0,
                volume: 0,
            },
            //多属性header头
            formHeader:[],
            // 规格数据
            formDynamic: {
                attrsName: '',
                attrsVal: ''
            },
            channels: '',
            careers: '',
            musical_instruments: '',
            activity:{'秒杀':'#1E9FFF','砍价':'#189688','拼团':'#FEB900'},
            attr: [],//临时属性
            newRule: false,//是否添加新规则
            newLabel: false,//是否添加新标签
            radioRule: ['is_sub','is_evaluation','is_show','is_spare','is_channels','is_apply','is_must_work','is_manage','is_questionnaire','is_bytedance_show','is_countdown','is_wish','status', 'is_hot', 'is_benefit', 'is_new','is_good' ,'is_best', 'spec_type'],//radio 当选规则
            radioLabel: ['is_sub','is_evaluation','is_show','is_spare','is_channels','is_apply','is_must_work','is_manage','is_questionnaire','is_bytedance_show','is_countdown','is_wish','status', 'is_hot', 'is_benefit', 'is_new','is_good' ,'is_best', 'spec_type'],//radio 当选规则
            rule: { //多图选择规则
							slider_image: {
									maxLength: 5
							}
            },
            ruleList:[],
            ruleIndex:-1,
            progress: 0,
            um: null,//编译器实例化
            form: null,//layui.form
            layTabId: 1,
            ruleBool: id ? true : false,
        },
        watch:{
            'formData.is_sub':function (n) {
                if (n == 1) {
                    this.formHeader.push({title:'一级返佣(元)'});
                    this.formHeader.push({title:'二级级返佣(元)'});
                } else {
                    this.formHeader.pop();
                    this.formHeader.pop();
                }
            },
            'formData.spec_type':function (n) {
                if (n) {
                    this.render();
                }
            }
        },
        methods: {
            back:function(){
                var that = this;
                layui.use(['element'], function () {
                    layui.element.tabChange('docTabBrief', that.layTabId == 1 ? 1 : parseInt(that.layTabId) - 1);
                });
            },
            next:function(){
                var that = this;
                layui.use(['element'], function () {
                    layui.element.tabChange('docTabBrief', that.layTabId == 5 ? 5 : parseInt(that.layTabId) + 1);
                });
            },
            goBack:function(){
                location.href = this.U({m:window.module,c:'market.StoreActivity',a:'index'});
            },
            U: function (opt) {
                var m = opt.m || 'admin', c = opt.c || window.controlle || '', a = opt.a || 'index', q = opt.q || '',
                    p = opt.p || {};
                var params = Object.keys(p).map(function (key) {
                    return key + '/' + p[key];
                }).join('/');
                var gets = Object.keys(q).map(function (key) {
                    return key+'='+ q[key];
                }).join('&');

                return '/' + m + '/' + c + '/' + a + (params == '' ? '' : '/' + params) + (gets == '' ? '' : '?' + gets);
            },
            /**
             * 提示
             * */
            showMsg: function (msg, success) {
                $('#submit').removeAttr('disabled').text('提交');
                layui.use(['layer'], function () {
                    layui.layer.msg(msg, success);
                });
            },
            batchClear:function(){
                this.$set(this,'batchAttr',{
                    pic: '',
                    price: 0,
                    dp_price: 0,
                    dpd_price: 0,
                    ot_price: 0,
                    stock: 0,
                    digits:1,
                    bar_code: '',
                    weight: 0,
                    volume: 0,
                });
            },
            /**
             * 批量添加
             * */
            batchAdd:function(){
                var that = this;
                this.$set(this.formData,'attrs',this.formData.attrs.map(function (item) {
                    if (that.batchAttr.pic) {
                        item.pic = that.batchAttr.pic;
                    }
                    if (that.batchAttr.price > 0){
                        item.price = that.batchAttr.price;
                    }
                    if (that.batchAttr.dp_price > 0){
                        item.dp_price = that.batchAttr.dp_price;
                    }
                    if (that.batchAttr.dpd_price > 0){
                        item.dpd_price = that.batchAttr.dpd_price;
                    }
                    if (that.batchAttr.ot_price > 0){
                        item.ot_price = that.batchAttr.ot_price;
                    }
                    if (that.batchAttr.stock > 0){
                        item.stock = that.batchAttr.stock;
                    }
                    if (that.batchAttr.digits > 0){
                        item.digits = that.batchAttr.digits;
                    }
                    if (that.batchAttr.bar_code != ''){
                        item.bar_code = that.batchAttr.bar_code;
                    }
                    if (that.batchAttr.weight > 0){
                        item.weight = that.batchAttr.weight;
                    }
                    if (that.batchAttr.volume > 0){
                        item.volume = that.batchAttr.volume;
                    }
                    return item;
                }));

            },
            /**
             * 获取活动信息
             * */
            getActivityInfo: function () {
                var that = this;
                that.requestGet(that.U({m:window.module,c:"market.StoreActivity",a:'get_activity_info',q:{id:that.id}})).then(function (res) {
                    that.$set(that,'cateList',res.data.cateList);
                    that.$set(that,'storeList',res.data.storeList);
                    var activityInfo = res.data.activityInfo || {};
                    if(activityInfo.id && that.id){
                        that.$set(that,'formData',activityInfo);
                        that.generate();
                    }
    				that.getRuleList(1);
                    that.init();
                }).catch(function (res) {
                    that.showMsg(res.msg);
                })
            },
            /**
             * 给某个属性添加属性值
             * @param item
             * */
            addDetail: function (item) {
                if (!item.detailValue) return false;
                if (item.detail.find(function (val) {
                    if(item.detailValue == val){
                        return true;
                    }
                })) {
                    return this.showMsg('添加的属性值重复');
                }
                item.detail.push(item.detailValue);
                item.detailValue = '';
            },
            /**
             * 删除某个属性值
             * @param item 父级循环集合
             * @param inx 子集index
             * */
            deleteValue: function (item, inx) {
                if (item.detail.length > 1) {
                    item.detail.splice(inx, 1);
                } else {
                    return this.showMsg('请设置至少一个属性');
                }
            },

            delLabel: function (index,field) {
                if (field == 'channels') {
                    this.formData.channels.splice(index, 1);
                    this.$set(this.formData, 'channels', this.formData.channels);
                }
                if (field == 'careers') {
                    this.formData.careers.splice(index, 1);
                    this.$set(this.formData, 'careers', this.formData.careers);
                }
                if (field == 'musical_instruments') {
                    this.formData.musical_instruments.splice(index, 1);
                    this.$set(this.formData, 'musical_instruments', this.formData.musical_instruments);
                }
            },
            addhannels: function () {
                if (this.channels) {
                    if (this.channels.length > 15) return this.showMsg('您输入的关键字字数太长');
                    var length = this.formData.channels.length;
                    // if (length >= 6) return this.showMsg('关键字最多添加6个');
                    for (var i = 0; i < length; i++) {
                        if (this.formData.channels[i] == this.channels) return this.showMsg('请勿重复添加');
                    }
                    this.formData.channels.push(this.channels);
                    this.$set(this.formData, 'channels', this.formData.channels);
                    this.channels = '';
                }else{
                    return this.showMsg('请输入正确渠道');
                }
            },
            addCareer: function () {
                if (this.careers) {
                    if (this.careers.length > 15) return this.showMsg('您输入的关键字字数太长');
                    var length = this.formData.careers.length;
                    // if (length >= 20) return this.showMsg('关键字最多添加20个');
                    for (var i = 0; i < length; i++) {
                        if (this.formData.careers[i] == this.careers) return this.showMsg('请勿重复添加');
                    }
                    this.formData.careers.push(this.careers);
                    this.$set(this.formData, 'careers', this.formData.careers);
                    this.careers = '';
                }else{
                    return this.showMsg('请输入正确职业');
                }
            },
            addMusicCate: function () {
                if (this.musical_instruments) {
                    if (this.musical_instruments.length > 15) return this.showMsg('您输入的关键字字数太长');
                    var length = this.formData.musical_instruments.length;
                    // if (length >= 20) return this.showMsg('关键字最多添加20个');
                    for (var i = 0; i < length; i++) {
                        if (this.formData.musical_instruments[i] == this.musical_instruments) return this.showMsg('请勿重复添加');
                    }
                    this.formData.musical_instruments.push(this.musical_instruments);
                    this.$set(this.formData, 'musical_instruments', this.formData.musical_instruments);
                    this.musical_instruments = '';
                }else{
                    return this.showMsg('请输入正确分类名称');
                }
            },
            /**
             * 删除某条属性
             * @param index
             * */
            deleteItem: function (index) {
                this.formData.items.splice(index, 1);
            },
            /**
             * 删除某条属性
             * @param index
             * */
            deleteAttrs: function (index) {
                var that = this;
                if(that.id > 0){
                    that.requestGet(that.U({m:window.module,c:"market.StoreActivity",a:'check_activity',q:{id:that.id}})).then(function (res) {
                        that.showMsg(res.msg);
                    }).catch(function (res) {
                        if (that.formData.attrs.length > 1) {
                            that.formData.attrs.splice(index, 1);
                        } else {
                            return that.showMsg('请设置至少一个规则');
                        }
                    })
                }else{
                    if (that.formData.attrs.length > 1) {
                        that.formData.attrs.splice(index, 1);
                    } else {
                        return that.showMsg('请设置至少一个规则');
                    }
                }
            },
            /**
             * 创建属性
             * */
            createAttrName: function () {
                if (this.formDynamic.attrsName && this.formDynamic.attrsVal) {
                    if (this.formData.items.find(function (val) {
                        if (val.value == this.formDynamic.attrsName) {
                            return true;
                        }
                    }.bind(this))) {
                        return this.showMsg('添加的属性重复');
                    }
                    this.formData.items.push({
                        value: this.formDynamic.attrsName,
                        detailValue: '',
                        attrHidden: false,
                        detail: [this.formDynamic.attrsVal]
                    });
                    this.formDynamic.attrsName = '';
                    this.formDynamic.attrsVal = '';
                    this.newRule = false;
                } else {
                    return this.showMsg('请添加完整的规格!');
                }
            },
            /**
             * 删除图片
             * */
            deleteImage: function (key, index) {
                var that = this;
                if (index != undefined) {
                    that.formData[key].splice(index, 1);
                    that.$set(that.formData, key, that.formData[key]);
                } else {
                    that.$set(that.formData, key, '');
                }
            },
            createFrame: function (title, src, opt) {
                opt === undefined && (opt = {});
                var h = 0;
                if (window.innerHeight < 800 && window.innerHeight >= 700) {
                    h = window.innerHeight - 50;
                } else if (window.innerHeight < 900 && window.innerHeight >= 800) {
                    h = window.innerHeight - 100;
                } else if (window.innerHeight < 1000 && window.innerHeight >= 900) {
                    h = window.innerHeight - 150;
                } else if (window.innerHeight >= 1000) {
                    h = window.innerHeight - 200;
                } else {
                    h = window.innerHeight;
                }
                var area = [(opt.w || window.innerWidth / 2) + 'px', (!opt.h || opt.h > h ? h : opt.h) + 'px'];
                layui.use('layer',function () {
                    return layer.open({
                        type: 2,
                        title: title,
                        area: area,
                        fixed: false, //不固定
                        maxmin: true,
                        moveOut: false,//true  可以拖出窗外  false 只能在窗内拖
                        anim: 5,//出场动画 isOutAnim bool 关闭动画
                        offset: 'auto',//['100px','100px'],//'auto',//初始位置  ['100px','100px'] t[ 上 左]
                        shade: 0,//遮罩
                        resize: true,//是否允许拉伸
                        content: src,//内容
                        move: '.layui-layer-title'
                    });
                });
            },
            changeIMG: function (name, value) {
                // alere
                if (this.getRule(name).maxLength !== undefined) {
                    var that = this;
                    value.map(function (v) {
                        that.formData[name].push(v);
                    });

                    this.$set(this.formData, name, this.formData[name]);
                } else {
                    if(name == 'batchAttr.pic'){
                        this.batchAttr.pic = value;
                    } else {
                        if (name.indexOf('.') !== -1) {
                            var key = name.split('.');
                            if (key.length == 2){
                                this.formData[key[0]][key[1]] = value;
                            } else if(key.length == 3){
                                this.formData[key[0]][key[1]][key[2]] = value;
                            } else if(key.length == 4){
                                this.$set(this.formData[key[0]][key[1]][key[2]],key[3],value)
                            }
                        } else {
                            this.formData[name] = value;
                        }
                    }
                }
            },
            getRule: function (name) {
                return this.rule[name] || {};
            },
            uploadImage: function (name) {
                return this.createFrame('选择图片',this.U({m:window.module,c:"widget.images",a:'index',p:{fodder:name}}),{h:545,w:900});
            },
            uploadVideo: function () {
                if (this.videoLink) {
                    this.formData.video_link = this.videoLink;
                } else {
                    $(this.$refs.filElem).click();
                }
            },
            openWindows: function(title, url, opt) {
                return this.createFrame(title, url, opt);
            },
            selectAdderss: function(data) {
                console.log(data.latlng);
                //lat 纬度 lng 经度
                this.formData.latlng = data.latlng.lat + ',' + data.latlng.lng;
                this.formData.detailed_address = data.poiaddress + data.poiname;
            },
            delVideo: function () {
                var that = this;
                that.$set(that.formData, 'video_link', '');
            },
            insertEditor: function (list) {
                this.um.execCommand('insertimage', list);
            },
            insertEditorAudio: function (src) {
                UM.getEditor('myEditor').focus();
                UM.getEditor('myEditor').execCommand('inserthtml','<div><audio style="width: 99%" src="'+src+'" class="audio-ue" controls="controls" width="100"><source src="'+src+'"></source></audio></div><br>');
            },
            insertEditorVideo: function (src) {
                  UM.getEditor('myEditor').focus();
                  UM.getEditor('myEditor').execCommand('inserthtml','<div><video style="width: 99%" src="'+src+'" class="video-ue" controls="controls" width="100"><source src="'+src+'"></source></video></div><br>');
                // this.um.setContent('<div><video style="width: 99%" src="'+src+'" class="video-ue" controls="controls" width="100"><source src="'+src+'"></source></video></div><br>',true);
            },
            getContent: function () {
                return this.um.getContent();
            },
            getContents: function () {
                return this.ums.getContent();
            },
            /**
             * 监听radio字段
             */
            eeventRadio: function () {
                var that = this;
                that.radioRule.map(function (val) {
                    that.form.on('radio(' + val + ')', function (res) {
                        that.formData[val] = res.value;
                    });
                })
            },
            init: function () {
                var that = this;
                window.UMEDITOR_CONFIG.toolbar = [
                    // 加入一个 test
                    'source | undo redo | bold italic underline strikethrough | superscript subscript | forecolor backcolor | removeformat |',
                    'insertorderedlist insertunorderedlist | selectall cleardoc paragraph | fontfamily fontsize',
                    '| justifyleft justifycenter justifyright justifyjustify |',
                    'link unlink | emotion selectimgs video  | map',
                    '| horizontal print preview fullscreen', 'drafts', 'formula'
                ];
                UM.registerUI('selectimgs', function (name) {
                    var me = this;
                    var $btn = $.eduibutton({
                        icon: 'image',
                        click: function () {
                            that.createFrame('选择图片', "{:Url('widget.images/index',['fodder'=>'editor'])}");
                        },
                        title: '选择图片'
                    });

                    this.addListener('selectionchange', function () {
                        //切换为不可编辑时，把自己变灰
                        var state = this.queryCommandState(name);
                        $btn.edui().disabled(state == -1).active(state == 1)
                    });
                    return $btn;

                });
                UM.registerUI('video', function (name) {
                    var me = this;
                    var $btn = $.eduibutton({
                        icon: 'video',
                        click: function () {
                            that.createFrame('选择视频', "{:Url('widget.video/index',['fodder'=>'video'])}");
                        },
                        title: '选择视频'
                    });

                    this.addListener('selectionchange', function () {
                        //切换为不可编辑时，把自己变灰
                        var state = this.queryCommandState(name);
                        $btn.edui().disabled(state == -1).active(state == 1)
                    });
                    return $btn;

                });
                //实例化编辑器
                this.um = UM.getEditor('myEditor', {initialFrameWidth: '99%', initialFrameHeight: 400});
                this.ums = UM.getEditor('myEditors', {initialFrameWidth: '99%', initialFrameHeight: 400});
                this.um.setContent(that.formData.description);
                this.ums.setContent(that.formData.text_agreement);
                that.$nextTick(function () {
                    layui.use(['form','element','table','laydate'], function () {
                        that.form = layui.form;
                        that.laydate = layui.laydate;
                        that.laydate.render({
                            elem: '#activity_start_end', //指定元素
                            type: 'datetime',
                            range: '/',
                            done: function(value, date, endDate) {
                                that.formData.activity_start_end = value;
                            }
                        });
                        //时间
                        that.laydate.render({
                            elem: '#start_end', //指定元素
                            type: 'datetime',
                            range: '/',
                            done: function(value, date, endDate) {
                                that.formData.start_end = value;
                            }
                        });
                        that.laydate.render({
                            elem: '#crowdfunding_start_end', //指定元素
                            type: 'datetime',
                            range: '/',
                            done: function(value, date, endDate) {
                                that.formData.crowdfunding_start_end = value;
                            }
                        });
                        that.laydate.render({
                            elem: '#end_discount_time', //指定元素
                            type: 'datetime',
                            done: function(value, date, endDate) {
                                that.formData.end_discount_time = value;
                            }
                        });
                        that.form.render();
						that.form.on('select(labelIndex)', function (data) {
								that.setLabelTable(parseInt(data.value),false)
						});
                        that.form.on('select(rule_index)', function (data) {
                            that.ruleIndex = data.value;
                        });
                        layui.element.on('tab(docTabBrief)', function(){
                            that.layTabId = this.getAttribute('lay-id');
                        });
                        that.eeventRadio();
                    });
                    layui.config({
                        base : '/static/plug/layui/'
                    }).extend({
                        selectN: './selectN',
                    }).use('selectM',function () {
                        var selectM = layui.selectM;
                        selectM({
                            //元素容器【必填】
                            elem: '#cate_id'
                            //候选数据【必填】
                            ,data: that.cateList
                            //默认值
                            ,selected: that.formData.cate_id || []
                            //最多选中个数，默认5
                            ,max : 10
                            ,name: 'cate_id'
                            ,model: 'formData.cate_id'
                            //值的分隔符
                            ,delimiter: ','
                            //候选项数据的键名
                            ,field: {idName:'value',titleName:'label',statusName:'disabled'}
                        });
                    });
                })
            },
            requestPost: function (url, data) {
                return new Promise(function (resolve, reject) {
                    axios.post(url, data).then(function (res) {
                        if (res.status == 200 && res.data.code == 200) {
                            resolve(res.data)
                        } else {
                            reject(res.data);
                        }
                    }).catch(function (err) {
                        reject({msg:err})
                    });
                })
            },
            requestGet: function (url) {
                return new Promise(function (resolve, reject) {
                    axios.get(url).then(function (res) {
                        if (res.status == 200 && res.data.code == 200) {
                            resolve(res.data)
                        } else {
                            reject(res.data);
                        }
                    }).catch(function (err) {
                        reject({msg:err})
                    });
                })
            },
            generates: function () {
                var that = this;
                that.generate(1);
            },
            generate: function (type = 0) {
                var that = this;
                this.requestPost(that.U({m:window.module,c:"market.StoreActivity",a:'is_format_attr',p:{id:that.id,type:type}}), {attrs:this.formData.items}).then(function (res) {
                    that.$set(that.formData, 'attrs', res.data.value);
                    that.$set(that, 'formHeader', res.data.header);
                    if (that.id && that.formData.is_sub == 1 && that.formData.spec_type == 1) {
                        that.formHeader.push({title:'一级返佣(元)'});
                        that.formHeader.push({title:'二级级返佣(元)'});
                    }
                }).catch(function (res) {
                    return that.showMsg(res.msg);
                });
            },
            handleSubmit:function () {
                var that = this,
                cate_id = $('input[name="cate_id"]').val();
                if (cate_id != '') {
                    this.formData.cate_id = cate_id.split(',');
                }else{
                    this.formData.cate_id = [];
                }
                if (!that.formData.cate_id.length) {
                    return that.showMsg('请选择活动分类');
                }
                if (!that.formData.store_name) {
                    return that.showMsg('请填写活动名称');
                }
                if (!that.formData.introduction) {
                    return that.showMsg('请填写活动简介 ');
                }
                if (!that.formData.info) {
                    return that.showMsg('请填写使用说明');
                }
                if (!that.formData.hint) {
                    return that.showMsg('请填写提示语');
                }
                // if (!that.formData.activity_order_notes) {
                //     return that.showMsg('请填写结算页文字备注');
                // }
                if (!that.formData.latlng) {
                    return that.showMsg('请选择经纬度');
                }
                if (!that.formData.detailed_address) {
                    return that.showMsg('请填写详细地址');
                }
                if (!that.formData.contact_info) {
                    return that.showMsg('请填写联系方式：文字');
                }
                if (!that.formData.contact_image) {
                    return that.showMsg('请上传联系方式：二维码');
                }
                if (!that.formData.activity_start_end) {
                    return that.showMsg('请填写活动开始/结束时间');
                }
                if (!that.formData.start_end) {
                    return that.showMsg('请填写报名开始/结束时间');
                }
                if (!that.formData.crowdfunding_start_end && that.formData.status ==0) {
                    return that.showMsg('请填写预报名开始/结束时间');
                }
                if (that.formData.is_countdown == 1) {
                    if (!that.formData.offer_notes) {
                        return that.showMsg('请填写活动优惠报名提示语');
                    }
                    if (!that.formData.end_discount_time) {
                        return that.showMsg('请填写活动优惠结束时间');
                    }
                }
                if (!that.formData.limit_crowdfunding_number && that.formData.status ==0) {
                    return that.showMsg('请填写预报名限制人数');
                }
                if (!that.formData.limit_signup_number && that.formData.status ==1) {
                    return that.showMsg('请填写报名限制人数');
                }
                if (that.formData.is_wish == 1) {
                    if (!that.formData.wish_title) {
                        return that.showMsg('请填写心愿说明标题');
                    }
                    if (!that.formData.wish_desc) {
                        return that.showMsg('请填写心愿说明文案');
                    }
                }
                if (!that.formData.image) {
                    return that.showMsg('请填选择活动图');
                }
                if (!that.formData.slider_image.length) {
                    return that.showMsg('请填选择活动轮播图');
                }
                if (that.formData.many_quota < 0) {
                    return that.showMsg('请正确输入多次买票数量限制');
                }
                if (that.formData.single_quota < 0) {
                    return that.showMsg('请正确输入单次买票限制');
                }
                if (that.formData.spec_type == 0) {
                    if (!that.formData.attr.pic) {
                        return that.showMsg('请选择单规则图片');
                    }
                    if (that.formData.attr.price == '') {
                        return that.showMsg('请输入单规格价格');
                    }
                    if (that.formData.attr.ot_price == '') {
                        return that.showMsg('请输入单规格原价');
                    }
                    if (!that.formData.attr.stock) {
                        return that.showMsg('请输入单规格库存');
                    }
                    // if (!that.formData.attr.digits) {
                    //     return that.showMsg('请输入单规格起售数量');
                    // }
                    var attr = {
                        pic:that.formData.attr.pic,
                        price:that.formData.attr.price,
                        // cost:that.formData.attr.cost,
                        dp_price:that.formData.attr.dp_price,
                        dpd_price:that.formData.attr.dpd_price,
                        ot_price:that.formData.attr.ot_price,
                        stock:that.formData.attr.stock,
                        digits:that.formData.attr.digits,
                        bar_code:that.formData.attr.bar_code,
                        volume:that.formData.attr.volume,
                        weight:that.formData.attr.weight,
                    };
                    that.formData.attrs = [attr];
                } else {
                    if (!that.formData.attrs.length) {
                        return that.showMsg('请添加多规格属性');
                    }
                    for(var index in that.formData.attrs){
                        if(!that.formData.attrs[index].pic){
                            return that.showMsg('请选择多规格属性第'+(parseInt(index) +1)+'条的图片');
                        }
                        if(that.formData.attrs[index].price === ''){
                            return that.showMsg('请填写多规格属性第'+(parseInt(index) +1)+'条的价格');
                        }
                        if(that.formData.attrs[index].ot_price === ''){
                            return that.showMsg('请填写多规格属性第'+(parseInt(index) +1)+'条的原价');
                        }
                        if(that.formData.attrs[index].stock === ''){
                            return that.showMsg('请填写多规格属性第'+(parseInt(index) +1)+'条的库存');
                        }
                        // if(that.formData.attrs[index].digits === ''){
                        //     return that.showMsg('请填写多规格属性第'+(parseInt(index) +1)+'条的起售数量');
                        // }
                    }
                }
                that.formData.description = that.getContent();
                that.formData.text_agreement = that.getContents();
                $('#submit').attr('disabled', 'disabled').text('保存中...');
                that.requestPost(that.U({m:window.module,c:'market.StoreActivity',a:'save',p:{id:that.id}}),that.formData).then(function (res) {
                    that.confirm();
                }).catch(function (res) {
                    that.showMsg(res.msg);
                });
            },
            confirm: function(){
                var that = this;
                layui.use(['layer'], function () {
                    var layer = layui.layer;
                    layer.confirm(that.id ? '修改成功是否返回活动列表' : '添加成功是否返回活动列表', {
                        btn: ['返回列表',that.id ? '继续修改' : '继续添加'] //按钮
                    }, function(){
                        location.href = that.U({m:window.module,c:'market.StoreActivity',a:'index'});
                    }, function(){
                        location.reload();
                    });
                });
            },
            render:function(){
                this.$nextTick(function(){
                    layui.use(['form'], function () {
                        layui.form.render('select');
                    });
                })
            },
            // 移动
            handleDragStart (e, item) {
                this.dragging = item;
            },
            handleDragEnd (e, item) {
                this.dragging = null
            },
            handleDragOver (e) {
                e.dataTransfer.dropEffect = 'move'
            },
            handleDragEnter (e, item) {
                e.dataTransfer.effectAllowed = 'move'
                if (item === this.dragging) {
                    return
                }
                var newItems = [...this.formData.activity];
                var src = newItems.indexOf(this.dragging);
                var dst = newItems.indexOf(item);
                newItems.splice(dst, 0, ...newItems.splice(src, 1))
                this.formData.activity = newItems;
            },
            getRuleList:function (type) {
                var that = this;
                that.requestGet(that.U({m:window.module,c:'market.StoreActivity',a:'get_rule'})).then(function (res) {
                    that.$set(that,'ruleList',res.data);
                    if(type !== undefined){
                        that.render();
                    }
                });
            },
            addRule:function(){
                return this.createFrame('添加活动规则',this.U({m:window.module,c:'market.StoreActivityRule',a:'create'}));
            },
            allRule:function () {
                if (this.ruleIndex != -1) {
                    var rule = this.ruleList[this.ruleIndex];
                    if (rule) {
                        this.ruleBool = true;
                        var rule_value = rule.rule_value.map(function (item) {
                            return item;
                        });
                        this.$set(this.formData,'items',rule_value);
                        this.$set(this.formData,'attrs',[]);
                        this.$set(this,'formHeader',[]);
                        return true;
                    }
                }
                this.showMsg('选择的属性无效');
            },
        },
        mounted: function () {
            var that = this;
            axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';
			that.getActivityInfo();
            window.$vm = that;
            window.selectAdderss = that.selectAdderss;
            window.changeIMG = that.changeIMG;
            window.insertEditor = that.insertEditor;
            window.insertEditorAudio = that.insertEditorAudio;
            window.insertEditorVideo = that.insertEditorVideo;
            window.successFun = function(){
                that.getRuleList(1);
            }
            $(that.$refs.filElem).change(function () {
                var inputFile = this.files[0];
                that.requestPost(that.U({m:window.module,c:"widget.video",a:'get_signature'})).then(function (res) {
                    AdminUpload.upload(res.data.uploadType,{
                        token: res.data.uploadToken || '',
                        file: inputFile,
                        accessKeyId: res.data.accessKey || '',
                        accessKeySecret: res.data.secretKey || '',
                        bucketName: res.data.storageName || '',
                        region: res.data.storageRegion || '',
                        domain: res.data.domain || '',
                        uploadIng:function (progress) {
                            that.upload.videoIng = true;
                            that.progress = progress;
                        }
                    }).then(function (res) {
                        //成功
                        that.$set(that.formData, 'video_link', res.url);
                        that.progress = 0;
                        that.upload.videoIng = false;
                        return that.showMsg('上传成功');
                    }).catch(function (err) {
                        //失败
                        console.info(err);
                        return that.showMsg('上传错误请检查您的配置');
                    });
                }).catch(function (res) {
                    return that.showMsg(res.msg || '获取密钥失败,请检查您的配置');
                });
            })
        }
    });
</script>
</body>
</html>
