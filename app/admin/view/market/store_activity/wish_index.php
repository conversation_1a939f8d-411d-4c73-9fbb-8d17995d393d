{extend name="public/container"}
{block name="head_top"}

{/block}
{block name="content"}
<style>
  .btn-outline {
    border: none;
  }

  .btn-outline:hover {
    background-color: #0e9aef;
    color: #fff;
  }

  .layui-form-item .layui-btn {
    margin-top: 5px;
    margin-right: 10px;
  }

  .layui-btn-primary {
    margin-right: 10px;
    margin-left: 0 !important;
  }

  label {
    margin-bottom: 0 !important;
    margin-top: 4px;
  }
</style>
<div class="layui-fluid">
  <div class="layui-row layui-col-space15" id="app">
    <!--搜索条件-->
    <div class="layui-col-md12">
      <div class="layui-card">
        <div class="layui-card-header">搜索条件</div>
        <div class="layui-card-body">
          <div class="layui-carousel layadmin-carousel layadmin-shortcut" lay-anim="" lay-indicator="inside"
            lay-arrow="none" style="background:none">
            <div class="layui-card-body">
              <div class="layui-row layui-col-space10 layui-form-item">
                <div class="layui-col-lg12">
                  <label class="layui-form-label">关键子:</label>
                  <div class="layui-input-block">
                    <input type="text" name="keywords" style="width: 50%" v-model="where.keywords"
                      placeholder="请输入城市、姓名、电话、文字备注等关键词" class="layui-input">
                  </div>
                </div>
                <div class="layui-col-lg12">
                  <div class="layui-input-block">
                    <button @click="search" type="button"
                      class="layui-btn layui-btn-sm layui-btn-normal">
                      <i class="layui-icon layui-icon-search"></i>搜索
                    </button>
                    <button @click="refresh" type="reset"
                      class="layui-btn layui-btn-primary layui-btn-sm">
                      <i class="layui-icon layui-icon-refresh"></i>刷新
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!--列表-->
  <div class="layui-row layui-col-space15">
    <div class="layui-col-md12">
      <div class="layui-card">
        <div class="layui-card-header">订单列表</div>
        <div class="layui-card-body">
          <table class="layui-hide" id="List" lay-filter="List"></table>
        </div>
      </div>
    </div>
  </div>
  <!--end-->
</div>
<script src="{__ADMIN_PATH}js/layuiList.js"></script>
{/block}
{block name="script"}
<script>
  layList.tableList('List', "{:Url('wish_list',['keywords'=>$keywords])}", function() {
    return [{
        type: 'checkbox'
      },
      {
        field: 'id',
        title: 'ID',
        sort: true,
        event: 'id',
        width: '6%'
      },
      {
        field: 'address',
        title: '城市',
        width: '15%'
      },
      {
        field: 'contact_name',
        title: '联系人',
        width: '10%'
      },
      {
        field: 'phone',
        title: '电话',
        width: '10%'
      },
      {
        field: 'comment',
        title: '备注',
        width: '45%'
      },
      {
        field: 'add_time',
        title: '时间',
        width: '12%'
      },
    ];
  });
  layList.tool(function(event, data, obj) {})
  var action = {};
  $('#container-action').find('button').each(function() {
    $(this).on('click', function() {
      var act = $(this).data('type');
      action[act] && action[act]();
    });
  })
  //下拉框
  $(document).click(function(e) {
    $('.layui-nav-child').hide();
  })

  function dropdown(that) {
    var oEvent = arguments.callee.caller.arguments[0] || event;
    oEvent.stopPropagation();
    var offset = $(that).offset();
    var top = offset.top - $(window).scrollTop();
    var index = $(that).parents('tr').data('index');
    $('.layui-nav-child').each(function(key) {
      if (key != index) {
        $(this).hide();
      }
    })
    if ($(document).height() < top + $(that).next('ul').height()) {
      $(that).next('ul').css({
        'padding': 10,
        'top': -($(that).parents('td').height() / 2 + $(that).height() + $(that).next('ul').height() / 2),
        'min-width': 'inherit',
        'position': 'absolute'
      }).toggle();
    } else {
      $(that).next('ul').css({
        'padding': 10,
        'top': $(that).parents('td').height() / 2 + $(that).height(),
        'min-width': 'inherit',
        'position': 'absolute'
      }).toggle();
    }
  }

  var keywords = '<?= $keywords ?>';
  require(['vue'], function(Vue) {
    new Vue({
      el: "#app",
      data: {
        where: {
          keywords: keywords || '',
          excel: 0,
        },
        showtime: false,
      },
      watch: {
        'where.keywords': function() {
          this.where.excel = 0;
          layList.reload(this.where, true);
        },
      },
      methods: {
        setData: function(item) {
          var that = this;
          if (item.is_zd == true) {
            that.showtime = true;
            this.where.data = this.$refs.date_time.innerText;
          } else {
            this.showtime = false;
            this.where.data = item.value;
          }
        },
        search: function() {
          this.where.excel = 0;
          layList.reload(this.where, true);
        },
        refresh: function() {
          layList.reload();
        },
        excel: function() {
          this.where.excel = 1;
          location.href = layList.U({
            c: 'market.store_activity',
            a: 'wish_list',
            q: this.where
          });
          this.where.excel = 0;
        }
      },
      mounted: function() {
        var that = this;
        window.formReload = this.search;
        layList.laydate.render({
          elem: this.$refs.date_time,
          trigger: 'click',
          eventElem: this.$refs.time,
          range: true,
          change: function(value) {
            that.where.data = value;
          }
        });
      }
    })
  });
</script>
{/block}