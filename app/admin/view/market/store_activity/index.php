{extend name="public/container"}
{block name="content"}
<div class="layui-fluid" style="background: #fff;margin-top: -10px;">
  <div class="layui-tab layui-tab-brief" lay-filter="tab">
    <ul class="layui-tab-title">
      <li lay-id="list" {eq name='type' value='0' }class="layui-this" {/eq}>
        <a href="{eq name='type' value='0'}javascript:;{else}{:Url('index',['type'=>0])}{/eq}">全部({$all})</a>
      </li>
      <li lay-id="list" {eq name='type' value='1' }class="layui-this" {/eq}>
        <a href="{eq name='type' value='1'}javascript:;{else}{:Url('index',['type'=>1])}{/eq}">进行中活动({$processing})</a>
      </li>
      <li lay-id="list" {eq name='type' value='2' }class="layui-this" {/eq}>
        <a href="{eq name='type' value='2'}javascript:;{else}{:Url('index',['type'=>2])}{/eq}">报名中活动({$registered})</a>
      </li>
      <li lay-id="list" {eq name='type' value='3' }class="layui-this" {/eq}>
        <a href="{eq name='type' value='3'}javascript:;{else}{:Url('index',['type'=>3])}{/eq}">预报名活动({$crowdfunding})</a>
      </li>
      <li lay-id="list" {eq name='type' value='4' }class="layui-this" {/eq}>
        <a href="{eq name='type' value='4'}javascript:;{else}{:Url('index',['type'=>4])}{/eq}">已结束活动({$ended})</a>
      </li>
      <li lay-id="list" {eq name='type' value='5' }class="layui-this" {/eq}>
        <a href="{eq name='type' value='5'}javascript:;{else}{:Url('index',['type'=>5])}{/eq}">已取消活动({$cancelled})</a>
      </li>
      <li lay-id="list" {eq name='type' value='6' }class="layui-this" {/eq}>
        <a href="{eq name='type' value='6'}javascript:;{else}{:Url('index',['type'=>6])}{/eq}">活动回收站({$recycle})</a>
      </li>
    </ul>
  </div>
  <div class="layui-row layui-col-space15" id="app">
    <div class="layui-col-md12">
      <div class="layui-card">
        <div class="layui-card-body">
          <form class="layui-form layui-form-pane" action="">
            <div class="layui-form-item">
              <div class="layui-inline">
                <label class="layui-form-label">所有分类</label>
                <div class="layui-input-block">
                  <select name="cate_id">
                    <option value=" ">全部</option>
                    {volist name='cate' id='vo'}
                    <option value="{$vo.id}">{$vo.html}{$vo.cate_name}</option>
                    {/volist}
                  </select>
                </div>
              </div>
              <div class="layui-inline">
                <label class="layui-form-label">活动名称</label>
                <div class="layui-input-block">
                  <input type="text" name="store_name" class="layui-input" placeholder="请输入活动名称,关键字,编号">
                  <input type="hidden" name="type" value="{$type}">
                </div>
              </div>
              <div class="layui-inline">
                <div class="layui-input-inline">
                  <button class="layui-btn layui-btn-sm layui-btn-normal" lay-submit="search" lay-filter="search">
                    <i class="layui-icon layui-icon-search"></i>搜索</button>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
    <!--活动列表-->
    <div class="layui-col-md12">
      <div class="layui-card">
        <div class="layui-card-body">
          <div class="layui-btn-container">
            <a class="layui-btn layui-btn-sm" href="{:Url('create')}">添加活动</a>
            {switch name='type'}
            {case value="5"}
            <button class="layui-btn layui-btn-sm" data-type="show">批量显示</button>
            {/case}
            {/switch}
          </div>
          <table class="layui-hide" id="List" lay-filter="List"></table>
          <!--图片-->
          <script type="text/html" id="image">
            <img style="cursor: pointer" lay-event="open_image" src="{{d.image}}">
          </script>
          <!--显示|隐藏-->
          <script type="text/html" id="checkisfreeactivity">
            <input type='checkbox' name='id' lay-skin='switch' value="{{d.id}}" lay-filter='is_free_activity' lay-text='免费|收费' {{ d.is_free_activity == 1 ? 'checked' : '' }}>
          </script>
          <!--显示|隐藏-->
          <script type="text/html" id="checkisshow">
            <input type='checkbox' name='id' lay-skin='switch' value="{{d.id}}" lay-filter='is_show' lay-text='显示|隐藏' {{ d.is_show == 1 ? 'checked' : '' }}>
          </script>
          <!--字节显示|隐藏-->
          <script type="text/html" id="checkisbyteshow">
            <input type='checkbox' name='id' lay-skin='switch' value="{{d.id}}" lay-filter='is_bytedance_show' lay-text='显示|隐藏' {{ d.is_bytedance_show == 1 ? 'checked' : '' }}>
          </script>
          <!--活动-->
          <script type="text/html" id="checkboxstatus">
          {{# if(d.status == 0 ){ }}
            <p>
              <font color="red">预报名</font>
            </p>
            {{# } }}
              {{# if(d.status == 1 ){ }}
                <p>
                  <font color="red">报名中</font>
                </p>
                {{# } }}
                  {{# if(d.status == 2 ){ }}
                    <p>
                      <font color="red">进行中</font>
                    </p>
                    {{# } }}
                      {{# if(d.status == 3 ){ }}
                        <p>
                          <font color="red">已结束</font>
                        </p>
                        {{# } }}
                          {{# if(d.status == 4 ){ }}
                            <p>
                              <font color="red">已取消</font>
                            </p>
                            {{# } }}
          </script>
          <!--收藏-->
          <script type="text/html" id="like">
            <span><i class="layui-icon layui-icon-praise"></i> {{d.like}}</span>
          </script>
          <!--点赞-->
          <script type="text/html" id="collect">
            <span><i class="layui-icon layui-icon-star"></i> {{d.collect}}</span>
          </script>
          <!--活动名称-->
          <script type="text/html" id="name">
            <h4>{{d.store_name}}</h4>
            <p>状态:
              {{# if(d.status == 0 ){ }}
                <font color="red">预报名</font>
                {{# } }}
                  {{# if(d.status == 1 ){ }}
                    <font color="red">活动报名中</font>
                    {{# } }}
                      {{# if(d.status == 2 ){ }}
                        <font color="red">活动进行中</font>
                        {{# } }}
                          {{# if(d.status == 3 ){ }}
                            <font color="red">活动已结束</font>
                            {{# } }}
                              {{# if(d.status == 4 ){ }}
                                <font color="red">活动已取消</font>
                                {{# } }}
            </p>
            <p>价格:<font color="red">{{d.price}}</font>
              {{# if(d.cate_name!=''){ }}
                <p>分类:{{d.cate_name}}</p>
                {{# } }}
          </script>
          <!--操作-->
          <script type="text/html" id="act">
            <button type="button" class="layui-btn layui-btn-xs layui-btn-normal" lay-event='edit'>
              编辑
            </button>
            <button type="button" class="layui-btn layui-btn-xs" onclick="dropdown(this)">操作 <span class="caret"></span></button>
            <ul class="layui-nav-child layui-anim layui-anim-upbit">
              <li>
                <a href="javascript:void(0);"
                  onclick="$eb.createModalFrame('状态管理','{:Url('activity_status')}?id={{d.id}}&type=1',{w:600,h:500})">
                  <i class="fa fa-history"></i> 状态管理
                </a>
              </li>
              {{# if(d.is_del){ }}
                <li>
                  <a href="javascript:void(0);" lay-event='delstor'>
                    <i class="fa fa-trash"></i> 恢复活动
                  </a>
                </li>
                {{# }else{ }}
                  <li>
                    <a href="javascript:void(0);" lay-event='delstor'>
                      <i class="fa fa-trash"></i> 移到回收站
                    </a>
                  </li>
                  {{# } }}
            </ul>
          </script>
        </div>
      </div>
    </div>
  </div>
</div>
<script src="{__ADMIN_PATH}js/layuiList.js"></script>
<script>
  var type = <?= $type ?>;
  //实例化form
  layList.form.render();
  //加载列表
  layList.tableList('List', "{:Url('activity_ist',['type'=>$type])}", function() {
    var join = new Array();
    switch (parseInt(type)) {
      case 0:
        join = [{
            field: 'id',
            title: 'ID',
            sort: true,
            event: 'id',
            width: '6%'
          },
          {
            field: 'image',
            title: '活动图片',
            templet: '#image',
            width: '10%',
            align: 'center'
          },
          {
            field: 'store_name',
            title: '活动名称',
            templet: '#name'
          },
          {
            field: 'ficti',
            title: '虚拟销量',
            edit: 'ficti',
            width: '8%'
          },
          {
            field: 'stock',
            title: '库存',
            width: '8%'
          },
          {
            field: 'sort',
            title: '排序',
            edit: 'sort',
            width: '6%'
          },
          {
            field: 'sales',
            title: '销量',
            sort: true,
            event: 'sales',
            width: '8%'
          },
          {
            field: 'is_bytedance_show',
            title: '字节显示状态',
            templet: "#checkisbyteshow",
            width: '8%'
          },
          {
            field: 'is_show',
            title: '显示状态',
            templet: "#checkisshow",
            width: '8%'
          },
          {
            field: 'right',
            title: '操作',
            align: 'center',
            toolbar: '#act',
            width: '14%'
          },
        ];
        break;
      case 1:
      case 4:
        join = [{
            field: 'id',
            title: 'ID',
            sort: true,
            event: 'id',
            width: '6%'
          },
          {
            field: 'image',
            title: '活动图片',
            templet: '#image',
            width: '10%',
            align: 'center'
          },
          {
            field: 'store_name',
            title: '活动名称',
            templet: '#name'
          },
          {
            field: 'activity_time',
            title: '开始时间',
            width: '8%'
          },
          {
            field: 'end_activity_time',
            title: '结束时间',
            width: '8%'
          },
          {
            field: 'sort',
            title: '排序',
            edit: 'sort',
            width: '6%'
          },
          {
            field: 'is_bytedance_show',
            title: '字节显示状态',
            templet: "#checkisbyteshow",
            width: '8%'
          },
          {
            field: 'is_show',
            title: '显示状态',
            templet: "#checkisshow",
            width: '8%'
          },
          {
            field: 'right',
            title: '操作',
            align: 'center',
            toolbar: '#act',
            width: '14%'
          },
        ];
        break;
      case 2:
        join = [{
            field: 'id',
            title: 'ID',
            sort: true,
            event: 'id',
            width: '6%'
          },
          {
            field: 'image',
            title: '活动图片',
            templet: '#image',
            width: '10%',
            align: 'center'
          },
          {
            field: 'store_name',
            title: '活动名称',
            templet: '#name'
          },
          {
            field: 'started_time',
            title: '开始时间',
            width: '8%'
          },
          {
            field: 'ends_time',
            title: '结束时间',
            width: '8%'
          },
          {
            field: 'limit_signup_number',
            title: '报名限制人数',
            width: '8%'
          },
          {
            field: 'signup_numbers',
            title: '已报名人数',
            width: '8%'
          },
          {
            field: 'surplus_quota',
            title: '剩余名额',
            width: '8%'
          },
          {
            field: 'stock',
            title: '库存',
            width: '8%'
          },
          {
            field: 'sort',
            title: '排序',
            edit: 'sort',
            width: '6%'
          },
          {
            field: 'is_bytedance_show',
            title: '字节显示状态',
            templet: "#checkisbyteshow",
            width: '8%'
          },
          {
            field: 'is_show',
            title: '显示状态',
            templet: "#checkisshow",
            width: '8%'
          },
          {
            field: 'right',
            title: '操作',
            align: 'center',
            toolbar: '#act',
            width: '14%'
          },
        ];
        break;
      case 3:
        join = [{
            field: 'id',
            title: 'ID',
            sort: true,
            event: 'id',
            width: '6%'
          },
          {
            field: 'image',
            title: '活动图片',
            templet: '#image',
            width: '10%',
            align: 'center'
          },
          {
            field: 'store_name',
            title: '活动名称',
            templet: '#name',
            width: '12%'
          },
          {
            field: 'crowdfunding_started_time',
            title: '开始时间',
            width: '9%'
          },
          {
            field: 'crowdfunding_ends_time',
            title: '结束时间',
            width: '9%'
          },
          {
            field: 'limit_crowdfunding_number',
            title: '预报名制人数',
            width: '10%'
          },
          {
            field: 'crowdfunding_numbers',
            title: '已预报名数',
            width: '8%'
          },
          {
            field: 'sort',
            title: '排序',
            edit: 'sort',
            width: '5%'
          },
          {
            field: 'is_bytedance_show',
            title: '字节显示状态',
            templet: "#checkisbyteshow",
            width: '9.4%'
          },
          {
            field: 'is_show',
            title: '显示状态',
            templet: "#checkisshow",
            width: '8%'
          },
          {
            field: 'right',
            title: '操作',
            align: 'center',
            toolbar: '#act',
            width: '14%'
          },
        ];
        break;
      case 5:
        join = [{
            field: 'id',
            title: 'ID',
            sort: true,
            event: 'id',
            width: '6%'
          },
          {
            field: 'image',
            title: '活动图片',
            templet: '#image',
            width: '10%',
            align: 'center'
          },
          {
            field: 'store_name',
            title: '活动名称',
            templet: '#name'
          },
          {
            field: 'cancel_time',
            title: '取消时间',
            width: '8%'
          },
          {
            field: 'sort',
            title: '排序',
            edit: 'sort',
            width: '6%'
          },
          {
            field: 'is_bytedance_show',
            title: '字节显示状态',
            templet: "#checkisbyteshow",
            width: '8%'
          },
          {
            field: 'is_show',
            title: '显示状态',
            templet: "#checkisshow",
            width: '8%'
          },
        ];
        break;
      case 6:
        join = [{
            field: 'id',
            title: '活动ID',
            sort: true,
            event: 'id'
          },
          {
            field: 'image',
            title: '活动图片',
            templet: '#image'
          },
          {
            field: 'store_name',
            title: '活动名称',
            templet: '#name'
          },
          {
            field: 'price',
            title: '活动价格',
            edit: 'price'
          },
          {
            field: 'ficti',
            title: '虚拟销量',
            edit: 'ficti'
          },
          {
            field: 'stock',
            title: '库存'
          },
          {
            field: 'sort',
            title: '排序',
            edit: 'sort'
          },
          {
            field: 'sales',
            title: '销量',
            sort: true,
            event: 'sales'
          },
          {
            field: 'right',
            title: '操作',
            align: 'center',
            toolbar: '#act',
            width: '14%'
          },
        ];
        break;
    }
    return join;
  })
  //excel下载
  layList.search('export', function(where) {
    where.excel = 1;
    location.href = layList.U({
      c: 'market.store_activity',
      a: 'activity_ist',
      q: where
    });
  })
  //下拉框
  $(document).click(function(e) {
    $('.layui-nav-child').hide();
  })

  function dropdown(that) {
    var oEvent = arguments.callee.caller.arguments[0] || event;
    oEvent.stopPropagation();
    var offset = $(that).offset();
    var top = offset.top - $(window).scrollTop();
    var index = $(that).parents('tr').data('index');
    $('.layui-nav-child').each(function(key) {
      if (key != index) {
        $(this).hide();
      }
    })
    if ($(document).height() < top + $(that).next('ul').height()) {
      $(that).next('ul').css({
        'padding': 10,
        'top': -($(that).parent('td').height() / 2 + $(that).height() + $(that).next('ul').height() / 2),
        'min-width': 'inherit',
        'position': 'absolute'
      }).toggle();
    } else {
      $(that).next('ul').css({
        'padding': 10,
        'top': $(that).parent('td').height() / 2 + $(that).height(),
        'min-width': 'inherit',
        'position': 'absolute'
      }).toggle();
    }
  }
  //快速编辑
  layList.edit(function(obj) {
    var id = obj.data.id,
      value = obj.value;
    switch (obj.field) {
      case 'price':
        action.set_activity('price', id, value);
        break;
      case 'stock':
        action.set_activity('stock', id, value);
        break;
      case 'sort':
        action.set_activity('sort', id, value);
        break;
      case 'ficti':
        action.set_activity('ficti', id, value);
        break;
    }
  });
  //上隐藏活动
  layList.switch('is_show', function(odj, value) {
    if (odj.elem.checked == true) {
      layList.baseGet(layList.Url({
        c: 'market.store_activity',
        a: 'set_show',
        p: {
          is_show: 1,
          id: value
        }
      }), function(res) {
        layList.msg(res.msg, function() {
          layList.reload();
        });
      });
    } else {
      layList.baseGet(layList.Url({
        c: 'market.store_activity',
        a: 'set_show',
        p: {
          is_show: 0,
          id: value
        }
      }), function(res) {
        layList.msg(res.msg, function() {
          layList.reload();
        });
      });
    }
  });
  //设置活动收费免费
  layList.switch('is_free_activity', function(odj, value) {
    if (odj.elem.checked == true) {
      layList.baseGet(layList.Url({
        c: 'market.store_activity',
        a: 'set_free_activity',
        p: {
          is_free_activity: 1,
          id: value
        }
      }), function(res) {
        layList.msg(res.msg, function() {
          layList.reload();
        });
      });
    } else {
      layList.baseGet(layList.Url({
        c: 'market.store_activity',
        a: 'set_free_activity',
        p: {
          is_free_activity: 0,
          id: value
        }
      }), function(res) {
        layList.msg(res.msg, function() {
          layList.reload();
        });
      });
    }
  });
  //设置活动是否在字节显示
  layList.switch('is_bytedance_show', function(odj, value) {
    if (odj.elem.checked == true) {
      layList.baseGet(layList.Url({
        c: 'market.store_activity',
        a: 'set_bytedance_show',
        p: {
          is_bytedance_show: 1,
          id: value
        }
      }), function(res) {
        layList.msg(res.msg, function() {
          layList.reload();
        });
      });
    } else {
      layList.baseGet(layList.Url({
        c: 'market.store_activity',
        a: 'set_bytedance_show',
        p: {
          is_bytedance_show: 0,
          id: value
        }
      }), function(res) {
        layList.msg(res.msg, function() {
          layList.reload();
        });
      });
    }
  });
  //点击事件绑定
  layList.tool(function(event, data, obj) {
    switch (event) {
      case 'delstor':
        var url = layList.U({
          c: 'market.store_activity',
          a: 'delete',
          q: {
            id: data.id
          }
        });
        if (data.is_del) var code = {
          title: "操作提示",
          text: "确定恢复活动操作吗？",
          type: 'info',
          confirm: '是的，恢复该活动'
        };
        else var code = {
          title: "操作提示",
          text: "确定将该活动移入回收站吗？",
          type: 'info',
          confirm: '是的，移入回收站'
        };
        $eb.$swal('delete', function() {
          $eb.axios.get(url).then(function(res) {
            if (res.status == 200 && res.data.code == 200) {
              $eb.$swal('success', res.data.msg);
              obj.del();
              location.reload();
            } else
              return Promise.reject(res.data.msg || '删除失败')
          }).catch(function(err) {
            $eb.$swal('error', err);
          });
        }, code)
        break;
      case 'open_image':
        $eb.openImage(data.image);
        break;
      case 'edit':
        location.href = layList.U({
          c: 'market.store_activity',
          a: 'create',
          q: {
            id: data.id
          }
        });
        break;
      case 'attr':
        $eb.createModalFrame(data.name + '-属性', layList.U({
          c: 'market.store_activity',
          a: 'attr',
          q: {
            id: data.id
          }
        }), {
          h: 600,
          w: 800
        })
        break;
    }
  })
  //排序
  layList.sort(function(obj) {
    var type = obj.type;
    switch (obj.field) {
      case 'id':
        layList.reload({
          order: layList.order(type, 'id')
        }, true, null, obj);
        break;
      case 'sales':
        layList.reload({
          order: layList.order(type, 'sales')
        }, true, null, obj);
        break;
    }
  });
  //查询
  layList.search('search', function(where) {
    layList.reload(where, true);
  });
  //自定义方法
  var action = {
    set_activity: function(field, id, value) {
      layList.baseGet(layList.Url({
        c: 'market.store_activity',
        a: 'set_activity',
        q: {
          field: field,
          id: id,
          value: value
        }
      }), function(res) {
        layList.msg(res.msg);
      });
    },
    show: function() {
      var ids = layList.getCheckData().getIds('id');
      if (ids.length) {
        layList.basePost(layList.Url({
          c: 'market.store_activity',
          a: 'activity_show'
        }), {
          ids: ids
        }, function(res) {
          layList.msg(res.msg);
          layList.reload();
        });
      } else {
        layList.msg('请选择要显示的活动');
      }
    }
  };
  //多选事件绑定
  $('.layui-btn-container').find('button').each(function() {
    var type = $(this).data('type');
    $(this).on('click', function() {
      action[type] && action[type]();
    })
  });
</script>
{/block}