{extend name="public/container"}
{block name="content"}
<style type="text/css">
    .video{
        width: 24%!important;
        margin-right: 4px;
        margin-top: 10px;
        margin-left: 10px;
        margin-bottom: -1px;
    }
</style>
<div class="ibox-content order-info">
    <div class="row">
        <div class="col-sm-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    报名信息
                </div>
                <div class="panel-body">
                    <div class="row show-grid">
                        <div class="col-xs-12">联系人: {$orderInfo.real_name}</div>
                        <div class="col-xs-12">联系电话: {$orderInfo.user_phone}</div>
                        {if condition="$orderInfo['student_id'] != 0"}
                            {volist name='studentInfo' id='vo' key="k"}
                                <div class="col-xs-12">学员姓名: {$vo.real_name}</div>
                                <div class="col-xs-12">年龄: {$vo.age}</div>
                                <div class="col-xs-12">性别: 
                                    {if condition="$vo['sex'] == 1"}
                                        男
                                    {/if}
                                    {if condition="$vo['sex'] == 2"}
                                        女
                                    {/if}
                                </div>
                                 <div class="col-xs-12">职业: 
                                    {notempty name="vo.career"}
                                        {volist name='vo.career' id='val'}
                                            {$val}&nbsp;
                                        {/volist}
                                    {/notempty}
                                </div>
                                <div class="col-xs-12">学习经历: <br>
                                    {notempty name="vo.learning_experience"}
                                        {volist name='vo.learning_experience' id='val'}
                                            {$val.content}&nbsp;
                                        {/volist}
                                    {/notempty}
                                </div>
                                <div class="col-xs-12">擅长乐器: <br>                   
                                    {notempty name="vo.skills"}
                                        {volist name='vo.skills' id='vals' key="kk"}
                                        <span>
                                            分类：{$vals.cate_name} 名称：{$vals.content}
                                        </span><br>
                                        {/volist}
                                    {/notempty}</div>
                                 {if condition="$vo.video_works != '' "}
                                 <div class="video col-xs-12">视频作品：
                                    <video  controls id="message-video">
                                      <source src="{$vo.video_works}" type="video/mp4">
                                      您的浏览器不支持 HTML5 video 标签。
                                    </video>
                                </div>
                                {/if}
                                {if condition="$vo.video_links != '' "}
                                    <div class="col-xs-12">外链作品链接: {$vo.video_links}</div>
                                {/if}
                                <div class="col-xs-12">状态: 
                                    {if condition="$vo.status eq 0"}
                                    初始
                                    {elseif condition="$vo.status eq 1 "/}
                                    报名成功
                                    {elseif condition="$vo.status eq 2 "/}
                                    初审
                                    {elseif condition="$vo.status eq 3 "/}
                                    初审不通过
                                    {elseif condition="$vo.status eq 4 "/}
                                    预约面试
                                    {elseif condition="$vo.status eq 5 "/}
                                    已预约面试
                                    {elseif condition="$vo.status eq 6 "/}
                                    面试不通过
                                    {elseif condition="$vo.status eq 7 "/}
                                    报名缴费
                                    {/if}
                                </div>
                            {/volist}
                        {/if}
                    </div>
                </div>
            </div>
        </div>
        <div class="col-sm-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    订单信息
                </div>
                <div class="panel-body">
                    <div class="row show-grid">
                        <div class="col-xs-6" >订单编号: {$orderInfo.order_id}</div>
                        <div class="col-xs-6" style="color: #8BC34A;">订单状态:
                            {if condition="$orderInfo['paid'] eq 0 && $orderInfo['status'] eq 0"}
                            未支付
                            {elseif condition="$orderInfo['paid'] eq 1 && $orderInfo['shipping_type'] eq 1 && $orderInfo['status'] eq 0 && $orderInfo['refund_status'] eq 0"/}
                            未发货
                            {elseif condition="$orderInfo['paid'] eq 1 && $orderInfo['shipping_type'] eq 2 && $orderInfo['status'] eq 0 && $orderInfo['refund_status'] eq 0"/}
                            待核销
                            {elseif condition="$orderInfo['paid'] eq 1 && $orderInfo['status'] eq 3 && $orderInfo['refund_status'] eq 0"/}
                            交易完成
                            {elseif condition="$orderInfo['paid'] eq 1 && $orderInfo['refund_status'] eq 1"/}
                            申请退款<b style="color:#f124c7">{$orderInfo.refund_reason_wap}</b>
                            {elseif condition="$orderInfo['paid'] eq 1 && $orderInfo['refund_status'] eq 2"/}
                            已退款
                            {/if}
                        </div>
                        <div class="col-xs-6">商品总数: {$orderInfo.total_num}</div>
                        <div class="col-xs-6">商品总价: ￥{$orderInfo.total_price}</div>
                        <div class="col-xs-6">优惠码金额: ￥{$orderInfo.promo_code_price}</div>
                        <div class="col-xs-6">实际支付: ￥{$orderInfo.pay_price}</div>
                        {if condition="$orderInfo['refund_price'] > 0"}
                        <div class="col-xs-6" style="color: #f1a417">退款金额: ￥{$orderInfo.refund_price}</div>
                        {/if}
                        <div class="col-xs-6">创建时间: {$orderInfo.add_time|date="Y/m/d H:i"}</div>
                        <div class="col-xs-6">支付方式:
                            {if condition="$orderInfo['paid'] eq 1"}
                               {if condition="$orderInfo['pay_type'] eq 'weixin' || $orderInfo['pay_type'] eq 'bytedance.weixin'"}
                               微信支付
                               {elseif condition="$orderInfo['pay_type'] eq 'alipay' || $orderInfo['pay_type'] eq 'bytedance.alipay'"}
                               支付宝支付
                               {elseif condition="$orderInfo['pay_type'] eq 'yue'"}
                               余额支付
                               {elseif condition="$orderInfo['pay_type'] eq 'offline'"}
                               线下支付
                               {else/}
                               其他支付
                               {/if}
                            {else/}
                            {if condition="$orderInfo['pay_type'] eq 'offline'"}
                                线下支付
                            {elseif condition="$orderInfo['pay_type'] eq 'bytedance'"}
                                发起字节跳动担保支付
                            {else/}
                            未支付
                            {/if}
                            {/if}
                        </div>
                        {notempty name="orderInfo.pay_time"}
                        <div class="col-xs-6">支付时间: {$orderInfo.pay_time|date="Y/m/d H:i"}</div>
                        {/notempty}
                        <div class="col-xs-6" style="color: #ff0005">用户备注: {$orderInfo.mark?:'无'}</div>
                        <div class="col-xs-6" style="color: #733b5c">商家备注: {$orderInfo.remark?:'无'}</div>

                    </div>
                </div>
            </div>
        </div>
        <div class="col-sm-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    邀请信息
                </div>
                <div class="panel-body">
                    <div class="row show-grid">
                        <div class="col-xs-6" >邀请人uid: {if $orderInfo.invite_uid}{$orderInfo.invite_uid}{else}{/if}</div>
                        <div class="col-xs-6">关联的订单ID: {$orderInfo.invite_order_id}</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-sm-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    渠道信息
                </div>
                <div class="panel-body">
                    <div class="row show-grid">
                        <div class="col-xs-6" >渠道来源: {$orderInfo.channel_name}</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-sm-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    调查问卷信息
                </div>
                <div class="panel-body">
                    <div class="row show-grid">
                        <div class="col-xs-6" >{if $orderInfo.questionnaire}{$orderInfo.questionnaire}{else}{/if}</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-sm-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    备注信息
                </div>
                <div class="panel-body">
                    <div class="row show-grid">
                        <div class="col-xs-6" >{if $orderInfo.mark}{$orderInfo.mark}{else}暂无备注信息{/if}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<script src="{__FRAME_PATH}js/content.min.js?v=1.0.0"></script>
{/block}
{block name="script"}

{/block}
