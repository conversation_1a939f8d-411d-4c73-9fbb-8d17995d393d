{extend name="public/container"}
{block name="head_top"}

{/block}
{block name="content"}
<style>
    .btn-outline{
        border:none;
    }
    .btn-outline:hover{
        background-color: #0e9aef;
        color: #fff;
    }
    .layui-form-item .layui-btn {
        margin-top: 5px;
        margin-right: 10px;
    }
    .layui-btn-primary{
        margin-right: 10px;
        margin-left: 0!important;
    }
    label{
        margin-bottom: 0!important;
        margin-top: 4px;
    }
</style>
<div class="layui-fluid">
    <div class="layui-row layui-col-space15" id="app">
        <!--搜索条件-->
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">搜索条件</div>
                <div class="layui-card-body">
                    <div class="layui-carousel layadmin-carousel layadmin-shortcut" lay-anim="" lay-indicator="inside"
                         lay-arrow="none" style="background:none">
                        <div class="layui-card-body layui-form">
                            <div class="layui-row layui-col-space10 layui-form-item">
                                <div class="layui-col-lg12">
                                    <label class="layui-form-label">订单状态:</label>
                                    <div class="layui-input-block" v-cloak="">
                                        <button class="layui-btn layui-btn-sm"
                                                :class="{'layui-btn-primary':where.status!==item.value}"
                                                @click="where.status = item.value" type="button"
                                                v-for="item in orderStatus">{{item.name}}
                                            <span v-if="item.count!=undefined"
                                                  :class="item.class!=undefined ? 'layui-badge': 'layui-badge layui-bg-gray' ">{{item.count}}</span>
                                        </button>
                                    </div>
                                </div>
                                <div class="layui-col-lg12">
                                    <label class="layui-form-label">订单类型:</label>
                                    <div class="layui-input-block" v-cloak="">
                                        <button class="layui-btn layui-btn-sm"
                                                :class="{'layui-btn-primary':where.order_type!=item.value}"
                                                @click="where.order_type = item.value" type="button"
                                                v-for="item in orderType">{{item.name}}
                                            <span v-if="item.count!=undefined" class="layui-badge layui-bg-gray">{{item.count}}</span>
                                        </button>
                                    </div>
                                </div>
                                <div class="layui-col-lg12">
                                    <label class="layui-form-label">支付方式:</label>
                                    <div class="layui-input-block" v-cloak="">
                                        <button class="layui-btn layui-btn-sm"
                                                :class="{'layui-btn-primary':where.pay_type!=item.value}"
                                                @click="where.pay_type = item.value" type="button"
                                                v-for="item in payType">{{item.name}}
                                            <span v-if="item.count!=undefined" class="layui-badge layui-bg-gray">{{item.count}}</span>
                                        </button>
                                    </div>
                                </div>
                                <div class="layui-col-lg12">
                                    <label class="layui-form-label">创建时间:</label>
                                    <div class="layui-input-block" data-type="data" v-cloak="">
                                        <button class="layui-btn layui-btn-sm" type="button" v-for="item in dataList"
                                                @click="setData(item)"
                                                :class="{'layui-btn-primary':where.data!=item.value}">{{item.name}}
                                        </button>
                                        <button class="layui-btn layui-btn-sm" type="button" ref="time"
                                                @click="setData({value:'zd',is_zd:true})"
                                                :class="{'layui-btn-primary':where.data!='zd'}">自定义
                                        </button>
                                        <button type="button" class="layui-btn layui-btn-sm layui-btn-primary"
                                                v-show="showtime==true" ref="date_time">{$year.0} - {$year.1}
                                        </button>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                  <label class="layui-form-label">活动名称:</label>
                                  <div class="layui-input-block" style="width: 496px">
                                       <select name="activity_id" v-model="where.activity_id" lay-filter="activity_id"   lay-search="">
                                            <option value="">全部</option>
                                            {volist name='activityList' id='vo'}
                                                <option value="{$vo.id}">{$vo.store_name}</option>
                                            {/volist}
                                        </select>     
                                  </div>
                                </div>
                                <div class="layui-col-lg12">
                                    <label class="layui-form-label">订单号:</label>
                                    <div class="layui-input-block">
                                        <input type="text" name="real_name" style="width: 50%" v-model="where.real_name"
                                               placeholder="请输入姓名、电话、订单编号" class="layui-input">
                                    </div>
                                </div>
                                <div class="layui-col-lg12">
                                    <div class="layui-input-block">
                                        <button @click="search" type="button"
                                                class="layui-btn layui-btn-sm layui-btn-normal" lay-filter="formDemo">
                                            <i class="layui-icon layui-icon-search"></i>搜索
                                        </button>
                                        <button @click="excel" type="button"
                                                class="layui-btn layui-btn-warm layui-btn-sm export" type="button">
                                            <i class="fa fa-floppy-o" style="margin-right: 3px;"></i>导出
                                        </button>
                                        <button @click="refresh" type="reset"
                                                class="layui-btn layui-btn-primary layui-btn-sm">
                                            <i class="layui-icon layui-icon-refresh"></i>刷新
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--列表-->
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">订单列表</div>
                <div class="layui-card-body">
                    <div class="layui-btn-container" id="container-action">
                        <button class="layui-btn layui-btn-sm" data-type="del_order">批量删除订单</button>
                        <button class="layui-btn layui-btn-sm layui-btn-warm" data-type="write_order">订单核销</button>
                    </div>
                    <table class="layui-hide" id="List" lay-filter="List"></table>
                    <!--订单-->
                    <script type="text/html" id="order_id">
                        {{d.order_id}}<br/>
                        {{d.deposit_order_id}}<br/>
                        {{d.final_payment_order_id}}<br/>
                        {{# if(d.store_name){ }}
                            {{d.store_name}}<br/>
                        {{# } }}
                        <span style="color: {{d.color}};">{{d.pink_name}}</span><br/>　
                        {{#  if(d.is_cancel == 1 ){ }}
                            <span style="color: {{d.color}};">订单已取消</span>
                        {{#  }else{ }}
                            {{#  if(d.is_del == 1){ }}<span style="color: {{d.color}};">用户已删除</span>{{# } }}　
                        {{# } }}
                    </script>
                    <!--用户信息-->
                    <script type="text/html" id="userinfo">
                        {{d.nickname==null ? '暂无信息':d.nickname}}/{{d.uid}}
                    </script>
                    <script type="text/html" id="invite_info">
                        {{#  if(d.invite_nickname !=null ){ }}
                            {{d.invite_nickname==null ? '暂无信息':d.invite_nickname}}/{{d.uid}}
                        {{#  }else{ }}
                            暂无信息
                        {{# } }}
                        <br>
                        {{#  if(d.invite_order_id !=null ){ }}
                            关联的订单ID：{{d.invite_order_id}}
                        {{# } }}
                    </script>
                    <!--支付状态-->
                    <script type="text/html" id="pay_type_name">
                        {{#  if(d.order_type==1){ }}
                            {{#  if(d.pay_type == 'weixin' || d.pay_type == 'bytedance.weixin'){ }}<span >全款：微信支付</span>{{# } }}　
                            {{#  if(d.pay_type == 'alipay' || d.pay_type == 'bytedance.alipay'){ }}<span >全款：支付宝支付</span>{{# } }}　
                            {{#  if(d.pay_type == 'yue'){ }}<span >全款：余额支付</span>{{# } }}　
                            {{#  if(d.pay_type == 'bytedance'){ }}<span >全款：发起字节跳动担保支付</span>{{# } }}　
                        {{# }else if(d.order_type==0 || d.order_type== 2 ){ }}
                            {{#  if(d.deposit_pay_type == 'weixin' || d.deposit_pay_type == 'bytedance.weixin'){ }}<span >定金：微信支付</span>{{# } }} <br> {{#  if(d.pay_type == 'weixin' || d.pay_type == 'bytedance.weixin'){ }}<span >尾款：微信支付</span>{{# } }}
                            {{#  if(d.deposit_pay_type == 'alipay' || d.deposit_pay_type == 'bytedance.alipay'){ }}<span >定金：支付宝支付</span>{{# } }}　<br> {{#  if(d.pay_type == 'alipay' || d.pay_type == 'bytedance.alipay' ){ }}<span >尾款：支付宝支付</span>{{# } }}
                            {{#  if(d.deposit_pay_type == 'yue'){ }}<span >定金：余额支付</span>{{# } }}　<br> {{#  if(d.pay_type == 'yue'){ }}<span >尾款：余额支付</span>{{# } }}
                            {{#  if(d.deposit_pay_type == 'bytedance'){ }}<span >定金：发起字节跳动担保支付</span>{{# } }}　<br> {{#  if(d.pay_type == 'bytedance'){ }}<span >尾款：发起字节跳动担保支付</span>{{# } }}
                       {{# }else if(d.order_type==3 ){ }}
                            字节跳动担保支付发起
                        {{# } }}
                    </script>
                    <script type="text/html" id="payinfo">

                        {{#  if(d.order_type==1){ }}
                            总金额：{{d.total_price}}<br/>
                            全款：{{d.total_price}}<br/>
                            折扣：{{d.deduction_price}}<br/>
                            {{#  if(d.paid == 1){ }}实际支付：{{d.pay_price}}<br/>{{# } }}
                            {{#  if(d.paid == 0){ }}实际支付：0.00<br/>{{# } }}
                
                        {{# }else if(d.order_type==0 || d.order_type== 2 ){ }}
                            总金额：{{d.total_price}}<br/>
                            定金：{{d.deposit_pay_price}}<br/>
                            尾款：{{d.pay_price}}<br/>
                            折扣：{{d.deduction_price}}<br/>
                            实际支付：{{d.actual_pay_price}}<br/>

                        {{# }else if(d.order_type==3 ){ }}
                                实际支付：0.00<br/>
                        {{# } }}
                    </script>
                    <!--订单状态-->
                    <script type="text/html" id="status">
                        {{d.status_name}}
                    </script>
                    <!--活动信息-->
                    <script type="text/html" id="info">
                        {{#  layui.each(d._info, function(index, item){ }}
                        {{#  if(item.cart_info.activityInfo.attrInfo!=undefined){ }}
                        <div>
                            <span>
                                <img style="width: 30px;height: 30px;margin:0;cursor: pointer;"
                                     src="{{item.cart_info.activityInfo.attrInfo.image}}">
                            </span>
                            <span>{{item.cart_info.activityInfo.store_name}}&nbsp;{{item.cart_info.activityInfo.attrInfo.suk}}</span>
                            <span> | ￥{{item.cart_info.truePrice}}×{{item.cart_info.cart_num}}</span>
                        </div>
                        {{#  }else{ }}
                        <div>
                            <span><img style="width: 30px;height: 30px;margin:0;cursor: pointer;"
                                       src="{{item.cart_info.activityInfo.image}}"></span>
                            <span>{{item.cart_info.activityInfo.store_name}}</span><span> | ￥{{item.cart_info.truePrice}}×{{item.cart_info.cart_num}}</span>
                        </div>
                        {{# } }}
                        {{#  }); }}
                    </script>
                    <script type="text/html" id="act">
                        {{# if(d._status==0){ }}
                        <button type="button" class="layui-btn layui-btn-xs" onclick="dropdown(this)">操作 <span
                                    class="caret"></span></button>
                        <ul class="layui-nav-child layui-anim layui-anim-upbit">
                            <li>
                                <a href="javascript:void(0);" lay-event='order_info'>
                                    <i class="fa fa-file-text"></i> 订单详情
                                </a>
                            </li>
                            <li>
                                <a href="javascript:void(0);"
                                   onclick="$eb.createModalFrame('修改订单','{:Url('edit')}?id={{d.id}}')">
                                    <i class="fa fa-edit"></i> 修改订单
                                </a>
                            </li>
                            <li>
                                <a lay-event='marke' href="javascript:void(0);">
                                    <i class="fa fa-edit"></i> 订单备注
                                </a>
                            </li>
                            <li>
                                <a href="javascript:void(0);"
                                   onclick="$eb.createModalFrame('订单记录','{:Url('order_status')}?oid={{d.id}}')">
                                    <i class="fa fa-newspaper-o"></i> 订单记录
                                </a>
                            </li>
                        </ul>
                        {{#  }else if(d._status==1){ }}
                        <button class="layui-btn layui-btn-xs" type="button" lay-event="verify">
                            <i class="fa fa-calendar"></i> 立即核销
                        </button>
                        <button type="button" class="layui-btn layui-btn-xs" onclick="dropdown(this)">操作 <span
                                    class="caret"></span></button>
                        <ul class="layui-nav-child layui-anim layui-anim-upbit">
                            <li>
                                <a href="javascript:void(0);" lay-event='order_info'>
                                    <i class="fa fa-file-text"></i> 订单详情
                                </a>
                            </li>
                            <li>
                                <a lay-event='marke' href="javascript:void(0);">
                                    <i class="fa fa-edit"></i> 订单备注
                                </a>
                            </li>
                            <li>
                                <a href="javascript:void(0);"
                                   onclick="$eb.createModalFrame('订单记录','{:Url('order_status')}?oid={{d.id}}')">
                                    <i class="fa fa-newspaper-o"></i> 订单记录
                                </a>
                            </li>
                        </ul>
                        {{#  }else if(d._status==2){ }}
                        <button type="button" class="layui-btn layui-btn-xs" onclick="dropdown(this)">操作 <span
                                    class="caret"></span></button>
                        <ul class="layui-nav-child layui-anim layui-anim-upbit">
                            <li>
                                <a href="javascript:void(0);" lay-event='order_info'>
                                    <i class="fa fa-file-text"></i> 订单详情
                                </a>
                            </li>
                            {{# if(parseFloat(d.actual_pay_price) > parseFloat(d.refund_price)){ }}
                            <li>
                                <a href="javascript:void(0);"
                                   onclick="$eb.createModalFrame('退款','{:Url('refund_y')}?id={{d.id}}')">
                                    <i class="fa fa-history"></i> 立即退款
                                </a>
                            </li>
                            {{# } }}
                            <li>
                                <a lay-event='marke' href="javascript:void(0);">
                                    <i class="fa fa-edit"></i> 订单备注
                                </a>
                            </li>
                            <li>
                                <a href="javascript:void(0);"
                                   onclick="$eb.createModalFrame('订单记录','{:Url('order_status')}?oid={{d.id}}')">
                                    <i class="fa fa-newspaper-o"></i> 订单记录
                                </a>
                            </li>
                        </ul>
                        {{#  }else if(d._status==3){ }}
                        <button type="button" class="layui-btn layui-btn-xs" onclick="dropdown(this)">操作 <span
                                    class="caret"></span></button>
                        <ul class="layui-nav-child layui-anim layui-anim-upbit">
                            <li>
                                <a href="javascript:void(0);" lay-event='order_info'>
                                    <i class="fa fa-file-text"></i> 订单详情
                                </a>
                            </li>
                            <li>
                                <a lay-event='marke' href="javascript:void(0);">
                                    <i class="fa fa-paste"></i> 订单备注
                                </a>
                            </li>
                            {{# if(d.refund_status ==1){ }}

                            {{# if(parseFloat(d.actual_pay_price) > parseFloat(d.refund_price)){ }}
                            <li>
                                <a href="javascript:void(0);"
                                   onclick="$eb.createModalFrame('退款','{:Url('refund_y')}?id={{d.id}}',{w:400,h:300})">
                                    <i class="fa fa-history"></i>立即退款
                                </a>
                            </li>
                            {{# } ;}}
                            <li>
                                <a href="javascript:void(0);"
                                   onclick="$eb.createModalFrame('不退款','{:Url('refund_n')}?id={{d.id}}',{w:400,h:300})">
                                    <i class="fa fa-openid"></i> 不退款
                                </a>
                            </li>
                            {{# } ;}}
                            <li>
                                <a href="javascript:void(0);"
                                   onclick="$eb.createModalFrame('订单记录','{:Url('order_status')}?oid={{d.id}}')">
                                    <i class="fa fa-newspaper-o"></i> 订单记录
                                </a>
                            </li>
                        </ul>
                        {{#  }else if(d._status==4){ }}
                        <button type="button" class="layui-btn layui-btn-xs" onclick="dropdown(this)">操作 <span
                                    class="caret"></span></button>
                        <ul class="layui-nav-child layui-anim layui-anim-upbit">
                            <li>
                                <a href="javascript:void(0);" lay-event='order_info'>
                                    <i class="fa fa-file-text"></i> 订单详情
                                </a>
                            </li>
                            <li>
                                <a lay-event='marke' href="javascript:void(0);">
                                    <i class="fa fa-paste"></i> 订单备注
                                </a>
                            </li>
                            {{# if(parseFloat(d.actual_pay_price) > parseFloat(d.refund_price)){ }}
                            <li>
                                <a href="javascript:void(0);"
                                   onclick="$eb.createModalFrame('退款','{:Url('refund_y')}?id={{d.id}}')">
                                    <i class="fa fa-history"></i> 立即退款
                                </a>
                            </li>
                            {{# } }}
                            <li>
                                <a href="javascript:void(0);"
                                   onclick="$eb.createModalFrame('订单记录','{:Url('order_status')}?oid={{d.id}}')">
                                    <i class="fa fa-newspaper-o"></i> 订单记录
                                </a>
                            </li>
                        </ul>
                        {{#  }else if(d._status==5){ }}
                        <button type="button" class="layui-btn layui-btn-xs" onclick="dropdown(this)">操作 <span
                                    class="caret"></span></button>
                        <ul class="layui-nav-child layui-anim layui-anim-upbit">
                            <li>
                                <a href="javascript:void(0);" lay-event='order_info'>
                                    <i class="fa fa-file-text"></i> 订单详情
                                </a>
                            </li>
                            <li>
                                <a href="javascript:void(0);"
                                   onclick="$eb.createModalFrame('修改订单','{:Url('edit')}?id={{d.id}}')">
                                    <i class="fa fa-edit"></i> 修改订单
                                </a>
                            </li>
                            <li>
                                <a lay-event='marke' href="javascript:void(0);">
                                    <i class="fa fa-paste"></i> 订单备注
                                </a>
                            </li>
                            <li>
                                <a href="javascript:void(0);"
                                   onclick="$eb.createModalFrame('订单记录','{:Url('order_status')}?oid={{d.id}}')">
                                    <i class="fa fa-newspaper-o"></i> 订单记录
                                </a>
                            </li>
                            {{# if(parseFloat(d.deposit_pay_price) > parseFloat(d.refund_price) &&  d.deposit_paid == 1){ }}
                            <li>
                                <a href="javascript:void(0);"
                                   onclick="$eb.createModalFrame('退定金','{:Url('refund_y')}?id={{d.id}}',{w:400,h:300})">
                                    <i class="fa fa-history"></i>立即退定金
                                </a>
                            </li>
                            <li>
                                <a href="javascript:void(0);"
                                   onclick="$eb.createModalFrame('不退款','{:Url('refund_n')}?id={{d.id}}',{w:400,h:300})">
                                    <i class="fa fa-openid"></i> 不退款
                                </a>
                            </li>
                            {{# } ;}}

                        </ul>
                        {{#  }else if(d._status==6){ }}
                        <button type="button" class="layui-btn layui-btn-xs" onclick="dropdown(this)">操作 <span
                                    class="caret"></span></button>
                        <ul class="layui-nav-child layui-anim layui-anim-upbit">
                            <li>
                                <a href="javascript:void(0);" lay-event='order_info'>
                                    <i class="fa fa-file-text"></i> 订单详情
                                </a>
                            </li>
                            <li>
                                <a lay-event='marke' href="javascript:void(0);">
                                    <i class="fa fa-paste"></i> 订单备注
                                </a>
                            </li>
                            {{# if(parseFloat(d.actual_pay_price) > parseFloat(d.refund_price)){ }}
                            <li>
                                <a href="javascript:void(0);"
                                   onclick="$eb.createModalFrame('退款','{:Url('refund_y')}?id={{d.id}}')">
                                    <i class="fa fa-history"></i> 立即退款
                                </a>
                            </li>
                            {{# } }}
                            <li>
                                <a href="javascript:void(0);"
                                   onclick="$eb.createModalFrame('订单记录','{:Url('order_status')}?oid={{d.id}}')">
                                    <i class="fa fa-newspaper-o"></i> 订单记录
                                </a>
                            </li>
                        </ul>
                        {{#  }; }}
                    </script>
                </div>
            </div>
        </div>
    </div>
    <!--end-->
</div>
<script src="{__ADMIN_PATH}js/layuiList.js"></script>
    <script src="/static/plug/layui/layui.js"></script>
{/block}
{block name="script"}
<script>
    //实例化form
    layList.tableList('List', "{:Url('order_list',['real_name'=>$real_name])}", function () {
        return [
            {type: 'checkbox'},
            {field: 'order_id', title: '订单号', sort: true, event: 'order_id', width: '14%', templet: '#order_id'},
            {field: 'nickname', title: '用户信息', templet: '#userinfo', width: '10%', align: 'center'},
            {field: 'invite_info', title: '邀请人信息', templet: '#invite_info', width: '10%', align: 'center'},
            {field: 'info', title: '活动信息', templet: "#info", height: 'full-20'},
            {field: 'pay_type', title: '支付方式', templet: '#pay_type_name', width: '8%', align: 'center'},
            {field: 'pay_price', title: '支付信息', width: '8%', align: 'center',templet: '#payinfo'},
            {field: 'status', title: '订单状态', templet: '#status', width: '8%', align: 'center'},
            {field: 'add_time', title: '下单时间', width: '10%', sort: true, align: 'center'},
            {field: 'right', title: '操作', align: 'center', toolbar: '#act', width: '10%'},
        ];
    });
    layList.tool(function (event, data, obj) {
        switch (event) {
            case 'verify':
                var url = layList.U({c: 'market.store_order', a: 'verify', p: {id: data.id}});
                $eb.$swal('delete', function () {
                    $eb.axios.get(url).then(function (res) {
                        if (res.status == 200 && res.data.code == 200) {
                            $eb.$swal('success', res.data.msg);
                        } else
                            return Promise.reject(res.data.msg || '核销失败')
                    }).catch(function (err) {
                        $eb.$swal('error', err);
                    });
                }, {'title': '您确定核销该订单吗？', 'text': '修改后将无法恢复,请谨慎操作！', 'confirm': '是的，我要核销'})
                break;
            case 'marke':
                var url = layList.U({c: 'market.store_order', a: 'remark'}),
                    id = data.id,
                    make = data.remark;
                $eb.$alert('textarea', {title: '请修改内容', value: make}, function (result) {
                    if (result) {
                        $.ajax({
                            url: url,
                            data: 'remark=' + result + '&id=' + id,
                            type: 'post',
                            dataType: 'json',
                            success: function (res) {
                                if (res.code == 200) {
                                    $eb.$swal('success', res.msg);
                                } else
                                    $eb.$swal('error', res.msg);
                            }
                        })
                    } else {
                        $eb.$swal('error', '请输入要备注的内容');
                    }
                });
                break;
            case 'order_info':
                $eb.createModalFrame(data.nickname + '订单详情', layList.U({a: 'order_info', q: {oid: data.id}}));
                break;
        }
    })
    var action = {
        del_order: function () {
            var ids = layList.getCheckData().getIds('id');
            if (ids.length) {
                var url = layList.U({c: 'market.store_order', a: 'del_order'});
                $eb.$swal('delete', function () {
                    $eb.axios.post(url, {ids: ids}).then(function (res) {
                        if (res.status == 200 && res.data.code == 200) {
                            $eb.$swal('success', res.data.msg);
                        } else
                            return Promise.reject(res.data.msg || '删除失败')
                    }).catch(function (err) {
                        $eb.$swal('error', err);
                    });
                }, {'title': '您确定要修删除订单吗？', 'text': '删除后将无法恢复,请谨慎操作！', 'confirm': '是的，我要删除'})
            } else {
                layList.msg('请选择要删除的订单');
            }
        },
        write_order: function () {
            return $eb.createModalFrame('订单核销', layList.U({a: 'write_order'}), {w: 500, h: 400});
        },
    };
    $('#container-action').find('button').each(function () {
        $(this).on('click', function () {
            var act = $(this).data('type');
            action[act] && action[act]();
        });
    })
    //下拉框
    $(document).click(function (e) {
        $('.layui-nav-child').hide();
    })
    function dropdown(that) {
        var oEvent = arguments.callee.caller.arguments[0] || event;
        oEvent.stopPropagation();
        var offset = $(that).offset();
        var top = offset.top - $(window).scrollTop();
        var index = $(that).parents('tr').data('index');
        $('.layui-nav-child').each(function (key) {
            if (key != index) {
                $(this).hide();
            }
        })
        if ($(document).height() < top + $(that).next('ul').height()) {
            $(that).next('ul').css({
                'padding': 10,
                'top': -($(that).parents('td').height() / 2 + $(that).height() + $(that).next('ul').height() / 2),
                'min-width': 'inherit',
                'position': 'absolute'
            }).toggle();
        } else {
            $(that).next('ul').css({
                'padding': 10,
                'top': $(that).parents('td').height() / 2 + $(that).height(),
                'min-width': 'inherit',
                'position': 'absolute'
            }).toggle();
        }
    }

    var real_name = '<?=$real_name?>';
    var activityList =<?=json_encode($activityList)?>;
    var orderCount =<?=json_encode($orderCount)?>, payTypeCount =<?=json_encode($payTypeCount)?>,
        status =<?=$status ? $status : "''"?>;
    require(['vue'], function (Vue) {
        new Vue({
            el: "#app",
            data: {
                checkList: [0],
                badge: [],
                payType: [
                    {name: '全部', value: ''},
                    {name: '微信支付', value: 1, count: payTypeCount.weixin},
                    {name: '余额支付', value: 2, count: payTypeCount.yue},
                ],
                orderType: [
                    {name: '全部', value: ''},
                    {name: '押金', value: 1, count: orderCount.deposit},
                    {name: '全款', value: 2, count: orderCount.full_amount},
                    {name: '预报名', value: 3, count: orderCount.advance_amount},
                ],
                orderStatus: [
                    {name: '全部', value: ''},
                    {name: '待核销', value: 1, count: orderCount.to_write_off, class: true},
                    {name: '待付押金', value: 3, count: orderCount.be_paid_deposit, class: true},
                    {name: '待付款', value: 0, count: orderCount.pending_payment, class: true},
                    {name: '已完成', value: 2, count: orderCount.completed},
                    {name: '退款中', value: -1, count: orderCount.tk, class: true},
                    {name: '已退款', value: -2, count: orderCount.yt},
                    {name: '已删除', value: -3, count: orderCount.del},
                ],
                dataList: [
                    {name: '全部', value: ''},
                    {name: '今天', value: 'today'},
                    {name: '昨天', value: 'yesterday'},
                    {name: '最近7天', value: 'lately7'},
                    {name: '最近30天', value: 'lately30'},
                    {name: '本月', value: 'month'},
                    {name: '本年', value: 'year'},
                ],
                activityList: activityList,
                where: {
                    data: '',
                    status: status,
                    order_type: '',
                    pay_type: '',
                    activity_id: '',
                    real_name: real_name || '',
                    excel: 0,
                },
                showtime: false,
            },
            watch: {
                'where.status': function () {
                    this.where.excel = 0;
                    layList.reload(this.where, true);
                },
                'where.data': function () {
                    this.where.excel = 0;
                    layList.reload(this.where, true);
                },
                'where.order_type': function () {
                    this.where.excel = 0;
                    layList.reload(this.where, true);
                },
                'where.pay_type': function () {
                    this.where.excel = 0;
                    layList.reload(this.where, true);
                },
                // 'where.activity_id': function () {
                //     this.where.excel = 0;
                //     layList.reload(this.where, true);
                // }
            },
            methods: {
                setData: function (item) {
                    var that = this;
                    if (item.is_zd == true) {
                        that.showtime = true;
                        this.where.data = this.$refs.date_time.innerText;
                    } else {
                        this.showtime = false;
                        this.where.data = item.value;
                    }
                },
                search: function () {
                    this.where.excel = 0;
                    layList.reload(this.where, true);
                },
                refresh: function () {
                    layList.reload();
                },
                excel: function () {
                    this.where.excel = 1;
                    location.href = layList.U({c: 'market.store_order', a: 'order_list', q: this.where});
                    this.where.excel = 0;
                },
                handleBtn: function (item,index) {
                    var that = this;
                   if(item == 0){
                        this.checkList = [0];
                        that.where.activity_id = [];
                        that.where.excel = 0;
                        layList.reload(that.where, true);
                   }else {
                        if (this.checkList == [0]) {
                            this.checkList.splice(this.checkList.indexOf(0), 1); //取消
                        }
                        if (this.checkList.indexOf(item) !== -1) {
                            this.checkList.splice(this.checkList.indexOf(item), 1); //取消
                            that.where.activity_id = that.checkList;
                            that.where.excel = 0;
                            layList.reload(that.where, true);
                        } else {
                          this.checkList.push(item);//选中添加到数组里
                            that.where.activity_id = that.checkList;
                            that.where.excel = 0;
                            layList.reload(that.where, true);
                        }
                   }
                },
            },
            mounted: function () {
                var that = this;
                window.formReload = this.search;
                layList.laydate.render({
                    elem: this.$refs.date_time,
                    trigger: 'click',
                    eventElem: this.$refs.time,
                    range: true,
                    change: function (value) {
                        that.where.data = value;
                    }
                });
                layui.use('form', function(){
                  var form = layui.form;
                  //监听提交
                  form.on('select(activity_id)', function(data){
                    that.where.activity_id = data.value;
                    that.where.excel = 0;
                    layList.reload(that.where, true);
                  });
                });
            }
        })
    });
</script>
{/block}