<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <link href="{__FRAME_PATH}css/font-awesome.min.css" rel="stylesheet">
  <link href="{__ADMIN_PATH}plug/umeditor/themes/default/css/umeditor.css" type="text/css" rel="stylesheet">
  <script type="text/javascript" src="{__ADMIN_PATH}plug/umeditor/third-party/jquery.min.js"></script>
  <script type="text/javascript" src="{__ADMIN_PATH}plug/umeditor/third-party/template.min.js"></script>
  <script type="text/javascript" charset="utf-8" src="{__ADMIN_PATH}plug/umeditor/umeditor.config.js"></script>
  <script type="text/javascript" charset="utf-8" src="{__ADMIN_PATH}plug/umeditor/umeditor.min.js"></script>
  <script type="text/javascript" src="{__ADMIN_PATH}plug/umeditor/lang/zh-cn/zh-cn.js"></script>
  <link rel="stylesheet" href="/static/plug/layui/css/layui.css">
  <script src="/static/plug/layui/layui.js"></script>
  <script src="{__PLUG_PATH}vue/dist/vue.min.js"></script>
  <script src="/static/plug/axios.min.js"></script>
  <script src="/static/plug/iview/dist/iview.min.js"></script>
  <script src="{__MODULE_PATH}widget/aliyun-oss-sdk-4.4.4.min.js"></script>
  <script src="{__MODULE_PATH}widget/cos-js-sdk-v5.min.js"></script>
  <script src="{__MODULE_PATH}widget/qiniu-js-sdk-2.5.5.js"></script>
  <script src="{__MODULE_PATH}widget/plupload.full.min.js"></script>
  <script src="{__MODULE_PATH}widget/videoUpload.js"></script>
  <script type="text/javascript">
    $eb = parent._mpApi;
    window.controlle = "<?php echo strtolower(trim(preg_replace("/[A-Z]/", "_\\0", app('request')->controller()), "_")); ?>";
    window.module = "6GqvmHa8HRGHoQEQ";
  </script>
  <style>
    body,
    div,
    ul,
    li,
    p {
      margin: 0;
      padding: 0;
      list-style: none;
    }

    body {
      padding-top: 20px;
    }

    .flex {
      display: flex;
      justify-content: center;
      align-content: center;
    }

    .btn-box {
      margin: 0 20px;
      padding-top: 100px;

      button {
        padding: 10px 20px;
        border: 1px solid #a4a4a4;
        border-radius: 5px;
        background: #eee;
      }

      .json-btn {
        margin-bottom: 20px;
      }
    }

    .code-btn-box {
      text-align: right;
      margin-top: 20px;

      button {
        padding: 10px 20px;
        border: 1px solid #a4a4a4;
        border-radius: 5px;
        background: #eee;
      }

      .save-btn {
        border: 1px #f60 solid;
        background: #ffc88b;
        color: #f15214;
        margin-left: 20px;
      }
    }

    .layui-form-item {
      margin-bottom: 0px;
    }

    .layui-form-item .special-label {
      width: 50px;
      float: left;
      height: 30px;
      line-height: 38px;
      margin-left: 10px;
      margin-top: 5px;
      border-radius: 5px;
      background-color: #0092DC;
      text-align: center;
    }

    .layui-form-item .special-label i {
      display: inline-block;
      width: 18px;
      height: 18px;
      font-size: 18px;
      color: #fff;
    }

    .layui-form-item .label-box {
      border: 1px solid;
      border-radius: 10px;
      position: relative;
      padding: 10px;
      height: 30px;
      color: #fff;
      background-color: #393D49;
      text-align: center;
      cursor: pointer;
      display: inline-block;
      line-height: 10px;
    }

    .layui-form-item .label-box p {
      line-height: inherit;
    }


    .pictrueBox {
      display: inline-block !important;
    }

    .deleteArr {
      margin-left: 15px;
      margin-top: 18px;
      cursor: pointer;
      color: #999;
    }

    .pictrue {
      width: 60px;
      height: 60px;
      border: 1px dotted rgba(0, 0, 0, 0.1);
      margin-right: 15px;
      display: inline-block;
      position: relative;
      cursor: pointer;
    }

    .pictrue img {
      width: 100%;
      height: 100%;
    }

    .upLoad {
      width: 58px;
      height: 58px;
      line-height: 58px;
      border: 1px dotted rgba(0, 0, 0, 0.1);
      border-radius: 4px;
      background: rgba(0, 0, 0, 0.02);
      cursor: pointer;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .rulesBox {
      display: flex;
      flex-wrap: wrap;
      margin-left: 10px;
    }

    .layui-tab-content {
      margin-top: 15px;
    }

    .ml110 {
      margin: 18px 0 4px 110px;
    }

    .rules {
      display: flex;
    }

    .rules-btn-sm {
      height: 30px;
      line-height: 30px;
      font-size: 12px;
      width: 109px;
    }

    .rules-btn-sm input {
      width: 79% !important;
      height: 84% !important;
      padding: 0 10px;
    }

    .ml10 {
      margin-left: 10px !important;
    }

    .ml40 {
      margin-left: 40px !important;
    }

    .closes {
      position: absolute;
      left: 86%;
      top: -18%;
    }

    .red {
      color: red;
    }

    .layui-input-block .layui-video-box {
      width: 22%;
      height: 180px;
      border-radius: 10px;
      background-color: #707070;
      margin-top: 10px;
      position: relative;
      overflow: hidden;
    }

    .layui-input-block .layui-video-box i {
      color: #fff;
      line-height: 180px;
      margin: 0 auto;
      width: 50px;
      height: 50px;
      display: inherit;
      font-size: 50px;
    }

    .layui-input-block .layui-video-box .mark {
      position: absolute;
      width: 100%;
      height: 30px;
      top: 0;
      background-color: rgba(0, 0, 0, .5);
      text-align: center;
    }

    .layui-form-select dl {
      z-index: 1015;
    }

    .store_box {
      display: flex;
    }

    .ivu-input {
      border-width: 0px !important;
      width: 100%;
      height: 36px;
    }

    .ivu-select-dropdown {
      z-index: 999;
      background: #fff;
    }

    .ivu-cascader-menu .ivu-cascader-menu-item-active {
      background-color: #f3f3f3;
      color: #2d8cf0;
    }

    .ivu-cascader-menu .ivu-cascader-menu-item {
      position: relative;
      padding-right: 24px;
      -webkit-transition: all .2s ease-in-out;
      transition: all .2s ease-in-out;
    }

    .ivu-cascader .ivu-cascader-menu-item {
      margin: 0;
      line-height: normal;
      padding: 7px 16px;
      clear: both;
      color: #495060;
      font-size: 12px !important;
      white-space: nowrap;
      list-style: none;
      cursor: pointer;
      -webkit-transition: background .2s ease-in-out;
      transition: background .2s ease-in-out;
    }

    .ivu-cascader-menu:last-child {
      border-right-color: transparent;
      margin-right: -1px;
    }

    .ivu-cascader-menu {
      display: inline-block;
      min-width: 100px;
      height: 180px;
      margin: 0;
      padding: 5px 0 !important;
      vertical-align: top;
      list-style: none;
      border-right: 1px solid #e9eaec;
      overflow: auto;
    }

    .info {
      color: #c9c9c9;
      padding-left: 10px;
      line-height: 30px;
    }
  </style>
</head>
<script type="text/javascript">
  window.controlle = "<?php echo strtolower(trim(preg_replace("/[A-Z]/", "_\\0", app('request')->controller()), "_")); ?>";
  window.module = "6GqvmHa8HRGHoQEQ";
</script>

<body>
  <div class="layui-fluid">
    <div class="layui-row layui-col-space15" id="app" v-cloak="">
      <div class="layui-card">
        <div class="layui-card-header">
          <span class="">{{id ? '运营活动-修改': '运营活动-添加' }}</span>
          <button style="margin-left: 20px" type="button" class="layui-btn layui-btn-primary layui-btn-xs" @click="goBack">返回列表</button>
        </div>
        <div class="layui-card-body">
          <form class="layui-form" action="" v-cloak="">
            <div class="layui-tab layui-tab-brief" lay-filter="docTabBrief">
              <ul class="layui-tab-title">
                <li class="layui-this" lay-id='1'>基础信息</li>
              </ul>
              <div class="layui-tab-content">
                <div class="layui-tab-item layui-show">
                  <div class="layui-row layui-col-space15">
                    <div class="layui-row layui-col-space15">
                      <div class="layui-col-xs12 layui-col-sm4 layui-col-md4">
                        <div class="grid-demo grid-demo-bg1">
                          <div class="layui-form-item">
                            <label class="layui-form-label">类型</label>
                            <div class="layui-input-block">
                              <input type="radio" name="type" lay-filter="type" value="1" title="线上征文" :checked="formData.type == 1 ? true : false">
                              <input type="radio" name="type" lay-filter="type" value="2" title="课程投票" :checked="formData.type == 2 ? true : false">
                              <input type="radio" name="type" lay-filter="type" value="3" title="课程预约" :checked="formData.type == 3 ? true : false">
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="layui-col-xs12 layui-col-sm7 layui-col-md12">
                      <div class="grid-demo grid-demo-bg1">
                        <div class="layui-form-item">
                          <label class="layui-form-label" style="width:auto;">活动名称</label>
                          <div class="layui-input-block">
                            <input type="text" name="title" lay-verify="title" autocomplete="off" placeholder="请输入活动名称" class="layui-input" v-model="formData.title" maxlength="100">
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="layui-col-xs12 layui-col-sm7 layui-col-md12">
                      <div class="grid-demo grid-demo-bg1">
                        <div class="layui-form-item">
                          <label class="layui-form-label">封面图</label>
                          <div class="pictrueBox">
                            <div class="pictrue" v-if="formData.image" @click="uploadImage('image')">
                              <img :src="formData.image">
                            </div>
                            <div class="upLoad" @click="uploadImage('image')" v-else>
                              <i class="layui-icon layui-icon-camera" class="iconfont" style="font-size: 26px;"></i>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                      <div class="grid-demo grid-demo-bg1">
                        <div class="layui-form-item">
                          <label class="layui-form-label">轮播图<br>(尺寸：750*1000)<i class="red">*</i></label>
                          <div class="pictrueBox pictrue" v-for="(item,index) in formData.slider_image">
                            <img :src="item">
                            <i class="layui-icon closes" @click="deleteImage('slider_image',index)">&#x1007</i>
                          </div>
                          <div class="pictrueBox">
                            <div class="upLoad" @click="uploadImage('slider_image')" v-if="formData.slider_image.length <= rule.slider_image.maxLength">
                              <i class="layui-icon layui-icon-camera" class="iconfont" style="font-size: 26px;"></i>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                      <div class="grid-demo grid-demo-bg1">
                        <div class="layui-form-item layui-form-text">
                          <label class="layui-form-label">活动主题<i class="red">*</i></label>
                          <div class="layui-input-block">
                            <textarea name="description" v-model="formData.description" placeholder="请输入活动奖励说明" class="layui-textarea"></textarea>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                      <div class="grid-demo grid-demo-bg1">
                        <div class="layui-form-item">
                          <label class="layui-form-label">活动说明<i class="red">*</i></label>
                          <div class="layui-input-block">
                            <textarea type="text/plain" name="content" id="myEditor" style="width:100%;">{{formData.content}}</textarea>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                      <div class="grid-demo grid-demo-bg1">
                        <div class="layui-form-item layui-form-text">
                          <label class="layui-form-label">引导加群文字提示<i class="red">*</i></label>
                          <div class="layui-input-block">
                            <input type="text" name="contact_title" autocomplete="off" placeholder="请输入引导加群文字提示" class="layui-input" v-model="formData.contact_title" maxlength="100">
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                      <div class="grid-demo grid-demo-bg1">
                        <div class="layui-form-item layui-form-text">
                          <label class="layui-form-label">联系方式：文字<i class="red">*</i></label>
                          <div class="layui-input-block">
                            <input type="text" name="contact_info" autocomplete="off" placeholder="请输入联系方式文字" class="layui-input" v-model="formData.contact_info" maxlength="100">
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="layui-col-xs12 layui-col-sm7 layui-col-md12">
                      <div class="grid-demo grid-demo-bg1">
                        <div class="layui-form-item">
                          <label class="layui-form-label">联系方式：二维码<i class="red">*</i></label>
                          <div class="pictrueBox">
                            <div class="pictrue" v-if="formData.contact_image" @click="uploadImage('contact_image')">
                              <img :src="formData.contact_image">
                            </div>
                            <div class="upLoad" @click="uploadImage('contact_image')" v-else>
                              <i class="layui-icon layui-icon-camera" class="iconfont" style="font-size: 26px;"></i>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                      <div class="grid-demo grid-demo-bg1">
                        <div class="layui-form-item layui-form-text">
                          <label class="layui-form-label">联系方式：文字<i class="red">*</i></label>
                          <div class="layui-input-block">
                            <input type="text" name="contact_info" autocomplete="off" placeholder="请输入联系方式文字" class="layui-input" v-model="formData.contact_info" maxlength="100">
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                      <div class="grid-demo grid-demo-bg1">
                        <div class="layui-form-item layui-form-text">
                          <label class="layui-form-label">提交按钮文字<i class="red">*</i></label>
                          <div class="layui-input-block">
                            <input type="text" name="submit_button_name" autocomplete="off" placeholder="请输入提交按钮文字" class="layui-input" v-model="formData.submit_button_name" maxlength="100">
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                      <div class="grid-demo grid-demo-bg1">
                        <div class="layui-form-item layui-form-text">
                          <label class="layui-form-label">问卷内容</label>
                          <div class="layui-input-block">
                            <div style="width: 100%;">
                              <div id="jsoneditor" style="width: 100%; height: 600px;"></div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12" v-show="formData.type == 3">
                      <div class="grid-demo grid-demo-bg1">
                        <div class="layui-form-item layui-form-text">
                          <label class="layui-form-label">消息模版跳转链接</label>
                          <div class="layui-input-block">
                            <input type="text" name="url" autocomplete="off" placeholder="请输入消息模版跳转链接" class="layui-input" v-model="formData.url" maxlength="100">
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="layui-row layui-col-space15">
                      <div class="layui-col-xs12 layui-col-sm6 layui-col-md6">
                        <div class="grid-demo grid-demo-bg1">
                          <div class="layui-form-item">
                            <label class="layui-form-label">是否显示<i class="red">*</i></label>
                            <div class="layui-input-block">
                              <input type="radio" name="is_show" lay-filter="is_show" value="1" title="是" :checked="formData.is_show == 1 ? true : false">
                              <input type="radio" name="is_show" lay-filter="is_show" value="0" title="否" :checked="formData.is_show == 0 ? true : false">
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="layui-row layui-col-space15">
                      <div class="layui-col-xs12 layui-col-sm6 layui-col-md6">
                        <div class="grid-demo grid-demo-bg1">
                          <div class="layui-form-item">
                            <label class="layui-form-label">是否展示参与用户<i class="red">*</i></label>
                            <div class="layui-input-block">
                              <input type="radio" name="is_partake_users_show" lay-filter="is_partake_users_show" value="1" title="是" :checked="formData.is_partake_users_show == 1 ? true : false">
                              <input type="radio" name="is_partake_users_show" lay-filter="is_partake_users_show" value="0" title="否" :checked="formData.is_partake_users_show == 0 ? true : false">
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="layui-row layui-col-space15">
                      <div class="layui-col-xs12 layui-col-sm6 layui-col-md6">
                        <div class="grid-demo grid-demo-bg1">
                          <div class="layui-form-item">
                            <label class="layui-form-label">是否展示评论<i class="red">*</i></label>
                            <div class="layui-input-block">
                              <input type="radio" name="is_post_show" lay-filter="is_post_show" value="1" title="是" :checked="formData.is_post_show == 1 ? true : false">
                              <input type="radio" name="is_post_show" lay-filter="is_post_show" value="0" title="否" :checked="formData.is_post_show == 0 ? true : false">
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="layui-row layui-col-space15" v-show="formData.type == 3">
                      <div class="layui-col-xs12 layui-col-sm6 layui-col-md6">
                        <div class="grid-demo grid-demo-bg1">
                          <div class="layui-form-item">
                            <label class="layui-form-label">是否发送消息<i class="red">*</i></label>
                            <div class="layui-input-block">
                              <input type="radio" name="is_push_message" lay-filter="is_push_message" value="1" title="是" :checked="formData.is_push_message == 1 ? true : false">
                              <input type="radio" name="is_push_message" lay-filter="is_push_message" value="0" title="否" :checked="formData.is_push_message == 0 ? true : false">
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="layui-row layui-col-space15">
                      <div class="layui-col-xs12 layui-col-sm4 layui-col-md4">
                        <div class="grid-demo grid-demo-bg1">
                          <div class="layui-form-item">
                            <label class="layui-form-label">活动开关<i class="red">*</i></label>
                            <div class="layui-input-block">
                              <input type="radio" name="status" lay-filter="status" value="1" title="开启" :checked="formData.status == 1 ? true : false">
                              <input type="radio" name="status" lay-filter="status" value="0" title="关闭" :checked="formData.status == 0 ? true : false">
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="layui-tab-content">
                <div class="layui-row layui-col-space15">
                  <div class="layui-col-xs12 layui-col-sm7 layui-col-md12">
                    <div class="grid-demo grid-demo-bg1">
                      <div class="layui-form-item" v-if="id">
                        <button class="layui-btn layui-btn-primary layui-btn-sm" id="submit" type="button" @click="handleSubmit()">保存</button>
                      </div>
                      <div class="layui-form-item" v-else>
                        <button class="layui-btn layui-btn-normal layui-btn-sm" id="submit" type="button" @click="handleSubmit()">提交</button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
  <script type="module">
    import {
      JSONEditor
    } from '{__ADMIN_PATH}plug/vanilla/jsoneditor.js'
    var id = "{$id}";
    new Vue({
      el: '#app',
      data: {
        id: id,
        //分类列表
        upload: {
          videoIng: false
        },
        formData: {
          type: 1,
          title: '',
          image: '',
          slider_image: [],
          push_message_data: '',
          content: '',
          contact_title: '',
          contact_info: '',
          submit_button_name: '我要投稿',
          url: '',
          contact_image: '',
          is_show: 1,
          is_post_show: 1,
          is_push_message: 1,
          is_partake_users_show: 1,
          status: 1,
        },
        //多属性header头
        formHeader: [],
        radioRule: ['type', 'status', 'is_show', 'is_post_show', 'is_push_message', 'is_partake_users_show'], //radio 当选规则
        rule: { //多图选择规则
          slider_image: {
            maxLength: 10
          }
        },
        progress: 0,
        um: null, //编译器实例化
        form: null, //layui.form
        layTabId: 1,
        ruleBool: id ? true : false,
      },
      watch: {
        'formData.type': function(n) {
          if (n == 1) {
            this.formData['submit_button_name'] = this.formData['submit_button_name'] != "" ? this.formData['submit_button_name'] : '我要投稿';
          }
          if (n == 2) {
            this.formData['submit_button_name'] = this.formData['submit_button_name'] != "" ? this.formData['submit_button_name'] : '发起心愿';
          }
          if (n == 3) {
            this.formData['submit_button_name'] = this.formData['submit_button_name'] != "" ? this.formData['submit_button_name'] : '我要预约';
          }
        },
      },
      methods: {
        back: function() {
          var that = this;
          layui.use(['element'], function() {
            layui.element.tabChange('docTabBrief', that.layTabId == 1 ? 1 : parseInt(that.layTabId) - 1);
          });
        },
        next: function() {
          var that = this;
          layui.use(['element'], function() {
            layui.element.tabChange('docTabBrief', that.layTabId == 5 ? 5 : parseInt(that.layTabId) + 1);
          });
        },
        goBack: function() {
          location.href = this.U({
            m: window.module,
            c: 'market.store_activity_online',
            a: 'index'
          });
        },
        U: function(opt) {
          var m = opt.m || 'admin',
            c = opt.c || window.controlle || '',
            a = opt.a || 'index',
            q = opt.q || '',
            p = opt.p || {};
          var params = Object.keys(p).map(function(key) {
            return key + '/' + p[key];
          }).join('/');
          var gets = Object.keys(q).map(function(key) {
            return key + '=' + q[key];
          }).join('&');

          return '/' + m + '/' + c + '/' + a + (params == '' ? '' : '/' + params) + (gets == '' ? '' : '?' + gets);
        },
        /**
         * 提示
         * */
        showMsg: function(msg, success) {
          $('#submit').removeAttr('disabled').text('提交');
          layui.use(['layer'], function() {
            layui.layer.msg(msg, success);
          });
        },
        /**
         * 获取运营活动信息
         * */
        getActivityInfo: function() {
          var that = this;
          that.requestGet(that.U({
            m: window.module,
            c: "market.store_activity_online",
            a: 'get_activity_info',
            q: {
              id: that.id
            }
          })).then(function(res) {
            var activityInfo = res.data.activityInfo || {};
            if (activityInfo.id && that.id) {
              that.$set(that, 'formData', activityInfo);
            }
            that.init();
          }).catch(function(res) {
            that.showMsg(res.msg);
          })
        },
        setedit: function(json) {
          var that = this;
          let content = {
            text: undefined,
            json: JSON.parse(json)
          };
          const editor = new JSONEditor({
            target: document.getElementById('jsoneditor'),
            props: {
              content,
              onChange: (updatedContent, previousContent, {
                contentErrors,
                patchResult
              }) => {
                if (!updatedContent.text) {
                  that.formData.push_message_data = JSON.stringify(updatedContent.json);
                } else {
                  that.formData.push_message_data = JSON.stringify(JSON.parse(updatedContent.text));
                }
              }
            }
          })
        },
        /**
         * 删除图片
         * */
        deleteImage: function(key, index) {
          var that = this;
          if (index != undefined) {
            that.formData[key].splice(index, 1);
            that.$set(that.formData, key, that.formData[key]);
          } else {
            that.$set(that.formData, key, '');
          }
        },
        createFrame: function(title, src, opt) {
          opt === undefined && (opt = {});
          var h = 0;
          if (window.innerHeight < 800 && window.innerHeight >= 700) {
            h = window.innerHeight - 50;
          } else if (window.innerHeight < 900 && window.innerHeight >= 800) {
            h = window.innerHeight - 100;
          } else if (window.innerHeight < 1000 && window.innerHeight >= 900) {
            h = window.innerHeight - 150;
          } else if (window.innerHeight >= 1000) {
            h = window.innerHeight - 200;
          } else {
            h = window.innerHeight;
          }
          var area = [(opt.w || window.innerWidth / 2) + 'px', (!opt.h || opt.h > h ? h : opt.h) + 'px'];
          layui.use('layer', function() {
            return layer.open({
              type: 2,
              title: title,
              area: area,
              fixed: false, //不固定
              maxmin: true,
              moveOut: false, //true  可以拖出窗外  false 只能在窗内拖
              anim: 5, //出场动画 isOutAnim bool 关闭动画
              offset: 'auto', //['100px','100px'],//'auto',//初始位置  ['100px','100px'] t[ 上 左]
              shade: 0, //遮罩
              resize: true, //是否允许拉伸
              content: src, //内容
              move: '.layui-layer-title'
            });
          });
        },
        changeIMG: function(name, value) {
          // alere
          if (this.getRule(name).maxLength !== undefined) {
            var that = this;
            value.map(function(v) {
              that.formData[name].push(v);
            });

            this.$set(this.formData, name, this.formData[name]);
          } else {
            if (name == 'batchAttr.pic') {
              this.batchAttr.pic = value;
            } else {
              if (name.indexOf('.') !== -1) {
                var key = name.split('.');
                if (key.length == 2) {
                  this.formData[key[0]][key[1]] = value;
                } else if (key.length == 3) {
                  this.formData[key[0]][key[1]][key[2]] = value;
                } else if (key.length == 4) {
                  this.$set(this.formData[key[0]][key[1]][key[2]], key[3], value)
                }
              } else {
                this.formData[name] = value;
              }
            }
          }
        },
        uploadImage: function(name) {
          return this.createFrame('选择图片', this.U({
            m: window.module,
            c: "widget.images",
            a: 'index',
            p: {
              fodder: name
            }
          }), {
            h: 545,
            w: 900
          });
        },
        openWindows: function(title, url, opt) {
          return this.createFrame(title, url, opt);
        },
        insertEditor: function(list) {
          this.um.execCommand('insertimage', list);
        },
        insertEditorVideo: function(src) {
          UM.getEditor('myEditor').focus();
          UM.getEditor('myEditor').execCommand('inserthtml', '<div><video style="width: 99%" src="' + src + '" class="video-ue" controls="controls" width="100"><source src="' + src + '"></source></video></div><br>');
        },
        insertEditorAudio: function(src) {
          UM.getEditor('myEditor').focus();
          UM.getEditor('myEditor').execCommand('inserthtml', '<div><audio style="width: 99%" src="' + src + '" class="audio-ue" controls="controls" width="100"><source src="' + src + '"></source></audio></div><br>');
        },
        getContent: function() {
          return this.um.getContent();
        },
        /**
         * 监听radio字段
         */
        eeventRadio: function() {
          var that = this;
          that.radioRule.map(function(val) {
            that.form.on('radio(' + val + ')', function(res) {
              that.formData[val] = res.value;
            });
          })
        },
        init: function() {
          var that = this;
          window.UMEDITOR_CONFIG.toolbar = [
            // 加入一个 test
            'source | undo redo | bold italic underline strikethrough | superscript subscript | forecolor backcolor | removeformat |',
            'insertorderedlist insertunorderedlist | selectall cleardoc paragraph | fontfamily fontsize',
            '| justifyleft justifycenter justifyright justifyjustify |',
            'link unlink | emotion selectimgs video  | map',
            '| horizontal print preview fullscreen', 'drafts', 'formula'
          ];
          UM.registerUI('selectimgs', function(name) {
            var me = this;
            var $btn = $.eduibutton({
              icon: 'image',
              click: function() {
                that.createFrame('选择图片', "{:Url('widget.images/index',['fodder'=>'editor'])}");
              },
              title: '选择图片'
            });

            this.addListener('selectionchange', function() {
              //切换为不可编辑时，把自己变灰
              var state = this.queryCommandState(name);
              $btn.edui().disabled(state == -1).active(state == 1)
            });
            return $btn;

          });
          UM.registerUI('video', function(name) {
            var me = this;
            var $btn = $.eduibutton({
              icon: 'video',
              click: function() {
                that.createFrame('选择音视频', "{:Url('widget.video/indexs',['fodder'=>'video'])}");
              },
              title: '选择音视频'
            });

            this.addListener('selectionchange', function() {
              //切换为不可编辑时，把自己变灰
              var state = this.queryCommandState(name);
              $btn.edui().disabled(state == -1).active(state == 1)
            });
            return $btn;

          });
          //实例化编辑器
          this.um = UM.getEditor('myEditor', {
            initialFrameWidth: '99%',
            initialFrameHeight: 400
          });
          this.um.setContent(that.formData.content);
          //实例化编辑器
          that.$nextTick(function() {
            layui.use(['form', 'element', 'table'], function() {
              that.form = layui.form;
              that.form.render();
              that.form.on('select(labelIndex)', function(data) {
                that.setLabelTable(parseInt(data.value), false)
              });
              layui.element.on('tab(docTabBrief)', function() {
                that.layTabId = this.getAttribute('lay-id');
              });
              that.eeventRadio();
              that.setedit(that.formData.push_message_data ? that.formData.push_message_data : '{}');
            });
          })
        },
        requestPost: function(url, data) {
          return new Promise(function(resolve, reject) {
            axios.post(url, data).then(function(res) {
              if (res.status == 200 && res.data.code == 200) {
                resolve(res.data)
              } else {
                reject(res.data);
              }
            }).catch(function(err) {
              reject({
                msg: err
              })
            });
          })
        },
        requestGet: function(url) {
          return new Promise(function(resolve, reject) {
            axios.get(url).then(function(res) {
              if (res.status == 200 && res.data.code == 200) {
                resolve(res.data)
              } else {
                reject(res.data);
              }
            }).catch(function(err) {
              reject({
                msg: err
              })
            });
          })
        },
        handleSubmit: function() {
          var that = this;
          // if (!that.formData.title) {
          //   return that.showMsg('请填写名称');
          // }
          if (!that.formData.image) {
            return that.showMsg('请填选择封面图');
          }
          if (!that.formData.slider_image.length) {
            return that.showMsg('请填选择轮播图');
          }
          if (!that.formData.description) {
            return that.showMsg('请填写：主题');
          }
          that.formData.content = that.getContent();
          if (!that.formData.content) {
            return that.showMsg('请填写说明');
          }
          if (!that.formData.description) {
            return that.showMsg('请填写：主题');
          }
          if (!that.formData.contact_title) {
            return that.showMsg('请填写：引导加群文字提示');
          }
          if (!that.formData.contact_info) {
            return that.showMsg('请填写：联系方式文字');
          }
          if (!that.formData.contact_image) {
            return that.showMsg('请上传：联系方式二维码');
          }
          $('#submit').attr('disabled', 'disabled').text('保存中...');
          that.requestPost(that.U({
            m: window.module,
            c: 'market.store_activity_online',
            a: 'save',
            p: {
              id: that.id
            }
          }), that.formData).then(function(res) {
            that.confirm();
          }).catch(function(res) {
            that.showMsg(res.msg);
          });
        },
        confirm: function() {
          var that = this;
          layui.use(['layer'], function() {
            var layer = layui.layer;
            layer.confirm(that.id ? '修改成功是否返回运营活动列表' : '添加成功是否返回运营活动列表', {
              btn: ['返回列表', that.id ? '继续修改' : '继续添加'] //按钮
            }, function() {
              location.href = that.U({
                m: window.module,
                c: 'market.store_activity_online',
                a: 'index'
              });
            }, function() {
              location.reload();
            });
          });
        },
        render: function() {
          this.$nextTick(function() {
            layui.use(['form'], function() {
              layui.form.render('select');
            });
          })
        },
        // 移动
        handleDragStart(e, item) {
          this.dragging = item;
        },
        handleDragEnd(e, item) {
          this.dragging = null
        },
        handleDragOver(e) {
          e.dataTransfer.dropEffect = 'move'
        },
        handleDragEnter(e, item) {
          e.dataTransfer.effectAllowed = 'move'
          if (item === this.dragging) {
            return
          }
          var newItems = [...this.formData.activity];
          var src = newItems.indexOf(this.dragging);
          var dst = newItems.indexOf(item);
          newItems.splice(dst, 0, ...newItems.splice(src, 1))
          this.formData.activity = newItems;
        },
        getRule: function(name) {
          return this.rule[name] || {};
        },
      },
      mounted: function() {
        var that = this;
        axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';
        that.getActivityInfo();
        window.$vm = that;
        window.changeIMG = that.changeIMG;
        window.insertEditor = that.insertEditor;
        window.insertEditorAudio = that.insertEditorAudio;
        window.insertEditorVideo = that.insertEditorVideo;
        $(that.$refs.filElem).change(function() {
          var inputFile = this.files[0];
          that.requestPost(that.U({
            m: window.module,
            c: "widget.video",
            a: 'get_signature'
          })).then(function(res) {
            AdminUpload.upload(res.data.uploadType, {
              token: res.data.uploadToken || '',
              file: inputFile,
              accessKeyId: res.data.accessKey || '',
              accessKeySecret: res.data.secretKey || '',
              bucketName: res.data.storageName || '',
              region: res.data.storageRegion || '',
              domain: res.data.domain || '',
              uploadIng: function(progress) {
                that.upload.videoIng = true;
                that.progress = progress;
              }
            }).then(function(res) {
              //成功
              that.$set(that.formData, 'video_link', res.url);
              that.progress = 0;
              that.upload.videoIng = false;
              return that.showMsg('上传成功');
            }).catch(function(err) {
              //失败
              console.info(err);
              return that.showMsg('上传错误请检查您的配置');
            });
          }).catch(function(res) {
            return that.showMsg(res.msg || '获取密钥失败,请检查您的配置');
          });
        })
      }
    });
  </script>
</body>

</html>