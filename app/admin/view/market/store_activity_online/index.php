{extend name="public/container"}
{block name="head_top"}
<script type="text/javascript" src="{__PLUG_PATH}jquery.downCount.js"></script>
{/block}
{block name="content"}
<div class="layui-fluid">
    <div class="layui-row layui-col-space15"  id="app">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">课程活动搜索</div>
                <div class="layui-card-body">
                    <form class="layui-form">
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">活动状态：</label>
                                <div class="layui-input-inline">
                                    <select name="status" lay-verify="status">
                                        <option value="">全部</option>
                                        <option value="1">开启</option>
                                        <option value="0">关闭</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">搜　　索：</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="store_name" lay-verify="store_name" style="width: 100%" autocomplete="off" placeholder="请输入活动名称,关键字" class="layui-input">
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">
                                <button class="layui-btn layui-btn-sm" lay-submit="" lay-filter="search" style="font-size:14px;line-height: 9px;">
                                    <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>搜索</button>
                            </label>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">运营活动列表</div>
                <div class="layui-card-body">
                    <div class="layui-btn-container">
                        <a class="layui-btn layui-btn-sm" onclick="$eb.createModalFrame(this.innerText,'{:Url('create')}',{h:700,w:1100});">添加</a>
                    </div>
                    <table class="layui-hide" id="activityList" lay-filter="activityList"></table>
                    <script type="text/html" id="status">
                        <input type='checkbox' name='status' lay-skin='switch' value="{{d.id}}" lay-filter='status' lay-text='开启|关闭'  {{ d.status == 1 ? 'checked' : '' }}>
                    </script>
                    <script type="text/html" id="type">
                        {{# if(d.type == 1){ }}
                            <span class="layui-badge layui-bg-blue">线上征文</span>
                        {{# }else if(d.type == 2){ }}
                            <span class="layui-badge layui-bg-blue">课程投票</span>
                        {{# }else if(d.type == 3){ }}
                            <span class="layui-badge layui-bg-blue">课程预约</span>
                        {{# } }}
                    </script>
                    <script type="text/html" id="barDemo">
                        <button type="button" class="layui-btn layui-btn-xs" onclick="$eb.createModalFrame('{{d.title}}-参与名单','{:Url('roster')}?id={{d.id}}',{h:1000,w:1400});"><i class="layui-icon layui-icon-util"></i>参与名单</button>
                        {{# if( d.type == 3 && d.is_push_message == 1 && d.is_admin_push_message_status == 0 ){ }}
                            <button type="button" class="layui-btn layui-btn-xs">
                                <a href="javascript:void(0);"  style="color: #fff;" lay-event='push_message'>
                                            <i class="fa fa-check-circle-o"></i> 推送
                                    </a>
                            </button>
                        {{# } }}
                        <button type="button" class="layui-btn layui-btn-xs" onclick="dropdown(this)">操作<span class="caret"></span></button>
                        <ul class="layui-nav-child layui-anim layui-anim-upbit">
                            <li>
                                <a href="javascript:void(0);" onclick="$eb.createModalFrame('{{d.title}}-编辑','{:Url('create')}?id={{d.id}}')"><i class="layui-icon layui-icon-edit"></i> 编辑</a>
                            </li>
                            <li>
                                <a href="javascript:void(0);" class="delstor" lay-event='delstor'><i class="layui-icon layui-icon-delete"></i> 删除</a>
                            </li>
                        </ul>
                    </script>
                </div>
            </div>
        </div>
    </div>
</div>
{/block}
{block name="script"}
<script src="{__ADMIN_PATH}js/layuiList.js"></script>
<script src="{__FRAME_PATH}js/content.min.js?v=1.0.0"></script>
<script>
    setTime();
    function setTime() {
        setTimeout(function () {
            $eb.axios.get("{:Url('get_activity_id')}").then(function(res){
                $.each(res.data.count,function (index,item) {
                    var time = $('.count-time-'+item).attr('data-time');
                    if(time != ''){
                        $('.count-time-'+item).downCount({
                            date: time,
                            offset: +8
                        });
                    }
                })
            }).catch(function(err){
                console.log(err);
            });
        },2000);
    }
</script>
<script>
    layList.form.render();
    layList.tableList('activityList',"{:Url('get_activity_list')}",function () {
        return [
            {field: 'id', title: 'ID', sort: true,width:'5%',event:'id'},
            {field: 'type', title: '类型',align:'center',width:'15%',templet: "#type"},
            {field: 'title', title: '活动名',align:'center',width:'30%'},
            {field: 'image', title: '封面', width: '20%',templet: '<p><img src="{{d.image}}" alt="{{d.title}}" class="open_image" data-image="{{d.image}}"></p>',align:'center'},
            {field: 'status', title: '状态',width:'10%',toolbar:"#status",align:'center'},
            {field: 'right', title: '操作',width:'20%', align: 'center', toolbar: '#barDemo'}
        ]
    });
    layList.tool(function (event,data,obj) {
        switch (event) {
            case 'push_message':
                var url = layList.U({c: 'market.store_activity_online', a: 'store_push_message', p: {id: data.id,status: data.status}});
                $eb.$swal('delete', function () {
                    $eb.axios.get(url).then(function (res) {
                        if (res.status == 200 && res.data.code == 200) {
                            $eb.$swal('success', res.data.msg);
                        } else
                            return Promise.reject(res.data.msg || '发送失败')
                    }).catch(function (err) {
                        $eb.$swal('error', err);
                    });
                }, {'title': '您确定要向用户发送课程已上架消息吗？', 'text': '发送后，用户将会收到课程，上线通知信息！', 'confirm': '是的，我要发送'})
                break;
            case 'delstor':
                var url=layList.U({c:'market.store_activity_online',a:'delete',q:{id:data.id}});
                $eb.$swal('delete',function(){
                    $eb.axios.get(url).then(function(res){
                        if(res.status == 200 && res.data.code == 200) {
                            $eb.$swal('success',res.data.msg);
                            obj.del();
                        }else
                            return Promise.reject(res.data.msg || '删除失败')
                    }).catch(function(err){
                        $eb.$swal('error',err);
                    });
                })
                break;
        }
    })
    $(document).click(function (e) {
        $('.layui-nav-child').hide();
    })
    function dropdown(that){
        var oEvent = arguments.callee.caller.arguments[0] || event;
        oEvent.stopPropagation();
        var offset = $(that).offset();
        var top=offset.top-$(window).scrollTop();
        var index = $(that).parents('tr').data('index');
        $('.layui-nav-child').each(function (key) {
            if (key != index) {
                $(this).hide();
            }
        })
        if($(document).height() < top+$(that).next('ul').height()){
            $(that).next('ul').css({
                'padding': 10,
                'top': - ($(that).parent('td').height() / 2 + $(that).height() + $(that).next('ul').height()/2),
                'min-width': 'inherit',
                'position': 'absolute'
            }).toggle();
        }else{
            $(that).next('ul').css({
                'padding': 10,
                'top':$(that).parent('td').height() / 2 + $(that).height(),
                'min-width': 'inherit',
                'position': 'absolute'
            }).toggle();
        }
    }
    layList.search('search',function(where){
        layList.reload(where);
        setTime();
    });
    layList.search('export',function(where){
        location.href=layList.U({c:'market.store_activity_online',a:'save_excel',q:{status:where.status,store_name:where.store_name}});
    })
    layList.switch('status',function (odj,value) {
        if(odj.elem.checked==true){
            layList.baseGet(layList.Url({c:'market.store_activity_online',a:'set_status',p:{status:1,id:value}}),function (res) {
                layList.msg(res.msg);
            });
        }else{
            layList.baseGet(layList.Url({c:'market.store_activity_online',a:'set_status',p:{status:0,id:value}}),function (res) {
                layList.msg(res.msg);
            });
        }
    });
    $(document).on('click',".open_image",function (e) {
        var image = $(this).data('image');
        $eb.openImage(image);
    });
</script>
{/block}
