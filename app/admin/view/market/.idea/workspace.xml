<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="e5425f5b-2351-4c1b-8c74-30d9c69e2935" name="Default Changelist" comment="">
      <change beforePath="$PROJECT_DIR$/../../controller/market/StoreCategory.php" beforeDir="false" afterPath="$PROJECT_DIR$/../../controller/market/StoreCategory.php" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../controller/market/StoreComment.php" beforeDir="false" afterPath="$PROJECT_DIR$/../../controller/market/StoreComment.php" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../controller/market/StoreOrder.php" beforeDir="false" afterPath="$PROJECT_DIR$/../../controller/market/StoreOrder.php" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/store_category/index.php" beforeDir="false" afterPath="$PROJECT_DIR$/store_category/index.php" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/store_comment/index.php" beforeDir="false" afterPath="$PROJECT_DIR$/store_comment/index.php" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/store_order/index.php" beforeDir="false" afterPath="$PROJECT_DIR$/store_order/index.php" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../public/system/js/layuiList.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../public/system/js/layuiList.js" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ComposerSettings">
    <execution>
      <executable />
    </execution>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/../../../.." />
  </component>
  <component name="ProjectId" id="1ubcdlw8Iut3BhqZnkwVC4N6AYY" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showExcludedFiles" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">
    <property name="RunOnceActivity.ShowReadmeOnStart" value="true" />
    <property name="WebServerToolWindowFactoryState" value="false" />
  </component>
  <component name="ServiceViewManager">
    <option name="viewStates">
      <list>
        <serviceView>
          <treeState>
            <expand />
            <select />
          </treeState>
        </serviceView>
      </list>
    </option>
  </component>
  <component name="SvnConfiguration">
    <configuration />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="e5425f5b-2351-4c1b-8c74-30d9c69e2935" name="Default Changelist" comment="" />
      <created>1624937819413</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1624937819413</updated>
      <workItem from="1624937822797" duration="10000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="1" />
  </component>
</project>