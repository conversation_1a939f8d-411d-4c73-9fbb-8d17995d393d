{extend name="public/container"}
{block name="head_top"}

{/block}
{block name="content"}
<style>
  .btn-outline {
    border: none;
  }

  .btn-outline:hover {
    background-color: #0e9aef;
    color: #fff;
  }

  .layui-form-item .layui-btn {
    margin-top: 5px;
    margin-right: 10px;
  }

  .layui-btn-primary {
    margin-right: 10px;
    margin-left: 0 !important;
  }

  label {
    margin-bottom: 0 !important;
    margin-top: 4px;
  }
</style>
<div class="layui-fluid">
  <div class="layui-row layui-col-space15" id="app">
    <!--搜索条件-->
    <div class="layui-col-md12">
      <div class="layui-card">
        <div class="layui-card-header">搜索条件</div>
        <div class="layui-card-body">
          <div class="layui-carousel layadmin-carousel layadmin-shortcut" lay-anim="" lay-indicator="inside" lay-arrow="none" style="background:none">
            <div class="layui-card-body layui-form">
              <div class="layui-row layui-col-space10 layui-form-item">
                <div class="layui-col-lg12">
                  <label class="layui-form-label">状态:</label>
                  <div class="layui-input-block" v-cloak="">
                    <button class="layui-btn layui-btn-sm" :class="{'layui-btn-primary':where.status!==item.value}" @click="where.status = item.value" type="button" v-for="item in status">{{item.name}}
                      <span v-if="item.count!=undefined" :class="item.class!=undefined ? 'layui-badge': 'layui-badge layui-bg-gray' ">{{item.count}}</span>
                    </button>
                  </div>
                </div>
                <div class="layui-col-lg12">
                  <label class="layui-form-label">创建时间:</label>
                  <div class="layui-input-block" data-type="data" v-cloak="">
                    <button class="layui-btn layui-btn-sm" type="button" v-for="item in dataList" @click="setData(item)" :class="{'layui-btn-primary':where.data!=item.value}">{{item.name}}
                    </button>
                    <button class="layui-btn layui-btn-sm" type="button" ref="time" @click="setData({value:'zd',is_zd:true})" :class="{'layui-btn-primary':where.data!='zd'}">自定义
                    </button>
                    <button type="button" class="layui-btn layui-btn-sm layui-btn-primary" v-show="showtime==true" ref="date_time">{$year.0} - {$year.1}
                    </button>
                  </div>
                </div>
                <div class="layui-form-item">
                  <label class="layui-form-label">在线活动名:</label>
                  <div class="layui-input-block" style="width: 496px">
                    <select name="activity_id" v-model="where.activity_id" lay-filter="activity_id" lay-search="">
                      <option value="">全部</option>
                      {volist name='activityList' id='vo'}
                      <option value="{$vo.id}">{$vo.title}-{$vo.description}</option>
                      {/volist}
                    </select>
                  </div>
                </div>
                <div class="layui-col-lg12">
                  <label class="layui-form-label">留言关键字:</label>
                  <div class="layui-input-block">
                    <input type="text" name="keywords" style="width: 50%" v-model="where.keywords" placeholder="请输入留言单名称，地址等关键字" class="layui-input">
                  </div>
                </div>
                <div class="layui-col-lg12">
                  <div class="layui-input-block">
                    <button @click="search" type="button" class="layui-btn layui-btn-sm layui-btn-normal">
                      <i class="layui-icon layui-icon-search"></i>搜索
                    </button>
                    <button @click="refresh" type="reset" class="layui-btn layui-btn-primary layui-btn-sm">
                      <i class="layui-icon layui-icon-refresh"></i>刷新
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!--列表-->
  <div class="layui-row layui-col-space15">
    <div class="layui-col-md12">
      <div class="layui-card">
        <div class="layui-card-header">留言列表</div>
        <div class="layui-card-body">
          <div class="layui-btn-container" id="container-action">
            <!-- <button class="layui-btn layui-btn-sm" class="btn btn-w-m btn-primary" onclick="$eb.createModalFrame(this.innerText,'{:Url('create')}',{w:1100,h:760})">添加留言</button> -->
            <!-- <button class="layui-btn layui-btn-sm" data-type="save_set_status">批量操作</button> -->
          </div>
          <table class="layui-hide" id="List" lay-filter="List"></table>
          <!--用户信息-->
          <script type="text/html" id="userinfo">
            <div class="layui-table-cell laytable-cell-1-0-1"> <img style="cursor: pointer" lay-event="open_image" src="{{ d.avatar }}"> </div>
            {{d.nickname==null ? '暂无信息':d.nickname}}/{{d.uid}}
          </script>
          <!--留言状态-->
          <script type="text/html" id="status">
          {{d.status_name}}
          </script>
          <!--显示|隐藏-->
          <script type="text/html" id="checkrefining">
            <input type='checkbox' name='id' lay-skin='switch' value="{{d.id}}" lay-filter='is_refining' lay-text='取消精华|设为精华' {{ d.is_refining == 1 ? 'checked' : '' }}>
          </script>
          <script type="text/html" id="act">
          {{# if( d.is_del == 1 ){ }}
            <button type="button" class="layui-btn layui-btn-xs layui-btn-normal" lay-event='show'>
              <a href="javascript:void(0);" style="color: #fff;" lay-event='review_restore'>
                <i class="fa"></i> 恢复
              </a>
            </button>
            {{# } }}
              {{# if( d.status == 2 && d.is_del == 0 ){ }}
                <button type="button" class="layui-btn layui-btn-xs layui-btn-normal" lay-event='show'>
                  <a href="javascript:void(0);" style="color: #fff;" lay-event='review'>
                    <i class="fa"></i> 审核
                  </a>
                </button>
                {{# } }}
                  </button>
                  <button type="button" class="layui-btn layui-btn-xs" onclick="dropdown(this)">操作 <span class="caret"></span></button>
                  <ul class="layui-nav-child layui-anim layui-anim-upbit">
                    <li>
                      <a href="javascript:void(0);" lay-event="message_info">
                        <i class="fa fa-file-text"></i> 详情
                      </a>
                    </li>
                    <!--                             <li>
                                <a href="javascript:void(0);" lay-event="edit">
                                    <i class="fa fa-edit"></i> 编辑
                                </a>
                            </li> -->
                    <li>
                      <a href="javascript:void(0);" lay-event="deleted">
                        <i class="fa fa-times"></i> 删除
                      </a>
                    </li>
                  </ul>
          </script>
        </div>
      </div>
    </div>
  </div>
  <!--end-->
</div>
<script src="{__ADMIN_PATH}js/layuiList.js"></script>
<script src="/static/plug/layui/layui.js"></script>
{/block}
{block name="script"}
<script>
  layList.tableList('List', "{:Url('message_list')}", function() {
    return [{
        type: 'checkbox'
      },
      {
        field: 'id',
        title: 'ID',
        width: '10%',
        align: 'center'
      },
      {
        field: 'user',
        title: '用户',
        templet: '#userinfo',
        width: '15%',
        align: 'center'
      },
      {
        field: 'title',
        title: '标题',
        width: '23%',
        align: 'center'
      },
      {
        field: 'browse',
        title: '浏览次数',
        width: '7%',
        align: 'center'
      },
      {
        field: 'is_refining',
        title: '是否精华',
        templet: "#checkrefining",
        width: '11%',
        align: 'center'
      },
      {
        field: 'status',
        title: '状态',
        templet: '#status',
        width: '7%',
        align: 'center'
      },
      {
        field: 'add_time',
        title: '提交时间',
        width: '10%',
        sort: true,
        align: 'center'
      },
      {
        field: 'right',
        title: '操作',
        align: 'center',
        toolbar: '#act',
        width: '14%'
      },
    ];
  });
  layList.tool(function(event, data, obj) {
    switch (event) {
      case 'edit':
        location.href = layList.U({
          a: 'edit',
          q: {
            id: data.id
          }
        });
        break;
      case 'deleted':
        var url = layList.U({
          c: 'market.store_activity_online_message',
          a: 'destroy',
          p: {
            id: data.id
          }
        });
        $eb.$swal('delete', function() {
          $eb.axios.get(url).then(function(res) {
            if (res.status == 200 && res.data.code == 200) {
              $eb.$swal('success', res.data.msg);
            } else
              return Promise.reject(res.data.msg || '删除失败')
          }).catch(function(err) {
            $eb.$swal('error', err);
          });
        }, {
          'title': '您确定要删除留言吗？',
          'text': '删除后将无法恢复,请谨慎操作！',
          'confirm': '是的，我要删除'
        })
        break;
      case 'review':
        var url = layList.U({
          c: 'market.store_activity_online_message',
          a: 'review',
          p: {
            id: data.id,
            status: data.status
          }
        });
        $eb.$swal('delete', function() {
          $eb.axios.get(url).then(function(res) {
            if (res.status == 200 && res.data.code == 200) {
              $eb.$swal('success', res.data.msg);
            } else
              return Promise.reject(res.data.msg || '审核失败')
          }).catch(function(err) {
            $eb.$swal('error', err);
          });
        }, {
          'title': '您确定审核该留言吗？',
          'text': '审核后，留言将在前台显示！',
          'confirm': '是的，我要审核'
        })
        break;
      case 'review_restore':
        var url = layList.U({
          c: 'market.store_activity_online_message',
          a: 'save_set_status',
          p: {
            id: data.id,
            field: 'is_del',
            values: 0
          }
        });
        $eb.$swal('delete', function() {
          $eb.axios.get(url).then(function(res) {
            if (res.status == 200 && res.data.code == 200) {
              $eb.$swal('success', res.data.msg);
            } else
              return Promise.reject(res.data.msg || '恢复失败')
          }).catch(function(err) {
            $eb.$swal('error', err);
          });
        }, {
          'title': '您确定恢复留言为已正常状态吗？',
          'text': '恢复后，留言状态为已正常状态！',
          'confirm': '是的，我要恢复'
        })
        break;
      case 'message_info':
        $eb.createModalFrame(data.title + '留言详情', layList.U({
          a: 'message_info',
          q: {
            id: data.id
          }
        }));
        break;
      case 'open_image':
        $eb.openImage(data.avatar);
        break;
    }
  })
  $('#container-action').find('button').each(function() {
    $(this).on('click', function() {
      var act = $(this).data('type');
      action[act] && action[act]();
    });
  })
  //下拉框
  $(document).click(function(e) {
    $('.layui-nav-child').hide();
  })
  //设置活动收费免费
  layList.switch('is_refining', function(odj, value) {
    if (odj.elem.checked == true) {
      layList.baseGet(layList.Url({
        c: 'market.store_activity_online_message',
        a: 'set_is_refining',
        p: {
          is_refining: 1,
          id: value
        }
      }), function(res) {
        layList.msg(res.msg, function() {
          layList.reload();
        });
      });
    } else {
      layList.baseGet(layList.Url({
        c: 'market.store_activity_online_message',
        a: 'set_is_refining',
        p: {
          is_refining: 0,
          id: value
        }
      }), function(res) {
        layList.msg(res.msg, function() {
          layList.reload();
        });
      });
    }
  });
  //自定义方法
  var action = {
    show: function() {
      var ids = layList.getCheckData().getIds('id');
      if (ids.length) {
        layList.basePost(layList.Url({
          c: 'market.store_activity_online_message',
          a: 'release'
        }), {
          ids: ids
        }, function(res) {
          layList.msg(res.msg);
          layList.reload();
        });
      } else {
        layList.msg('请选择要审核的留言');
      }
    },
    save_set_status: function() {
      var ids = layList.getCheckData().getIds('id');
      if (ids.length) {
        var str = ids.join(',');
        console.log(str);
        $eb.createModalFrame('批量设置状态', layList.Url({
          a: 'save_set_status',
          p: {
            id: str
          }
        }), {
          w: 500,
          h: 300
        });
      } else {
        layList.msg('请选择要批量设置状态的留言');
      }
    },
    refresh: function() {
      layList.reload();
    }
  };

  function dropdown(that) {
    var oEvent = arguments.callee.caller.arguments[0] || event;
    oEvent.stopPropagation();
    var offset = $(that).offset();
    var top = offset.top - $(window).scrollTop();
    var index = $(that).parents('tr').data('index');
    $('.layui-nav-child').each(function(key) {
      if (key != index) {
        $(this).hide();
      }
    })
    if ($(document).height() < top + $(that).next('ul').height()) {
      $(that).next('ul').css({
        'padding': 10,
        'top': -($(that).parents('td').height() / 2 + $(that).height() + $(that).next('ul').height() / 2),
        'min-width': 'inherit',
        'position': 'absolute'
      }).toggle();
    } else {
      $(that).next('ul').css({
        'padding': 10,
        'top': $(that).parents('td').height() / 2 + $(that).height(),
        'min-width': 'inherit',
        'position': 'absolute'
      }).toggle();
    }
  }
  var messageCount = <?= json_encode($messageCount) ?>,
    status = <?= $status ? $status : "''" ?>;
  require(['vue'], function(Vue) {
    new Vue({
      el: "#app",
      data: {
        badge: [],
        status: [{
            name: '全部',
            value: ''
          },
          {
            name: '已审核',
            value: 1,
            count: messageCount.reviewed
          },
          {
            name: '待审核',
            value: 2,
            count: messageCount.unreviewed,
            class: true
          },
          {
            name: '已删除',
            value: 3,
            count: messageCount.deleted,
            class: true
          }
        ],
        dataList: [{
            name: '全部',
            value: ''
          },
          {
            name: '今天',
            value: 'today'
          },
          {
            name: '昨天',
            value: 'yesterday'
          },
          {
            name: '最近7天',
            value: 'lately7'
          },
          {
            name: '最近30天',
            value: 'lately30'
          },
          {
            name: '本月',
            value: 'month'
          },
          {
            name: '本年',
            value: 'year'
          },
        ],
        where: {
          data: '',
          status: '',
          activity_id: '',
          keywords: '',
        },
        showtime: false,
      },
      watch: {
        'where.status': function() {
          layList.reload(this.where, true);
        },
        'where.data': function() {
          layList.reload(this.where, true);
        },
        'where.keywords': function() {
          layList.reload(this.where, true);
        }
      },
      methods: {
        setData: function(item) {
          var that = this;
          if (item.is_zd == true) {
            that.showtime = true;
            this.where.data = this.$refs.date_time.innerText;
          } else {
            this.showtime = false;
            this.where.data = item.value;
          }
        },
        search: function() {
          layList.reload(this.where, true);
        },
        refresh: function() {
          layList.reload();
        },
        excel: function() {
          this.where.excel = 1;
          location.href = layList.U({
            c: 'market.store_activity_online_message',
            a: 'message_list',
            q: this.where
          });
          this.where.excel = 0;
        },
        init: function() {
          var that = this;
          that.$nextTick(function() {
            layui.use(['form', 'element'], function() {
              that.form = layui.form;
              that.form.render();
              layui.element.on('tab(docTabBrief)', function() {
                that.layTabId = this.getAttribute('lay-id');
              });
            });
          })
        },
      },
      mounted: function() {
        this.init();
        var that = this;
        window.formReload = this.search;
        layList.laydate.render({
          elem: this.$refs.date_time,
          trigger: 'click',
          eventElem: this.$refs.time,
          range: true,
          change: function(value) {
            that.where.data = value;
          }
        });
        layui.use('form', function() {
          var form = layui.form;
          //监听提交
          form.on('select(activity_id)', function(data) {
            that.where.activity_id = data.value;
            that.where.excel = 0;
            layList.reload(that.where, true);
          });
        });
      }
    })
  });
</script>
{/block}