{extend name="public/container"}
{block name="head_top"}
<script src="{__PLUG_PATH}echarts.common.min.js"></script>
{/block}
{block name="content"}
<style>
    .layui-card {
        border-radius: 2px;
        background-color: #fff;
        box-shadow: 0 1px 2px 0 rgba(0,0,0,.05);
    }
    .statistics-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 2px 2px 0 0;
    }
    .stat-card {
        text-align: center;
        padding: 20px;
        border-radius: 4px;
        color: white;
        margin-bottom: 15px;
    }
    .stat-card.red { background: linear-gradient(135deg, #ff6b6b, #ee5a52); }
    .stat-card.lazur { background: linear-gradient(135deg, #1ab394, #138d75); }
    .stat-card.navy { background: linear-gradient(135deg, #1c84c6, #1a6fa0); }
    .stat-card.yellow { background: linear-gradient(135deg, #f8ac59, #f39c12); }
    
    .stat-value {
        font-size: 28px;
        font-weight: bold;
        margin-bottom: 5px;
    }
    .stat-name {
        font-size: 14px;
        opacity: 0.9;
    }
    .chart-container {
        height: 400px;
        margin: 20px 0;
    }
    .distribution-table {
        margin-top: 20px;
    }
    .distribution-table th {
        background-color: #f8f9fa;
        font-weight: 600;
    }
    .progress-bar {
        height: 8px;
        border-radius: 4px;
        overflow: hidden;
        background-color: #e9ecef;
    }
    .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #667eea, #764ba2);
        transition: width 0.3s ease;
    }
</style>

<div class="layui-fluid" id="app">
    <!-- 页面标题 -->
    <div class="layui-card">
        <div class="statistics-header">
            <h2><i class="fa fa-bar-chart"></i> 调查问卷统计分析</h2>
            <p style="margin: 10px 0 0 0; opacity: 0.9;">全面的问卷数据统计与分析报告</p>
        </div>
    </div>

    <!-- 筛选条件 -->
    <div class="layui-card">
        <div class="layui-card-body">
            <form class="layui-form layui-row layui-col-space10" lay-filter="search">
                <div class="layui-col-md4">
                    <label class="layui-form-label">时间范围</label>
                    <div class="layui-input-block">
                        <input type="text" name="data" value="{$where.data}" id="date-range" placeholder="选择时间范围" class="layui-input">
                    </div>
                </div>
                <div class="layui-col-md2">
                    <button type="button" class="layui-btn" onclick="search()">
                        <i class="layui-icon layui-icon-search"></i> 搜索
                    </button>
                </div>
                <div class="layui-col-md2">
                    <button type="button" class="layui-btn layui-btn-primary" onclick="refresh()">
                        <i class="layui-icon layui-icon-refresh"></i> 刷新
                    </button>
                </div>
                <div class="layui-col-md2">
                    <button type="button" class="layui-btn layui-btn-normal" onclick="exportData()">
                        <i class="layui-icon layui-icon-export"></i> 导出
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- 统计卡片 -->
    <div class="layui-row layui-col-space15">
        {volist name="header" id="item"}
        <div class="layui-col-md4">
            <div class="stat-card {$item.color}">
                <div class="stat-value">{$item.value}</div>
                <div class="stat-name">
                    <i class="fa {$item.class}"></i> {$item.name}
                </div>
            </div>
        </div>
        {/volist}
    </div>

    <!-- 趋势图表 -->
    <div class="layui-card">
        <div class="layui-card-header">
            <h3><i class="fa fa-line-chart"></i> 问卷统计趋势</h3>
        </div>
        <div class="layui-card-body">
            <div id="trendChart" class="chart-container"></div>
        </div>
    </div>

    <!-- 分布统计 -->
    <div class="layui-row layui-col-space15">
        <!-- 身份分布 -->
        <div class="layui-col-md6">
            <div class="layui-card">
                <div class="layui-card-header">
                    <h4><i class="fa fa-user-circle"></i> 身份分布</h4>
                </div>
                <div class="layui-card-body">
                    <div id="identityChart" style="height: 300px;"></div>
                    <div class="distribution-table">
                        <table class="layui-table">
                            <thead>
                                <tr>
                                    <th>身份</th>
                                    <th>数量</th>
                                    <th>占比</th>
                                    <th>进度</th>
                                </tr>
                            </thead>
                            <tbody>
                                {volist name="pollsterStats.identity_statistics" id="identity"}
                                <tr>
                                    <td>{$identity.identity}</td>
                                    <td>{$identity.count}</td>
                                    <td>{$identity.percentage}%</td>
                                    <td>
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: {$identity.percentage}%"></div>
                                        </div>
                                    </td>
                                </tr>
                                {/volist}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 性别分布 -->
        <div class="layui-col-md6">
            <div class="layui-card">
                <div class="layui-card-header">
                    <h4><i class="fa fa-venus-mars"></i> 性别分布</h4>
                </div>
                <div class="layui-card-body">
                    <div id="genderChart" style="height: 300px;"></div>
                    <div class="distribution-table">
                        <table class="layui-table">
                            <thead>
                                <tr>
                                    <th>性别</th>
                                    <th>数量</th>
                                    <th>占比</th>
                                    <th>进度</th>
                                </tr>
                            </thead>
                            <tbody>
                                {volist name="pollsterStats.gender_statistics" id="gender"}
                                <tr>
                                    <td>{$gender.gender}</td>
                                    <td>{$gender.count}</td>
                                    <td>{$gender.percentage}%</td>
                                    <td>
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: {$gender.percentage}%"></div>
                                        </div>
                                    </td>
                                </tr>
                                {/volist}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 年龄分布 -->
        <div class="layui-col-md6">
            <div class="layui-card">
                <div class="layui-card-header">
                    <h4><i class="fa fa-birthday-cake"></i> 年龄分布</h4>
                </div>
                <div class="layui-card-body">
                    <div id="ageChart" style="height: 300px;"></div>
                    <div class="distribution-table">
                        <table class="layui-table">
                            <thead>
                                <tr>
                                    <th>年龄段</th>
                                    <th>数量</th>
                                    <th>占比</th>
                                    <th>进度</th>
                                </tr>
                            </thead>
                            <tbody>
                                {volist name="pollsterStats.age_statistics" id="age"}
                                <tr>
                                    <td>{$age.age_range}</td>
                                    <td>{$age.count}</td>
                                    <td>{$age.percentage}%</td>
                                    <td>
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: {$age.percentage}%"></div>
                                        </div>
                                    </td>
                                </tr>
                                {/volist}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 地区分布 -->
        <div class="layui-col-md6">
            <div class="layui-card">
                <div class="layui-card-header">
                    <h4><i class="fa fa-map-marker"></i> 地区分布</h4>
                </div>
                <div class="layui-card-body">
                    <div id="regionChart" style="height: 300px;"></div>
                    <div class="distribution-table">
                        <table class="layui-table">
                            <thead>
                                <tr>
                                    <th>地区</th>
                                    <th>数量</th>
                                    <th>占比</th>
                                    <th>进度</th>
                                </tr>
                            </thead>
                            <tbody>
                                {volist name="pollsterStats.region_statistics" id="region"}
                                <tr>
                                    <td>{$region.region}</td>
                                    <td>{$region.count}</td>
                                    <td>{$region.percentage}%</td>
                                    <td>
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: {$region.percentage}%"></div>
                                        </div>
                                    </td>
                                </tr>
                                {/volist}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 渠道分布 -->
    <div class="layui-card">
        <div class="layui-card-header">
            <h4><i class="fa fa-share-alt"></i> 渠道来源分布</h4>
        </div>
        <div class="layui-card-body">
            <div class="layui-row">
                <div class="layui-col-md6">
                    <div id="channelChart" style="height: 300px;"></div>
                </div>
                <div class="layui-col-md6">
                    <div class="distribution-table">
                        <table class="layui-table">
                            <thead>
                                <tr>
                                    <th>渠道</th>
                                    <th>数量</th>
                                    <th>占比</th>
                                    <th>进度</th>
                                </tr>
                            </thead>
                            <tbody>
                                {volist name="pollsterStats.channel_statistics" id="channel"}
                                <tr>
                                    <td>{$channel.channel}</td>
                                    <td>{$channel.count}</td>
                                    <td>{$channel.percentage}%</td>
                                    <td>
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: {$channel.percentage}%"></div>
                                        </div>
                                    </td>
                                </tr>
                                {/volist}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 填写来源分布 -->
    <div class="layui-card">
        <div class="layui-card-header">
            <h4><i class="fa fa-link"></i> 填写来源分布</h4>
        </div>
        <div class="layui-card-body">
            <div class="layui-row">
                <div class="layui-col-md6">
                    <div id="sourceChart" style="height: 300px;"></div>
                </div>
                <div class="layui-col-md6">
                    <div class="distribution-table">
                        <table class="layui-table">
                            <thead>
                                <tr>
                                    <th>来源</th>
                                    <th>数量</th>
                                    <th>占比</th>
                                    <th>进度</th>
                                </tr>
                            </thead>
                            <tbody>
                                {volist name="pollsterStats.source_statistics" id="source"}
                                <tr>
                                    <td>{$source.source}</td>
                                    <td>{$source.count}</td>
                                    <td>{$source.percentage}%</td>
                                    <td>
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: {$source.percentage}%"></div>
                                        </div>
                                    </td>
                                </tr>
                                {/volist}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // 初始化图表
    layui.use(['laydate', 'element'], function(){
        var laydate = layui.laydate;
        
        // 日期选择器
        laydate.render({
            elem: '#date-range',
            range: true,
            format: 'yyyy-MM-dd'
        });
        
        // 初始化所有图表
        initCharts();
    });
    
    function initCharts() {
        // 趋势图表
        var trendChart = echarts.init(document.getElementById('trendChart'));
        var trendOption = {
            title: {
                text: '问卷统计趋势'
            },
            tooltip: {
                trigger: 'axis'
            },
            legend: {
                data: ['问卷总数', '参与用户', '完成率']
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: {$trendDays|json_encode|raw}
            },
            yAxis: {
                type: 'value'
            },
            series: {$Statistic|json_encode|raw}
        };
        trendChart.setOption(trendOption);
        
        // 身份分布饼图
        var identityChart = echarts.init(document.getElementById('identityChart'));
        var identityOption = {
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            series: [{
                name: '身份分布',
                type: 'pie',
                radius: '70%',
                data: {$identityData|json_encode|raw},
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }]
        };
        identityChart.setOption(identityOption);
        
        // 性别分布饼图
        var genderChart = echarts.init(document.getElementById('genderChart'));
        var genderOption = {
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            series: [{
                name: '性别分布',
                type: 'pie',
                radius: '70%',
                data: {$genderData|json_encode|raw},
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }]
        };
        genderChart.setOption(genderOption);
        
        // 年龄分布饼图
        var ageChart = echarts.init(document.getElementById('ageChart'));
        var ageOption = {
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            series: [{
                name: '年龄分布',
                type: 'pie',
                radius: '70%',
                data: {$ageData|json_encode|raw},
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }]
        };
        ageChart.setOption(ageOption);
        
        // 地区分布饼图
        var regionChart = echarts.init(document.getElementById('regionChart'));
        var regionOption = {
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            series: [{
                name: '地区分布',
                type: 'pie',
                radius: '70%',
                data: {$regionData|json_encode|raw},
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }]
        };
        regionChart.setOption(regionOption);
        
        // 渠道分布饼图
        var channelChart = echarts.init(document.getElementById('channelChart'));
        var channelOption = {
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            series: [{
                name: '渠道分布',
                type: 'pie',
                radius: '70%',
                data: {$channelData|json_encode|raw},
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }]
        };
        channelChart.setOption(channelOption);
        
        // 来源分布饼图
        var sourceChart = echarts.init(document.getElementById('sourceChart'));
        var sourceOption = {
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            series: [{
                name: '来源分布',
                type: 'pie',
                radius: '70%',
                data: {$sourceData|json_encode|raw},
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }]
        };
        sourceChart.setOption(sourceOption);
        
        // 响应式处理
        window.addEventListener('resize', function() {
            trendChart.resize();
            identityChart.resize();
            genderChart.resize();
            ageChart.resize();
            regionChart.resize();
            channelChart.resize();
            sourceChart.resize();
        });
    }
    
    // 搜索功能
    function search() {
        var data = $('#date-range').val();
        window.location.href = '{:url("pollster_index")}?data=' + encodeURIComponent(data);
    }
    
    // 刷新功能
    function refresh() {
        window.location.href = '{:url("pollster_index")}';
    }
    
    // 导出功能
    function exportData() {
        var data = $('#date-range').val();
        window.location.href = '{:url("pollster_index")}?export=1&data=' + encodeURIComponent(data);
    }
</script>
{/block}