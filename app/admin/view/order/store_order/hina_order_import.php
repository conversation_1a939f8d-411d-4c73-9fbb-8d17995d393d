<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="referrer" content="never">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link href="{__FRAME_PATH}css/font-awesome.min.css" rel="stylesheet">
    <link href="{__ADMIN_PATH}plug/umeditor/themes/default/css/umeditor.css" type="text/css" rel="stylesheet">
    <script type="text/javascript" src="{__ADMIN_PATH}plug/umeditor/third-party/jquery.min.js"></script>
    <script type="text/javascript" src="{__ADMIN_PATH}plug/umeditor/third-party/template.min.js"></script>
    <script type="text/javascript" charset="utf-8" src="{__ADMIN_PATH}plug/umeditor/umeditor.config.js"></script>
    <script type="text/javascript" charset="utf-8" src="{__ADMIN_PATH}plug/umeditor/umeditor.min.js"></script>
    <script type="text/javascript" src="{__ADMIN_PATH}plug/umeditor/lang/zh-cn/zh-cn.js"></script>
    <link rel="stylesheet" href="/static/plug/layui/css/layui.css">
    <script src="/static/plug/layui/layui.js"></script>
    <script src="{__PLUG_PATH}vue/dist/vue.min.js"></script>
    <script src="/static/plug/axios.min.js"></script>
    <script src="/static/plug/iview/dist/iview.min.js"></script>
    <script src="{__MODULE_PATH}widget/aliyun-oss-sdk-4.4.4.min.js"></script>
    <script src="{__MODULE_PATH}widget/cos-js-sdk-v5.min.js"></script>
    <script src="{__MODULE_PATH}widget/qiniu-js-sdk-2.5.5.js"></script>
    <script src="{__MODULE_PATH}widget/plupload.full.min.js"></script>
    <script src="{__MODULE_PATH}widget/videoUpload.js"></script>
    <script type="text/javascript">
        $eb = parent._mpApi;
        window.controlle="<?php echo strtolower(trim(preg_replace("/[A-Z]/", "_\\0", app('request')->controller()), "_"));?>";
        window.module="6GqvmHa8HRGHoQEQ";
    </script>
    <style>
        .layui-form-item {
            margin-bottom: 0px;
        }

        .layui-form-item .special-label {
            width: 50px;
            float: left;
            height: 30px;
            line-height: 38px;
            margin-left: 10px;
            margin-top: 5px;
            border-radius: 5px;
            background-color: #0092DC;
            text-align: center;
        }

        .layui-form-item .special-label i {
            display: inline-block;
            width: 18px;
            height: 18px;
            font-size: 18px;
            color: #fff;
        }

        .layui-form-item .label-box {
            border: 1px solid;
            border-radius: 10px;
            position: relative;
            padding: 10px;
            height: 30px;
            color: #fff;
            background-color: #393D49;
            text-align: center;
            cursor: pointer;
            display: inline-block;
            line-height: 10px;
        }

        .layui-form-item .label-box p {
            line-height: inherit;
        }

        .pictrueBox {
            display: inline-block !important;
        }

        .pictrue {
            width: 60px;
        height: 60px;
            border: 1px dotted rgba(0, 0, 0, 0.1);
            margin-right: 15px;
            display: inline-block;
            position: relative;
            cursor: pointer;
        }

        .pictrue img {
            width: 100%;
            height: 100%;
        }

        .upLoad {
            width: 58px;
            height: 58px;
            line-height: 58px;
            border: 1px dotted rgba(0, 0, 0, 0.1);
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.02);
            cursor: pointer;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .rulesBox {
            display: flex;
            flex-wrap: wrap;
            margin-left: 10px;
        }

        .layui-tab-content {
            margin-top: 15px;
        }

        .ml110 {
            margin: 18px 0 4px 110px;
        }

        .rules {
            display: flex;
        }

        .rules-btn-sm {
            height: 30px;
            line-height: 30px;
            font-size: 12px;
            width: 109px;
        }

        .rules-btn-sm input {
            width: 79% !important;
            height: 84% !important;
            padding: 0 10px;
        }

        .ml10 {
            margin-left: 10px !important;
        }

        .ml40 {
            margin-left: 40px !important;
        }

        .closes {
            position: absolute;
            left: 86%;
            top: -18%;
        }
        .red {
            color: red;
        }
        .layui-input-block .layui-video-box{
            width: 22%;
            height: 180px;
            border-radius: 10px;
            background-color: #707070;
            margin-top: 10px;
            position: relative;
            overflow: hidden;
        }
        .layui-input-block .layui-video-box i{
            color: #fff;
            line-height: 180px;
            margin: 0 auto;
            width: 50px;
            height: 50px;
            display: inherit;
            font-size: 50px;
        }
        .layui-input-block .layui-video-box .mark{
            position: absolute;
            width: 100%;
            height: 30px;
            top: 0;
            background-color: rgba(0,0,0,.5);
            text-align: center;
        }
        .layui-input-block .layui-audio-box{
            width: 50%;
            height: 54px;
            border-radius: 10px;
            background-color: #707070;
            margin-top: 10px;
            position: relative;
            overflow: hidden;
        }
        .layui-input-block .layui-audio-box i{
            color: #fff;
            line-height: 180px;
            margin: 0 auto;
            width: 50px;
            height: 50px;
            display: inherit;
            font-size: 50px;
        }
        .layui-input-block .layui-audio-box .mark{
            width: 11%;
            height: 30px;
            top: 0px;
            background-color: rgba(0,0,0,.5);
            text-align: center;
            float: right;
            margin-right: 10px;
            margin-top: 11px;
        }
        .layui-form-select dl {
            z-index: 1015;
        }
        .store_box{
            display: flex;
        }
        .ivu-input{
             border-width: 0px !important;
             width: 100%;
             height: 36px;
         }
         .ivu-select-dropdown{
             z-index: 999;
             background: #fff;
         }
         .ivu-cascader-menu .ivu-cascader-menu-item-active {
             background-color: #f3f3f3;
             color: #2d8cf0;
         }
         .ivu-cascader-menu .ivu-cascader-menu-item {
             position: relative;
             padding-right: 24px;
             -webkit-transition: all .2s ease-in-out;
             transition: all .2s ease-in-out;
         }
         .ivu-cascader .ivu-cascader-menu-item {
             margin: 0;
             line-height: normal;
             padding: 7px 16px;
             clear: both;
             color: #495060;
             font-size: 12px!important;
             white-space: nowrap;
             list-style: none;
             cursor: pointer;
             -webkit-transition: background .2s ease-in-out;
             transition: background .2s ease-in-out;
         }
         .ivu-cascader-menu:last-child {
             border-right-color: transparent;
             margin-right: -1px;
         }
         .ivu-cascader-menu {
             display: inline-block;
             min-width: 100px;
             height: 180px;
             margin: 0;
             padding: 5px 0!important;
             vertical-align: top;
             list-style: none;
             border-right: 1px solid #e9eaec;
             overflow: auto;
                 }
        .info{
            color: #c9c9c9;
            padding-left: 10px;
            line-height: 30px;
        }

        .video_wrap{
           width: 600px;position: relative;
        }
        .jiqu{
           position: absolute;
           top: 50%;
           z-index: 999;top: 10px;right:10px;width: 60px;text-align: center;
           background: rgb(0,0,0,0.8);
           color: #fff;
           font-size: 20px;
           height: 60px;
           line-height: 60px;
           cursor: pointer;
           /* display: none; */
        }
        .video_wrap:hover .jiqu{
            display: block;
        }
    </style>
</head>
<script type="text/javascript">
    window.controlle="<?php echo strtolower(trim(preg_replace("/[A-Z]/", "_\\0", app('request')->controller()), "_"));?>";
    window.module="6GqvmHa8HRGHoQEQ";
</script>
<body>
<div class="layui-fluid">
    <div class="layui-row layui-col-space15"  id="app" v-cloak="">
        <div class="layui-card">
            <div class="layui-card-header">
                <span class="">导入发货订单文件</span>
                <button style="margin-left: 20px" type="button" class="layui-btn layui-btn-primary layui-btn-xs" @click="goBack">返回列表</button>
            </div>
            <div class="layui-card-body">
                <form class="layui-form" action="" v-cloak="">
                    <div class="layui-tab layui-tab-brief" lay-filter="docTabBrief">
                        <ul class="layui-tab-title">
                            <li class="layui-this" lay-id='1'>导入</li>
                        </ul>
                        <div class="layui-tab-content">
                            <div class="layui-tab-item layui-show">
	                            <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
	                                <label class="layui-form-label">文档<i class="red">*</i></label>
	                                <div class="layui-input-block">
	                                    <input type="text" name="title" v-model="formData.file_link" style="width:60%;display:inline-block;margin-right: 10px;" autocomplete="off" placeholder="请点击上传文档按钮，选择文档文件上传" class="layui-input">
	                                    <button type="button" @click="uploadFile" class="layui-btn layui-btn-sm layui-btn-normal">{{fileLink ? '确认添加' : '上传文档'}}</button>
	                                    <input ref="documentFilElem" type="file" style="display: none">
	                                </div>
	                                <div class="layui-input-block audio_show" style="width: 30%;margin-top: 20px;" v-if="upload.fileIng">
	                                    <div class="layui-progress" style="margin-bottom: 10px">
	                                        <div class="layui-progress-bar layui-bg-blue" :style="'width:'+file_progress+'%'"></div>
	                                    </div>
	                                    <button type="button" class="layui-btn layui-btn-sm layui-btn-danger percent">{{file_progress}}%</button>
	                                </div>
	                            </div>
                        	</div>
                        </div>
                            <div class="layui-tab-content">
                                <div class="layui-row layui-col-space15">
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <button class="layui-btn layui-btn-primary layui-btn-sm" id="submit" type="button" @click="handleSubmit()">提交</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<script src="{__PLUG_PATH}city.js"></script>
<script>
    var id = {$id};
    server_time  = "<?php echo date("Y-m-d H:i:s",time()); ?>";
    $.each(city,function (key,item) {
       city[key].value = item.label;
       if(item.children && item.children.length){
           $.each(item.children,function (i,v) {
               city[key].children[i].value=v.label;
               if(v.children && v.children.length){
                   $.each(v.children,function (k,val) {
                       city[key].children[i].children[k].value=val.label;
                   });
               }
           });
       }
   });
    new Vue({
        el: '#app',
        data: {
            id:id,
            //分类列表
            upload:{
                fileIng:false,
            },
            formData: {
                file_name:'',
                file_link:'',
            },
            formVideoData: {
                link: 0,
                time: 0,
                width:1420,
                height:800,
            },
            clickIdx:0,
            link:'',
            fileLink:'',
            //多属性header头
            formHeader:[],
            attr: [],//临时属性
            label: '',
            radioRule: [],//radio 当选规则
            radioLabel: [],//radio 当选规则
            progress: 0,
            file_progress: 0,
            searchTask:false,
            um: null,//编译器实例化
            form: null,//layui.form
            layTabId: 1,
            ruleBool: id ? true : false,
        },
        watch:{
        },
        methods: {
            back:function(){
                var that = this;
                layui.use(['element'], function () {
                    layui.element.tabChange('docTabBrief', that.layTabId == 1 ? 1 : parseInt(that.layTabId) - 1);
                });
            },
            next:function(){
                var that = this;
                layui.use(['element'], function () {
                    layui.element.tabChange('docTabBrief', that.layTabId == 5 ? 5 : parseInt(that.layTabId) + 1);
                });
            },
            goBack:function(){
                location.href = this.U({m:window.module,c:'order.storeOrder',a:'index'});
            },
            U: function (opt) {
                var m = opt.m || 'admin', c = opt.c || window.controlle || '', a = opt.a || 'index', q = opt.q || '',
                    p = opt.p || {};
                var params = Object.keys(p).map(function (key) {
                    return key + '/' + p[key];
                }).join('/');
                var gets = Object.keys(q).map(function (key) {
                    return key+'='+ q[key];
                }).join('&');
                return '/' + m + '/' + c + '/' + a + (params == '' ? '' : '/' + params) + (gets == '' ? '' : '?' + gets);
            },
            /**
             * 提示
             * */
            showMsg: function (msg, success) {
                $('#submit').removeAttr('disabled').text('提交');
                layui.use(['layer'], function () {
                    layui.layer.msg(msg, success);
                });
            },
            /**
             * 删除图片
             * */
            deleteImage: function (key, index) {
                var that = this;
                if (index != undefined) {
                    that.formData[key].splice(index, 1);
                    that.$set(that.formData, key, that.formData[key]);
                } else {
                    that.$set(that.formData, key, '');
                }
            },
            createFrame: function (title, src, opt) {
                var that = this;
                opt === undefined && (opt = {});
                var h = 0;
                if (window.innerHeight < 800 && window.innerHeight >= 700) {
                    h = window.innerHeight - 50;
                } else if (window.innerHeight < 900 && window.innerHeight >= 800) {
                    h = window.innerHeight - 100;
                } else if (window.innerHeight < 1000 && window.innerHeight >= 900) {
                    h = window.innerHeight - 150;
                } else if (window.innerHeight >= 1000) {
                    h = window.innerHeight - 200;
                } else {
                    h = window.innerHeight;
                }
                var area = [(opt.w || window.innerWidth / 2) + 'px', (!opt.h || opt.h > h ? h : opt.h) + 'px'];
                layui.use('layer',function () {
                    return layer.open({
                        type: 2,
                        title: title,
                        area: area,
                        fixed: false, //不固定
                        maxmin: true,
                        moveOut: false,//true  可以拖出窗外  false 只能在窗内拖
                        anim: 5,//出场动画 isOutAnim bool 关闭动画
                        offset: 'auto',//['100px','100px'],//'auto',//初始位置  ['100px','100px'] t[ 上 左]
                        shade: 0,//遮罩
                        resize: true,//是否允许拉伸
                        content: src,//内容
                        move: '.layui-layer-title'
                    });
                });
                if (title == '选择图片') {
                    that.$set(that.formData, 'video_data', {});
                    that.$set(that.formData, 'is_snapshot', false);
                }
            },
            changeIMG: function (name, value) {
                if (this.getRule(name).maxLength !== undefined) {
                    var that = this;
                    value.map(function (v) {
                        that.formData[name].push(v);
                    });
                    this.$set(this.formData, name, this.formData[name]);
                } else {
                    if(name == 'batchAttr.pic'){
                        this.batchAttr.pic = value;
                    } else {
                        if (name.indexOf('.') !== -1) {
                            var key = name.split('.');
                            if (key.length == 2){
                                this.formData[key[0]][key[1]] = value;
                            } else if(key.length == 3){
                                this.formData[key[0]][key[1]][key[2]] = value;
                            } else if(key.length == 4){
                                this.$set(this.formData[key[0]][key[1]][key[2]],key[3],value)
                            }
                        } else {
                            this.formData[name] = value;
                        }
                    }
                }
            },
            getRule: function (name) {
                return name || {};
            },
            uploadImage: function (name) {
                return this.createFrame('选择图片',this.U({m:window.module,c:"widget.images",a:'index',p:{fodder:name}}),{h:545,w:900});
            },
            uploadFile: function (opt) {
                if (this.fileLink) {
                    this.formData.file_link = this.fileLink;
                } else {
                    $(this.$refs.documentFilElem).click();
                }
            },
            openWindows: function(title, url, opt) {
                return this.createFrame(title, url, opt);
            },
            /**
             * 监听radio字段
             */
            eeventRadio: function () {
                var that = this;
                that.radioRule.map(function (val) {
                    that.form.on('radio(' + val + ')', function (res) {
                        that.formData[val] = res.value;
                    });
                })
            },
            init: function () {
                var that = this;
                window.UMEDITOR_CONFIG.toolbar = [
                    // 加入一个 test
                    'source | undo redo | bold italic underline strikethrough | superscript subscript | forecolor backcolor | removeformat |',
                    'insertorderedlist insertunorderedlist | selectall cleardoc paragraph | fontfamily fontsize',
                    '| justifyleft justifycenter justifyright justifyjustify |',
                    'link unlink | emotion selectimgs video | map',
                    '| horizontal print preview fullscreen', 'drafts', 'formula'
                ];
                that.$nextTick(function () {
                    layui.use(['form','element','table','laydate'], function () {
                        that.form = layui.form;
                        that.laydate = layui.laydate;
                        that.form.render();
                        that.form.on('select(labelIndex)', function (data) {
                                that.setLabelTable(parseInt(data.value),false)
                        });
                        that.form.on('select(rule_index)', function (data) {
                            that.ruleIndex = data.value;
                        });
                        layui.element.on('tab(docTabBrief)', function(){
                            that.layTabId = this.getAttribute('lay-id');
                        });
                        that.eeventRadio();
                        that.laydate.render({
                            elem: '#add_time', //指定元素
                            type: 'datetime',
                            done: function(value) {
                                that.formData.add_time = value;
                            }
                        });
                        that.show_source_list();
                        that.show_special_list();
                    });
                })
            },
            requestPost: function (url, data) {
                return new Promise(function (resolve, reject) {
                    axios.post(url, data).then(function (res) {
                        if (res.status == 200 && res.data.code == 200) {
                            resolve(res.data)
                        } else {
                            reject(res.data);
                        }
                    }).catch(function (err) {
                        reject({msg:err})
                    });
                })
            },
            requestGet: function (url) {
                return new Promise(function (resolve, reject) {
                    axios.get(url).then(function (res) {
                        if (res.status == 200 && res.data.code == 200) {
                            resolve(res.data)
                        } else {
                            reject(res.data);
                        }
                    }).catch(function (err) {
                        reject({msg:err})
                    });
                })
            },
            handleSubmit:function () {
                var that = this;
                if (!that.formData.file_link) {
                    return that.showMsg('请上传音频素材');
                }
                $('#submit').attr('disabled', 'disabled').text('保存中...');
                that.requestPost(that.U({m:window.module,c:'order.storeOrder',a:'hina_order_import_save',p:{id:that.id,special_type:that.formData.source_type}}),that.formData).then(function (res) {
                    that.confirm();
                }).catch(function (res) {
                    that.showMsg(res.msg);
                });
            },
            clone_form:function () {
                if(parseInt(id) == 0){
                    var that = this;
                    if(that.formData.image) return layList.msg('请先删除上传的图片在尝试取消');
                    parent.layer.closeAll();
                }
                parent.layer.closeAll();
            },
            confirm: function(){
                var that = this;
                layui.use(['layer'], function () {
                    var layer = layui.layer;
                    layer.confirm('导入成功是否返回订单列表', {
                        btn: ['返回列表','继续导入'] //按钮
                    }, function(){
                        location.href = that.U({m:window.module,c:'order.storeOrder',a:'index'});
                    }, function(){
                        location.reload();
                    });
                });
            },
            render:function(){
                this.$nextTick(function(){
                    layui.use(['form'], function () {
                        layui.form.render('select');
                    });
                })
            },
            // 移动
            handleDragStart (e, item) {
                this.dragging = item;
            },
            handleDragEnd (e, item) {
                this.dragging = null
            },
            handleDragOver (e) {
                e.dataTransfer.dropEffect = 'move'
            },
            handleDragEnter (e, item) {
                e.dataTransfer.effectAllowed = 'move'
                if (item === this.dragging) {
                    return
                }
                var newItems = [...this.formData.activity];
                var src = newItems.indexOf(this.dragging);
                var dst = newItems.indexOf(item);
                newItems.splice(dst, 0, ...newItems.splice(src, 1))
                this.formData.activity = newItems;
            },
        },
        mounted: function () {
            var that = this;
            axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';
            window.$vm = that;
            window.changeIMG = that.changeIMG;
            $(that.$refs.documentFilElem).change(function (type) {
                var inputFile = this.files[0];
                that.requestPost(that.U({m:window.module,c:"widget.video",a:'get_signature_image'})).then(function (res) {
                    AdminUpload.upload(res.data.uploadType,{
                        token: res.data.uploadToken || '',
                        file: inputFile,
                        accessKeyId: res.data.accessKey || '',
                        accessKeySecret: res.data.secretKey || '',
                        bucketName: res.data.storageName || '',
                        region: res.data.storageRegion || '',
                        domain: res.data.domain || '',
                        uploadIng:function (progress) {
                            that.upload.fileIng = true;
                            that.file_progress = progress;
                        }
                    }).then(function (res) {
                    	console.log(res);
                        //成功
                        that.$set(that.formData, 'file_name', inputFile.name);
                        that.$set(that.formData, 'file_link', res.origin_url);
                        that.file_progress = 0;
                        that.upload.fileIng = false;
                        return that.showMsg('上传成功');
                    }).catch(function (err) {
                        //失败
                        return that.showMsg('上传错误请检查您的配置');
                    });
                }).catch(function (res) {
                    return that.showMsg(res.msg || '获取密钥失败,请检查您的配置');
                });
            });
        }
    });
</script>
</body>
</html>
