{extend name="public/container"}
{block name="content"}
<style type="text/css">
    .form-add{position: fixed;left: 0;bottom: 0;width:100%;}
    .form-add .sub-btn{border-radius: 0;width: 100%;padding: 6px 0;font-size: 14px;outline: none;border: none;color: #fff;background-color: #2d8cf0;}
</style>
<div class="layui-fluid">
    <div class="layui-row layui-col-space15"  id="app">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-body">
                    <table class="layui-hide" id="List" lay-filter="List"></table>
                    <!--图片-->
                    <script type="text/html" id="image">
                        <img style="cursor: pointer" lay-event="open_image" src="{{d.image}}">
                    </script>
                    <!--操作-->
                </div>
            </div>
        </div>
</div>
<div class="form-add">
    <button type="submit" class="sub-btn">提交</button>
</div>
<script src="{__ADMIN_PATH}js/layuiList.js"></script>
{/block}
{block name='script'}
<script>
    var id = '{$Request.param.id}'; //是否允许多个 
    var parentinputname = '{$Request.param.fodder}';
    var parentinputfield = '{$Request.param.field}';
    layList.form.render();
    //加载列表
    layList.tableList('List',"{:Url('order.storeOrder/order_product_ist',['id'=>1])}",function (){
        return [
            {type: 'checkbox'},
            {field: 'id', title: 'ID', sort: true,event:'id'},
            {field: 'image', title: '产品图片',templet:'#image'},
            {field: 'store_name', title: '产品名称',templet:'#store_name'},
        ]
    });
    //点击事件绑定
    $(".sub-btn").on("click",function(){
        var ids = layList.getCheckData().getIds('id');
        var pics = layList.getCheckData().getIds('image');
        var ids_arr;
        if(typeof (ids) != 'string' && ids != 0){
            ids_arr = ids;
        }else{
            ids_arr = ids;
        }
        parent.$f.changeField('image',pics);
        parent.$f.changeField('product_infos[out_product_id]',1);
        parent.$f.changeField('product_infos[out_sku_id]',2);
        parent.$f.changeField('product_infos[product_cnt]',3);
    	parent.$f.closeModal(parentinputname);
    });
    //查询
    layList.search('search',function(where){
        layList.reload(where);
    });
    function distinct (arr) {
        var newArr = [];
        for( i = 0; i < arr.length; i++) {
            if(!newArr.includes(arr[i])) {
                newArr.push(arr[i])
            }
        }
        return newArr
    }
</script>
{/block}