{extend name="public/container"}
{block name="content"}
<div class="layui-fluid" style="background: #fff">
    <form class="layui-form" action="">
     <div class="layui-form-item">
            <label class="layui-form-label">订单号</label>
            <div class="layui-input-block">
                <input type="text" name="order_id"   disabled="disabled" value="{$orderInfo['order_id']}" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">微信订单号</label>
            <div class="layui-input-block">
                <input type="text" name="wx_shop_order_id"   disabled="disabled" value="{$orderInfo['wx_shop_order_id']}" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">发货状态</label>
            <div class="layui-input-block">
                <input type="radio" name="finish_all_delivery" value="1" lay-filter="finish_all_delivery" title="全发" checked>
                <?php if ($is_many == 1): ?>
                    <input type="radio" name="finish_all_delivery" value="2" lay-filter="finish_all_delivery" title="部分发货">
                <?php endif ?>
            </div>
        </div>
               
        <?php if ($is_many == 1): ?>
            <div class="finish_all_delivery" data-finish_all_delivery="2" style="display: none">
         <!-- 商品列表 -->
         <div class="layui-form-item">
          <label class="layui-form-label">选择商品</label>
                <div class="layui-input-block" id="tag_ids1"></div>
         </div>
        </div>
        <?php endif ?>
        <div class="layui-form-item">
                <label class="layui-form-label">快递公司</label>
                <div class="layui-input-block">
               <select name="delivery_id">
                        <option value="">请选择</option>
                        {volist name='list' id='vo'}
                        <option value="{$vo['delivery_id']}">{$vo['delivery_name']}</option>
                        {/volist}
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">快递单号</label>
                <div class="layui-input-block">
                    <input type="text" name="waybill_id"   placeholder="请输入快递单号" autocomplete="off" class="layui-input">
                </div>
            </div>
        
        <div class="layui-form-item" style="margin:10px 0;padding-bottom: 10px;">
            <div class="layui-input-block">
                <button  type="button" class="layui-btn layui-btn-sm" lay-submit="" lay-filter="delivery">立即提交</button>
                <button type="reset" class="layui-btn layui-btn-primary layui-btn-sm">重置</button>
      
            </div>
        </div>
    </form>
</div>
<script src="{__ADMIN_PATH}js/layuiList.js"></script>
{/block}
{block name="script"}
<script>
    var product_list = [{"store_name":"test","out_product_id":"1","out_sku_id":"11","product_cnt":"111"},
                        {"store_name":"test2","out_product_id":"2","out_sku_id":"22","product_cnt":"222"},
                        {"store_name":"test3","out_product_id":"3","out_sku_id":"33","product_cnt":"333"}];
                        
    var id={$id};
    layList.form.render();
    layList.form.on('radio(finish_all_delivery)', function(data){
       $('.finish_all_delivery').each(function () {
           if($(this).data('finish_all_delivery') == data.value){
               $(this).show();
           }else{
               $(this).hide();
           }
       })
    });

    layui.use(['form','element','table'], function () {
    
        layui.element.on('tab(docTabBrief)', function(){
            that.layTabId = this.getAttribute('lay-id');
        });
        layui.form.render();
  
    });
    layui.config({
        base : '/static/plug/layui/'
    }).extend({
        selectN: './selectN',
        selectM: './selectM',
    }).use('selectM',function () {
        $ = layui.jquery; 
        var form = layui.form
        ,selectM = layui.selectM;
         //多选标签-基本配置
         var tagIns1 = selectM({
        //元素容器【必填】
        elem: '#tag_ids1'
        //候选数据【必填】
        ,data: product_list
        ,max:5
        ,field: {idName:'out_product_id',titleName:'store_name',statusName:'disabled'}
        });  

        layList.search('delivery',function (data) {
            data.delivery_list = [];
            let storeData = {};
            let delivery_id =  $('select[name="delivery_id"]').val();
            let waybill_id =  $('input[name="waybill_id"]').val();
            storeData.delivery_id = delivery_id;
            storeData.waybill_id = waybill_id;
            if(data.finish_all_delivery == '2' ){
                storeData.product_info_list = tagIns1.selected;
                for(let i = 0; i < storeData.product_info_list.length;i++){
                    for(let j = 0; j < product_list.length;j++){
                        if(storeData.product_info_list[i].out_product_id == product_list[j].out_product_id){
                            storeData.product_info_list[i].out_sku_id = product_list[j].out_sku_id;
                            storeData.product_info_list[i].product_cnt = product_list[j].product_cnt;
                            delete storeData.product_info_list[i].store_name ;
                        }
                    }
                }
            }
            if(data.finish_all_delivery == '1' ){
                storeData.product_info_list = product_list;
                for(let i = 0; i < storeData.product_info_list.length;i++){
                    delete storeData.product_info_list[i].store_name ;
                }
            }
            data.delivery_list[0] = storeData;
            if(data.finish_all_delivery == '2'){
                if(!data.tag_ids1) return layList.msg('请选择部分发货商品');
            }
            if(!delivery_id) return layList.msg('请选择快递公司');
            if(!waybill_id) return layList.msg('请填写快递单号');
            var index = layList.layer.load(1, {
                shade: [0.1,'#fff']
            });
            let data1 = {};
            data1.finish_all_delivery = data.finish_all_delivery;
            data1.order_id = data.order_id;
            data1.wx_shop_order_id = data.wx_shop_order_id;
            data1.delivery_list = data.delivery_list;
            layList.basePost(layList.U({a:'update_shop_delivery',q:{id:id}}),data1,function (res) {
                layList.layer.close(index);
                layList.msg(res.msg);
                parent.layer.close(parent.layer.getFrameIndex(window.name));
                parent.window.frames[parent.$(".page-tabs-content .active").index()].location.reload();
            },function (res) {
                layList.layer.close(index);
                layList.msg(res.msg);
            });
        });
    });
    

</script>
{/block}