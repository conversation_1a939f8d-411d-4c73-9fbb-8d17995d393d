{extend name="public/container"}
{block name="content"}
<div class="ibox-content order-info">

    <div class="row">
        <div class="col-sm-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    收货信息
                </div>
                <div class="panel-body">
                    <div class="row show-grid">
                        <div class="col-xs-12" >用户昵称: {$userInfo.nickname}</div>
                        <div class="col-xs-12">收货人: {$orderInfo.real_name}</div>
                        <div class="col-xs-12">联系电话: {if $orderInfo.user_phone}{$orderInfo.user_phone}{else}{/if} {if $orderInfo.mark}{$orderInfo.mark}{else}{/if} </div>
                        <div class="col-xs-12">收货地址: {$orderInfo.user_address}</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-sm-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    订单信息
                </div>
                <div class="panel-body">
                    <div class="row show-grid">
                        {if condition="$orderInfo['type'] eq 5"}
                            <div class="col-xs-6" >订单类型: {$orderInfo.fund_type == 1 ? '二级商户单' : '普通单'}</div>
                            <div class="col-xs-6" > 下单小程序场景值: {$orderInfo.scene }</div>
                            <div class="col-xs-6" >订单编号: {$orderInfo.order_id}</div>
                            <div class="col-xs-6" >微信侧订单id: {$wx_orderInfo.order_id}</div>
                            <div class="col-xs-6" style="color: #8BC34A;">订单状态:
                                {if condition="$orderInfo['paid'] eq 0 && $orderInfo['status'] eq 0"}
                                未支付
                                {elseif condition="$orderInfo['paid'] eq 1 && $orderInfo['shipping_type'] eq 1 && $orderInfo['status'] eq 0 && $orderInfo['refund_status'] eq 0"/}
                                未发货
                                {elseif condition="$orderInfo['paid'] eq 1 && $orderInfo['shipping_type'] eq 2 && $orderInfo['status'] eq 0 && $orderInfo['refund_status'] eq 0"/}
                                待核销
                                {elseif condition="$orderInfo['paid'] eq 1 && $orderInfo['shipping_type'] eq 1 && $orderInfo['status'] eq 1 && $orderInfo['refund_status'] eq 0"/}
                                待收货
                                {elseif condition="$orderInfo['paid'] eq 1 && $orderInfo['status'] eq 2 && $orderInfo['refund_status'] eq 0"/}
                                待评价
                                {elseif condition="$orderInfo['paid'] eq 1 && $orderInfo['status'] eq 3 && $orderInfo['refund_status'] eq 0"/}
                                交易完成
                                {elseif condition="$orderInfo['paid'] eq 1 && $orderInfo['refund_status'] eq 1"/}
                                申请退款<b style="color:#f124c7">{$orderInfo.refund_reason_wap}</b>
                                {elseif condition="$orderInfo['paid'] eq 1 && $orderInfo['refund_status'] eq 2"/}
                                已退款
                                {/if}
                            </div>
                           
                            <div class="col-xs-6" >多久确认收货: {$wx_orderInfo.aftersale_duration}天</div>
                            {if condition="$wx_orderInfo['fund_type'] eq 1"}
                                <div class="col-xs-6" >确认收货时间: {$wx_orderInfo.receive_time}</div>
                            {/if}
                            <div class="col-xs-6">商品总数: {$orderInfo.total_num}</div>
                            <div class="col-xs-6">商品总价: ￥{$orderInfo.total_price}</div>
                            <div class="col-xs-6">支付邮费: ￥{$orderInfo.total_postage}</div>
                            <div class="col-xs-6">优惠券金额: ￥{$orderInfo.coupon_price}</div>
                            <div class="col-xs-6">实际支付: ￥{$orderInfo.pay_price}</div>
                            {if condition="$orderInfo['refund_price'] > 0"}
                            <div class="col-xs-6" style="color: #f1a417">退款金额: ￥{$orderInfo.refund_price}</div>
                            {/if}
                            {if condition="$orderInfo['use_integral'] > 0"}
                            <div class="col-xs-6" style="color: #f1a417">使用积分: {$orderInfo.use_integral}积分(抵扣了￥{$orderInfo.deduction_price})</div>
                            {/if}
                            {if condition="$orderInfo['back_integral'] > 0"}
                            <div class="col-xs-6" style="color: #f1a417">退回积分: ￥{$orderInfo.back_integral}</div>
                            {/if}
                            <div class="col-xs-6">支付方式:
                                {if condition="$orderInfo['paid'] eq 1"}
                                   {if condition="$orderInfo['pay_type'] eq 'weixin' || $orderInfo['pay_type'] eq 'bytedance.weixin'"}
                                   微信支付
                                   {elseif condition="$orderInfo['pay_type'] eq 'alipay' || $orderInfo['pay_type'] eq 'bytedance.alipay'"}
                                   支付宝支付
                                   {elseif condition="$orderInfo['pay_type'] eq 'yue'"}
                                   余额支付
                                   {elseif condition="$orderInfo['pay_type'] eq 'offline'"}
                                   线下支付
                                   {else/}
                                   其他支付
                                   {/if}
                                {else/}
                                    {if condition="$orderInfo['pay_type'] eq 'offline'"}
                                        线下支付
                                    {elseif condition="$orderInfo['pay_type'] eq 'bytedance'"}
                                        发起字节跳动担保支付
                                    {else/}
                                        未支付
                                     {/if}
                                {/if}
                            </div>
                            {notempty name="orderInfo.pay_time"}
                            <div class="col-xs-6">支付时间: {$orderInfo.pay_time|date="Y/m/d H:i"}</div>
                            {/notempty}
                            <div class="col-xs-6" style="color: #ff0005">用户备注: {$orderInfo.mark?:'无'}</div>
                            <div class="col-xs-6" style="color: #733AF9">推广人: {if $spread}{$spread}{else}无{/if}</div>
                            <div class="col-xs-6" style="color: #733b5c">商家备注: {$orderInfo.remark?:'无'}</div>

                        {else/}
                            <div class="col-xs-6" >订单编号: {$orderInfo.order_id}</div>
                            <div class="col-xs-6" style="color: #8BC34A;">订单状态:
                                {if condition="$orderInfo['paid'] eq 0 && $orderInfo['status'] eq 0"}
                                未支付
                                {elseif condition="$orderInfo['paid'] eq 1 && $orderInfo['shipping_type'] eq 1 && $orderInfo['status'] eq 0 && $orderInfo['refund_status'] eq 0"/}
                                未发货
                                {elseif condition="$orderInfo['paid'] eq 1 && $orderInfo['shipping_type'] eq 2 && $orderInfo['status'] eq 0 && $orderInfo['refund_status'] eq 0"/}
                                待核销
                                {elseif condition="$orderInfo['paid'] eq 1 && $orderInfo['shipping_type'] eq 1 && $orderInfo['status'] eq 1 && $orderInfo['refund_status'] eq 0"/}
                                待收货
                                {elseif condition="$orderInfo['paid'] eq 1 && $orderInfo['status'] eq 2 && $orderInfo['refund_status'] eq 0"/}
                                待评价
                                {elseif condition="$orderInfo['paid'] eq 1 && $orderInfo['status'] eq 3 && $orderInfo['refund_status'] eq 0"/}
                                交易完成
                                {elseif condition="$orderInfo['paid'] eq 1 && $orderInfo['refund_status'] eq 1"/}
                                申请退款<b style="color:#f124c7">{$orderInfo.refund_reason_wap}</b>
                                {elseif condition="$orderInfo['paid'] eq 1 && $orderInfo['refund_status'] eq 2"/}
                                已退款
                                {/if}
                            </div>
                            <div class="col-xs-6">商品总数: {$orderInfo.total_num}</div>
                            <div class="col-xs-6">商品总价: ￥{$orderInfo.total_price}</div>
                            <div class="col-xs-6">支付邮费: ￥{$orderInfo.total_postage}</div>
                            <div class="col-xs-6">优惠券金额: ￥{$orderInfo.coupon_price}</div>
                            <div class="col-xs-6">实际支付: ￥{$orderInfo.pay_price}</div>
                            {if condition="$orderInfo['refund_price'] > 0"}
                            <div class="col-xs-6" style="color: #f1a417">退款金额: ￥{$orderInfo.refund_price}</div>
                            {/if}
                            {if condition="$orderInfo['use_integral'] > 0"}
                            <div class="col-xs-6" style="color: #f1a417">使用积分: {$orderInfo.use_integral}积分(抵扣了￥{$orderInfo.deduction_price})</div>
                            {/if}
                            {if condition="$orderInfo['back_integral'] > 0"}
                            <div class="col-xs-6" style="color: #f1a417">退回积分: ￥{$orderInfo.back_integral}</div>
                            {/if}
                            <div class="col-xs-6">创建时间: {$orderInfo.add_time|date="Y/m/d H:i"}</div>
                            <div class="col-xs-6">支付方式:
                                {if condition="$orderInfo['paid'] eq 1"}
                                   {if condition="$orderInfo['pay_type'] eq 'weixin' || $orderInfo['pay_type'] eq 'bytedance.weixin'"}
                                   微信支付
                                   {elseif condition="$orderInfo['pay_type'] eq 'alipay' || $orderInfo['pay_type'] eq 'bytedance.alipay'"}
                                   支付宝支付
                                   {elseif condition="$orderInfo['pay_type'] eq 'yue'"}
                                   余额支付
                                   {elseif condition="$orderInfo['pay_type'] eq 'offline'"}
                                   线下支付
                                   {else/}
                                   其他支付
                                   {/if}
                                {else/}
                                    {if condition="$orderInfo['pay_type'] eq 'offline'"}
                                        线下支付
                                    {elseif condition="$orderInfo['pay_type'] eq 'bytedance'"}
                                        发起字节跳动担保支付
                                    {else/}
                                        未支付
                                     {/if}
                                {/if}
                            </div>
                            {notempty name="orderInfo.pay_time"}
                            <div class="col-xs-6">支付时间: {$orderInfo.pay_time|date="Y/m/d H:i"}</div>
                            {/notempty}
                            <div class="col-xs-6" style="color: #ff0005">用户备注: {$orderInfo.mark?:'无'}</div>
                            <div class="col-xs-6" style="color: #733AF9">推广人: {if $spread}{$spread}{else}无{/if}</div>
                            <div class="col-xs-6" style="color: #733b5c">商家备注: {$orderInfo.remark?:'无'}</div>
                        {/if}
                    </div>
                </div>
            </div>
        </div>
        {if condition="$orderInfo['delivery_type'] eq 'express'"}
        <div class="col-sm-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    物流信息
                </div>
                <div class="panel-body">
                    <div class="row show-grid">
                        {if condition="$orderInfo['type'] eq 5" && $wx_orderInfo['delivery_detail'] != [] }
                            <div class="col-xs-6" >配送类型: 
                                {if condition="$wx_orderInfo['delivery_detail']['delivery_type'] eq '1'"}
                                    正常快递
                                {elseif condition="$wx_orderInfo['delivery_detail']['delivery_type'] eq '2'"}
                                    无需快递
                                {elseif condition="$wx_orderInfo['delivery_detail']['delivery_type'] eq '3'"}
                                    线下配送
                                {elseif condition="$wx_orderInfo['delivery_detail']['delivery_type'] eq '4'"}
                                    用户自提
                                {/if}
                            </div>
                            <div class="col-xs-6" >是否发货完成: 
                                {if condition="isset($wx_orderInfo['delivery_detail']['finish_all_delivery']) &&  $wx_orderInfo['delivery_detail']['finish_all_delivery'] eq '1'"}
                                    已完成
                                {else}
                                    未完成
                                {/if}
                            </div>
                            {volist name='wx_orderInfo.delivery_detail.delivery_list' id='vo'}
                                <div class="col-xs-12">快递单号: {$vo.waybill_id} | <button class="btn btn-info btn-xs" type="button"  onclick="$eb.createModalFrame('物流查询','{:Url('express',array('oid'=>$orderInfo['id']))}',{w:322,h:568})">物流查询</button></div>
                                <div class="col-xs-6" >快递公司: {$vo.delivery_id}</div>
                                <div class="col-xs-6" >配送类型: 
                                    {if condition="$vo['delivery_type'] eq '1'"}
                                        正常快递
                                    {elseif condition="$vo['delivery_type'] eq '2'"}
                                        无需快递
                                    {elseif condition="$vo['delivery_type'] eq '3'"}
                                        线下配送
                                    {elseif condition="$vo['delivery_type'] eq '4'"}
                                        用户自提
                                    {/if}
                                </div>
                                <div class="col-xs-12" >商品信息: <br>
                                    {volist name='vo.product_info_list' id='voo'}
                                       商家侧spu id: {$voo.out_product_id}<br>
                                       商家侧sku id: {$voo.out_sku_id}<br>
                                       商品个数: {$voo.product_cnt}<br>
                                    {/volist}
                                </div>
                            {/volist}
                        {else}
                        <div class="col-xs-12" >快递公司: {$orderInfo.delivery_name}</div>
                        <div class="col-xs-6">快递单号: {$orderInfo.delivery_id} | <button class="btn btn-info btn-xs" type="button"  onclick="$eb.createModalFrame('物流查询','{:Url('express',array('oid'=>$orderInfo['id']))}',{w:322,h:568})">物流查询</button></div>
                        {/if}
                    </div>
                </div>
            </div>
        </div>
        {elseif condition="$orderInfo['delivery_type'] eq 'send'"}
        <div class="col-sm-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    配送信息
                </div>
                <div class="panel-body">
                    <div class="row show-grid">
                        <div class="col-xs-6" >送货人姓名: {$orderInfo.delivery_name}</div>
                        <div class="col-xs-6">送货人电话: {$orderInfo.delivery_id}</div>
                    </div>
                </div>
            </div>
        </div>
        {/if}
        {if condition="$orderInfo['type'] eq 5"}
        <div class="col-sm-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    促销信息
                </div>
                <div class="panel-body">
                    <div class="row show-grid">
                        {if condition="$wx_orderInfo['order_detail']['pay_info']"}
                            <?php if(isset($wx_orderInfo['order_detail']['pay_info']['promotion_detail_info']) && isset($wx_orderInfo['order_detail']['pay_info']['promotion_detail_info']['promotion_detail_list'])){?>
                                
                                <div class="col-xs-6" >券ID: {$wx_orderInfo.order_detail.pay_info.promotion_detail_info.promotion_detail_list.coupon_id}</div>
                                <div class="col-xs-6" >优惠名称: {$wx_orderInfo.order_detail.pay_info.promotion_detail_info.promotion_detail_list.name}</div>
                                <div class="col-xs-6" >优惠范围: 
                                    {if condition="$wx_orderInfo['order_detail']['pay_info']['promotion_detail_info']['promotion_detail_list']['scope'] eq 'GLOBAL'"}
                                        全场代金券
                                    {elseif condition="$wx_orderInfo['order_detail']['pay_info']['promotion_detail_info']['promotion_detail_list']['scope'] eq 'SINGLE'"}
                                        单品优惠
                                    {/if}
                                </div>
                                <div class="col-xs-6" >优惠类型: 
                                {if condition="$wx_orderInfo['order_detail']['pay_info']['promotion_detail_info']['promotion_detail_list']['type'] eq 'CASH'"}
                                        充值型代金券
                                    {elseif condition="$wx_orderInfo['order_detail']['pay_info']['promotion_detail_info']['promotion_detail_list']['type'] eq 'NOCASH'"}
                                        免充值型代金券
                                    {/if}
                                </div>
                                <div class="col-xs-6" >优惠券面额: {$wx_orderInfo.order_detail.pay_info.promotion_detail_info.promotion_detail_list.amount / 100}元</div>
                                <div class="col-xs-6" >活动ID: {$wx_orderInfo.order_detail.pay_info.promotion_detail_info.promotion_detail_list.stock_id}</div>
                                <div class="col-xs-6" >微信出资: {$wx_orderInfo.order_detail.pay_info.promotion_detail_info.promotion_detail_list.wechatpay_contribute / 100}元</div>
                                <div class="col-xs-6" >商户出资: {$wx_orderInfo.order_detail.pay_info.promotion_detail_info.promotion_detail_list.merchant_contribute / 100}元</div>
                                <div class="col-xs-12" >其他出资: {$wx_orderInfo.order_detail.pay_info.promotion_detail_info.promotion_detail_list.other_contribute / 100}元</div>
                                <div class="col-xs-12" >单品列表信息: <br>
                                    <?php if(isset($wx_orderInfo['order_detail']['pay_info']['promotion_detail_info']['promotion_detail_list']['goods_detail'])){?>
                                           商品编码: {$wx_orderInfo.order_detail.pay_info.promotion_detail_info.promotion_detail_list.goods_detail.goods_id}<br>
                                           商品数量: {$wx_orderInfo.order_detail.pay_info.promotion_detail_info.promotion_detail_list.goods_detail.quantity}<br>
                                           商品单价: {$wx_orderInfo.order_detail.pay_info.promotion_detail_info.promotion_detail_list.goods_detail.unit_price / 100}元<br>
                                           商品优惠金额: {$wx_orderInfo.order_detail.pay_info.promotion_detail_info.promotion_detail_list.goods_detail.discount_amount / 100}元<br>
                                           商品备注: {$wx_orderInfo.order_detail.pay_info.promotion_detail_info.promotion_detail_list.goods_detail.goods_reamark}
                                    <?php }?>
                                </div>
                            <?php }?>
                        {/if}

                    </div>
                </div>
            </div>
        </div>
        <div class="col-sm-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    推广信息
                </div>
                <div class="panel-body">
                    <div class="row show-grid">
                        {if condition="$wx_orderInfo['order_detail']['promotion_info']"}
                            <div class="col-xs-6" >推广员唯一ID: {$wx_orderInfo['order_detail']['promotion_info']['promoter_id']}</div>
                            <div class="col-xs-6">推广员昵称: {$wx_orderInfo['order_detail']['promotion_info']['finder_nickname']}</div>
                            <div class="col-xs-6">分享员: {$wx_orderInfo['order_detail']['promotion_info']['sharer_openid']}</div>
                        {/if}
                    </div>
                </div>
            </div>
        </div>
        <div class="col-sm-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    退款信息
                </div>
                <div class="panel-body">
                    <div class="row show-grid">
                        {if condition="$wx_orderInfo['refund_info'] != []"}
                            {volist name='wx_orderInfo.refund_info.refund_list' id='vo'}
                                <div class="col-xs-6" >退款类型: {$vo.type == 1 ? '售后退款' : ''}</div>
                                <div class="col-xs-6">金额: {$vo.amount / 100}元</div>
                                <div class="col-xs-6">视频号系统分账单号: {$vo.refund_no}</div>
                                <div class="col-xs-6">微信支付系统分账单号: {$vo.refund_id}</div>
                                <div class="col-xs-6">分账创建时间: {$vo.create_time}</div>
                                <div class="col-xs-6">分账完成时间: {$vo.finish_time}</div>
                                <div class="col-xs-6">退款结果: 
                                    {if condition="$vo['result'] eq 'PROCESSING'"}
                                        退款处理中
                                    {elseif condition="$vo['result'] eq 'SUCCESS'"}
                                        退款成功
                                    {elseif condition="$vo['result'] eq 'CLOSED'"}
                                        退款关闭
                                    {elseif condition="$vo['result'] eq 'ABNORMAL'"}
                                        退款异常
                                    {/if}
                                </div>
                                <div class="col-xs-6">退款失败原因: <?php echo isset($vo['fail_reason'])? $vo['fail_reason'] :'';?></div>
                            {/volist}
                        {/if}    
                    </div>
                </div>
            </div>
        </div>
<!--    <div class="col-sm-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    佣金信息
                </div>
                <div class="panel-body">
                    <div class="row show-grid">
                        <div class="col-xs-6" >商户号: {$orderInfo.delivery_name}</div>
                    </div>
                </div>
            </div>
        </div> -->
        <div class="col-sm-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    分账信息
                </div>
                <div class="panel-body">
                    <div class="row show-grid">
                        {if condition="$wx_orderInfo['settlement_info']['settlement_list'] != []"}
                            {volist name='wx_orderInfo.settlement_info.settlement_list' id='vo'}
                                <div class="col-xs-12" >
                                    <div class="col-xs-6">分账方类型: {$vo.type==1 ? '服务商' : '商家'}</div>
                                    <div class="col-xs-6">金额: {$vo.amount / 100}元</div>
                                    <div class="col-xs-6">视频号系统分账单号: {$vo.settle_no}</div>
                                    <div class="col-xs-6">微信支付系统分账单号: {$vo.settle_id}</div>
                                    <div class="col-xs-6">分账创建时间: {$vo.create_time}</div>
                                    <div class="col-xs-6">分账完成时间: {$vo.finish_time}</div>
                                    <div class="col-xs-12">分账结果: 
                                        {if condition="$vo['result'] eq 'PENDING'"}
                                            待分账
                                        {elseif condition="$vo['result'] eq 'SUCCESS'"}
                                            分账成功
                                        {elseif condition="$vo['result'] eq 'CLOSED'"}
                                            已关闭
                                        {/if}
                                    </div>
                                    <div class="col-xs-6">分账失败原因: <?php echo isset($vo['fail_reason'])? $vo['fail_reason'] :'';?></div>
                                    <div class="col-xs-6">是否完成分账: {$vo.is_finished  ? '已完成' : '未完成'}</div>
                                </div>
                            {/volist}
                            <div class="col-xs-6">结算最终收取的支付手续费: {$wx_orderInfo['settlement_info']['handing_fee']}</div>
                            <div class="col-xs-6">商家结算状态: 
                                {if condition="$wx_orderInfo['settlement_info']['settle_status'] eq 1"}
                                    待结算
                                {elseif condition="$wx_orderInfo['settlement_info']['settle_status'] eq 2"}
                                    已结算
                                {elseif condition="$wx_orderInfo['settlement_info']['settle_status'] eq 3"}
                                    已关闭
                                {elseif condition="$wx_orderInfo['settlement_info']['settle_status'] eq 4"}
                                    交易异常
                                {/if}
                            </div>
                            <div class="col-xs-6">商家结算时间: {$wx_orderInfo['settlement_info']['settle_time']}</div>
                            <div class="col-xs-6">结算异常原因: <?php echo isset($wx_orderInfo['settlement_info']['settle_abnormal_reason'])? $wx_orderInfo['settlement_info']['settle_abnormal_reason'] :'';?></div>
                        {/if}     
                    </div>
                </div>
            </div>
        </div>
        {/if}
        <div class="col-sm-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    备注信息
                </div>
                <div class="panel-body">
                    <div class="row show-grid">
                        <div class="col-xs-6" >{if $orderInfo.mark}{$orderInfo.mark}{else}暂无备注信息{/if}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="{__FRAME_PATH}js/content.min.js?v=1.0.0"></script>
{/block}
{block name="script"}

{/block}
