<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link href="{__FRAME_PATH}css/font-awesome.min.css" rel="stylesheet">
    <link href="{__ADMIN_PATH}plug/umeditor/themes/default/css/umeditor.css" type="text/css" rel="stylesheet">
    <script type="text/javascript" src="{__ADMIN_PATH}plug/umeditor/third-party/jquery.min.js"></script>
    <script type="text/javascript" src="{__ADMIN_PATH}plug/umeditor/third-party/template.min.js"></script>
    <script type="text/javascript" charset="utf-8" src="{__ADMIN_PATH}plug/umeditor/umeditor.config.js"></script>
    <script type="text/javascript" charset="utf-8" src="{__ADMIN_PATH}plug/umeditor/umeditor.min.js"></script>
    <script type="text/javascript" src="{__ADMIN_PATH}plug/umeditor/lang/zh-cn/zh-cn.js"></script>
    <link rel="stylesheet" href="/static/plug/layui/css/layui.css">
    <script src="/static/plug/layui/layui.js"></script>
    <script src="{__PLUG_PATH}vue/dist/vue.min.js"></script>
		<link rel="stylesheet" href="/static/plug/iview/dist/styles/iview.css">
    <script src="/static/plug/axios.min.js"></script>
    <script src="{__MODULE_PATH}widget/aliyun-oss-sdk-4.4.4.min.js"></script>
    <script src="{__MODULE_PATH}widget/cos-js-sdk-v5.min.js"></script>
    <script src="{__MODULE_PATH}widget/qiniu-js-sdk-2.5.5.js"></script>
    <script src="{__MODULE_PATH}widget/plupload.full.min.js"></script>
    <script src="{__MODULE_PATH}widget/videoUpload.js"></script>
    <script type="text/javascript">
        $eb = parent._mpApi;
        window.controlle="<?php echo strtolower(trim(preg_replace("/[A-Z]/", "_\\0", app('request')->controller()), "_"));?>";
        window.module="6GqvmHa8HRGHoQEQ";
    </script>
    <style>
        .layui-form-item {
            margin-bottom: 0px;
        }

        .pictrueBox {
            display: inline-block !important;
        }

        .pictrue {
            width: 60px;
            height: 60px;
            border: 1px dotted rgba(0, 0, 0, 0.1);
            margin-right: 15px;
            display: inline-block;
            position: relative;
            cursor: pointer;
        }

        .pictrue img {
            width: 100%;
            height: 100%;
        }

        .upLoad {
            width: 58px;
            height: 58px;
            line-height: 58px;
            border: 1px dotted rgba(0, 0, 0, 0.1);
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.02);
            cursor: pointer;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .rulesBox {
            display: flex;
            flex-wrap: wrap;
            margin-left: 10px;
        }

        .layui-tab-content {
            margin-top: 15px;
        }

        .ml110 {
            margin: 18px 0 4px 110px;
        }

        .rules {
            display: flex;
        }

        .rules-btn-sm {
            height: 30px;
            line-height: 30px;
            font-size: 12px;
            width: 109px;
        }

        .rules-btn-sm input {
            width: 79% !important;
            height: 84% !important;
            padding: 0 10px;
        }

        .ml10 {
            margin-left: 10px !important;
        }

        .ml40 {
            margin-left: 40px !important;
        }

        .closes {
            position: absolute;
            left: 86%;
            top: -18%;
        }
        .red {
            color: red;
        }
        .layui-input-block .layui-video-box{
            width: 22%;
            height: 180px;
            border-radius: 10px;
            background-color: #707070;
            margin-top: 10px;
            position: relative;
            overflow: hidden;
        }
        .layui-input-block .layui-video-box i{
            color: #fff;
            line-height: 180px;
            margin: 0 auto;
            width: 50px;
            height: 50px;
            display: inherit;
            font-size: 50px;
        }
        .layui-input-block .layui-video-box .mark{
            position: absolute;
            width: 100%;
            height: 30px;
            top: 0;
            background-color: rgba(0,0,0,.5);
            text-align: center;
        }
        .store_box{
            display: flex;
        }
        .info{
            color: #c9c9c9;
            padding-left: 10px;
            line-height: 30px;
        }

        .flex {
            display: flex;
        }
        .flex_align_center {
            align-items: center;
        }
				.fc-upload-cover{
					    opacity: 0;
					    position: absolute;
					    top: 0;
					    bottom: 0;
					    left: 0;
					    right: 0;
					    background: rgba(0, 0, 0, .6);
					    transition: opacity .3s;
							line-height: 58px;
				}
				.fc-upload-cover:hover{
					opacity: 1;
				}
				.fc-upload-cover i {
				    color: #fff;
				    font-size: 20px;
				    cursor: pointer;
				    margin: 0 2px;
				}
    </style>
</head>
<body>
<div class="layui-fluid">
    <div class="layui-row layui-col-space15"  id="app" v-cloak="">
        <div class="layui-card">
            <div class="layui-card-header">
                <span class="">{{id ? '评测修改': '评测添加' }}</span>
                <button style="margin-left: 20px" type="button" class="layui-btn layui-btn-primary layui-btn-xs" @click="goBack">返回列表</button>
            </div>
            <div class="layui-card-body">
                <form class="layui-form" action="" v-cloak="">
                    <div class="layui-tab layui-tab-brief" lay-filter="docTabBrief">
                        <ul class="layui-tab-title">
                            <li class="layui-this" lay-id='1'>设置评测内容</li>
                        </ul>
                        <div class="layui-tab-content">
                            <div class="layui-tab-item layui-show">
                                <div class="layui-row layui-col-space15">
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">类型<i class="red">*</i></label>
                                                <div class="layui-input-block">
                                                <select name="type" lay-filter="type" class="layui-input">
                                                    <option value="">请选择,评测类型</option>
                                                    <option value="2" :selected="formData.type == 2">官评</option>
                                                    <option value="1" :selected="formData.type == 1">用户</option>
                                                </select>
                                            </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12" v-if="hidden_data">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">选择用户：</label>
                                                <div class="pictrueBox">
                                                    <div class="pictrue" v-if="formData.uid" @click="chooseUser('uid')">
                                                        <img :src="formData.avatar"></div>
                                                    <div class="upLoad" @click="chooseUser('uid')" v-else>
                                                        <i class="layui-icon layui-icon-camera" class="iconfont"
                                                           style="font-size: 26px;"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">选择产品：<i class="red">*</i></label>
                                                <div class="pictrueBox">
                                                    <div class="pictrue" v-if="formData.product_id" @click="chooseProduct('product_id')">
                                                        <img :src="formData.product_image"></div>
                                                    <div class="upLoad" @click="chooseProduct('product_id')" v-else>
                                                        <i class="layui-icon layui-icon-camera" class="iconfont"
                                                           style="font-size: 26px;"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">选择关联的门店：<i class="red">*</i></label>
                                                <div class="pictrueBox">
                                                    <div class="pictrue" v-if="formData.store_id" @click="chooseStore('store_id')">
                                                        <img :src="formData.store_image"></div>
                                                    <div class="upLoad" @click="chooseStore('store_id')" v-else>
                                                        <i class="layui-icon layui-icon-camera" class="iconfont"
                                                           style="font-size: 26px;"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12" v-if="hidden_url_data">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">官评文章地址</label>
                                                <div class="layui-input-block">
                                                    <input type="text" name="url" lay-verify="title" autocomplete="off"
                                                           placeholder="请输入官评外部地址" class="layui-input" v-model="formData.url" maxlength="100">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12" v-if="hidden_data">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">评测图<i class="red">*</i></label>
                                                <div class="pictrueBox pictrue" v-for="(item,index) in formData.images" style="text-align: center;">
                                                    <img :src="item">
													<div class="fc-upload-cover">
														<i class="ivu-icon ivu-icon-ios-eye-outline" @click="previewImage('images',index)"></i>
													</div>
                                                    <i class="layui-icon closes" @click="deleteImage('images',index)">&#x1007</i>
                                                </div>
                                                <div class="pictrueBox" v-if="formData.images">
                                                    <div class="upLoad" @click="uploadImage('images')"
                                                         v-if="formData.images.length <= rule.images.maxLength">
                                                        <i class="layui-icon layui-icon-camera" class="iconfont"
                                                           style="font-size: 26px;"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12" v-if="hidden_data">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item layui-form-text">
                                                <label class="layui-form-label">评语<i class="red">*</i></label>
                                                <div class="layui-input-block">
                                                    <textarea name="content" v-model="formData.content"
                                                              placeholder="请输入简介" class="layui-textarea"></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">状态<i class="red">*</i></label>
                                                <div class="layui-input-block">
                                                <select name="type" lay-filter="status" >
                                                    <option value="">请选择,评测状态</option>
                                                        <option value="1" :selected="formData.status == 1">通过</option>
                                                        <option value="2" :selected="formData.status == 2">待审核</option>
                                                        <option value="3" :selected="formData.status == 3">拒绝</option>
                                                </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                    <label class="layui-form-label">总评分<i class="red">*</i></label>
                                                    <div class="layui-input-block" >
                                                    <input type="text" name="score" autocomplete="off" placeholder="" class="layui-input" v-model="formData.score">
                                                    </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="label_wrap" v-if="labelClassList.length">
                                                <div>添加测评维度</div>
                                                <div class="layui-form-item" >
                                                        <label class="layui-form-label">细分维度<i class="red">*</i></label>
                                                        <div class="layui-input-block" >
                                                            <select name="lable_class" lay-filter="lable_class">
                                                                <option value="-1">请选择</option>
                                                                <option :value="index" :key="index"  v-for="(item,index) in labelClassList" >{{item.cate_name}}</option>
                                                            </select>
                                                        </div>
                                                </div>
                                                <div class="layui-form-item" style="padding: 8px 0;">
                                                        <label class="layui-form-label">测评标签<i class="red">*</i></label>
                                                        <div class="layui-input-block" :style="{display: labelDiy ? 'flex':''}">
                                                            <select name="lable_span" lay-filter="lable_span">
                                                                <option value="-1">请选择</option>
																																<option :value="lableSpanList.length" v-if="labelHasOther===1">其它</option>
                                                                <option :value="index" v-for="(item,index) in lableSpanList" >{{item.name}}</option>

                                                            </select>

															<div v-if="labelDiy" style="margin-left: 30px; display: flex;align-items: center;">
																<input type="text" autocomplete="off" placeholder="请输入测评标签" class="layui-input" v-model="diy_label" style="margin-right: 30rpx;">
																<!-- <div class="layui-btn this.diy_label" @click="createDiyLabel" >确定</div> -->
															</div>
                                                        </div>
                                                </div>
                                                <div class="layui-form-item" >
                                                        <label class="layui-form-label">打分<i class="red">*</i></label>
                                                        <div class="layui-input-block" >
                                                        <input type="text" name="o_score" autocomplete="off" placeholder="" class="layui-input" v-model="o_score">
                                                        </div>
                                                </div>
                                                <div class="layui-btn" @click="labelOk">确定</div>
                                                <table class="layui-table" >
                                                    <thead>
                                                    <tr>
                                                        <th>测评分类</th>
                                                        <th>标签</th>
                                                        <th>打分</th>
                                                        <th>操作</th>
                                                    </tr>
                                                    </thead>
                                                    <tr v-for="(item,index) in labelCheckList">
                                                        <td>{{item.cate_name}}</td>
                                                        <td>{{item.name}} ({{item.diy ? '自建' :'系统'}})</td>
                                                        <td>{{item.score}}</td>
                                                        <td @click="delLabel(index,1)">删除</td>
                                                    </tr>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-tab-content">
                            <div class="layui-row layui-col-space15">
                                <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                    <div class="grid-demo grid-demo-bg1">
                                        <div class="layui-form-item" v-if="id">
                                            <button class="layui-btn layui-btn-primary layui-btn-sm" id="submit" type="button" @click="handleSubmit()">保存</button>
                                        </div>
                                        <div class="layui-form-item" v-else>
                                            <button class="layui-btn layui-btn-normal layui-btn-sm" id="submit" type="button" @click="handleSubmit()">提交</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        </div>
</div>
<script>
    var id = {$id};
    new Vue({
        el: '#app',
        data: {
            id:id,
            hidden_data:true,
            hidden_url_data: true,
            //分类列表
            evaluationInfo: [],
            articleInfo: [],
            upload:{
                videoIng:false
            },
            formData: {
                uid:0,
                product_id:0,
                store_id:0,
                product_image:'',
                store_image:'',
                images: [],
                url: '',
                avatar:'',
                content: '',
                score:0,//总评分
                labelCheckList:[],
                type: 0,
                status: 0,
            },
            //多属性header头
            formHeader:[],
            radioRule: [],//radio 当选规则
            rule: { //多图选择规则
                images: {
                    maxLength: 5
                }
            },
            ruleIndex:-1,
            progress: 0,
            um: null,//编译器实例化
            form: null,//layui.form
            layTabId: 1,
            ruleBool: id ? true : false,
            labelClassList:[],//测评分类
            labelClassIndex:-1,
            lableSpanList:[],//测评标签
            labelSpanIndex:-1,
            o_score:0,//单标签打分
            labelCheckList:[] ,//细分维度
            labelCheckSubmit:[] ,//最终提交合并数据
labelHasOther:0,//其它
labelDiy:false,
diy_label:''
        },
        watch: {
            "formData.score":function(a,b){
                this.o_score = a
            },
            "labelCheckList":function(a,b){
               if (!a.length) { return;}
               let score = 0;
                a.forEach((item,index)=>{
                score = score + Number(item.score)
               });
                this.formData.score = (score / a.length).toFixed(1)
          }
        },
        methods: {
            back:function(){
                var that = this;
                layui.use(['element'], function () {
                    layui.element.tabChange('docTabBrief', that.layTabId == 1 ? 1 : parseInt(that.layTabId) - 1);
                });
            },
            next:function(){
                var that = this;
                layui.use(['element'], function () {
                    layui.element.tabChange('docTabBrief', that.layTabId == 2 ? 2 : parseInt(that.layTabId) + 1);
                });
            },
            goBack:function(){
                location.href = this.U({c:'evaluation.management',a:'index'});
            },
            U: function (opt) {
                var m = opt.m || window.module, c = opt.c || window.controlle || '', a = opt.a || 'index', q = opt.q || '',
                    p = opt.p || {};
                var params = Object.keys(p).map(function (key) {
                    return key + '/' + p[key];
                }).join('/');
                var gets = Object.keys(q).map(function (key) {
                    return key+'='+ q[key];
                }).join('&');

                return '/' + m + '/' + c + '/' + a + (params == '' ? '' : '/' + params) + (gets == '' ? '' : '?' + gets);
            },
            /**
             * 获取商品信息
             * */
            getarticleInfo: function () {
                var that = this;
                that.requestGet(that.U({c:"evaluation.management",a:'get_evaluation_info',q:{id:that.id}})).then(function (res) {
                    var evaluationInfo = res.data.evaluationInfo || {};
                    if(evaluationInfo.id && that.id){
                        that.getLabelList(true,evaluationInfo.product_id);
                        that.$set(that,'formData',evaluationInfo);
                        that.$set(that,'labelCheckList',evaluationInfo.labelCheckList);
                        if (evaluationInfo.type == "1") {
                            that.hidden_data = true;
                            that.hidden_url_data = false;
                        }
                        if (evaluationInfo.type == "2") {
                            that.hidden_data = false;
                            that.hidden_url_data = true;
                        }
                    }
                    that.init();
                }).catch(function (res) {
                    that.showMsg(res.msg);
                })
            },
            /**
             * 提示
             * */
            showMsg: function (msg, success) {
                $('#submit').removeAttr('disabled').text('提交');
                layui.use(['layer'], function () {
                    layui.layer.msg(msg, success);
                });
            },
            /**
             * 删除图片
             * */
            deleteImage: function (key, index) {
                var that = this;
                if (index != undefined) {
                    that.formData[key].splice(index, 1);
                    that.$set(that.formData, key, that.formData[key]);
                } else {
                    that.$set(that.formData, key, '');
                }
            },
    		previewImage(key, index){
    			let _this = this;
    			layui.use('layer', function(){
    				let html = `<img src="${_this.formData[key][index]}" style="width:100%;height:100%;object-fit: contain"/>`
    			  var layer = layui.layer;
    			  layer.open({
    					title:'预览',
    					area: ['800px', '600px'],
    			        type: 1,
    			        content:html
    			  });
    			});
    		},
            createFrame: function (title, src, opt) {
                opt === undefined && (opt = {});
                var h = 0;
                if (window.innerHeight < 800 && window.innerHeight >= 700) {
                    h = window.innerHeight - 50;
                } else if (window.innerHeight < 900 && window.innerHeight >= 800) {
                    h = window.innerHeight - 100;
                } else if (window.innerHeight < 1000 && window.innerHeight >= 900) {
                    h = window.innerHeight - 150;
                } else if (window.innerHeight >= 1000) {
                    h = window.innerHeight - 200;
                } else {
                    h = window.innerHeight;
                }
                var area = [(opt.w || window.innerWidth / 2) + 'px', (!opt.h || opt.h > h ? h : opt.h) + 'px'];
                layui.use('layer',function () {
                    return layer.open({
                        type: 2,
                        title: title,
                        area: area,
                        fixed: false, //不固定
                        maxmin: true,
                        moveOut: false,//true  可以拖出窗外  false 只能在窗内拖
                        anim: 5,//出场动画 isOutAnim bool 关闭动画
                        offset: 'auto',//['100px','100px'],//'auto',//初始位置  ['100px','100px'] t[ 上 左]
                        shade: 0,//遮罩
                        resize: true,//是否允许拉伸
                        content: src,//内容
                        move: '.layui-layer-title'
                    });
                });
            },
            changeIMG: function (name, value) {
                if (this.getRule(name).maxLength !== undefined) {
                    var that = this;
                    value.map(function (v) {
                        that.formData[name].push(v);
                    });
                    this.$set(this.formData, name, this.formData[name]);
                } else {
                    if(name == 'batchAttr.pic'){
                        this.batchAttr.pic = value;
                    } else {
                        if (name.indexOf('.') !== -1) {
                            var key = name.split('.');
                            if (key.length == 2){
                                this.formData[key[0]][key[1]] = value;
                            } else if(key.length == 3){
                                this.formData[key[0]][key[1]][key[2]] = value;
                            } else if(key.length == 4){
                                this.$set(this.formData[key[0]][key[1]][key[2]],key[3],value)
                            }
                        } else {
                            this.formData[name] = value;
                        }
                    }
                }
            },
            getRule: function (name) {
                return this.rule[name] || {};
            },
            uploadImage: function (name) {
                return this.createFrame('选择图片',this.U({c:"widget.images",a:'index',p:{fodder:name}}),{h:545,w:900});
            },
            chooseUser: function (name) {
                return this.createFrame('选择用户',this.U({c:"user.user",a:'userList',p:{fodder:name}}),{h:545,w:900});
            },
            chooseProduct: function (name) {
                return this.createFrame('选择产品',this.U({c:"evaluation.management",a:'productList',p:{fodder:name}}),{h:545,w:900});
            },
            chooseStore: function (name) {
                return this.createFrame('选择门店',this.U({c:"evaluation.management",a:'storeList',p:{fodder:name}}),{h:545,w:900});
            },
            /**
             * 监听radio字段
             */
            eeventRadio: function () {
                var that = this;
                that.radioRule.map(function (val) {
                    that.form.on('radio(' + val + ')', function (res) {
                        that.formData[val] = res.value;
                    });
                })
            },
            init: function () {
                var that = this;
                that.$nextTick(function () {
                    layui.use(['form','element'], function () {
                        that.form = layui.form;
                        that.form.render();
                        that.form.on('select(temp_id)', function (data) {
                            that.$set(that.formData, 'temp_id', data.value);
                        });
                        that.form.on('select(rule_index)', function (data) {
                            that.ruleIndex = data.value;
                        });
                        layui.element.on('tab(docTabBrief)', function(){
                            that.layTabId = this.getAttribute('lay-id');
                        });
                        that.eeventRadio();

                        that.form.on('select(lable_class)', function (data) {
													that.labelDiy = false;
                        that.setLabelSpan(parseInt(data.value));
                        });
                        that.form.on('select(lable_span)', function (data) {
							console.log(parseInt(data.value)  , parseInt(that.lableSpanList.length))

							if(parseInt(data.value)  === parseInt(that.lableSpanList.length)){
								that.diy_label = '';
								that.labelDiy = true;
							}else{
								that.labelDiy = false;
							}
							that.labelSpanIndex=parseInt(data.value);
                        });
                       that.form.on('select(type)', function (data) {
                            if (data.value ) { //用户
                                if (data.value == 1) {
                                    that.hidden_url_data = false;
                                    that.hidden_data = true;
                                }
                                if (data.value == 2) {
                                    that.hidden_data = false;
                                    that.hidden_url_data = true;
                                }
                            }else{
                                 that.hidden_data = true;
                                 that.hidden_url_data = true;
                            }
                            that.formData.type = parseInt(data.value);
                        });
                        that.form.on('select(status)', function (data) {
                            that.formData.status = parseInt(data.value);
                        });
                    });
                })
            },
            requestPost: function (url, data) {
                return new Promise(function (resolve, reject) {
                    axios.post(url, data).then(function (res) {
                        if (res.status == 200 && res.data.code == 200) {
                            resolve(res.data)
                        } else {
                            reject(res.data);
                        }
                    }).catch(function (err) {
                        reject({msg:err})
                    });
                })
            },
            requestGet: function (url) {
                return new Promise(function (resolve, reject) {
                    axios.get(url).then(function (res) {
                        if (res.status == 200 && res.data.code == 200) {
                            resolve(res.data)
                        } else {
                            reject(res.data);
                        }
                    }).catch(function (err) {
                        reject({msg:err})
                    });
                })
            },
            handleSubmit:function () {
                var that = this;
                var type = that.formData.type;
                if (that.formData.type == 0 ) {
                    return that.showMsg('请选择评测评类型');
                }
                //官评
                if (type == 2) {
                    if (!that.formData.product_id) {
                        return that.showMsg('请选择关联产品');
                    }
                    if (!that.formData.url) {
                        return that.showMsg('请填写官方评测文章');
                    }
                    if (that.formData.score > 5 || that.formData.score <= 0) {
                        return that.showMsg('请正确填写打分');
                    }
                }
                //用户
                if (type == 1) {
                    if (!that.formData.uid) {
                        return that.showMsg('请选择关联用户');
                    }
                    if (!that.formData.product_id) {
                        return that.showMsg('请选择关联产品');
                    }
                    if (!that.formData.images.length) {
                        return that.showMsg('请上传评测图');
                    }
                    if (!that.formData.content) {
                        return that.showMsg('请上传评语');
                    }
                    if (that.formData.score > 5 || that.formData.score < 0) {
                        return that.showMsg('请正确填写打分');
                    }
                }
                $('#submit').attr('disabled', 'disabled').text('保存中...');
                that.requestPost(that.U({c:'evaluation.management',a:'save',p:{id:that.id}}),that.formData).then(function (res) {
                    that.confirm();
                }).catch(function (res) {
                    that.showMsg(res.msg);
                });
            },
            confirm: function(){
                var that = this;
                layui.use(['layer'], function () {
                    var layer = layui.layer;
                    layer.confirm(that.id ? '修改成功是否返回评测列表' : '添加成功是否返回评测列表', {
                        btn: ['返回列表',that.id ? '继续修改' : '继续添加'] //按钮
                    }, function(){
                        location.href = that.U({c:'evaluation.management',a:'index'});
                    }, function(){
                        location.reload();
                    });
                });
            },
            render:function(){
                this.$nextTick(function(){
                    layui.use(['form'], function () {
                        layui.form.render('select');
                    });
                })
            },
            // 移动
            handleDragStart (e, item) {
                this.dragging = item;
            },
            handleDragEnd (e, item) {
                this.dragging = null
            },
            handleDragOver (e) {
                e.dataTransfer.dropEffect = 'move'
            },
            handleDragEnter (e, item) {
                e.dataTransfer.effectAllowed = 'move'
                if (item === this.dragging) {
                    return
                }
                var newItems = [...this.formData.activity];
                var src = newItems.indexOf(this.dragging);
                var dst = newItems.indexOf(item);
                newItems.splice(dst, 0, ...newItems.splice(src, 1))
                this.formData.activity = newItems;
            },
            getLabelList:function (type,id) {
                var that = this;
                that.requestGet(that.U({m:window.module,c:'evaluation.LabelCategory',a:'get_category_default',p:{id:id}})).then(function (res) {
                    that.labelClassList = res.data;
                    that.$set(that,'labelClassList',that.labelClassList);
                    if (type !== undefined) {
                        that.render();
                    }
                });
            },
            inputChange(item,idx){
                let t = 0;
                this.formData.score_info.forEach((item,index)=>{
                    t= t + item.number
                })

                this.$set(this.formData, 'dimensionTotal', t);
                if(t!==100){
                     layer.msg('累计之和不为100');
                }
            },
            getVal(type,idArr,arr2,data){
                // 评测维度
                this.$set(this.formData, 'uid', idArr.uid);
                this.$set(this.formData, 'avatar', idArr.avatar);
            },
            getEvaluationProduct(data){
                // 评测维度
                this.$set(this.formData, 'product_id', data.id);
                this.$set(this.formData, 'product_image', data.image);
				this.getLabelList(true,data.id)
            },
            getEvaluationStore(data){
                // 评测维度
                this.$set(this.formData, 'store_id', data.id);
                this.$set(this.formData, 'store_image', data.image);
            },
            setLabelSpan(index){
                if(this.labelClassIndex===index)return;
                if(index!=-1){
                    this.labelClassIndex=index;
										this.labelHasOther = this.labelClassList[index].is_other;
                    this.lableSpanList = this.labelClassList[index].label_group || [];
                }else{
                    this.lableSpanList = []
                }
            this.render()
            },
            delLabel(index){
               this.labelCheckList.splice(index,1);
               this.formData.labelCheckList = this.labelCheckList;
             console.log(this.labelCheckList);
             console.log(this.formData);
            },
            labelOk(){
                if(this.labelClassIndex!=-1){
                    if(this.labelSpanIndex===-1){
                        return this.showMsg('请选择测评标签');
                    }
					if(this.labelDiy){
						if(this.diy_label===''){ return this.showMsg('请输入测评标签');}
						let obj = {
							score:'1.0',
							name: this.diy_label,
							diy: true
						},index = this.labelClassIndex,label_groups = this.labelClassList[index].label_group,is_has=false;
						label_groups.forEach((item,index)=>{
							if(item.name===obj.name){
								is_has = true; //'diy测评标签已存在'
								return this.showMsg('测评标签已存在');
							}
						})
						if(!is_has){
							this.diy_label = '';
							obj.id=label_groups.length;
							label_groups.push(obj);
							this.labelClassList[index].label_group = label_groups;
							this.$set(this.labelClassList[index],'label_group',label_groups);
							this.render();
						}

					}
                    let label = this.labelClassList[this.labelClassIndex],
						label_group=label.label_group[this.labelSpanIndex],
						labelCheckList = this.labelCheckList,
						labelCheckSubmit = this.labelCheckSubmit;
                    let obj = {
                        cate_name:label.cate_name,
                        name:label_group.name,
                        id:label.id,
                        label_id:label_group.id,
                        score:this.o_score,
    					label_group:[{
    						id:label_group.id,
    						name:label_group.name,
    						score:this.o_score,
    					}]
                    };
					if(label_group.diy){
						obj.diy = label_group.diy
					}
                    if(labelCheckList.length){
                        let isHas = false,isConcat=false;
                        labelCheckList.forEach((item,index)=>{
                            if(item.id===obj.id&&item.label_id===obj.label_id){
								console.log('仅修改')
								item.score=this.o_score;
								isHas = true;
                            }
                        })
                        labelCheckSubmit.forEach((item,index)=>{
                            if(item.id===obj.id  ){
                                if(item.label_id===obj.label_id){
									console.log('仅修改')
									item.score=this.o_score;
									isConcat=true;
								}else{
									console.log('同类合并',item);
									item.label_group=item.label_group.concat(obj.label_group)
									isConcat=true;
								}
                            }
                        })
                        if(!isHas){
                          this.labelCheckList.push(obj);
                        }
						if(!isConcat){
							this.labelCheckSubmit.push(obj);
						}
                    }else{
                        this.labelCheckList.push(obj);
					   this.labelCheckSubmit.push(obj);
                    }
                    console.log('this.formData.labelCheckList',this.labelCheckList)
                    this.formData.labelCheckList = this.labelCheckList;
										if(this.labelDiy){
											this.labelSpanIndex = parseInt(this.labelClassList[this.labelClassIndex].label_group.length)
										}
                }else{
                    return this.showMsg('请选择测评分类');
                }
            }
        },
        mounted: function () {
            var that = this;
            axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';
            that.getarticleInfo();
            // that.getLabelList(1);
            window.$vm = that;
            window.changeIMG = that.changeIMG;
            window.getVal = that.getVal;
            window.getEvaluationProduct = that.getEvaluationProduct;
            window.getEvaluationStore = that.getEvaluationStore;
            window.successFun = function(){
                that.getRuleList(1);
            }
        }
    });
</script>
</body>
</html>
