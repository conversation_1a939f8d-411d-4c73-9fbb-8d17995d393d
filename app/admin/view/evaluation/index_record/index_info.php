{extend name="public/container"}
{block name="content"}
<style>
    .backlog-body{
        padding: 10px 15px;
        background-color: #f8f8f8;
        color: #999;
        border-radius: 2px;
        transition: all .3s;
        -webkit-transition: all .3s;
        overflow: hidden;
        max-height: 84px;
    }
    .backlog-body h3{
        margin-bottom: 10px;
    }
    .right-icon{
        position: absolute;
        right: 10px;
    }
    .backlog-body p cite {
        font-style: normal;
        font-size: 17px;
        font-weight: 300;
        color: #009688;
    }
    .layuiadmin-badge, .layuiadmin-btn-group, .layuiadmin-span-color {
        position: absolute;
        right: 15px;
    }
    .layuiadmin-badge {
        top: 50%;
        margin-top: -9px;
        color: #01AAED;
    }
    .imgsize{
        margin-bottom: 2px;
        width: 320px;
        height: : 200px;
    }
</style>
<div class="layui-flid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12 layui-col-sm12 layui-col-lg12">
            <div class="layui-card">
                <div class="layui-card-header">源未指数详情</div>
                <div class="layui-card-body">
                    <div class="layui-row layui-col-space15">
                        <div class="layui-col-md12">
                            <div class="layui-card">
                                <div class="layui-card-body">
                                    <div class="layui-inline" style="width: 305px">
                                        <label class="layui-form-label">产品标题：<span>{$IndexInfo.title}</span></label>
                                    </div>
                                    <div class="layui-inline" style="margin-left: 50px;">
                                        <label class="layui-form-label"><span> 统计时间：{$IndexInfo.add_time|date="Y/m/d H:i"} &nbsp;&nbsp; </span></label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                 <div class="layui-row layui-col-space15">
                    <div class="layui-col-md12">
                        <div class="layui-card">
                            <div class="layui-card-body">
                                <div class="layui-inline" style="width: 305px">
                                    <label class="layui-form-label">源未指数：<span>{$IndexInfo.yw_index}</span></label>
                                </div>
                                <div class="layui-inline" style="margin-left: 50px;">
                                    <label class="layui-form-label">官方评分：<span>{$IndexInfo.official_rating}</span></label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-row layui-col-space15">
                    <div class="layui-col-md12">
                        <div class="layui-card">
                            <div class="layui-card-body">
                                <div class="layui-inline" style="width: 305px">
                                    <label class="layui-form-label">本次用户评分：<span>{$IndexInfo.this_user_rating}</span></label>
                                </div>
                                <div class="layui-inline" style="margin-left: 50px;">
                                    <label class="layui-form-label">上期用户评分：<span>{$IndexInfo.previous_user_rating}</span></label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-col-md12 layui-col-sm12 layui-col-lg12">
            <div class="layui-card">
                <div class="layui-card-header">标签</div>
                <div class="layui-card-body">
                    <div class="layui-tab layui-tab-card">
                        <ul class="layui-tab-title">
                            <li class="layui-this">列表</li>
                        </ul>
                        <div class="layui-tab-content" id="content">
                            <div class="layui-tab-item layui-show">
                                <table class="layui-table" lay-skin="line" v-cloak="">
                                    <thead>
                                        <tr>
                                            <th style="text-align: center;">产品ID/产品名</th>
                                            <th style="text-align: center;">标签名</th>
                                            <th style="text-align: center;">分数</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr v-for="item in labelList">
                                            <td class="text-center">{{item.product_id==null ? '暂无信息':item.product_id}}/{{item.title}}</td>
                                            <td class="text-center">{{item.name}}</td>
                                            <td class="text-center">{{item.this_rating}}</td>
                                        </tr>
                                        <tr v-show="labelList.length<=0" style="text-align: center">
                                            <td colspan="6">暂无数据</td>
                                        </tr>
                                    </tbody>
                                </table>
                                <div ref="page_order" v-show="count.order_count > limit" style="text-align: right;"></div>
                            </div>
                            <!--end-->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="{__ADMIN_PATH}js/layuiList.js"></script>
<script>
    layList.tool(function (event, data, obj) {
        switch (event) {
            case 'exclude_lottery':
                var url = layList.U({c: 'order.store_order', a: 'offline', p: {id: data.id}});
                $eb.$swal('delete', function () {
                    $eb.axios.get(url).then(function (res) {
                        if (res.status == 200 && res.data.code == 200) {
                            $eb.$swal('success', res.data.msg);
                        } else
                            return Promise.reject(res.data.msg || '修改失败')
                    }).catch(function (err) {
                        $eb.$swal('error', err);
                    });
                }, {'title': '您确定要修改支付状态吗？', 'text': '修改后将无法恢复,请谨慎操作！', 'confirm': '是的，我要修改'})
                break;
            case 'show_report':
                $eb.createModalFrame(data.nickname + '订单详情', layList.U({a: 'order_info', q: {oid: data.id}}));
                break;
        }
    })
    var count=1,
    $id=<?=$id?>;
    require(['vue'],function(Vue) {
        new Vue({
            el: "#content",
            data: {
                limit:10,
                id:$id,
                labelList:[],
                page:{
                    label_page:1,
                },
            },
            watch:{
                'page.label_page':function () {
                    this.getLabelList();
                }
            },
            methods:{
                getLabelList:function(){
                    this.request('getLabelList',this.page.label_page,'labelList');
                },
                request:function (action,page,name) {
                    var that=this;
                    layList.baseGet(layList.U({a:action,p:{page:page,limit:this.limit,id:this.id}}),function (res) {
                        that.$set(that,name,res.data || [])
                    });
                }
            },
            mounted:function () {
                this.getLabelList();
                var that=this;
                layList.laypage.render({
                    elem: that.$refs.page_order
                    ,count:3
                    ,limit:that.limit
                    ,theme: '#1E9FFF',
                    jump:function(obj){
                        that.page.label_page=obj.curr;
                    }
                });
            }
        });
    });
</script>
{/block}