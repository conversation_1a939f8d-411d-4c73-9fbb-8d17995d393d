{extend name="public/container"}
{block name="head_top"}

{/block}
{block name="content"}
<style>
    .backlog-body{
        padding: 10px 15px;
        background-color: #f8f8f8;
        color: #999;
        border-radius: 2px;
        transition: all .3s;
        -webkit-transition: all .3s;
        overflow: hidden;
        max-height: 84px;
    }
    .backlog-body h3{
        margin-bottom: 10px;
    }
    .right-icon{
        position: absolute;
        right: 10px;
    }
    .backlog-body p cite {
        font-style: normal;
        font-size: 17px;
        font-weight: 300;
        color: #009688;
    }
    .layuiadmin-badge, .layuiadmin-btn-group, .layuiadmin-span-color {
        position: absolute;
        right: 15px;
    }
    .layuiadmin-badge {
        top: 50%;
        margin-top: -9px;
        color: #01AAED;
    }
    .imgsize{
        margin-bottom: 2px;
        width: 320px;
        height: : 200px;
    }
</style>
<div class="layui-flid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12 layui-col-sm12 layui-col-lg12">
            <div class="layui-card">
                <div class="layui-card-header">评测详情</div>
                <div class="layui-card-body">
                    <div class="layui-row layui-col-space15">
                        <div class="layui-col-md12">
                            <div class="layui-card">
                                <div class="layui-card-body">
                                    <div class="layui-inline" style="width: 305px">
                                        <label class="layui-form-label">主题：<span>{$articleInfo.name}</span></label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                     <div class="layui-row layui-col-space15">
                        <div class="layui-col-md12">
                            <div class="layui-card">
                                <div class="layui-card-body">
                                    <div class="layui-inline" style="width: 305px">
                                        <label class="layui-form-label">评测维度：<span></span></label>
                                        <div class="layui-input-block">
                                            {volist name='articleInfo.score_info' id='vo'}
                                                <span>{$vo.name}</span> - <span>权重：{$vo.number}</span><br>
                                            {/volist}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-row layui-col-space15">
                        <div class="layui-col-md12">
                            <div class="layui-card">
                                <div class="layui-card-body">
                                    <div class="layui-inline" style="width: 305px">
                                        <label class="layui-form-label">总评分：
                                            <span>
                                                {$articleInfo.evaluation_score}
                                            </span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-row layui-col-space15">
                        <div class="layui-col-md12">
                            <div class="layui-card">
                                <div class="layui-card-body">
                                    <div class="layui-inline" style="width: 305px">
                                        <label class="layui-form-label">评语：
                                            {$articleInfo.comment}
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-row layui-col-space15">
                        <div class="layui-col-md12">
                            <div class="layui-card">
                                <div class="layui-card-body">
                                    <div class="layui-inline">
                                        <label class="layui-form-label">评测图</label>
                                        <div class="layui-input-block">
                                            {volist name='articleInfo.pics' id='vo'}
                                                <img src="{$vo}" class="imgsize" style="width: 200px;height: 200px;">
                                            {/volist}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-col-md12 layui-col-sm12 layui-col-lg12">
            <div class="layui-card">
                <div class="layui-card-header">相关评论</div>
                <div class="layui-card-body">
                    <div class="layui-tab layui-tab-card">
                        <ul class="layui-tab-title">
                            <li class="layui-this">评论列表</li>&nbsp;&nbsp;&nbsp;

                            <button class="layui-btn layui-btn-sm" class="btn btn-w-m btn-primary" onclick="$eb.createModalFrame('新建评论','{:Url('create_comment')}?activity_reviews_id={$articleInfo.id}')">&nbsp;&nbsp;&nbsp;新建评论</button>
                        </ul>
                        <div class="layui-tab-content" id="content">
                            <div class="layui-tab-item layui-show">
                                <table class="layui-table" lay-skin="line" v-cloak="">
                                    <thead>
                                        <tr>
                                            <th style="text-align: center;">ID</th>
                                            <th style="text-align: center;">用户信息</th>
                                            <th style="text-align: center;">回复</th>
                                            <th style="text-align: center;">评论</th>
                                            <th style="text-align: center;">提交时间</th>
                                            <th style="text-align: center;">状态</th>
                                            <th style="text-align: center;">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr v-for="(item,index) in commentList" :key="index">
                                            <td class="text-center">{{item.id}}</td>
                                            <td class="text-center">{{item.nickname==null ? '暂无信息':item.nickname}}/{{item.uid}}</td>
                                            <td class="text-center">{{item.type_name}}</td>
                                            <td class="text-center">{{item.comment}}</td>
                                            <td class="text-center">{{item.status_name}}</td>
                                            <td class="text-center">{{item.add_time}}</td>
                                            <td class="text-center">
                                                <span class="edit layui-btn layui-btn-xs layui-btn-normal" @click="editHandle('delete',item,index)" v-if="item.status == 1"><i class="fa fa-window-close-o"></i> 屏蔽</span>
                                                <span class="del layui-btn layui-btn-xs layui-btn-normal" @click="editHandle('review',item,index)" v-if="item.status == 2"><i class="fa fa-calendar"></i> 审核通过</span>
												<span class="del layui-btn layui-btn-xs layui-btn-normal" @click="editHandle('reply',item,index)"  v-if="item.is_del == 0"><i class="fa fa-comments-o"></i> 回复</span>
                                            </td>
                                        </tr>
                                        <tr v-show="commentList.length<=0" style="text-align: center">
                                            <td colspan="6">暂无数据</td>
                                        </tr>
                                    </tbody>
                                </table>
                                <div ref="page_order" v-show="count.order_count > limit" style="text-align: right;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="{__ADMIN_PATH}js/layuiList.js"></script>
{/block}
{block name="script"}
<script>
    var count=<?=json_encode($count)?>,
    $id=<?=$id?>;
    require(['vue'],function(Vue) {
        new Vue({
            el: "#content",
            data: {
                limit:10,
                id:$id,
                commentList:[],
                page:{
                    comment_page:1
                },
            },
            watch:{
                'page.comment_page':function () {
                    this.getCommentList();
                }
            },
            methods:{
                getCommentList:function(){
                    this.request('getCommentList',this.page.comment_page,'commentList');
                },
                editHandle:function(type,item,index){
                	switch (type) {
                	    case 'delete':
                	        var url = layList.U({c: 'evaluation.comment', a: 'destroy', p: {id: item.id}});
                	        $eb.$swal('delete', function () {
                	            $eb.axios.get(url).then(function (res) {
                	                if (res.status == 200 && res.data.code == 200) {
                	                    $eb.$swal('success', res.data.msg);
                	                } else
                	                    return Promise.reject(res.data.msg || '屏蔽失败')
                	            }).catch(function (err) {
                	                $eb.$swal('error', err);
                	            });
                	        }, {'title': '您确定要屏蔽评论吗？', 'text': '屏蔽后将无法恢复,请谨慎操作！', 'confirm': '是的，我要屏蔽'})
                	        break;
                	    case 'review':
                	        var url = layList.U({c: 'evaluation.comment', a: 'review', p: {id: item.id,status: item.status}});
                	        $eb.$swal('delete', function () {
                	            $eb.axios.get(url).then(function (res) {
                	                if (res.status == 200 && res.data.code == 200) {
                	                    $eb.$swal('success', res.data.msg);
                	                } else
                	                    return Promise.reject(res.data.msg || '审核失败')
                	            }).catch(function (err) {
                	                $eb.$swal('error', err);
                	            });
                	        }, {'title': '您确定审核该评论吗？', 'text': '审核后，评论状态为待发布状态！', 'confirm': '是的，我要审核'})
                	    	break;
                	   case 'reply':
                	        $eb.createModalFrame(item.nickname + '回复评论', layList.U({a: 'reply', q: {id: item.id}}));
                	        break;
                	}
                },
                request:function (action,page,name) {
                    var that=this;
                    layList.baseGet(layList.U({a:action,p:{page:page,limit:this.limit,id:this.id}}),function (res) {
                        that.$set(that,name,res.data || [])
                    });
                }
            },
            mounted:function () {
                this.getCommentList();
                var that=this;
                layList.laypage.render({
                    elem: that.$refs.page_order
                    ,count:3
                    ,limit:that.limit
                    ,theme: '#1E9FFF',
                    jump:function(obj){
                        that.page.comment_page=obj.curr;
                    }
                });
                layList.laypage.render({
                    elem: that.$refs.sales_order
                    ,count:3
                    ,limit:that.limit
                    ,theme: '#1E9FFF',
                    jump:function(obj){
                        that.page.welfare_page=obj.curr;
                    }
                });
            }
        });
    });
</script>
{/block}