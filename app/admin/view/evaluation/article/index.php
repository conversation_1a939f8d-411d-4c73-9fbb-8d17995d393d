{extend name="public/container"}
{block name="head_top"}

{/block}
{block name="content"}
<style>
    .btn-outline{
        border:none;
    }
    .btn-outline:hover{
        background-color: #0e9aef;
        color: #fff;
    }
    .layui-form-item .layui-btn {
        margin-top: 5px;
        margin-right: 10px;
    }
    .layui-btn-primary{
        margin-right: 10px;
        margin-left: 0!important;
    }
    label{
        margin-bottom: 0!important;
        margin-top: 4px;
    }
</style>
<div class="layui-fluid">
    <div class="layui-row layui-col-space15" id="app">
        <!--搜索条件-->
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">搜索条件</div>
                <div class="layui-card-body">
                    <div class="layui-carousel layadmin-carousel layadmin-shortcut" lay-anim="" lay-indicator="inside"
                         lay-arrow="none" style="background:none">
                        <div class="layui-card-body">
                            <div class="layui-row layui-col-space10 layui-form-item">
                                <div class="layui-col-lg12">
                                    <label class="layui-form-label">状态:</label>
                                    <div class="layui-input-block" v-cloak="">
                                        <button class="layui-btn layui-btn-sm"
                                                :class="{'layui-btn-primary':where.status!==item.value}"
                                                @click="where.status = item.value" type="button"
                                                v-for="item in userStatus">{{item.name}}
                                            <span v-if="item.count!=undefined"
                                                  :class="item.class!=undefined ? 'layui-badge': 'layui-badge layui-bg-gray' ">{{item.count}}</span>
                                        </button>
                                    </div>
                                </div>
                                <div class="layui-col-lg12">
                                    <label class="layui-form-label">创建时间:</label>
                                    <div class="layui-input-block" data-type="data" v-cloak="">
                                        <button class="layui-btn layui-btn-sm" type="button" v-for="item in dataList"
                                                @click="setData(item)"
                                                :class="{'layui-btn-primary':where.data!=item.value}">{{item.name}}
                                        </button>
                                        <button class="layui-btn layui-btn-sm" type="button" ref="time"
                                                @click="setData({value:'zd',is_zd:true})"
                                                :class="{'layui-btn-primary':where.data!='zd'}">自定义
                                        </button>
                                        <button type="button" class="layui-btn layui-btn-sm layui-btn-primary"
                                                v-show="showtime==true" ref="date_time">{$year.0} - {$year.1}
                                        </button>
                                    </div>
                                </div>
                                <div class="layui-col-lg12">
                                    <label class="layui-form-label">看板:</label>
                                    <div class="layui-input-block" id="billboard_id" lay-filter="billboard_id">
                                    </div>
                                </div>
                                <div class="layui-col-lg12">
                                    <label class="layui-form-label">关联活动:</label>
                                    <div class="layui-input-block" id="activity_id" lay-filter="activity_id">
                                    </div>
                                </div>
                                <div class="layui-col-lg12">
                                    <div class="layui-input-block">
                                        <button @click="search" type="button"
                                                class="layui-btn layui-btn-sm layui-btn-normal">
                                            <i class="layui-icon layui-icon-search"></i>搜索
                                        </button>
                                        <button @click="refresh" type="reset"
                                                class="layui-btn layui-btn-primary layui-btn-sm">
                                            <i class="layui-icon layui-icon-refresh"></i>刷新
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--end-->
    </div>
    <!--列表-->
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">用户评测列表</div>
                <div class="layui-card-body">
                    <div class="layui-btn-container" id="container-action">
                        <button class="layui-btn layui-btn-sm" class="btn btn-w-m btn-primary" onclick="$eb.createModalFrame(this.innerText,'{:Url('create')}',{w:1100,h:760})">创建评测</button>
                        <button class="layui-btn layui-btn-sm" data-type="show">批量审核</button>
                    </div>
                    <table class="layui-hide" id="List" lay-filter="List"></table>
                    <!--头图-->
                    <script type="text/html" id="info">
                        <div class="layui-table-cell laytable-cell-1-0-1"> <img style="cursor: pointer" lay-event="open_image" src="{{ d.image }}"> </div>
                        {{d.name==null ? '暂无信息':d.name}}
                    </script>
                    <!-- 时间 -->、
                    <script type="text/html" id="time">
                        创建时间：{{d.add_time}}</br>
                        更新时间：{{d.add_time}}
                    </script>
                    <!--评测文章状态-->
                    <script type="text/html" id="status">
                        {{d.status_name}}
                    </script>
                    <script type="text/html" id="act">
                        <button type="button" class="layui-btn layui-btn-xs layui-btn-normal" lay-event='review'>
                            审核
                        </button>
                        <button type="button" class="layui-btn layui-btn-xs layui-btn-normal" lay-event='article_info'>
                            详情
                        </button>
                        <button type="button" class="layui-btn layui-btn-xs" onclick="dropdown(this)">操作 <span class="caret"></span></button>
                        <ul class="layui-nav-child layui-anim layui-anim-upbit">
                            <li>
                                <a href="javascript:void(0);" lay-event="deleted">
                                    <i class="fa fa-times"></i> 删除
                                </a>
                            </li>
                        </ul>
                    </script>
                </div>
            </div>
        </div>
    </div>
    <!--end-->
</div>
<script src="{__ADMIN_PATH}js/layuiList.js"></script>
{/block}
{block name="script"}
<script>
    layList.tableList('List', "{:Url('article_list')}", function () {
        return [
            {type: 'checkbox'},
            {field: 'id', title: 'ID',width: '5%'},
            {field: 'info', title: '所属看板', templet: "#info", width: '20%',align:'center'},
            {field: 'add_time', title: '提交时间', width: '20%',templet: "#time", sort: true, align: 'center'},
            {field: 'evaluation_score', title: '评分', width: '14%', align: 'center'},
            {field: 'comment', title: '评语', width: '14%', align: 'center'},
            {field: 'status', title: '状态', templet: '#status', width: '12%', align: 'center'},
            {field: 'right', title: '操作', align: 'center', toolbar: '#act', width: '12.4%'},
        ];
    });
    layList.tool(function (event, data, obj) {
        switch (event) {
            case 'deleted':
                var url = layList.U({c: 'evaluation.article', a: 'destroy', p: {id: data.id}});
                $eb.$swal('delete', function () {
                    $eb.axios.get(url).then(function (res) {
                        if (res.status == 200 && res.data.code == 200) {
                            $eb.$swal('success', res.data.msg);
                        } else
                            return Promise.reject(res.data.msg || '删除失败')
                    }).catch(function (err) {
                        $eb.$swal('error', err);
                    });
                }, {'title': '您确定要删除评测文章吗？', 'text': '删除后将无法恢复,请谨慎操作！', 'confirm': '是的，我要删除'})
                break;
            case 'review':
                var url = layList.U({c: 'evaluation.article', a: 'review', p: {id: data.id,status: data.status}});
                $eb.$swal('delete', function () {
                    $eb.axios.get(url).then(function (res) {
                        if (res.status == 200 && res.data.code == 200) {
                            $eb.$swal('success', res.data.msg);
                        } else
                            return Promise.reject(res.data.msg || '审核失败')
                    }).catch(function (err) {
                        $eb.$swal('error', err);
                    });
                }, {'title': '您确定审核该评测文章吗？', 'text': '审核后，评测文章状态为待发布状态！', 'confirm': '是的，我要审核'})
                break;
            case 'open_image':
                $eb.openImage(data.image);
                break;
            case 'edit':
                location.href = layList.U({a:'create',q:{id:data.id}});
                break;
            case 'article_info':
                $eb.createModalFrame(data.name + '评测文章详情', layList.U({a: 'article_info', q: {id: data.id}}));
                break;
        }
    })
    $('#container-action').find('button').each(function () {
        $(this).on('click', function () {
            var act = $(this).data('type');
            action[act] && action[act]();
        });
    })
    //下拉框
    $(document).click(function (e) {
        $('.layui-nav-child').hide();
    })
    //自定义方法
    var action={
        show:function(){
            var ids=layList.getCheckData().getIds('id');
            if(ids.length){
                layList.basePost(layList.Url({c:'evaluation.article',a:'release'}),{ids:ids},function (res) {
                    layList.msg(res.msg);
                    layList.reload();
                });
            }else{
                layList.msg('请选择要审核的评测文章');
            }
        }
    };

    function dropdown(that) {
        var oEvent = arguments.callee.caller.arguments[0] || event;
        oEvent.stopPropagation();
        var offset = $(that).offset();
        var top = offset.top - $(window).scrollTop();
        var index = $(that).parents('tr').data('index');
        $('.layui-nav-child').each(function (key) {
            if (key != index) {
                $(this).hide();
            }
        })
        if ($(document).height() < top + $(that).next('ul').height()) {
            $(that).next('ul').css({
                'padding': 10,
                'top': -($(that).parents('td').height() / 2 + $(that).height() + $(that).next('ul').height() / 2),
                'min-width': 'inherit',
                'position': 'absolute'
            }).toggle();
        } else {
            $(that).next('ul').css({
                'padding': 10,
                'top': $(that).parents('td').height() / 2 + $(that).height(),
                'min-width': 'inherit',
                'position': 'absolute'
            }).toggle();
        }
    }

    var founder = '<?=$founder?>';
    var KanbanCount =<?=$KanbanCount?>,evaluationCategory = <?= $evaluationCategory?>,evaluationActivity = <?= $evaluationActivity?>,
        status =<?=$status ? $status : "''"?>;
    require(['vue'], function (Vue) {
        new Vue({
            el: "#app",
            data: {
                badge: [],
                evaluationList: evaluationCategory,
                activityList: evaluationActivity,
                userStatus: [
                    {name: '全部', value: ''},
                    {name: '待审核', value: 2, count: KanbanCount.pending, class: true},
                    {name: '已发布', value: 1, count: KanbanCount.published},
                    {name: '已删除', value: 3, count: KanbanCount.deleted},
                ],
                dataList: [
                    {name: '全部', value: ''},
                    {name: '今天', value: 'today'},
                    {name: '昨天', value: 'yesterday'},
                    {name: '最近7天', value: 'lately7'},
                    {name: '最近30天', value: 'lately30'},
                    {name: '本月', value: 'month'},
                    {name: '本年', value: 'year'},
                ],
                where: {
                    data: '',
                    status: status,
                    founder: founder || '',
                    cate_id:  [],
                },
                showtime: false,
            },
            watch: {
                'where.status': function () {
                    this.where.excel = 0;
                    layList.reload(this.where, true);
                },
                'where.data': function () {
                    this.where.excel = 0;
                    layList.reload(this.where, true);
                },
                'where.type': function () {
                    this.where.excel = 0;
                    layList.reload(this.where, true);
                },
            },
            methods: {
                setData: function (item) {
                    var that = this;
                    if (item.is_zd == true) {
                        that.showtime = true;
                        this.where.data = this.$refs.date_time.innerText;
                    } else {
                        this.showtime = false;
                        this.where.data = item.value;
                    }
                },
                search: function () {
                    that = this
                    let billboard_id = $('input[name="billboard_id"]').val();
                    if (billboard_id != '') {
                        this.where.billboard_id = billboard_id.split(',');
                    }else{
                        this.where.billboard_id = [];
                    }
                    let activity_id = $('input[name="activity_id"]').val();
                    if (activity_id != '') {
                        this.where.activity_id = activity_id.split(',');
                    }else{
                        this.where.activity_id = [];
                    }               
                    layList.reload(this.where, true);
                },
                refresh: function () {
                    layList.reload();
                },
                excel: function () {
                    location.href = layList.U({c: 'evaluation.article', a: 'article_list', q: this.where});
                },
                init: function () {
                var that = this;
                that.$nextTick(function () {
                    layui.use(['form','element'], function () {
                        that.form = layui.form;
                        that.form.render();
                        that.form.on('select(billboard_id)', function (data) {
                            console.log(data);
                            that.$set(that.where, 'billboard_id', data.value);
                        });
                        that.form.on('select(activity_id)', function (data) {
                            console.log(data);
                            that.$set(that.where, 'activity_id', data.value);
                        });
                        layui.element.on('tab(docTabBrief)', function(){
                            that.layTabId = this.getAttribute('lay-id');
                        });
                    });

                    layui.config({
                        base : '/static/plug/layui/'
                    }).extend({
                        selectN: './selectN',
                    }).use('selectM',function () {
                        var selectM = layui.selectM;
                        selectM({
                            //元素容器【必填】
                            elem: '#billboard_id'
                            //候选数据【必填】
                            ,data: that.evaluationList
                            //默认值
                            ,selected: that.where.billboard_id || []
                            //最多选中个数，默认5
                            ,max : 10
                            ,name: 'billboard_id'
                            ,model: 'where.billboard_id'
                            //值的分隔符
                            ,delimiter: ','
                            //候选项数据的键名
                            ,field: {idName:'id',titleName:'cate_name',statusName:'disabled'}
                        }),
                        selectM({
                            //元素容器【必填】
                            elem: '#activity_id'
                            //候选数据【必填】
                            ,data: that.activityList
                            //默认值
                            ,selected: that.where.activity_id || []
                            //最多选中个数，默认5
                            ,max : 10
                            ,name: 'activity_id'
                            ,model: 'where.activity_id'
                            //值的分隔符
                            ,delimiter: ','
                            //候选项数据的键名
                            ,field: {idName:'id',titleName:'name',statusName:'disabled'}
                        });
                    });
                })
            },
            },
            mounted: function () {
                this.init();
                var that = this;
                window.formReload = this.search;
                layList.laydate.render({
                    elem: this.$refs.date_time,
                    trigger: 'click',
                    eventElem: this.$refs.time,
                    range: true,
                    change: function (value) {
                        that.where.data = value;
                    }
                });
            }
        })
    });
</script>
{/block}