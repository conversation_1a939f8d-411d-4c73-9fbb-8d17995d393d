<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
		<link href="{__FRAME_PATH}css/font-awesome.min.css" rel="stylesheet">
		<link href="{__ADMIN_PATH}plug/umeditor/themes/default/css/umeditor.css" type="text/css" rel="stylesheet">
		<script type="text/javascript" src="{__ADMIN_PATH}plug/umeditor/third-party/jquery.min.js"></script>
		<script type="text/javascript" src="{__ADMIN_PATH}plug/umeditor/third-party/template.min.js"></script>
		<script type="text/javascript" charset="utf-8" src="{__ADMIN_PATH}plug/umeditor/umeditor.config.js"></script>
		<script type="text/javascript" charset="utf-8" src="{__ADMIN_PATH}plug/umeditor/umeditor.min.js"></script>
		<script type="text/javascript" src="{__ADMIN_PATH}plug/umeditor/lang/zh-cn/zh-cn.js"></script>
		<link rel="stylesheet" href="/static/plug/layui/css/layui.css">
		<script src="/static/plug/layui/layui.js"></script>
		<script src="{__PLUG_PATH}vue/dist/vue.min.js"></script>
		<script src="/static/plug/axios.min.js"></script>
		<script src="/static/plug/iview/dist/iview.min.js"></script>
		<script src="{__MODULE_PATH}widget/aliyun-oss-sdk-4.4.4.min.js"></script>
		<script src="{__MODULE_PATH}widget/cos-js-sdk-v5.min.js"></script>
		<script src="{__MODULE_PATH}widget/qiniu-js-sdk-2.5.5.js"></script>
		<script src="{__MODULE_PATH}widget/plupload.full.min.js"></script>
		<script src="{__MODULE_PATH}widget/videoUpload.js"></script>
		<style>
			.pictrueBox {
            display: inline-block !important;
        }

        .pictrue {
            width: 60px;
            height: 60px;
            border: 1px dotted rgba(0, 0, 0, 0.1);
            margin-right: 15px;
            display: inline-block;
            position: relative;
            cursor: pointer;
        }

        .pictrue img {
            width: 100%;
            height: 100%;
        }

        .upLoad {
            width: 58px;
            height: 58px;
            line-height: 58px;
            border: 1px dotted rgba(0, 0, 0, 0.1);
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.02);
            cursor: pointer;
            display: flex;
            justify-content: center;
            align-items: center;
        }


        .closes {
            position: absolute;
            left: 86%;
            top: -18%;
        }
        .red {
            color: red;
        }

     .flex {
     	display: flex;
     }
     .flex_align_center {
     	align-items: center;
     }
		 .add{
			 width: 28px;
			 height: 28px;
			 text-align: center;
			 line-height: 28px;
			 border:1px solid #000;
			 border-radius: 50%;
			 font-size: 20px;
			 margin:0 15px;
			 cursor: pointer;
		 }
		.ivu-input{
			 border-width: 0px !important;
			 width: 100%;
			 height: 36px;
		 }
		 .ivu-select-dropdown{
			 z-index: 999;
			 background: #fff;
		 }
		 .ivu-cascader-menu .ivu-cascader-menu-item-active {
		     background-color: #f3f3f3;
		     color: #2d8cf0;
		 }
		 .ivu-cascader-menu .ivu-cascader-menu-item {
		     position: relative;
		     padding-right: 24px;
		     -webkit-transition: all .2s ease-in-out;
		     transition: all .2s ease-in-out;
		 }
		 .ivu-cascader .ivu-cascader-menu-item {
		     margin: 0;
		     line-height: normal;
		     padding: 7px 16px;
		     clear: both;
		     color: #495060;
		     font-size: 12px!important;
		     white-space: nowrap;
		     list-style: none;
		     cursor: pointer;
		     -webkit-transition: background .2s ease-in-out;
		     transition: background .2s ease-in-out;
		 }
		 .ivu-cascader-menu:last-child {
		     border-right-color: transparent;
		     margin-right: -1px;
		 }
		 .ivu-cascader-menu {
		     display: inline-block;
		     min-width: 100px;
		     height: 180px;
		     margin: 0;
		     padding: 5px 0!important;
		     vertical-align: top;
		     list-style: none;
		     border-right: 1px solid #e9eaec;
		     overflow: auto;
				 }
		 .padding{
			 padding:10px 0;
		 }
        .info{
            color: #c9c9c9;
            padding-left: 10px;
            line-height: 30px;
        }

    </style>
    <script type="text/javascript">
        $eb = parent._mpApi;
        window.controlle="<?php echo strtolower(trim(preg_replace("/[A-Z]/", "_\\0", app('request')->controller()), "_"));?>";
        window.module="6GqvmHa8HRGHoQEQ";
    </script>
	</head>
	<body>
		<div class="layui-fluid">
			<div class="layui-row layui-col-space15" id="app" v-cloak="">
				<div class="layui-card">
					<div class="layui-card-header">
						<span class="">{{id ? '活动修改': '活动添加' }}</span>
						<button style="margin-left: 20px" type="button" class="layui-btn layui-btn-primary layui-btn-xs" @click="goBack">返回列表</button>
					</div>
					<div class="layui-card-body">
						<form class="layui-form" action="" v-cloak="">
							<div class="layui-tab layui-tab-brief" lay-filter="docTabBrief">
								<ul class="layui-tab-title">
									<li class="layui-this" lay-id='1'>设置基础信息</li>
									<li lay-id='2'>设置奖励</li>
								</ul>
								<div class="layui-tab-content">
									<div class="layui-tab-item layui-show">
										<div class="layui-row layui-col-space15">
											<div class="layui-form-item">
												<label class="layui-form-label">活动主题<i class="red">*</i></label>
												<div class="layui-input-block">
													<input type="text" name="name" lay-verify="title" autocomplete="off" placeholder="请输入活动主题" class="layui-input"
													 v-model="formData.name" maxlength="100">
												</div>
											</div>
											<div class="layui-form-item">
												<label class="layui-form-label">活动时间</label>
												<div class="layui-input-block">
													<input type="text" name="form_start_end" id="form_start_end" v-model="formData.form_start_end" required
													 lay-verify="required" placeholder="选择活动时间" class="layui-input" readonly="readonly">
												</div>
											</div>
											<div class="layui-form-item">
												<label class="layui-form-label">导览图<i class="red">*</i></label>
												<div class="pictrueBox pictrue" v-for="(item,index) in formData.slider_image">
													<img :src="item" />
													<i class="layui-icon closes" @click="deleteImage('slider_image',index)">&#x1007;</i>
												</div>
												<div class="pictrueBox">
													<div class="upLoad" @click="uploadImage('slider_image')" v-if="formData.slider_image.length <= rule.slider_image.maxLength">
														<i class="layui-icon layui-icon-camera" class="iconfont" style="font-size: 26px;"></i>
													</div>
												</div>
											</div>

											<div class="layui-form-item">
												<label class="layui-form-label">活动地点<i class="red">*</i></label>
												<div class="layui-input-block">
													<Cascader :data="addresData" v-model="formData.address" @on-change="handleChange" class="layui-input"></Cascader>
												</div>
											</div>
											<div class="layui-form-item">
												<label class="layui-form-label">详细地址<i class="red">*</i></label>
												<div class="layui-input-block">
													<input type="text" name="detailed_address" autocomplete="off" placeholder="请输入详细地址" class="layui-input" v-model="formData.detailed_address">
												</div>
											</div>
											<div class="layui-form-item">
												<label class="layui-form-label">经纬度：<i class="red">*</i></label>
												<div class="layui-input-block flex">
													<input type="text" name="latlng" autocomplete="off" placeholder="请点击查找位置，选择经纬度" class="layui-input"
													 v-model="formData.latlng">
													<div class="layui-btn layui-btn-normal" @click="openWindows('查找位置','{:Url('select_address')}',{w:400,h:700})">查找位置</div>
												</div>
											</div>
											<div class="layui-form-item layui-form-text">
												<label class="layui-form-label">活动说明</label>
												<div class="layui-input-block">
													<textarea name="description" v-model="formData.description" placeholder="请输入活动说明" class="layui-textarea"></textarea>
												</div>
											</div>


											<div class="layui-form-item">
												<label class="layui-form-label">活动类型<i class="red">*</i></label>
												<div class="layui-input-block" id="type">
												</div>
											</div>

											<div class="layui-form-item">
												<label class="layui-form-label">条件限制(参与者身份)<i class="red">*</i></label>
												<div class="layui-input-block" id="identity">
												</div>
											</div>

											<div class="layui-form-item">
												<label class="layui-form-label">人数限额：</label>
												<div class="layui-input-block">
													<input type="number" name="quota" lay-verify="title" autocomplete="off" placeholder="请输入人数限额：" class="layui-input"
													 v-model="formData.quota">
												</div>
											</div>
										</div>
									</div>
									<div class="layui-tab-item">
										<div class="layui-row layui-col-space15">
											<div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
												<div class="grid-demo grid-demo-bg1">
													<div class="flex flex_align_center">
														<div>奖励方式<i class="red">*</i></div>
														<div class="layui-input-block" style="margin-left: 20px;">
															<input type="radio" name="reward_method" value="0" :checked="formData.reward_method == 0 ? true : false"  title="无" lay-filter="reward_method" checked>
															<input type="radio" name="reward_method" value="1" :checked="formData.reward_method == 1 ? true : false"  title="赏金" lay-filter="reward_method">
															<input type="radio" name="reward_method" value="2" :checked="formData.reward_method == 2 ? true : false"  title="现场礼品" lay-filter="reward_method">
															<input type="radio" name="reward_method" value="3" :checked="formData.reward_method == 3 ? true : false"  title="物流礼品" lay-filter="reward_method">
														</div>
													</div>
												</div>
											</div>
											<!-- 无 -->
											<div class="layui-col-xs12 layui-col-sm12 layui-col-md12" v-show="formData.reward_method == 0">
											</div>
											<!-- 赏金 -->
											<div class="layui-col-xs12 layui-col-sm12 layui-col-md12" v-show="formData.reward_method == 1">
												<div class="layui-col-xs12 layui-col-sm4 layui-col-md4">
													<div class="grid-demo grid-demo-bg1">
														<div class="layui-form-item">
															<label class="layui-form-label">赏金总额：</label>
															<div class="layui-input-block">
																<input type="number" name="bounty_amount" lay-verify="title" autocomplete="off" placeholder="请输入赏金"
																 class="layui-input" v-model="formData.bounty_amount">
															</div>
														</div>
													</div>
												</div>
											</div>
											<!-- 现场礼品、物流礼品 -->
											<div class="layui-col-xs12 layui-col-sm12 layui-col-md12" v-show="formData.reward_method == 2 || formData.reward_method == 3">
												<!-- 添加奖品 -->
												<div>
													<div class="flex flex_align_center padding">
														<div>从奖品库添加</div>
														<div class="add" @click="addHandle('jiangpin')">+</div>
														<div>已选奖品 {{prize_list.length}} 种，库存共{$prize_inventory}件</div>
													</div>
													<table class="layui-table">
														<colgroup>
															<col width="150">
															<col width="200">
															<col width="200">
															<col>
														</colgroup>
														<thead>
															<tr>
																<th>序号</th>
																<th>奖品</th>
																<th>库存</th>
																<th>操作</th>
															</tr>
														</thead>
														<tbody>
															<tr v-for="(item,index) in prize_list" v-key="index">
																<td>{{index+1}}</td>
																<td>{{item.name}}</td>
																<td>{{item.number}}</td>
																<td><span class="del layui-btn layui-btn-xs layui-btn-normal" @click="delHandle('jiangpin',item,index)">删除</span></td>
															</tr>
														</tbody>
													</table>
												</div>
												<!-- 问卷调查 -->
												<div class=" ">
													<div class="flex flex_align_center padding">
														<label class="">领取条件：</label>
														<div class="layui-input-block" style="margin-left: 20px;">
															<input type="radio" name="question_switch" value="1"   v-model="formData.question_switch" :checked="formData.question_switch == 1 ? true : false" lay-filter="question_switch" title="问卷调查">
														</div>
													</div>
													<div class="flex flex_align_center ">
														<div>添加问题</div>
														<div class="add" @click="addHandle('question')">+</div>
													</div>
													<table class="layui-table">
														<colgroup>
															<col width="80" />
															<col width="300" />
															<col width="300" />
															<col width="100" />
															<col width="100" />
															<col />
														</colgroup>
														<thead>
															<tr>
																<th>序号</th>
																<th>题目</th>
																<th>答案项</th>
																<th>是否填空</th>
																<th>是否传图</th>
																<th>操作</th>
															</tr>
														</thead>
														<tbody>
															<tr v-for="(item, index) in question_list" v-key="index">
																<td>{{ index + 1 }}</td>
																<td>{{ item.name }}</td>
																<td>
																	<p v-for="(item1,index1) in item.answer">{{ item1.text }}</p>
																</td>
																<td>{{ item.is_empty ? '是' : '否' }}</td>
																<td>{{ item.is_upload ? '是' : '否'  }}</td>
																<td><span class="edit layui-btn layui-btn-xs layui-btn-normal" @click="editHandle('question',item,index)">编辑</span><span
																	 class="del layui-btn layui-btn-xs layui-btn-normal" @click="delHandle('question',item,index)">删除</span></td>
															</tr>
														</tbody>
													</table>
												</div>
												<!-- 领取玩法 -->
												<div class="flex flex_align_center padding">
													<label>领取玩法</label>
													<div class="layui-input-block" style="margin-left: 20px;">
														<input type="radio" name="collection_method" name="type" v-model="formData.collection_method" :checked="formData.collection_method == 1 ? true : false" value="1" lay-filter="collection_method" title="系统随机选一件"
														 checked>
														<input type="radio" name="collection_method" :checked="formData.collection_method == 2 ? true : false" value="2" v-model="formData.collection_method" lay-filter="collection_method" title="抽奖（礼品可选择5个或者8个）">
													</div>
												</div>
												<div class="flex flex_align_center padding">
													<label>抽奖开关</label>
													<div class="layui-input-block" style="margin-left: 20px;">
														<input type="checkbox" name="winning_switch" lay-skin="switch" lay-filter="winning_switch" lay-text="ON|OFF" :checked="formData.winning_switch == 1 ? true : false">
													</div>
												</div>
												<div class="flex flex_align_center padding">
													<div>添加中奖时间段（只在活动期间）</div>
													<div class="add" @click="addHandle('timeline')">+</div>
												</div>
												<table class="layui-table">
													<colgroup>
														<col width="80" />
														<col width="200" />
														<col width="200" />
														<col width="100" />
														<col width="300" />
														<col />
													</colgroup>
													<thead>
														<tr>
															<th>序号</th>
															<th>开始时间</th>
															<th>结束时间</th>
															<th>奖品数</th>
															<th>策略</th>
															<th>操作</th>
														</tr>
													</thead>
													<tbody>
														<tr v-for="(item, index) in period_list" v-key="index">
															<td>{{ index + 1 }}</td>
															<td>{{ item.begin_time }}</td>
															<td>{{ item.last_time }}</td>
															<td>{{ item.number }}</td>
															<td>{{ item.strategy == 1  ? '先到先得' : '5分钟出1个奖品，先到先得，出完为止' }}</td>
															<td><span class="edit layui-btn layui-btn-xs layui-btn-normal" @click="editHandle('timeline',item,index)">编辑</span><span
																 class="del layui-btn layui-btn-xs layui-btn-normal" @click="delHandle('timeline',item,index)">删除</span></td>
														</tr>
													</tbody>
												</table>
											</div>
										</div>
									</div>
								</div>
								<div class="layui-tab-content">
									<div class="layui-row layui-col-space15">
										<div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
											<div class="grid-demo grid-demo-bg1">
												<div class="layui-form-item" v-if="id">
													<button class="layui-btn layui-btn-primary layui-btn-sm" id="submit" type="button" @click="handleSubmit()">保存</button>
													<button class="layui-btn layui-btn-primary layui-btn-sm" type="button" @click="back" v-if="layTabId != 1">上一步</button>
													<button class="layui-btn layui-btn-normal layui-btn-sm" type="button" v-if="layTabId == 1" @click="next">下一步</button>
												</div>
												<div class="layui-form-item" v-else>
													<button class="layui-btn layui-btn-primary layui-btn-sm" type="button" @click="back" v-if="layTabId != 1">上一步</button>
													<button class="layui-btn layui-btn-normal layui-btn-sm" type="button" @click="next" v-if="layTabId == 1">下一步</button>
													<button class="layui-btn layui-btn-normal layui-btn-sm" id="submit" type="button" @click="handleSubmit()" v-if="layTabId == 2">提交</button>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</form>
					</div>
				</div>
				<div id="questionForm" lay-filter="questionForm" style="display: none;">
					<form class="layui-form" id="formQuestion" style="width:600px;margin:20px 0 ">
						<div class="layui-form-item">
							<label class="layui-form-label">题目</label>
							<div class="layui-input-block">
								<input type="text" name="name" required lay-verify="required" placeholder="请输入题目" autocomplete="off" class="layui-input">
							</div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label">答题类型</label>
							<div class="layui-input-block">
								<select name="type" id="questionSelect" lay-verify="required" lay-filter="select_type">
									<option value="1">单选</option>
									<option value="2">多选</option>
									<option value="3">文本框</option>
								</select>
							</div>
						</div>
						<div class="layui-form-item" v-if="selectType!=='3'">
							<label class="layui-form-label">答案项</label>
							<div class="layui-input-block">
								<div v-for="(item,index) in inputAnswer" class="flex flex_align_center" :style="{marginTop: index >0 ?  '15px' : '0'}">
									<input type="text" v-model="item.text" required lay-verify="required" placeholder="请输入答案项" class="layui-input">
									<template v-if="index===0">
										<div class="layui-btn layui-btn-md layui-btn-normal" @click="inputAnswerHandle('add',item,index)" style="margin-left: 20px;">添加</div>
									</template>
									<template v-else>
										<div class="layui-btn layui-btn-md layui-btn-primary" @click="inputAnswerHandle('del',item,index)" style="margin-left: 20px;">删除</div>
									</template>
								</div>
							</div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label">是否填空</label>
							<div class="layui-input-block">
								<input type="checkbox" name="is_empty" lay-text="是|否" lay-skin="switch">
							</div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label">是否传图</label>
							<div class="layui-input-block">
								<input type="checkbox" name="is_upload" lay-text="是|否" lay-skin="switch">
							</div>
						</div>
						<div class="layui-form-item">
							<div class="layui-input-block">
								<button class="layui-btn" lay-submit lay-filter="*">提交</button>
								<button type="reset" class="layui-btn layui-btn-primary">重置</button>
							</div>
						</div>
					</form>
				</div>
			</div>

			<div id="timelineForm" lay-filter="" style="display: none;">
				<form class="layui-form" id="formTimeline" style="width:600px;margin:20px 0 ">
					<div class="layui-form-item">
						<label class="layui-form-label">活动时间</label>
						<div class="layui-input-block">
							<input type="text" name="start_end" id="start_end" required lay-verify="required" placeholder="选择活动时间"
							 autocomplete="off" class="layui-input" readonly="readonly">
						</div>
					</div>
					<div class="layui-form-item">
						<label class="layui-form-label">奖品数</label>
						<div class="layui-input-block">
							<input type="text" name="number" required lay-verify="required" class="layui-input">
						</div>
					</div>
					<div class="layui-form-item">
						<label class="layui-form-label">策略</label>
						<div class="layui-input-block">
							<select name="strategy" id="timelineSelect" lay-verify="required" lay-filter="strategy">
								<option value="1">先到先得</option>
								<option value="2">5分钟出1个奖品，先到先得，出完为止</option>
							</select>
						</div>
					</div>
					<div class="layui-form-item">
						<div class="layui-input-block">
							<button class="layui-btn" lay-submit lay-filter="*">提交</button>
							<button type="reset" class="layui-btn layui-btn-primary">重置</button>
						</div>
					</div>
				</form>
			</div>
		</div>
		<script src="{__PLUG_PATH}city.js"></script>
		<script>
			var id = {$id};
			$.each(city,function (key,item) {
		       city[key].value = item.label;
		       if(item.children && item.children.length){
		           $.each(item.children,function (i,v) {
		               city[key].children[i].value=v.label;
		               if(v.children && v.children.length){
		                   $.each(v.children,function (k,val) {
		                       city[key].children[i].children[k].value=val.label;
		                   });
		               }
		           });
		       }
		   });
			new Vue({
				el: '#app',
				data: {
					id: id,
					uid: 0,
					//分类列表
					cateList: [{
						label: '招募体验官',
						value: 1
					}, {
						label: '免费体验',
						value: 2
					}],
					storeList: [{
						label: '全部用户',
						value: 1
					}, {
						label: '仅会员用户',
						value: 2
					}, {
						label: '完成分享任务数任务用户',
						value: 3
					}],
					addresData: city,
					formData: {
						collection_method: 1,
						question_switch: 0,
						winning_switch: 0,
						reward_method: 0,
						type: [0],
						identity: [0],
						name: '',
						form_start_end: '',
						slider_image: [],
						address: '',
						address_detail: '',
						latlng: '',
						description: '',
						quota: '',
						bounty_amount: '',
						period_list: [],
						prize_list: [],
						question_list: [],
					},
					//多属性header头
					formHeader: [],
					selectType: '1',
					selectVal: 1,
					inputAnswer: [{
						text: 0
					}], //答案项列表
					brokerage: {
						brokerage: '',
						brokerage_two: '',
					},
					activity: {
						'秒杀': '#1E9FFF',
						'砍价': '#189688',
						'拼团': '#FEB900'
					},
					attr: [], //临时属性
					newRule: false, //是否添加新规则
					radioRule: ['is_sub', 'delivery_method', 'is_show', 'is_hot', 'is_benefit', 'is_new', 'is_good', 'is_best',
						'reward_method', 'collection_method', 'question_switch'
					], //radio 当选规则
					checkboxRule: ['winning_switch'], //radio 当选规则
					rule: { //多图选择规则
						slider_image: {
							maxLength: 5
						}
					},
					ruleList: [],
					ruleIndex: -1,
					progress: 0,
					um: null, //编译器实例化
					form: null, //layui.form
					layTabId: 1,
					ruleBool: id ? true : false,
					//现场礼品
					prize_list: [],
					question_list: [],
					questionForm: {
						name: '',
						type: 1,
						answer: '',
						is_empty: false,
						is_upload: false
					},
					period_list: [],
					timelineForm: {
						begin_time: '',
						last_time: '',
						number: 10,
						strategy: 0
					},
					isEdit: false, //是否为编辑状态
					editIdx: 0, // 编辑数据下标
					handleFormType: 'question'


				},
				watch: {

				},
				methods: {
					back: function() {
						var that = this;
						layui.use(['element'], function() {
							layui.element.tabChange('docTabBrief', that.layTabId == 1 ? 1 : parseInt(that.layTabId) - 1);
						});
					},
					next: function() {
						var that = this;
						layui.use(['element'], function() {
							layui.element.tabChange('docTabBrief', that.layTabId == 2 ? 2 : parseInt(that.layTabId) + 1);
						});
					},
					goBack: function() {
						location.href = this.U({
							c: 'evaluation.activity',
							a: 'index'
						});
					},
					U: function(opt) {
						var m = opt.m || 'admin',
							c = opt.c || window.controlle || '',
							a = opt.a || 'index',
							q = opt.q || '',
							p = opt.p || {};
						var params = Object.keys(p).map(function(key) {
							return key + '/' + p[key];
						}).join('/');
						var gets = Object.keys(q).map(function(key) {
							return key + '=' + q[key];
						}).join('&');

						return '/' + m + '/' + c + '/' + a + (params == '' ? '' : '/' + params) + (gets == '' ? '' : '?' + gets);
					},
					/**
					 * 提示
					 * */
					showMsg: function(msg, success) {
						$('#submit').removeAttr('disabled').text('提交');
						layui.use(['layer'], function() {
							layui.layer.msg(msg, success);
						});
					},
					/**
		             * 获取活动信息
		             * */
		            getActivityInfo: function () {
		                var that = this;
		                that.requestGet(that.U({c:"evaluation.activity",a:'get_activity_info',q:{id:that.id}})).then(function (res) {
		                    that.$set(that,'cateList',res.data.cateList);
		                    var activityInfo = res.data.activityInfo || {};
		                    if(activityInfo.id && that.id){
		                        that.$set(that,'formData',activityInfo);
		                        that.$set(that,'prize_list',activityInfo.prize_list);
		                        that.$set(that,'question_list',activityInfo.question_list);
		                        that.$set(that,'period_list',activityInfo.period_list);
		                    }
		                    // that.init();
		                }).catch(function (res) {
		                    that.showMsg(res.msg);
		                })
		            },
					/**
					 * 删除图片
					 * */
					deleteImage: function(key, index) {
						var that = this;
						if (index != undefined) {
							that.formData[key].splice(index, 1);
							that.$set(that.formData, key, that.formData[key]);
						} else {
							that.$set(that.formData, key, '');
						}
					},
					createFrame: function(title, src, opt, type) {
						opt === undefined && (opt = {});
						var h = 0;
						if (window.innerHeight < 800 && window.innerHeight >= 700) {
							h = window.innerHeight - 50;
						} else if (window.innerHeight < 900 && window.innerHeight >= 800) {
							h = window.innerHeight - 100;
						} else if (window.innerHeight < 1000 && window.innerHeight >= 900) {
							h = window.innerHeight - 150;
						} else if (window.innerHeight >= 1000) {
							h = window.innerHeight - 200;
						} else {
							h = window.innerHeight;
						}
						var area = [(opt.w || 800) + 'px', (!opt.h || opt.h > h ? h : opt.h) + 'px'],
							that = this;
						layui.use('layer', function() {
							that.layerIdx = layer.open({
								type: type || 2,
								title: title,
								area: area,
								fixed: false, //不固定
								maxmin: true,
								moveOut: false, //true  可以拖出窗外  false 只能在窗内拖
								anim: 5, //出场动画 isOutAnim bool 关闭动画
								offset: 'auto', //['100px','100px'],//'auto',//初始位置  ['100px','100px'] t[ 上 左]
								shade: 0, //遮罩
								resize: true, //是否允许拉伸
								content: src, //内容
								move: '.layui-layer-title'
							});
							return;
						});
					},
					changeIMG: function(name, value) {
						console.log(name)
						if (this.getRule(name).maxLength !== undefined) {
							var that = this;
							value.map(function(v) {
								that.formData[name].push(v);
							});
							this.$set(this.formData, name, this.formData[name]);
						} else {
							if (name == 'batchAttr.pic') {
								this.batchAttr.pic = value;
							} else {
								if (name.indexOf('.') !== -1) {
									var key = name.split('.');
									if (key.length == 2) {
										this.formData[key[0]][key[1]] = value;
									} else if (key.length == 3) {
										this.formData[key[0]][key[1]][key[2]] = value;
									} else if (key.length == 4) {
										this.$set(this.formData[key[0]][key[1]][key[2]], key[3], value)
									}
								} else {
									this.formData[name] = value;
								}
							}
						}
					},
					getRule: function(name) {
						return this.rule[name] || {};
					},
					uploadImage: function(name) {
						return this.createFrame('选择图片', this.U({
							m: window.module,
							c: "widget.images",
							a: 'index',
							p: {
								fodder: name
							}
						}), {
							h: 545,
							w: 900
						});
					},
					insertEditor: function(list) {
						this.um.execCommand('insertimage', list);
					},
					insertEditorVideo: function(src) {
						this.um.setContent('<div><video style="width: 99%" src="' + src +
							'" class="video-ue" controls="controls" width="100"><source src="' + src + '"></source></video></div><br>',
							true);
					},
					getContent: function() {
						return this.um.getContent();
					},

					/**
					 * 监听radio字段
					 */
					eeventRadio: function() {
						var that = this;
						that.radioRule.map(function(val) {
							that.form.on('radio(' + val + ')', function(res) {
								that.formData[val] = res.value;
							});
						})
					},
					/**
					 * 监听checkbox字段
					 */
					eeventCheckbox: function(type) {
						var that = this;
						if (type) {
							that.checkboxRule.map(function(val) {
								that.form.on('checkbox(' + val + ')', function(res) {
									that.formData[val] = res.value;
								});
							})
						} else {
							that.checkboxRule.map(function(val) {
								that.form.on('switch(' + val + ')', function(res) {
									console.log(res)
									that.formData[val] = res.elem.checked;
								});
							})
						}


					},
					init: function() {
						var that = this;
						window.UMEDITOR_CONFIG.toolbar = [
							// 加入一个 test
							'source | undo redo | bold italic underline strikethrough | superscript subscript | forecolor backcolor | removeformat |',
							'insertorderedlist insertunorderedlist | selectall cleardoc paragraph | fontfamily fontsize',
							'| justifyleft justifycenter justifyright justifyjustify |',
							'link unlink | emotion selectimgs video  | map',
							'| horizontal print preview fullscreen', 'drafts', 'formula'
						];
						UM.registerUI('selectimgs', function(name) {
							var me = this;
							var $btn = $.eduibutton({
								icon: 'image',
								click: function() {
									that.createFrame('选择图片', "{:Url('widget.images/index',['fodder'=>'editor'])}");
								},
								title: '选择图片'
							});

							this.addListener('selectionchange', function() {
								//切换为不可编辑时，把自己变灰
								var state = this.queryCommandState(name);
								$btn.edui().disabled(state == -1).active(state == 1)
							});
							return $btn;

						});
						UM.registerUI('video', function(name) {
							var me = this;
							var $btn = $.eduibutton({
								icon: 'video',
								click: function() {
									that.createFrame('选择视频', "{:Url('widget.video/index',['fodder'=>'video'])}");
								},
								title: '选择视频'
							});

							this.addListener('selectionchange', function() {
								//切换为不可编辑时，把自己变灰
								var state = this.queryCommandState(name);
								$btn.edui().disabled(state == -1).active(state == 1)
							});
							return $btn;

						});
						//实例化编辑器
						this.um = UM.getEditor('myEditor', {
							initialFrameWidth: '99%',
							initialFrameHeight: 400
						});
						// this.um.setContent(that.formData.description);
						that.$nextTick(function() {
							layui.use(['form', 'element', 'laydate'], function() {
								that.form = layui.form;
								that.laydate = layui.laydate;
								that.form.render();
								that.form.on('select(temp_id)', function(data) {
									that.$set(that.formData, 'temp_id', data.value);
								});
								that.form.on('submit(*)', function(data) {
									console.log(data.field) //当前容器的全部表单字段，名值对形式：{name: value}
									let obj = data.field;
									that.$nextTick(() => {
										if (that.handleFormType === 'question') {
											if (that.isEdit) {
												that.$set(that.question_list[that.editIdx], 'name', obj.name)
												that.$set(that.question_list[that.editIdx], 'answer', that.selectType !== '3' ? that.inputAnswer :
													[])
												that.$set(that.question_list[that.editIdx], 'is_upload', obj.is_upload)
												that.$set(that.question_list[that.editIdx], 'is_empty', obj.is_empty)
											} else {

												obj.answer = that.selectType !== '3' ? that.inputAnswer : [];

												that.question_list.push(obj);
												that.$set(that, 'question_list', that.question_list)
											}

										}
										if (that.handleFormType === 'timeline') {
											const {
												start_end,
												strategy,
												number
											} = data.field;
											let obj = {
												strategy,
												number,
												begin_time: start_end.split('/')[0],
												last_time: start_end.split('/')[1]
											}
											if (that.isEdit) {
												that.period_list[that.editIdx].begin_time = obj.begin_time;
												that.period_list[that.editIdx].last_time = obj.last_time;
												that.period_list[that.editIdx].number = obj.number;
												that.period_list[that.editIdx].strategy = obj.strategy;
											} else {
												that.period_list.push(obj);
											}

										}
										that.isEdit = false

									})

									// const index = parent.layer.getFrameIndex(window.name); //获取窗口索引
									//  parent.layer.close(index);

									layer.close(that.layerIdx);
									// that.form.render();
									return false; //阻止表单跳转。如果需要表单跳转，去掉这段即可。
								});

								that.form.on('select(select_type)', function(data) {
									that.selectType = data.value;
									switch (data.value) {
										case '1':
											that.inputAnswer = that.inputAnswer.length ? that.inputAnswer : [{
												text: ''
											}];
											break;
										case '2':
											that.inputAnswer = that.inputAnswer.length ? that.inputAnswer : [{
												text: ''
											}];
											break;
										case "3":
											that.inputAnswer = that.inputAnswer.length ? [that.inputAnswer[0]] : [{
												text: ''
											}];
											break;
										default:
											break
									}
								});
								layui.element.on('tab(docTabBrief)', function() {
									that.layTabId = this.getAttribute('lay-id');
								});

								that.laydate.render({
									elem: '#start_end', //指定元素
									type: 'time',
									range: '/',
									done: function(value, date, endDate) {}
								});
								that.laydate.render({
									elem: '#form_start_end', //指定元素
									type: 'datetime',
									range: '/',
									done: function(value, date, endDate) {}
								});
								that.eeventRadio();
								that.eeventCheckbox(false)
							});

		                    layui.config({
		                        base : '/static/plug/layui/'
		                    }).extend({
		                        selectN: './selectN',
		                    }).use('selectM',function () {
		                    	var selectM = layui.selectM;
		                        //门店
		                        selectM({
		                            //元素容器【必填】
		                            elem: '#type'
		                            //候选数据【必填】
		                            ,data: that.cateList
		                            //默认值
		                            ,selected: that.formData.type || []
		                            //最多选中个数，默认5
		                            ,max : 1
		                            ,name: 'type'
		                            ,model: 'formData.type'
		                            //值的分隔符
		                            ,delimiter: ','
		                            //候选项数据的键名
		                            ,field: {idName:'value',titleName:'label',statusName:'disabled'}
		                        });
		                       selectM({
		                            //元素容器【必填】
		                            elem: '#identity'
		                            //候选数据【必填】
		                            ,data: that.storeList
		                            //默认值
		                            ,selected: that.formData.identity || []
		                            //最多选中个数，默认5
		                            ,max : 1
		                            ,name: 'identity'
		                            ,model: 'formData.identity'
		                            //值的分隔符
		                            ,delimiter: ','
		                            //候选项数据的键名
		                            ,field: {idName:'value',titleName:'label',statusName:'disabled'}
		                        });
		                    });

						})
					},
					requestPost: function(url, data) {
						return new Promise(function(resolve, reject) {
							axios.post(url, data).then(function(res) {
								if (res.status == 200 && res.data.code == 200) {
									resolve(res.data)
								} else {
									reject(res.data);
								}
							}).catch(function(err) {
								reject({
									msg: err
								})
							});
						})
					},
					requestGet: function(url) {
						return new Promise(function(resolve, reject) {
							axios.get(url).then(function(res) {
								if (res.status == 200 && res.data.code == 200) {
									resolve(res.data)
								} else {
									reject(res.data);
								}
							}).catch(function(err) {
								reject({
									msg: err
								})
							});
						})
					},

					handleSubmit: function() {
						var that = this;
						console.log('handleSubmit', that.layTabId)
						let layTabId = parseInt(that.layTabId)
						if (layTabId === 1) {
							let obj = {
								form_start_end: $('input[name="form_start_end"]').val(),
								identity: $('input[name="identity"]').val(),
								type: $('input[name="type"]').val()
							}
							// 设置基础信息
							if (!that.formData.name) {
								return that.showMsg('请填写活动主题');
							}
							if (!obj.form_start_end.length) {
								return that.showMsg('请选择活动时间');
							}
							if (!that.formData.slider_image.length) {
								return that.showMsg('请选择导览图');
							}
							if (!that.formData.address.length) {
								return that.showMsg('请选择活动地点');
							}
							// if (!that.formData.address_detail.length) {
							// 	return that.showMsg('请填写详细地址');
							// }
							if (!that.formData.latlng.length) {
								return that.showMsg('请填写经纬度');
							}
							if (!that.formData.description) {
								return that.showMsg('请填写活动说明');
							}
							if (!that.formData.type.length) {
								return that.showMsg('选择活动类型');
							}
							if (!that.formData.identity.length) {
								return that.showMsg('选择参与者身份');
							}
						} else if (layTabId === 2) {
							// 设置奖励
							let {
								reward_method
							} = that.formData;
							that.formData.prize_list = this.prize_list;
							that.formData.question_list = this.question_list;
							that.formData.period_list = this.period_list;
							reward_method = parseInt(reward_method);
							if (reward_method === 0) {} else if (reward_method === 1) {
								console.log('商金', that.formData)
							} else if (reward_method === 2 || $reward_method == 3) {
								console.log('现场礼品', that.formData)
							}
						}
						$('#submit').attr('disabled', 'disabled').text('保存中...');
						that.requestPost(that.U({
							c: 'evaluation.activity',
							a: 'save',
							p: {
								id: that.id
							}
						}), that.formData).then(function(res) {
							that.confirm();
						}).catch(function(res) {
							that.showMsg(res.msg);
						});
					},
					confirm: function() {
						var that = this;
						layui.use(['layer'], function() {
							var layer = layui.layer;
							layer.confirm(that.id ? '修改成功是否返回产品列表' : '添加成功是否返回产品列表', {
								btn: ['返回列表', that.id ? '继续修改' : '继续添加'] //按钮
							}, function() {
								location.href = that.U({
									c: 'evaluation.activity',
									a: 'index'
								});
							}, function() {
								location.reload();
							});
						});
					},
					render: function() {
						this.$nextTick(function() {
							layui.use(['form'], function() {
								layui.form.render('select');
							});
						})
					},
					// 移动
					handleDragStart(e, item) {
						this.dragging = item;
					},
					handleDragEnd(e, item) {
						this.dragging = null
					},
					handleDragOver(e) {
						e.dataTransfer.dropEffect = 'move'
					},
					handleDragEnter(e, item) {
						e.dataTransfer.effectAllowed = 'move'
						if (item === this.dragging) {
							return
						}
						var newItems = [...this.formData.activity];
						var src = newItems.indexOf(this.dragging);
						var dst = newItems.indexOf(item);
						newItems.splice(dst, 0, ...newItems.splice(src, 1))
						this.formData.activity = newItems;
					},
					getRuleList: function(type) {
						var that = this;
						that.requestGet(that.U({
							c: 'evaluation.activity',
							a: 'get_rule'
						})).then(function(res) {
							that.$set(that, 'ruleList', res.data);
							if (type !== undefined) {
								that.render();
							}
						});
					},
					handleChange: function(value, selectedData) {
						var that = this;
						that.formData.address = [];
						$.each(selectedData, function(key, item) {
							that.formData.address.push(item.label);
						});
						that.$set(that.formData, 'address', that.formData.address);
					},
					addHandle(type) {
						this.handleFormType = type;
						this.isEdit = false;
						switch (type) {
							case 'jiangpin':
								this.createFrame('添加奖品', this.U({
									c: "welfare.warehouse",
									a: 'welfare_list',
									p: {
										fodder: 'welfare',
										id: id
									}
								}), {
									h: 545,
									w: 900
								});
								break;
							case 'question':
								this.inputAnswer = [{
									text: ''
								}];
								$('#formQuestion')[0].reset();
								this.form.render();
								this.createFrame('添加问题', $('#questionForm'), {
									h: 545,
									w: 900
								}, 1);
								break;
							case 'timeline':

								$('#formTimeline')[0].reset();
								this.form.render();
								this.createFrame('添加中奖时间段', $('#timelineForm'), {
									h: 545,
									w: 900
								}, 1);
								break;
							default:
								return;
						}

					},
					editHandle(type, item, idx) {
						const that = this;
						console.log(type, item, idx)
						that.handleFormType = type;
						that.editIdx = idx;
						that.isEdit = true;
						switch (type) {
							case 'jiangpin':
								break;
							case 'question':
								$('input[name="name"]').val(item.name)
								// $('input[name="answer"]').val(item.answer)
								that.inputAnswer = item.answer || [{
									text: ''
								}];
								$('input[name="is_empty"]')[0].checked = item.is_empty ? true : false;
								$('input[name="is_upload"]')[0].checked = item.is_upload ? true : false;

								var select = 'dd[lay-value=' + item.type + ']';
								$('#questionSelect').siblings("div.layui-form-select").find('dl').find(select).click();
								layui.form.render();
								this.createFrame('编辑问题', $('#questionForm'), {
									h: 545,
									w: 900
								}, 1);
								break;
							case 'timeline':
								that.laydate.render({
									elem: '#start_end', //指定元素
									value: `${item.begin_time} / ${item.last_time}`,
									range: '/',
									done: function(value, date, endDate) {
										console.log(value); //得到日期生成的值，如：2017-08-18
										console.log(date); //得到日期时间对象：{year: 2017, month: 8, date: 18, hours: 0, minutes: 0, seconds: 0}
										console.log(endDate); //得结束的日期时间对象，开启范围选择（range: true）才会返回。对象成员同上。
									}
								});
								$('input[name="number"]').val(item.number)
								var select = 'dd[lay-value=' + item.strategy + ']';
								$('#timelineSelect').siblings("div.layui-form-select").find('dl').find(select).click();
								layui.form.render();
								this.createFrame('编辑中奖时间段', $('#timelineForm'), {
									h: 545,
									w: 900
								}, 1);
								break;
							default:
								return;
						}
					},
					delHandle(type, item, idx) {
						const that = this;
						that.handleFormType = type;
						switch (type) {
							case 'jiangpin':
								that.prize_list.splice(idx, 1);
								that.getActivityInfo();
								break;
							case 'question':
								that.question_list.splice(idx, 1);
								break;
							case 'timeline':
								that.period_list.splice(idx, 1)
								break;
							default:
								return;
						}
					},
					chooseHandle(type, item, idx) {
						console.log(type, item, idx)
						const that = this;
						that.handleFormType = type;
						switch (type) {
							case 'jiangpin':
								that.$nextTick(() => {
									that.prize_list.push(item);
								})
								break;
							case 'question':
								break;
							case 'timeline':
								break;
							default:
								return;
						}

						layer.close(layer.index);
					},
					questionForm() {
						console.log(this.questionForm)
					},
					openWindows: function(title, url, opt) {
						return this.createFrame(title, url, opt);
					},
					selectAdderss: function(data) {
						//lat 纬度 lng 经度
						this.formData.latlng = data.latlng.lat + ',' + data.latlng.lng;
					},
					inputAnswerHandle(type, item, index) {
						switch (type) {
							case 'add':
								this.inputAnswer.push({
									text: ''
								})
								break;
							case 'del':
								this.inputAnswer.splice(index, 1)
								break;
							default:
								break;
						}
						this.$set(this, 'inputAnswer', this.inputAnswer)

					},
					getVal(type, arr) {
						if (type === '1') {
							// 奖品设置
							this.$nextTick(() => {
								this.prize_list = [...this.prize_list, ...arr];
							})
						} else if (type === '2') {
							// 评测维度
							this.$set(this.formData, 'dimension_id', idArr);
							this.$set(this.formData, 'dimensionName', arr2);
						}
					}
				},
				mounted: function() {
					var that = this;
					that.getActivityInfo();
					axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';
					that.init();
					window.$vm = that;
					window.changeIMG = that.changeIMG;
					window.selectAdderss = that.selectAdderss;
					window.getVal = that.getVal;
					// window.chooseHandle=this.chooseHandle; //js动态插 事件挂在至window用原生onclick触发
					window.successFun = function() {
						that.getRuleList(1);
					}
				}
			});
		</script>
	</body>
</html>
