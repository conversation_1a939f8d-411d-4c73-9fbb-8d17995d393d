{extend name="public/container"}
{block name="head_top"}

{/block}
{block name="content"}
<style>
    .btn-outline{
        border:none;
    }
    .btn-outline:hover{
        background-color: #0e9aef;
        color: #fff;
    }
    .layui-form-item .layui-btn {
        margin-top: 5px;
        margin-right: 10px;
    }
    .layui-btn-primary{
        margin-right: 10px;
        margin-left: 0!important;
    }
    label{
        margin-bottom: 0!important;
        margin-top: 4px;
    }
</style>
<div class="layui-fluid">
    <div class="layui-row layui-col-space15" id="app">
        <!--搜索条件-->
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">搜索条件</div>
                <div class="layui-card-body">
                    <div class="layui-carousel layadmin-carousel layadmin-shortcut" lay-anim="" lay-indicator="inside"
                         lay-arrow="none" style="background:none">
                        <div class="layui-card-body">
                            <div class="layui-row layui-col-space10 layui-form-item">
                                <div class="layui-col-lg12">
                                    <label class="layui-form-label">状态:</label>
                                    <div class="layui-input-block" v-cloak="">
                                        <button class="layui-btn layui-btn-sm"
                                                :class="{'layui-btn-primary':where.status!==item.value}"
                                                @click="where.status = item.value" type="button"
                                                v-for="item in activityStatus">{{item.name}}
                                            <span v-if="item.count!=undefined"
                                                  :class="item.class!=undefined ? 'layui-badge': 'layui-badge layui-bg-gray' ">{{item.count}}</span>
                                        </button>
                                    </div>
                                </div>
                                <div class="layui-col-lg12">
                                    <label class="layui-form-label">创建时间:</label>
                                    <div class="layui-input-block" data-type="data" v-cloak="">
                                        <button class="layui-btn layui-btn-sm" type="button" v-for="item in dataList"
                                                @click="setData(item)"
                                                :class="{'layui-btn-primary':where.data!=item.value}">{{item.name}}
                                        </button>
                                        <button class="layui-btn layui-btn-sm" type="button" ref="time"
                                                @click="setData({value:'zd',is_zd:true})"
                                                :class="{'layui-btn-primary':where.data!='zd'}">自定义
                                        </button>
                                        <button type="button" class="layui-btn layui-btn-sm layui-btn-primary"
                                                v-show="showtime==true" ref="date_time">{$year.0} - {$year.1}
                                        </button>
                                    </div>
                                </div>
                                <div class="layui-col-lg12">
                                    <label class="layui-form-label">活动类型:</label>
                                    <div class="layui-input-block" v-cloak="">
                                        <button class="layui-btn layui-btn-sm"
                                                :class="{'layui-btn-primary':where.type!==item.value}"
                                                @click="where.type = item.value" type="button"
                                                v-for="item in activityType">{{item.name}}
                                            <span v-if="item.count!=undefined"
                                                  :class="item.class!=undefined ? 'layui-badge': 'layui-badge layui-bg-gray' ">{{item.count}}</span>
                                        </button>
                                    </div>
                                </div>
                                <div class="layui-col-lg12">
                                    <label class="layui-form-label">奖励类型:</label>
                                    <div class="layui-input-block" v-cloak="">
                                        <button class="layui-btn layui-btn-sm"
                                                :class="{'layui-btn-primary':where.reward_method!==item.value}"
                                                @click="where.reward_method = item.value" type="button"
                                                v-for="item in rewardType">{{item.name}}
                                            <span v-if="item.count!=undefined"
                                                  :class="item.class!=undefined ? 'layui-badge': 'layui-badge layui-bg-gray' ">{{item.count}}</span>
                                        </button>
                                    </div>
                                </div>
                                <div class="layui-col-lg12">
                                    <label class="layui-form-label">主题:</label>
                                    <div class="layui-input-block">
                                        <input type="text" name="subject_name" style="width: 50%" v-model="where.subject_name"
                                               placeholder="请输入主题名称，看板名" class="layui-input">
                                    </div>
                                </div>
                                <div class="layui-col-lg12">
                                    <label class="layui-form-label">创建人:</label>
                                    <div class="layui-input-block">
                                        <input type="text" name="real_name" style="width: 50%" v-model="where.real_name"
                                               placeholder="请输入用户名或用户ID" class="layui-input">
                                    </div>
                                </div>
                                <div class="layui-col-lg12">
                                    <div class="layui-input-block">
                                        <button @click="search" type="button"
                                                class="layui-btn layui-btn-sm layui-btn-normal">
                                            <i class="layui-icon layui-icon-search"></i>搜索
                                        </button>
                                        <button @click="refresh" type="reset"
                                                class="layui-btn layui-btn-primary layui-btn-sm">
                                            <i class="layui-icon layui-icon-refresh"></i>刷新
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--end-->
        <!-- 中间详细信息-->
        <div :class="item.col!=undefined ? 'layui-col-sm'+item.col+' '+'layui-col-md'+item.col:'layui-col-sm6 layui-col-md3'"
             v-for="item in badge" v-cloak="" v-if="item.count > 0">
            <div class="layui-card">
                <div class="layui-card-header">
                    {{item.name}}
                    <span class="layui-badge layuiadmin-badge" :class="item.background_color">{{item.field}}</span>
                </div>
                <div class="layui-card-body">
                    <p class="layuiadmin-big-font">{{item.count}}</p>
                    <p v-show="item.content!=undefined">
                        {{item.content}}
                        <span class="layuiadmin-span-color">{{item.sum}}<i :class="item.class"></i></span>
                    </p>
                </div>
            </div>
        </div>
        <!--enb-->
    </div>
    <!--列表-->
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">活动列表</div>
                <div class="layui-card-body">
                    <div class="layui-btn-container" id="container-action">
                        <button class="layui-btn layui-btn-sm" class="btn btn-w-m btn-primary" onclick="$eb.createModalFrame(this.innerText,'{:Url('create')}',{w:1100,h:760})">创建活动</button>
                    </div>
                    <table class="layui-hide" id="List" lay-filter="List"></table>
                    <!--创建活动人信息-->
                    <script type="text/html" id="userinfo">
                        {{d.nickname==null ? '暂无信息':d.nickname}}/{{d.uid}}
                    </script>
                    <!--看版状态-->
                    <script type="text/html" id="status">
                        {{d.status_name}}
                    </script>
                    <!--活动图-->
                    <script type="text/html" id="info">
                          <div class="layui-table-cell laytable-cell-1-0-1"> <img style="cursor: pointer" lay-event="open_image" src="{{ d.image }}"> </div>
                    </script>
                    <!--日期信息-->
                    <script type="text/html" id="time">
                        <div>
                            <span>创建时间：{{d.add_time}}</span><br>
                            <span>开始时间：{{d.start_time}}</span><br>
                            <span>结束时间：{{d.end_time}}</span>
                        </div>
                    </script>
                    <!--奖励方式-->
                    <script type="text/html" id="reward">
                        <div>
                            <span>{{d.reward_name}}</span><br>
                            <span>奖励说明：{{d.reward_desc}}</span><br>
                            <span>限额：{{d.quota !=0  ? d.quota +'人' : '不限人数'}}</span><br>
                        </div>
                    </script>
                    <script type="text/html" id="act">
                        <button type="button" class="layui-btn layui-btn-xs layui-btn-normal" lay-event='edit'>
                            编辑
                        </button>
                        <button type="button" class="layui-btn layui-btn-xs" onclick="dropdown(this)">操作 <span class="caret"></span></button>
                        <ul class="layui-nav-child layui-anim layui-anim-upbit">
                            <li>
                                <a href="javascript:void(0);" lay-event='activity_info'>
                                    <i class="fa fa-file-text"></i> 活动详情
                                </a>
                            </li>
                            <li>
                                <a href="javascript:void(0);" lay-event='cancel'>
                                    <i class="fa fa-fire"></i> 取消活动
                                </a>
                            </li>
                            <li>
                                <a  href="javascript:void(0);" lay-event='open_qrcode'>
                                    <i class="fa fa-qrcode"></i> 福利二维码
                                </a>
                            </li>
                            <li>
                                <a href="javascript:void(0);" lay-event="deleted">
                                    <i class="fa fa-times"></i> 删除
                                </a>
                            </li>
                        </ul>
                    </script>
                </div>
            </div>
        </div>
    </div>
    <!--end-->
</div>
<script src="{__ADMIN_PATH}js/layuiList.js"></script>
{/block}
{block name="script"}
<script>
    layList.tableList('List', "{:Url('activity_list',['real_name'=>$real_name])}", function () {
        return [
            {type: 'checkbox'},
            {field: 'id', title: 'ID', width: '4%', align: 'center'},
            {field: 'info', title: '活动图', templet: "#info", width: "10%", align: 'center'},
            {field: 'name', title: '主题', width: '13%', align: 'center'},
            {field: 'add_time', title: '日期',templet: "#time", width: '18%', align: 'center'},
            {field: 'type_name', title: '活动类型', width: '12.5%', align: 'center'},
            {field: 'reward_name', title: '奖励方式',templet: "#reward", width: '15%', align: 'center'},
            {field: 'status_name', title: '活动状态', width: '15%', align: 'center'},
            {field: 'right', title: '操作', align: 'center', toolbar: '#act', width: '10%'},
        ];
    });
    layList.tool(function (event, data, obj) {
        switch (event) {
            case 'edit':
                location.href = layList.U({a:'create',q:{id:data.id}});
                break;
            case 'cancel':
                var url = layList.U({c: 'evaluation.activity', a: 'offline', p: {id: data.id}});
                $eb.$swal('delete', function () {
                    $eb.axios.get(url).then(function (res) {
                        if (res.status == 200 && res.data.code == 200) {
                            $eb.$swal('success', res.data.msg);
                        } else
                            return Promise.reject(res.data.msg || '修改失败')
                    }).catch(function (err) {
                        $eb.$swal('error', err);
                    });
                }, {'title': '您确定要取消活动吗？', 'text': '取消后将无法恢复,请谨慎操作！', 'confirm': '是的，我要取消'})
                break;
            case 'deleted':
                var url = layList.U({c: 'evaluation.activity', a: 'destroy', p: {id: data.id}});
                $eb.$swal('delete', function () {
                    $eb.axios.get(url).then(function (res) {
                        if (res.status == 200 && res.data.code == 200) {
                            $eb.$swal('success', res.data.msg);
                        } else
                            return Promise.reject(res.data.msg || '删除失败')
                    }).catch(function (err) {
                        $eb.$swal('error', err);
                    });
                }, {'title': '您确定要删除活动吗？', 'text': '删除后将无法恢复,请谨慎操作！', 'confirm': '是的，我要删除'})
                break;
            case 'activity_info':
                $eb.createModalFrame(data.name + '-活动详情', layList.U({a: 'activity_info', q: {id: data.id}}));
                break;
            case 'open_qrcode':
                $eb.openImage(data.code_path);
                break;
        }
    })
    $('#container-action').find('button').each(function () {
        $(this).on('click', function () {
        });
    })
    //下拉框
    $(document).click(function (e) {
        $('.layui-nav-child').hide();
    })

    function dropdown(that) {
        var oEvent = arguments.callee.caller.arguments[0] || event;
        oEvent.stopPropagation();
        var offset = $(that).offset();
        var top = offset.top - $(window).scrollTop();
        var index = $(that).parents('tr').data('index');
        $('.layui-nav-child').each(function (key) {
            if (key != index) {
                $(this).hide();
            }
        })
        if ($(document).height() < top + $(that).next('ul').height()) {
            $(that).next('ul').css({
                'padding': 10,
                'top': -($(that).parents('td').height() / 2 + $(that).height() + $(that).next('ul').height() / 2),
                'min-width': 'inherit',
                'position': 'absolute'
            }).toggle();
        } else {
            $(that).next('ul').css({
                'padding': 10,
                'top': $(that).parents('td').height() / 2 + $(that).height(),
                'min-width': 'inherit',
                'position': 'absolute'
            }).toggle();
        }
    }
    var real_name = '<?=$real_name?>';
    var ActivityCount =<?=json_encode($ActivityCount)?>,ActivityTyepeCount =<?=json_encode($ActivityTyepeCount)?>,status =<?=$status ? $status : "''"?>;
    require(['vue'], function (Vue) {
        new Vue({
            el: "#app",
            data: {
                badge: [],
                activityStatus: [
                    {name: '全部', value: ''},
                    {name: '待发布', value: 0, count: ActivityCount.tobereleased, class: true},
                    {name: '待审核', value: 2, count: ActivityCount.pending, class: true},
                    {name: '已发布待开始', value: 3, count: ActivityCount.tostart, class: true},
                    {name: '招募中', value: 4, count: ActivityCount.recruiting, class: true},
                    {name: '招募完成', value: 5, count: ActivityCount.recruitmentcompleted},
                    {name: '进行中', value: 6, count: ActivityCount.processing},
                    {name: '已结束', value: 1, count: ActivityCount.over},
                    {name: '已取消', value: 7, count: ActivityCount.cancelled},
                    {name: '已删除', value: 8, count: ActivityCount.deleted, class: true},
                ],
                dataList: [
                    {name: '全部', value: ''},
                    {name: '今天', value: 'today'},
                    {name: '昨天', value: 'yesterday'},
                    {name: '最近7天', value: 'lately7'},
                    {name: '最近30天', value: 'lately30'},
                    {name: '本月', value: 'month'},
                    {name: '本年', value: 'year'},
                ],
                activityType: [
                    {name: '全部', value: ''},
                    {name: '招募评测官', value: 1, count: ActivityTyepeCount.recruit},
                    {name: '免费现场体验', value: 2, count: ActivityTyepeCount.free},
                ],
                rewardType: [
                    {name: '全部', value: ''},
                    {name: '赏金', value: 1},
                    {name: '现场礼品', value: 2},
                    {name: '物流礼品', value: 3},
                    {name: '无', value: 4},
                ],
                where: {
                    status: status,//状态
                    data: '',//时间
                    type: '',//活动类型
                    reward_method: '',//奖励类型
                    subject_name: '',//主题
                    real_name: '',//创建人关键信息
                },
                showtime: false,
            },
            watch: {
                'where.status': function () {
                    layList.reload(this.where, true);
                },
                'where.data': function () {
                    layList.reload(this.where, true);
                },
                'where.type': function () {
                    layList.reload(this.where, true);
                },
                'where.reward_method': function () {
                    layList.reload(this.where, true);
                },
                'where.subject_name': function () {
                    layList.reload(this.where, true);
                },
                'where.real_name': function () {
                    layList.reload(this.where, true);
                }
            },
            methods: {
                setData: function (item) {
                    var that = this;
                    if (item.is_zd == true) {
                        that.showtime = true;
                        this.where.data = this.$refs.date_time.innerText;
                    } else {
                        this.showtime = false;
                        this.where.data = item.value;
                    }
                },
                search: function () {
                    layList.reload(this.where, true);
                },
                refresh: function () {
                    layList.reload();
                },
            },
            mounted: function () {
                var that = this;
                window.formReload = this.search;
                layList.laydate.render({
                    elem: this.$refs.date_time,
                    trigger: 'click',
                    eventElem: this.$refs.time,
                    range: true,
                    change: function (value) {
                        that.where.data = value;
                    }
                });
            }
        })
    });
</script>
{/block}