{extend name="public/container"}
{block name="content"}
<style>
    .backlog-body{
        padding: 10px 15px;
        background-color: #f8f8f8;
        color: #999;
        border-radius: 2px;
        transition: all .3s;
        -webkit-transition: all .3s;
        overflow: hidden;
        max-height: 84px;
    }
    .backlog-body h3{
        margin-bottom: 10px;
    }
    .right-icon{
        position: absolute;
        right: 10px;
    }
    .backlog-body p cite {
        font-style: normal;
        font-size: 17px;
        font-weight: 300;
        color: #009688;
    }
    .layuiadmin-badge, .layuiadmin-btn-group, .layuiadmin-span-color {
        position: absolute;
        right: 15px;
    }
    .layuiadmin-badge {
        top: 50%;
        margin-top: -9px;
        color: #01AAED;
    }
    .imgsize{
        margin-bottom: 2px;
        width: 320px;
        height: : 200px;
    }
</style>
<div class="layui-flid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12 layui-col-sm12 layui-col-lg12">
            <div class="layui-card">
                <div class="layui-card-header">活动详情</div>
                <div class="layui-card-body">
                    <div class="layui-row layui-col-space15">
                        <div class="layui-col-md12">
                            <div class="layui-card">
                                <div class="layui-card-body">
                                    <div class="layui-inline" style="width: 305px">
                                        <label class="layui-form-label">活动主题：<span>{$activityInfo.name}</span></label>
                                    </div>
                                    <div class="layui-inline" style="margin-left: 50px;">
                                        <label class="layui-form-label"><span> 创建时间：{$activityInfo.add_time|date="Y/m/d H:i"} &nbsp;&nbsp;开始时间：{$activityInfo.add_time|date="Y/m/d H:i"} &nbsp;&nbsp;结束时间：{$activityInfo.add_time|date="Y/m/d H:i"} </span></label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                     <div class="layui-row layui-col-space15">
                        <div class="layui-col-md12">
                            <div class="layui-card">
                                <div class="layui-card-body">
                                    <div class="layui-inline" style="width: 305px">
                                        <label class="layui-form-label">活动地点：<span>{$activityInfo.address}  {$activityInfo.detailed_address}</span></label>
                                    </div>
                                    <div class="layui-inline" style="margin-left: 50px;">
                                        <label class="layui-form-label">经纬度：<span>{$activityInfo.latitude},{$activityInfo.longitude}</span></label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                     <div class="layui-row layui-col-space15">
                        <div class="layui-col-md12">
                            <div class="layui-card">
                                <div class="layui-card-body">
                                    <div class="layui-inline" style="width: 305px">
                                        <label class="layui-form-label">活动类型：
                                            <span>
                                                {if condition="$activityInfo['type'] eq 1"}
                                                    招募体验官
                                                {elseif condition="$activityInfo['type'] eq 2"}
                                                    免费现场体验活动
                                                {else/}
                                                    未设置
                                                {/if}
                                            </span>
                                        </label>
                                    </div>
                                    <div class="layui-inline" style="margin-left: 50px;">
                                        <label class="layui-form-label">活动状态： 
                                            <span>
                                                {if condition="$activityInfo['status'] eq 0"}
                                                    招募体验官
                                                {elseif condition="$activityInfo['status'] eq 1"}
                                                    已结束
                                                {elseif condition="$activityInfo['status'] eq 2"}
                                                    待审核
                                                {elseif condition="$activityInfo['status'] eq 3"}
                                                    已发布待开始
                                                {elseif condition="$activityInfo['status'] eq 4"}
                                                    招募中
                                                {elseif condition="$activityInfo['status'] eq 5"}
                                                    招募完成
                                                {elseif condition="$activityInfo['status'] eq 6"}
                                                    进行中
                                                {elseif condition="$activityInfo['status'] eq 7"}
                                                    已取消
                                                {elseif condition="$activityInfo['status'] eq 8"}
                                                    已删除
                                                {else/}
                                                    未设置
                                                {/if}
                                        </span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-row layui-col-space15">
                        <div class="layui-col-md12">
                            <div class="layui-card">
                                <div class="layui-card-body">
                                    <div class="layui-inline" style="width: 305px">
                                        <label class="layui-form-label">条件设置：
                                            <span>
                                                {if condition="$activityInfo['identity'] eq 0"}
                                                    全部用户
                                                {elseif condition="$activityInfo['identity'] eq 1"}
                                                    仅会员用户
                                                {elseif condition="$activityInfo['identity'] eq 2"}
                                                    完成分享任务数任务用户
                                                {else/}
                                                    未设置
                                                {/if}
                                            </span>
                                        </label>
                                    </div>
                                    <div class="layui-inline" style="margin-left: 50px;">
                                        <label class="layui-form-label">人数限额： <span>{$activityInfo.quota !=0 ? $activityInfo.quota.'人' : '不限'}</span></label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-row layui-col-space15">
                        <div class="layui-col-md12">
                            <div class="layui-card">
                                <div class="layui-card-body">
                                    <div class="layui-inline">
                                        <label class="layui-form-label">活动图</label>
                                        <div class="layui-input-block">
                                            {volist name='activityInfo.image' id='vo'}
                                                <img src="{$vo}" class="imgsize">
                                            {/volist}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-col-md12 layui-col-sm12 layui-col-lg12">
            <div class="layui-card">
                <div class="layui-card-header">参与情况</div>
                <div class="layui-card-body">
                    <div class="layui-row layui-col-space15">
                        <div class="layui-col-md12">
                            <div class="layui-card">
                                <div class="layui-card-body">
                                    <div class="layui-inline" style="width: 120px">
                                        <label class="layui-form-label">访问人数：<span>100人</span></label>
                                    </div>
                                    <div class="layui-inline" style="margin-left: 50px;">
                                        <label class="layui-form-label">评测官申请人数：<span>￥2000</span></label>
                                    </div>
                                    <div class="layui-inline" style="margin-left: 50px;">
                                        <label class="layui-form-label">是否已摇号：<span>10</span></label>
                                    </div>
                                    <div class="layui-inline" style="margin-left: 50px;">
                                        <label class="layui-form-label">摇号日期还剩：<span>11</span></label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-row layui-col-space15">
                        <div class="layui-col-md12">
                            <div class="layui-card">
                                <div class="layui-card-body">
                                    <div class="layui-inline" style="width: 120px">
                                        <label class="layui-form-label">总评测数：<span>否</span></label>
                                    </div>
                                    <div class="layui-inline" style="margin-left: 50px;">
                                        <label class="layui-form-label">评测官评测数：<span>￥2000</span></label>
                                    </div>
                                    <div class="layui-inline" style="margin-left: 50px;">
                                        <label class="layui-form-label">红包发放数：<span>10</span></label>
                                    </div>
                                    <div class="layui-inline" style="margin-left: 50px;">
                                        <label class="layui-form-label">红包领取：<span>11</span></label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-col-md12 layui-col-sm12 layui-col-lg12">
            <div class="layui-card">
                <div class="layui-card-header">福利发放</div>
                <div class="layui-card-body">
                    <div class="layui-row layui-col-space15">
                        <div class="layui-col-md12">
                            <div class="layui-card">
                                <div class="layui-card-body">
                                    <div class="layui-inline" style="width: 65px">
                                        <label class="layui-form-label">赏金：<span>否</span></label>
                                    </div>
                                    <div class="layui-inline" style="margin-left: 50px;">
                                        <label class="layui-form-label">总金额：<span>￥2000</span></label>
                                    </div>
                                    <div class="layui-inline" style="margin-left: 50px;">
                                        <label class="layui-form-label">应发人数：<span>10</span></label>
                                    </div>
                                    <div class="layui-inline" style="margin-left: 50px;">
                                        <label class="layui-form-label">实发人数：<span>11</span></label>
                                    </div>
                                    <div class="layui-inline" style="margin-left: 50px;">
                                        <label class="layui-form-label">已领取：<span>23</span></label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-row layui-col-space15">
                        <div class="layui-col-md12">
                            <div class="layui-card">
                                <div class="layui-card-body">
                                    <div class="layui-inline" style="width: 65px">
                                        <label class="layui-form-label">礼品：<span>否</span></label>
                                    </div>
                                    <div class="layui-inline" style="margin-left: 50px;">
                                        <label class="layui-form-label">总礼品数：<span>￥2000</span></label>
                                    </div>
                                    <div class="layui-inline" style="margin-left: 50px;">
                                        <label class="layui-form-label">已发放：<span>10</span></label>
                                    </div>
                                    <div class="layui-inline" style="margin-left: 50px;">
                                        <label class="layui-form-label">已领取：<span>11</span></label>
                                    </div>
                                    <div class="layui-inline" style="margin-left: 50px;">
                                        <label class="layui-form-label">过期失效：<span>23</span></label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-col-md12 layui-col-sm12 layui-col-lg12">
            <div class="layui-card">
                <div class="layui-card-header">其他记录</div>
                <div class="layui-card-body">
                    <div class="layui-tab layui-tab-card">
                        <ul class="layui-tab-title">
                            <li class="layui-this">申请评测官</li>
                            <li>福利发放</li>
                        </ul>
                        <div class="layui-tab-content" id="content">
                            <div class="layui-tab-item layui-show">
                                <table class="layui-table" lay-skin="line" v-cloak="">
                                    <thead>
                                        <tr>
                                            <th style="text-align: center;">序号</th>
                                            <th style="text-align: center;">用户信息</th>
                                            <th style="text-align: center;">申请时间</th>
                                            <th style="text-align: center;">摇号排除</th>
                                            <th style="text-align: center;">评测官</th>
                                            <th style="text-align: center;">评测报告完成</th>
                                            <th style="text-align: center;">评测分</th>
                                            <th style="text-align: center;">交易完成时间</th>
                                            <th style="text-align: center;">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr v-for="item in joinList">
                                            <td class="text-center">{{item.id}}
                                            </td>
                                            <td class="text-center">{{item.nickname==null ? '暂无信息':item.nickname}}/{{item.uid}}</td>
                                            <td class="text-center">{{item.add_time}}</td>
                                            <td class="text-center">{{item.is_lottery ? '是' : '否'}}</td>
                                            <td class="text-center">{{item.is_management ? '是' : '否'}}</td>
                                            <td class="text-center">{{item.evaluation_status ? '已完成' : '未完成'}}</td>
                                            <td class="text-center">{{item.status_name}}</td>
                                            <td class="text-center">{{item.complete_time}}</td>
                                            <td class="text-center">
                                                <button class="layui-btn layui-btn-xs" type="button" lay-event="exclude_lottery">
                                                    <i class="fa fa-close"></i> 排除摇号资格
                                                </button>

                                                <button class="layui-btn layui-btn-xs" type="button">
                                                    <a href="{:Url('evaluation.article/article_info')}?id="  style="color: #fff;">
                                                            <i class="fa"></i> 查看评测报告
                                                    </a>
                                                </button>
                                            </td>
                                        </tr>
                                        <tr v-show="joinList.length<=0" style="text-align: center">
                                            <td colspan="6">暂无数据</td>
                                        </tr>
                                    </tbody>
                                </table>
                                <div ref="page_order" v-show="count.order_count > limit" style="text-align: right;"></div>
                            </div>
                            <div class="layui-tab-item">
                                <table class="layui-table" lay-skin="line" v-cloak="">
                                    <thead>
                                        <tr>
                                            <th>序号</th>
                                            <th>用户信息</th>
                                            <th>福利类型</th>
                                            <th>是否领取</th>
                                            <th>福利信息</th>
                                            <th>领取过期时间</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr v-for="item in issueList">
                                            <td class="text-center">{{item.id}}
                                                <p>
                                                    <span class="layui-badge" :class="{'layui-bg-green':item.paid==1}" v-text="item.paid==1 ? '已支付': '未支付' ">正在加载</span>
                                                    <span class="layui-badge" :class="{'layui-bg-cyan':item.pay_type=='yue','layui-bg-blue':item.pay_type=='weixin'}" v-text="item.pay_type=='weixinh5' ? '微信支付': '微信H5支付' ">正在加载</span>
                                                </p>
                                            </td>
                                            <td class="text-center">{{item.pay_price}}</td>
                                            <td>{{item.pay_time}}</td>
                                            <td>{{item.pay_time}}</td>
                                            <td>{{item.pay_time}}</td>
                                        </tr>
                                        <tr v-show="issueList.length<=0" style="text-align: center">
                                            <td colspan="6">暂无数据</td>
                                        </tr>
                                    </tbody>
                                </table>
                                <div ref="sales_order" v-show="count.order_count > limit" style="text-align: right;"></div>
                            </div>
                            <!--end-->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="{__ADMIN_PATH}js/layuiList.js"></script>
<script>
    layList.tool(function (event, data, obj) {
        switch (event) {
            case 'exclude_lottery':
                var url = layList.U({c: 'order.store_order', a: 'offline', p: {id: data.id}});
                $eb.$swal('delete', function () {
                    $eb.axios.get(url).then(function (res) {
                        if (res.status == 200 && res.data.code == 200) {
                            $eb.$swal('success', res.data.msg);
                        } else
                            return Promise.reject(res.data.msg || '修改失败')
                    }).catch(function (err) {
                        $eb.$swal('error', err);
                    });
                }, {'title': '您确定要修改支付状态吗？', 'text': '修改后将无法恢复,请谨慎操作！', 'confirm': '是的，我要修改'})
                break;
            case 'show_report':
                $eb.createModalFrame(data.nickname + '订单详情', layList.U({a: 'order_info', q: {oid: data.id}}));
                break;
        }
    })
    var count=<?=json_encode($count)?>,
    $id=<?=$id?>;
    require(['vue'],function(Vue) {
        new Vue({
            el: "#content",
            data: {
                limit:10,
                id:$id,
                joinList:[],
                issueList:[],
                page:{
                    evaluation_page:1,
                    welfare_page:1,
                },
            },
            watch:{
                'page.evaluation_page':function () {
                    this.getJoinList();
                },
                'page.welfare_page':function () {
                    this.getIssueList();
                }
            },
            methods:{
                getJoinList:function(){
                    this.request('getJoinList',this.page.evaluation_page,'joinList');
                },
                getIssueList:function () {
                    this.request('getIssueList',this.page.welfare_page,'issueList');
                },
                request:function (action,page,name) {
                    var that=this;
                    layList.baseGet(layList.U({a:action,p:{page:page,limit:this.limit,id:this.id}}),function (res) {
                        that.$set(that,name,res.data || [])
                    });
                }
            },
            mounted:function () {
                this.getIssueList();
                this.getJoinList();
                var that=this;
                layList.laypage.render({
                    elem: that.$refs.page_order
                    ,count:3
                    ,limit:that.limit
                    ,theme: '#1E9FFF',
                    jump:function(obj){
                        that.page.evaluation_page=obj.curr;
                    }
                });
                layList.laypage.render({
                    elem: that.$refs.sales_order
                    ,count:3
                    ,limit:that.limit
                    ,theme: '#1E9FFF',
                    jump:function(obj){
                        that.page.welfare_page=obj.curr;
                    }
                });
            }
        });
    });
</script>
{/block}