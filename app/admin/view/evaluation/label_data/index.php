{extend name="public/container"}
{block name="content"}
<style>
    .btn-outline{
        border:none;
    }
    .btn-outline:hover{
        background-color: #0e9aef;
        color: #fff;
    }
    .layui-form-item .layui-btn {
        margin-top: 5px;
        margin-right: 10px;
    }
    .layui-btn-primary{
        margin-right: 10px;
        margin-left: 0!important;
    }
    label{
        margin-bottom: 0!important;
        margin-top: 4px;
    }
</style>
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12" id="app">
            <div class="layui-card">
                <div class="layui-card-header">搜索标签</div>
                <div class="layui-card-body">
                    <div class="layui-carousel layadmin-carousel layadmin-shortcut" lay-anim="" lay-indicator="inside"
                         lay-arrow="none" style="background:none">
                        <div class="layui-card-body">
                            <div class="layui-row layui-col-space10 layui-form-item">
                                <div class="layui-col-lg12">
                                    <label class="layui-form-label">状态:</label>
                                    <div class="layui-input-block" v-cloak="">
                                        <button class="layui-btn layui-btn-sm"
                                                :class="{'layui-btn-primary':where.status!==item.value}"
                                                @click="where.status = item.value" type="button"
                                                v-for="item in labelStatus">{{item.name}}
                                            <span v-if="item.count!=undefined"
                                                  :class="item.class!=undefined ? 'layui-badge': 'layui-badge layui-bg-gray' ">{{item.count}}</span>
                                        </button>
                                    </div>
                                </div>
                                <div class="layui-col-lg12">
                                    <label class="layui-form-label">状态:</label>
                                    <div class="layui-input-block" v-cloak="">
                                        <button class="layui-btn layui-btn-sm"
                                                :class="{'layui-btn-primary':where.type!==item.value}"
                                                @click="where.type = item.value" type="button"
                                                v-for="item in labelType">{{item.name}}
                                            <span v-if="item.count!=undefined"
                                                  :class="item.class!=undefined ? 'layui-badge': 'layui-badge layui-bg-gray' ">{{item.count}}</span>
                                        </button>
                                    </div>
                                </div>
                                <div class="layui-col-lg12">
                                    <label class="layui-form-label">创建时间:</label>
                                    <div class="layui-input-block" data-type="data" v-cloak="">
                                        <button class="layui-btn layui-btn-sm" type="button" v-for="item in dataList"
                                                @click="setData(item)"
                                                :class="{'layui-btn-primary':where.data!=item.value}">{{item.name}}
                                        </button>
                                        <button class="layui-btn layui-btn-sm" type="button" ref="time"
                                                @click="setData({value:'zd',is_zd:true})"
                                                :class="{'layui-btn-primary':where.data!='zd'}">自定义
                                        </button>
                                        <button type="button" class="layui-btn layui-btn-sm layui-btn-primary"
                                                v-show="showtime==true" ref="date_time">{$year.0} - {$year.1}
                                        </button>
                                    </div>
                                </div>
                                <div class="layui-col-lg12">
                                    <label class="layui-form-label">标签名:</label>
                                    <div class="layui-input-block">
                                        <input type="text" name="keywords" style="width: 50%" v-model="where.keywords"
                                               placeholder="请输入标签名" class="layui-input">
                                    </div>
                                </div>
                                <div class="layui-col-lg12">
                                    <div class="layui-input-block">
                                        <button @click="search" type="button"
                                                class="layui-btn layui-btn-sm layui-btn-normal">
                                            <i class="layui-icon layui-icon-search"></i>搜索
                                        </button>
                                        <button @click="refresh" type="reset"
                                                class="layui-btn layui-btn-primary layui-btn-sm">
                                            <i class="layui-icon layui-icon-refresh"></i>刷新
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">标签列表</div>
                <div class="layui-card-body">
                    <div class="layui-btn-container" id="container-action">
                        <button class="layui-btn layui-btn-sm" class="btn btn-w-m btn-primary" onclick="$eb.createModalFrame(this.innerText,'{:Url('create')}',{w:1100,h:760})">添加标签</button>
                    </div>
                    <table class="layui-hide" id="Label_list" lay-filter="Label_list"></table>
                    <!--用户信息-->
                    <script type="text/html" id="userinfo">
                        <div class="layui-table-cell laytable-cell-1-0-1"> <img style="cursor: pointer" lay-event="open_image" src="{{ d.avatar }}"> </div>
                        {{d.nickname==null ? '暂无信息':d.nickname}}/{{d.uid}}
                    </script>
                    <!--来源-->
                    <script type="text/html" id="source">
                        {{d.cate == 1 ? '心愿单': '测评'}} 
                    </script>
                    <!--类型-->
                    <script type="text/html" id="type_name">
                        {{d.type_name}}
                    </script>
                    <!--评论状态-->
                    <script type="text/html" id="status">
                        {{d.status}}
                    </script>
                    <script type="text/html" id="act">
                        <button class="layui-btn layui-btn-xs" onclick="$eb.createModalFrame('编辑','{:Url('edit')}?id={{d.id}}')">
                            <i class="fa fa-edit"></i> 编辑
                        </button>
                        <button class="layui-btn btn-danger layui-btn-xs" lay-event='delstor'>
                            <i class="fa fa-times"></i> 删除
                        </button>
                    </script>
                </div>
            </div>
    </div>
    </div>
</div>
<script src="{__ADMIN_PATH}js/layuiList.js"></script>
{/block}
{block name="script"}
<script>
    setTimeout(function () {
        $('.alert-info').hide();
    },3000);
    //实例化form
    layList.form.render();
    //加载列表
    layList.tableList('Label_list',"{:Url('label_list')}",function (){
        return [
            {field: 'id', title: 'ID', sort: true,event:'id',width:'4%',align:'center'},
            {field: 'name', title: '标签名',align:'center'},
            {field: 'type', title: '类型',templet:'#is_single',align:'center'},
            {field: 'type_name', title: '所属分类',templet:'#is_scoring',align:'center'},
            {field: 'points', title: '打分人数',align:'center'},
            {field: 'add_time', title: '提交时间',align:'center'},
            {field: 'status', title: '状态',templet:'#status',align:'center'},
            {field: 'right', title: '操作',align:'center',toolbar:'#act',width:'10%',align:'center'},
        ];
    });
    //自定义方法
    var action= {
        set_category: function (field, id, value) {
            layList.baseGet(layList.Url({
                c: 'evaluation.label_category',
                a: 'set_category',
                q: {field: field, id: id, value: value}
            }), function (res) {
                layList.msg(res.msg);
            });
        },
    }
    //查询
    layList.search('search',function(where){
        layList.reload(where,true);
    });
    layList.switch('is_show',function (odj,value) {
        if(odj.elem.checked==true){
            layList.baseGet(layList.Url({c:'evaluation.label_category',a:'set_category',p:{field: 'is_show', id: value, value: 1}}),function (res) {
                layList.msg(res.msg);
            });
        }else{
            layList.baseGet(layList.Url({c:'evaluation.label_category',a:'set_category',p:{field: 'is_show', id: value, value: 0}}),function (res) {
                layList.msg(res.msg);
            });
        }
    });
     //快速编辑
    layList.edit(function (obj) {
        var id=obj.data.id,value=obj.value;
        switch (obj.field) {
            case 'title':
                action.set_category('title',id,value);
                break;
            case 'remarks':
                action.set_category('sort',id,value);
                break;
        }
    });
    //监听并执行排序
    layList.sort(['id','sort'],true);
    //点击事件绑定
    layList.tool(function (event,data,obj) {
        switch (event) {
            case 'delstor':
                var url=layList.U({c:'evaluation.label_data',a:'delete',q:{id:data.id}});
                $eb.$swal('delete',function(){
                    $eb.axios.get(url).then(function(res){
                        if(res.status == 200 && res.data.code == 200) {
                            $eb.$swal('success',res.data.msg);
                            obj.del();
                        }else
                            return Promise.reject(res.data.msg || '删除失败')
                    }).catch(function(err){
                        $eb.$swal('error',err);
                    });
                })
                break;
            case 'open_image':
                $eb.openImage(data.pic);
                break;
        }
    });
function dropdown(that) {
        var oEvent = arguments.callee.caller.arguments[0] || event;
        oEvent.stopPropagation();
        var offset = $(that).offset();
        var top = offset.top - $(window).scrollTop();
        var index = $(that).parents('tr').data('index');
        $('.layui-nav-child').each(function (key) {
            if (key != index) {
                $(this).hide();
            }
        })
        if ($(document).height() < top + $(that).next('ul').height()) {
            $(that).next('ul').css({
                'padding': 10,
                'top': -($(that).parents('td').height() / 2 + $(that).height() + $(that).next('ul').height() / 2),
                'min-width': 'inherit',
                'position': 'absolute'
            }).toggle();
        } else {
            $(that).next('ul').css({
                'padding': 10,
                'top': $(that).parents('td').height() / 2 + $(that).height(),
                'min-width': 'inherit',
                'position': 'absolute'
            }).toggle();
        }
    }
    var LabelCount =<?=json_encode($LabelCount)?>;
    require(['vue'], function (Vue) {
        new Vue({
            el: "#app",
            data: {
                badge: [],
                labelStatus: [
                    {name: '全部', value: ''},
                    {name: '已通过', value: 1, count: LabelCount.passed},
                    {name: '待审核', value: 2, count: LabelCount.pending, class: true},
                    {name: '已屏蔽', value: 3, count: LabelCount.deleted},
                ],
                labelType: [
                    {name: '全部', value: ''},
                    {name: '系统', value: 1, count: LabelCount.system},
                    {name: '用户', value: 2, count: LabelCount.users, class: true},
                ],
                dataList: [
                    {name: '全部', value: ''},
                    {name: '今天', value: 'today'},
                    {name: '昨天', value: 'yesterday'},
                    {name: '最近7天', value: 'lately7'},
                    {name: '最近30天', value: 'lately30'},
                    {name: '本月', value: 'month'},
                    {name: '本年', value: 'year'},
                ],
                where: {
                    data: '',
                    status: '',
                    type: '',
                    keywords: '',
                },
                showtime: false,
            },
            watch: {
                'where.status': function () {
                    layList.reload(this.where, true);
                },                
                'where.type': function () {
                    layList.reload(this.where, true);
                },
                'where.data': function () {
                    layList.reload(this.where, true);
                },
                'where.keywords': function () {
                    layList.reload(this.where, true);
                }
            },
            methods: {
                setData: function (item) {
                    var that = this;
                    if (item.is_zd == true) {
                        that.showtime = true;
                        this.where.data = this.$refs.date_time.innerText;
                    } else {
                        this.showtime = false;
                        this.where.data = item.value;
                    }
                },
                search: function () {       
                    layList.reload(this.where, true);
                },
                refresh: function () {
                    layList.reload();
                },
                init: function () {
                var that = this;
                that.$nextTick(function () {
                    layui.use(['form','element'], function () {
                        that.form = layui.form;
                        that.form.render();
                        layui.element.on('tab(docTabBrief)', function(){
                            that.layTabId = this.getAttribute('lay-id');
                        });
                    });
                })
            },
            },
            mounted: function () {
                this.init();
                var that = this;
                window.formReload = this.search;
                layList.laydate.render({
                    elem: this.$refs.date_time,
                    trigger: 'click',
                    eventElem: this.$refs.time,
                    range: true,
                    change: function (value) {
                        that.where.data = value;
                    }
                });
            }
        })
    });
</script>
{/block}
