<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link href="{__FRAME_PATH}css/font-awesome.min.css" rel="stylesheet">
    <link href="{__ADMIN_PATH}plug/umeditor/themes/default/css/umeditor.css" type="text/css" rel="stylesheet">
    <script type="text/javascript" src="{__ADMIN_PATH}plug/umeditor/third-party/jquery.min.js"></script>
    <script type="text/javascript" src="{__ADMIN_PATH}plug/umeditor/third-party/template.min.js"></script>
    <script type="text/javascript" charset="utf-8" src="{__ADMIN_PATH}plug/umeditor/umeditor.config.js"></script>
    <script type="text/javascript" charset="utf-8" src="{__ADMIN_PATH}plug/umeditor/umeditor.min.js"></script>
    <script type="text/javascript" src="{__ADMIN_PATH}plug/umeditor/lang/zh-cn/zh-cn.js"></script>
    <link rel="stylesheet" href="/static/plug/layui/css/layui.css">
    <script src="/static/plug/layui/layui.js"></script>
    <script src="{__PLUG_PATH}vue/dist/vue.min.js"></script>
    <script src="/static/plug/axios.min.js"></script>
    <script src="{__MODULE_PATH}widget/aliyun-oss-sdk-4.4.4.min.js"></script>
    <script src="{__MODULE_PATH}widget/cos-js-sdk-v5.min.js"></script>
    <script src="{__MODULE_PATH}widget/qiniu-js-sdk-2.5.5.js"></script>
    <script src="{__MODULE_PATH}widget/plupload.full.min.js"></script>
    <script src="{__MODULE_PATH}widget/videoUpload.js"></script>
    <style>
        .layui-form-item {
            margin-bottom: 0px;
        }

        .pictrueBox {
            display: inline-block !important;
        }

        .pictrue {
            width: 60px;
            height: 60px;
            border: 1px dotted rgba(0, 0, 0, 0.1);
            margin-right: 15px;
            display: inline-block;
            position: relative;
            cursor: pointer;
        }

        .pictrue img {
            width: 100%;
            height: 100%;
        }

        .upLoad {
            width: 58px;
            height: 58px;
            line-height: 58px;
            border: 1px dotted rgba(0, 0, 0, 0.1);
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.02);
            cursor: pointer;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .rulesBox {
            display: flex;
            flex-wrap: wrap;
            margin-left: 10px;
        }

        .layui-tab-content {
            margin-top: 15px;
        }

        .ml110 {
            margin: 18px 0 4px 110px;
        }

        .rules {
            display: flex;
        }

        .rules-btn-sm {
            height: 30px;
            line-height: 30px;
            font-size: 12px;
            width: 109px;
        }

        .rules-btn-sm input {
            width: 79% !important;
            height: 84% !important;
            padding: 0 10px;
        }

        .ml10 {
            margin-left: 10px !important;
        }

        .ml40 {
            margin-left: 40px !important;
        }

        .closes {
            position: absolute;
            left: 86%;
            top: -18%;
        }
        .red {
            color: red;
        }
        .layui-input-block .layui-video-box{
            width: 22%;
            height: 180px;
            border-radius: 10px;
            background-color: #707070;
            margin-top: 10px;
            position: relative;
            overflow: hidden;
        }
        .layui-input-block .layui-video-box i{
            color: #fff;
            line-height: 180px;
            margin: 0 auto;
            width: 50px;
            height: 50px;
            display: inherit;
            font-size: 50px;
        }
        .layui-input-block .layui-video-box .mark{
            position: absolute;
            width: 100%;
            height: 30px;
            top: 0;
            background-color: rgba(0,0,0,.5);
            text-align: center;
        }
        .store_box{
            display: flex;
        }
        .info{
            color: #c9c9c9;
            padding-left: 10px;
            line-height: 30px;
        }
				
				.flex {
					display: flex;
				}
				.flex_align_center {
					align-items: center;
				}
	
		
    </style>
</head>
<body>
<div class="layui-fluid">
    <div class="layui-row layui-col-space15"  id="app" v-cloak="">
        <div class="layui-card">
            <div class="layui-card-header">
                <span class="">{{id ? '看板修改': '看板添加' }}</span>
                <button style="margin-left: 20px" type="button" class="layui-btn layui-btn-primary layui-btn-xs" @click="goBack">返回列表</button>
            </div>
            <div class="layui-card-body">
                <form class="layui-form" action="" v-cloak="">
                    <div class="layui-tab layui-tab-brief" lay-filter="docTabBrief">
                        <ul class="layui-tab-title">
                            <li class="layui-this" lay-id='1'>设置看板信息</li>
                        </ul>
                        <div class="layui-tab-content">
                            <div class="layui-tab-item layui-show">
                                <div class="layui-row layui-col-space15">
                                	<div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">看板分类<i class="red">*</i></label>
                                                <div class="layui-input-block" id="cate_id">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                	<div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">主标题<i class="red">*</i></label>
                                                <div class="layui-input-block">
                                                    <input type="text" name="main_title" lay-verify="title" autocomplete="off"
                                                           placeholder="请输入看板主标题" class="layui-input" v-model="formData.main_title" maxlength="100">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">副标题<i class="red">*</i></label>
                                                <div class="layui-input-block">
                                                    <input type="text" name="sub_title" lay-verify="title" autocomplete="off"
                                                           placeholder="请输入看板副标题" class="layui-input" v-model="formData.sub_title" maxlength="100">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">封面图<i class="red">*</i></label>
                                                <div class="pictrueBox">
                                                    <div class="pictrue" v-if="formData.image" @click="uploadImage('image')">
                                                        <img :src="formData.image"></div>
                                                    <div class="upLoad" @click="uploadImage('image')" v-else>
                                                        <i class="layui-icon layui-icon-camera" class="iconfont"
                                                           style="font-size: 26px;"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">导览图<i class="red">*</i></label>
                                                <div class="pictrueBox pictrue" v-for="(item,index) in formData.slider_image">
                                                    <img :src="item">
                                                    <i class="layui-icon closes" @click="deleteImage('slider_image',index)">&#x1007</i>
                                                </div>
                                                <div class="pictrueBox">
                                                    <div class="upLoad" @click="uploadImage('slider_image')"
                                                         v-if="formData.slider_image.length <= rule.slider_image.maxLength">
                                                        <i class="layui-icon layui-icon-camera" class="iconfont"
                                                           style="font-size: 26px;"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item layui-form-text">
                                                <label class="layui-form-label">简介<i class="red">*</i></label>
                                                <div class="layui-input-block">
                                                    <textarea name="description" v-model="formData.description"
                                                              placeholder="请输入简介" class="layui-textarea"></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
												<div class="layui-form-item">
												    <label class="layui-form-label">关联商品<i class="red">* </i></label>
												    <div class="pictrueBox pictrue" v-for="(item,index) in formData.product_image">
												        <img :src="item">
												        <i class="layui-icon closes" @click="deleteImage('product_image',index)">&#x1007</i>
												    </div>
												    <div class="pictrueBox" >
														<button class=" upLoad" id="submit" type="button" @click="chooseShop('product')"  >
												        <i class="layui-icon layui-icon-camera iconfont"
												               style="font-size: 26px;"></i></button>
												    </div>
												</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">评测维度<i class="red">*</i></label>								
												<div class="pictrueBox">
													<button class="layui-btn layui-btn-normal layui-btn-sm" id="submit" type="button" @click="chooseDimension('formData.dimension_id')">从模版导入</button>
												</div>
                                            </div>
    										<div class="layui-form-item">
    											<div  v-for="(item,index) in formData.dimension"  class="flex flex_align_center" style="margin-top:10px">
    												<label class="layui-form-label"></label>
    												<div class="flex flex_align_center ">
    													<label  style="width:100px;font-weight: bold;">{{item.name}} :</label>
    													<input type="text" v-model.number="item.dimension" @change="inputChange(item,index)" class="layui-input" style="width:80px;height: 30px;">
    													<div style="margin:0 10px 0 5px"> %</div>
    												</div>
    												<div class="layui-btn layui-btn-normal layui-btn-sm" @click="deleteDimension('dimension_id',index)" >删除 </div>
    											</div>
    											<div v-if="formData.dimension.length" style="margin:10px 0;" class="flex flex_align_center"> <label class="layui-form-label" style="font-weight: bold;">合计 ：</label>  <span :class="{red:formData.dimensionTotal>100} ">{{formData.dimensionTotal}} %</span></div>
    										</div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                        <div class="grid-demo grid-demo-bg1">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">创建看板人：</label>
                                                <div class="pictrueBox">
                                                    <div class="pictrue" v-if="formData.uid" @click="chooseUser('uid')">
                                                        <img :src="formData.avatar"></div>
                                                    <div class="upLoad" @click="chooseUser('uid')" v-else>
                                                        <i class="layui-icon layui-icon-camera" class="iconfont"
                                                           style="font-size: 26px;"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-tab-content">
                            <div class="layui-row layui-col-space15">
                                <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                                    <div class="grid-demo grid-demo-bg1">
                                        <div class="layui-form-item" v-if="id">
                                            <button class="layui-btn layui-btn-primary layui-btn-sm" id="submit" type="button" @click="handleSubmit()">保存</button>
                                        </div>
                                        <div class="layui-form-item" v-else>
                                            <button class="layui-btn layui-btn-normal layui-btn-sm" id="submit" type="button" @click="handleSubmit()">提交</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>  
		</div>
</div>
<script>
    var id = {$id};
    new Vue({
        el: '#app',
        data: {
            id:id,
            //分类列表
            cateList: [],
            dimension_id:[],
            upload:{
                videoIng:false
            },
            cate_id:[],
            formData: {
                uid:0,
                cate_id: [],
                main_title: '',
                sub_title: '',
                image: '',
                avatar:'',
                slider_image: [],
				product_image:[],
                description: '',
                sort: 0,
                product_id: [],
                dimension: [],
				dimension_id:[],//评测维度
				dimensionName:[],
				dimensionTotal:100
            },
            //多属性header头
            formHeader:[],
            radioRule: [],//radio 当选规则
            rule: { //多图选择规则
                slider_image: {
                    maxLength: 5
                },
                product_id: {
                    maxLength: 5
                },
				product_image: {
                    maxLength: 2
                }
            },
            ruleIndex:-1,
            progress: 0,
            um: null,//编译器实例化
            form: null,//layui.form
            layTabId: 1,
            ruleBool: id ? true : false
						
        },
				watch:{

				},
        methods: {
            back:function(){
                var that = this;
                layui.use(['element'], function () {
                    layui.element.tabChange('docTabBrief', that.layTabId == 1 ? 1 : parseInt(that.layTabId) - 1);
                });
            },
            next:function(){
                var that = this;
                layui.use(['element'], function () {
                    layui.element.tabChange('docTabBrief', that.layTabId == 2 ? 2 : parseInt(that.layTabId) + 1);
                });
            },
            goBack:function(){
                location.href = this.U({c:'evaluation.kanban',a:'index'});
            },
            U: function (opt) {
                var m = opt.m || 'admin', c = opt.c || window.controlle || '', a = opt.a || 'index', q = opt.q || '',
                    p = opt.p || {};
                var params = Object.keys(p).map(function (key) {
                    return key + '/' + p[key];
                }).join('/');
                var gets = Object.keys(q).map(function (key) {
                    return key+'='+ q[key];
                }).join('&');

                return '/' + m + '/' + c + '/' + a + (params == '' ? '' : '/' + params) + (gets == '' ? '' : '?' + gets);
            },
            /**
             * 提示
             * */
            showMsg: function (msg, success) {
                $('#submit').removeAttr('disabled').text('提交');
                layui.use(['layer'], function () {
                    layui.layer.msg(msg, success);
                });
            },
            /**
             * 获取商品信息
             * */
            getBillboardInfo: function () {
                var that = this;
                that.requestGet(that.U({c:"evaluation.kanban",a:'get_kanban_info',q:{id:that.id}})).then(function (res) {
                    that.$set(that,'cateList',res.data.cateList);
                    var kanbanInfo = res.data.kanbanInfo || {};
                    if(kanbanInfo.id && that.id){
                        that.$set(that,'formData',kanbanInfo);
                    }
                    that.init();
                }).catch(function (res) {
                    that.showMsg(res.msg);
                })
            },
            /**
             * 删除图片
             * */
            deleteImage: function (key, index) {
                var that = this;
                if (index != undefined) {
                    that.formData[key].splice(index, 1);
                    that.$set(that.formData, key, that.formData[key]);
                    if (key == 'product_image') {
                        that.formData['product_id'].splice(index, 1);
                        that.$set(that.formData, 'product_id', that.formData['product_id']);
                    }
                } else {
                    that.$set(that.formData, key, '');
                    if (key == 'product_image') {
                        that.$set(that.formData, 'product_id', '');
                    }
                }
            },
						
			deleteDimension: function (key, index) {
			    var that = this;
			    if (index != undefined) {	
			        that.formData['dimension_id'].splice(index, 1);
			        that.formData['dimensionName'].splice(index, 1);
			        that.formData['dimension'].splice(index, 1);
			        that.$set(that.formData, 'dimension', that.formData['dimension']);
			        that.$set(that.formData, 'dimension_id', that.formData['dimension_id']);
			        that.$set(that.formData, 'dimensionName', that.formData['dimensionName']);
			    } else {
			        that.$set(that.formData, 'dimension_id', []);
			        that.$set(that.formData, 'dimensionName', []);
			    }
					this.inputChange();
			},
            createFrame: function (title, src, opt) {
                opt === undefined && (opt = {});
                var h = 0;
                if (window.innerHeight < 800 && window.innerHeight >= 700) {
                    h = window.innerHeight - 50;
                } else if (window.innerHeight < 900 && window.innerHeight >= 800) {
                    h = window.innerHeight - 100;
                } else if (window.innerHeight < 1000 && window.innerHeight >= 900) {
                    h = window.innerHeight - 150;
                } else if (window.innerHeight >= 1000) {
                    h = window.innerHeight - 200;
                } else {
                    h = window.innerHeight;
                }
                var area = [(opt.w || window.innerWidth / 2) + 'px', (!opt.h || opt.h > h ? h : opt.h) + 'px'];
                layui.use('layer',function () {
                    return layer.open({
                        type: 2,
                        title: title,
                        area: area,
                        fixed: false, //不固定
                        maxmin: true,
                        moveOut: false,//true  可以拖出窗外  false 只能在窗内拖
                        anim: 5,//出场动画 isOutAnim bool 关闭动画
                        offset: 'auto',//['100px','100px'],//'auto',//初始位置  ['100px','100px'] t[ 上 左]
                        shade: 0,//遮罩
                        resize: true,//是否允许拉伸
                        content: src,//内容
                        move: '.layui-layer-title'
                    });
                });
            },
            changeIMG: function (name, value) {
                if (this.getRule(name).maxLength !== undefined) {
                    var that = this;
                    value.map(function (v) {
                        that.formData[name].push(v);
                    });
                    this.$set(this.formData, name, this.formData[name]);
                } else {
                    if(name == 'batchAttr.pic'){
                        this.batchAttr.pic = value;
                    } else {
                        if (name.indexOf('.') !== -1) {
                            var key = name.split('.');
                            if (key.length == 2){
                                this.formData[key[0]][key[1]] = value;
                            } else if(key.length == 3){
                                this.formData[key[0]][key[1]][key[2]] = value;
                            } else if(key.length == 4){
                                this.$set(this.formData[key[0]][key[1]][key[2]],key[3],value)
                            }
                        } else {
                            this.formData[name] = value;
                        }
                    }
                }
            },
            getRule: function (name) {
                return this.rule[name] || {};
            },
            uploadImage: function (name) {
                return this.createFrame('选择图片',this.U({c:"widget.images",a:'index',p:{fodder:name}}),{h:545,w:900});
            },
            chooseDimension: function (name) {
                return this.createFrame('选择维度',this.U({c:"evaluation.dimension",a:'dimension_lists',p:{fodder:name,id:id}}),{h:545,w:900});
            },
			chooseShop(name){
				return this.createFrame('选择商品',this.U({c:"store.StoreProduct",a:'productList',p:{fodder:name}}),{h:545,w:900});
			},
            chooseUser: function (name) {
                return this.createFrame('选择用户',this.U({c:"user.user",a:'userList',p:{fodder:name}}),{h:545,w:900});
            },
            /**
             * 监听radio字段
             */
            eeventRadio: function () {
                var that = this;
                that.radioRule.map(function (val) {
                    that.form.on('radio(' + val + ')', function (res) {
                        that.formData[val] = res.value;
                    });
                })
            },
            init: function () {
                var that = this;
                that.$nextTick(function () {
                    layui.use(['form','element','selectM'], function () {
                        that.form = layui.form;
                        that.form.render();
                        that.form.on('select(temp_id)', function (data) {
                            that.$set(that.formData, 'temp_id', data.value);
                        });
                        that.form.on('select(rule_index)', function (data) {
                            that.ruleIndex = data.value;
                        });
                        layui.element.on('tab(docTabBrief)', function(){
                            that.layTabId = this.getAttribute('lay-id');
                        });
                        that.eeventRadio();
                    });

                    layui.config({
                        base : '/static/plug/layui/'
                    }).extend({
                        selectN: './selectN',
                    }).use('selectM',function () {
                        var selectM = layui.selectM;
                        selectM({
                            //元素容器【必填】
                            elem: '#cate_id'
                            //候选数据【必填】
                            ,data: that.cateList
                            //默认值
                            ,selected: that.formData.cate_id || []
                            //最多选中个数，默认5
                            ,max : 10
                            ,name: 'cate_id'
                            ,model: 'formData.cate_id'
                            //值的分隔符
                            ,delimiter: ','
                            //候选项数据的键名
                            ,field: {idName:'value',titleName:'label',statusName:'disabled'}
                        });
                    })
                })
            },
            requestPost: function (url, data) {
                return new Promise(function (resolve, reject) {
                    axios.post(url, data).then(function (res) {
                        if (res.status == 200 && res.data.code == 200) {
                            resolve(res.data)
                        } else {
                            reject(res.data);
                        }
                    }).catch(function (err) {
                        reject({msg:err})
                    });
                })
            },
            requestGet: function (url) {
                return new Promise(function (resolve, reject) {
                    axios.get(url).then(function (res) {
                        if (res.status == 200 && res.data.code == 200) {
                            resolve(res.data)
                        } else {
                            reject(res.data);
                        }
                    }).catch(function (err) {
                        reject({msg:err})
                    });
                })
            },
            handleSubmit:function () {
                var that = this, 
                cate_id = $('input[name="cate_id"]').val();
                if (cate_id != '') {
                    this.formData.cate_id = cate_id.split(',');
                }else{
                    this.formData.cate_id = [];
                }
                if (!that.formData.cate_id.length) {
                    return that.showMsg('请选择看板分类');
                }
                if (!that.formData.main_title) {
                    return that.showMsg('请填写看板主标题');
                }
                if (!that.formData.sub_title) {
                    return that.showMsg('请填写看板副标题');
                }
                if (!that.formData.image) {
                    return that.showMsg('请上传看板封面图');
                }
                if (!that.formData.slider_image.length) {
                    return that.showMsg('请上传看板导览图');
                }
                if (!that.formData.description) {
                    return that.showMsg('请填写看板简介');
                }
                // if(this.formData.dimensionTotal !==100){
                //     return that.showMsg('评测维度 计算错误');
                // }
                // $('#submit').attr('disabled', 'disabled').text('保存中...');
                that.requestPost(that.U({c:'evaluation.kanban',a:'save',p:{id:that.id}}),that.formData).then(function (res) {
                    that.confirm();
                }).catch(function (res) {
                    that.showMsg(res.msg);
                });
            },
            confirm: function(){
                var that = this;
                layui.use(['layer'], function () {
                    var layer = layui.layer;
                    layer.confirm(that.id ? '修改成功是否返回看板列表' : '添加成功是否返回看板列表', {
                        btn: ['返回列表',that.id ? '继续修改' : '继续添加'] //按钮
                    }, function(){
                        var index = parent.layer.getFrameIndex(window.name);
                        parent.layer.close(index);//关闭当前页
                        //todo 刷新列表页面
                    }, function(){
                        location.reload();
                    });
                });
            },
            render:function(){
                this.$nextTick(function(){
                    layui.use(['form'], function () {
                        layui.form.render('select');
                    });
                })
            },
            // 移动
            handleDragStart (e, item) {
                this.dragging = item;
            },
            handleDragEnd (e, item) {
                this.dragging = null
            },
            handleDragOver (e) {
                e.dataTransfer.dropEffect = 'move'
            },
            handleDragEnter (e, item) {
                e.dataTransfer.effectAllowed = 'move'
                if (item === this.dragging) {
                    return
                }
                var newItems = [...this.formData.activity];
                var src = newItems.indexOf(this.dragging);
                var dst = newItems.indexOf(item);
                newItems.splice(dst, 0, ...newItems.splice(src, 1))
                this.formData.activity = newItems;
            },
			getVal(type,idArr,arr2,data){
				if(type==='1'){
					// 关联商品
					this.$set(this.formData, 'product_image', arr2);
					this.$set(this.formData, 'product_id', idArr);
				}else if(type==='2'){
					// 评测维度
                    this.$set(this.formData, 'dimension_id', idArr);
                    this.$set(this.formData, 'dimensionName', arr2);
                    let dimensionList = [];
                    let remainder = 100 % data.length,average = parseInt(100 / data.length);
                    data.forEach((item,index)=>{
                        let obj  = {
                            dimension : (index===data.length-1) ? (remainder + average) : average,
                            ...item
                        }
                        dimensionList.push(obj)
                    })
                    this.$set(this.formData,'dimension',dimensionList)
				}else if(type==='3'){
                    // 评测维度
                    this.$set(this.formData, 'uid', idArr.uid);
                    this.$set(this.formData, 'avatar', idArr.avatar);
                }
			},
			inputChange(item,idx){
				let t = 0;
				this.formData.dimension.forEach((item,index)=>{
					t= t + item.dimension
				})
				
				this.$set(this.formData, 'dimensionTotal', t);
				if(t!==100){
					 layer.msg('累计之和不为100');
				}
			}
        },
        mounted: function () {
            var that = this;
            axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';
            that.getBillboardInfo();
            window.$vm = that;
            window.changeIMG = that.changeIMG;
			window.getVal = that.getVal;
            window.successFun = function(){
                that.getRuleList(1);
            }
        }
    });
</script>
</body>
</html>
