{extend name="public/container"}
{block name="content"}
<style type="text/css">
    .form-add{position: fixed;left: 0;bottom: 0;width:100%;}
    .form-add .sub-btn{border-radius: 0;width: 100%;padding: 6px 0;font-size: 14px;outline: none;border: none;color: #fff;background-color: #2d8cf0;}
</style>
<div class="layui-fluid">
    <div class="layui-row layui-col-space15"  id="app">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">搜索条件</div>
                <div class="layui-card-body">
                    <form class="layui-form layui-form-pane" action="">
                        <div class="layui-inline">
                            <label class="layui-form-label">所有分类</label>
                            <div class="layui-input-block">
                                <select name="cate_id">
                                    <option value=" ">全部</option>
                                    {volist name='cate' id='vo'}
                                        <option value="{$vo.id}">{$vo.html}{$vo.cate_name}</option>
                                    {/volist}
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">维度名称</label>
                            <div class="layui-input-block">
                                <input type="text" name="name" class="layui-input" placeholder="请输入维度名称">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <div class="layui-input-inline">
                                <button class="layui-btn layui-btn-sm layui-btn-normal" lay-submit="search" lay-filter="search">
                                    <i class="layui-icon layui-icon-search"></i>搜索</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-body">
                    <table class="layui-hide" id="List" lay-filter="List"></table>
                </div>	
            </div>
        </div>
		<div >
		  <button class="layui-btn" lay-submit id="submitForm">立即提交</button>
		</div>
    </div>
</div>
<script src="{__ADMIN_PATH}js/layuiList.js"></script>
{/block}
{block name='script'}
<script>
    var parentinputname = '{$Request.param.fodder}';
		let boxids = 'ids';
    layList.form.render();
    //加载列表
    layList.tableList('List',"{:Url('evaluation.dimension/dimension_list',['type'=>3,'operating_id'=>$id])}",function (){
        return [
            {field: 'id', title: 'ID', sort: true,event:'id',width:'8%'},
            {field: 'name', title: '维度名称',templet:'#name',width:'46%', align: "center"},
			{field: 'right',type: 'checkbox',width:'46.5%'}
        ]
    },undefined,undefined,boxids);
	$('#submitForm').on('click', function () {
	    var ids=layList.getCheckData().getIds('id');
	    var imgs=layList.getCheckData().getIds('name');
		let data = layList.getCheckData(boxids);
		parent.getVal('2',ids,imgs,data);	
		var index = parent.layer.getFrameIndex(window.name);
		parent.layer.close(index);	
	});
    //查询
    layList.search('search',function(where){
        layList.reload(where);
    });
</script>
{/block}
