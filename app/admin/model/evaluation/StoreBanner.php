<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2020-12-13 15:54:17
 * @Last Modified time: 2023-03-29 14:24:24
 */
namespace app\admin\model\evaluation;

use crmeb\basic\BaseModel;
use crmeb\traits\ModelTrait;

/**
 * 首页配置-横幅Model
 * Class StoreActivity
 * @package app\admin\model\store
 */
class StoreBanner extends BaseModel
{
    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'system_banners';

    use ModelTrait;

    /**
     * 分级排序列表
     * @param null $model
     * @return array
     */
    public static function getTierList($model = null)
    {
        if ($model === null) $model = new self();
        return $model->where('status', 1)->select()->toArray();
    }

    public static function BannerList($where)
    {
        $model = self::getBannerWhere()->order('id desc');
        $data = ($data = $model->page((int)$where['page'], (int)$where['limit'])->select()) && count($data) ? $data->toArray() : [];
        foreach ($data as &$item) {
            $item['add_time'] = $item['add_time'] ? date('Y-m-d H:i:s', $item['add_time']) : '';
            $item['updated_time'] = $item['updated_time'] ? date('Y-m-d H:i:s', $item['updated_time']) : '';
        }
        $count = self::getBannerWhere($where, self::alias('a'))->count();
        return compact('count', 'data');
    }

     /**
     * 处理where条件
     * @param $where
     * @param $model
     * @return mixed
     */
    public static function getBannerWhere()
    {
    	$model = new self();
        return  $model->where('is_system_del', 0);
    }


    /* 横幅隐藏显示
     * @param $id
     * @param $show
     * @return bool
     */
    public static function setBannerShow($id, $show)
    {
        self::beginTrans();
        $res1 = true;
        $res2 = self::where('id', $id)->update(['is_show' => $show]);
        $res = $res1 && $res2;
        self::checkTrans($res);
        return $res;
    }


        /* 横幅隐藏显示
     * @param $id
     * @param $show
     * @return bool
     */
    public static function setBannerByteDanceShow($id, $show)
    {
        self::beginTrans();
        $res1 = true;
        $res2 = self::where('id', $id)->update(['is_bytedance_show' => $show]);
        $res = $res1 && $res2;
        self::checkTrans($res);
        return $res;
    }
}