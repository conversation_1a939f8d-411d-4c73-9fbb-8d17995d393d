<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2020-12-10 14:16:05
 * @Last Modified time: 2021-01-27 09:45:38
 */
namespace app\admin\model\evaluation;

use crmeb\traits\ModelTrait;
use crmeb\basic\BaseModel;
use app\admin\model\store\StoreProductLabel;

/**
 * Class StoreCategory
 * @package app\admin\model\store
 */
class StoreLabelCategory extends BaseModel
{

    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'system_label_category';

    use ModelTrait;

    /**
     * 异步获取分类列表
     * @param $where
     * @return array
     */
    public static function CategoryList($where)
    {
        $data = ($data = self::systemPage($where, true)->page((int)$where['page'], (int)$where['limit'])->select()) && count($data) ? $data->toArray() : [];
        $count = self::systemPage($where, true)->count();
        return compact('count', 'data');
    }

    /**
     * @param $where
     * @return array
     */
    public static function systemPage($where, $isAjax = false)
    {
        $model = new self;
        $model = $model->where('is_del',0);
        if ($where['title'] != '') $model = $model->where('title', 'LIKE', "%$where[title]%");
        if ($isAjax === true) {
            if (isset($where['order']) && $where['order'] != '') {
                $model = $model->order(self::setOrder($where['order']));
            } else {
                $model = $model->order('sort desc,id desc');
            }
            return $model;
        }
        return self::page($model, function ($item) {
        }, $where);
    }

    /**
     * 获取顶级分类
     * @return array
     */
    public static function getCategory()
    {
        return self::where('is_show', 1)->column('name', 'id');
    }

    /**
     * 分级排序列表
     * @param null $model
     * @param int $type
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public static function getTierList($model = null, $type = 0)
    {
        if ($model === null) $model = new self();
        if (!$type) return sort_list_tier($model->order('sort desc,id desc')->where('pid', 0)->select()->toArray());
        return sort_list_tier($model->order('sort desc,id desc')->select()->toArray());
    }

    /**
     * 标签组
     * @param null $model
     * @param int $type
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public static function getTierLabel($model = null, $type = 0)
    {
        $model = new self();
        $list = $model->field('id,title as cate_name,is_single,is_required,is_other,is_scoring')->order('sort asc')->where('is_show', 1)->where('is_del', 0)->select()->toArray();
        foreach ($list as &$category) {
            $cate_id_arr = StoreLabelCate::field('label_id')->where('cate_id',$category['id'])->select()->toArray();
            $label_id_arr = array_column($cate_id_arr, 'label_id');
            $category['label_group'] = StoreLabelData::field('id,name')->where('id','IN', $label_id_arr)->select()->toArray();
        }
        return $list;
    }


    /**
     * 默认标签组
     * @param null $model
     * @param int $type
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public static function getTierProductLabel($productId = 0)
    {
        $model = StoreProductLabel::where('A.product_id', $productId)->alias('A')
            ->field('A.default,A.label_category_id as id,c.title as cate_name,c.is_single,c.is_required,c.is_scoring,c.is_other,c.add_time,c.sort')
            ->join('system_label_category c', 'A.label_category_id = c.id', 'left');
        $list = $model->order('sort asc')->where('c.is_show', 1)->where('c.is_del', 0)->select()->toArray();
        foreach ($list as &$category) {
            $default = json_decode($category['default']) ?: [];
            foreach ($default as $key => $value) {
                $category['label_group'][$key]['id'] =  StoreLabelData::where('cate_id',$category['id'])->where('name',$value)->where('type',1)->value('id');
                $category['label_group'][$key]['name'] =  $value;
            }
            unset($category['default']);
        }
        return $list;
    }

    public static function delCategory($id)
    {
        $count = StoreLabelCate::where('cate_id', $id)->count();
        if ($count)
            return self::setErrorInfo('请先删除下级子分类标签');
        else {
            return self::edit(['is_del'=>1],$id,'id');
        }
    }

    /**
     * 产品分类隐藏显示
     * @param $id
     * @param $show
     * @return bool
     */
    public static function setCategoryShow($id, $show)
    {
        $count = self::where('id', $id)->count();
        if (!$count) return self::setErrorInfo('参数错误');
        $count = self::where('id', $id)->where('is_show', $show)->count();
        if ($count) return true;
        $pid = self::where('id', $id)->value('pid');
        self::beginTrans();
        $res1 = true;
        $res2 = self::where('id', $id)->update(['is_show' => $show]);
        if (!$pid) {//一级分类隐藏
            $count = self::where('pid', $id)->count();
            if ($count) {
                $count      = self::where('pid', $id)->where('is_show', $show)->count();
                $countWhole = self::where('pid', $id)->count();
                if (!$count || $countWhole > $count) {
                    $res1 = self::where('pid', $id)->update(['is_show' => $show]);
                }
            }
        }
        $res = $res1 && $res2;
        self::checkTrans($res);
        return $res;
    }
}