<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2020-12-27 12:46:05
 * @Last Modified time: 2020-12-27 13:36:37
 */

namespace app\admin\model\evaluation;

use crmeb\basic\BaseModel;
use crmeb\traits\ModelTrait;
use app\admin\model\user\{User};

/**
 * 源未指数统计日志Model
 * Class StoreIndexStatistics
 * @package app\admin\model\evaluation
 */
class StoreIndexStatistics extends BaseModel
{
	 /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'store_index_statistics';

    use ModelTrait;

    public static function IndexCount()
    {
        $data['passed'] = self::statusByWhere(1, new self())->count();
        $data['pending'] = self::statusByWhere(2, new self())->count();
        $data['type_automatic'] = self::typeByWhere(1, new self())->count();
        $data['type_manual'] = self::typeByWhere(2, new self())->count();
        return $data;
    }


    public static function IndexList($where)
    {
        $model = self::getIndexWhere($where, self::alias('a')
            ->join('store_product r', 'r.id=a.product_id', 'LEFT'), 'a.', 'r')
            ->field('a.*,r.store_name as title');
        $model = $model->order('a.id desc');
        $data = ($data = $model->page((int)$where['page'], (int)$where['limit'])->select()) && count($data) ? $data->toArray() : [];
        foreach ($data as &$item) {
            if ($item['status'] == 1) {
                $status_name = '成功';
            } elseif ($item['status'] == 2) {
                $status_name = '失败';
            }
            $item['status_name'] = $status_name;
            $item['add_time'] = $item['add_time'] ? date('Y-m-d H:i:s',time()) : '';
        }
        $count = self::getIndexWhere($where, self::alias('a')->join('store_product r', 'r.id=a.product_id', 'LEFT'), 'a.', 'r')->count();
        return compact('count', 'data');
    }

    /**
     * 处理where条件
     * @param $where
     * @param $model
     * @return mixed
     */
    public static function getIndexWhere($where, $model, $aler = '', $join = '')
    {
        if (isset($where['status']) && $where['status'] != '') {
            $model = self::statusByWhere($where['status'], $model, $aler);
        }
        if (isset($where['type']) && $where['type'] != '') {
            $model = self::typeByWhere($where['type'], $model, $aler);
        }
        if (isset($where['keywords']) && $where['keywords'] != '') {
            $model = $model->where($aler . 'yw_index' . ($join ? '|' . $join . '.store_info|keyword' : ''), 'LIKE', "%$where[keywords]%");
        }
        if (isset($where['data']) && $where['data'] !== '') {
            $model = self::getModelTime($where, $model, $aler . 'add_time');
        }
        return $model;
    }


    public static function statusByWhere($status, $model = null, $alert = '')
    {
        if ($model == null) $model = new self;
        if ('' === $status)
            return $model;
        else if ($status == 1)//自动
            return $model->where($alert . 'status', 1);
        else if ($status == 2)//手动
            return $model->where($alert . 'status', 2);
        else
            return $model;
    }

    public static function typeByWhere($type, $model = null, $alert = '')
    {
        if ($model == null) $model = new self;
        if ('' === $type)
            return $model;
        else if ($type == 1)//成功
            return $model->where($alert . 'type', 1);
        else if ($type == 2)//失败
            return $model->where($alert . 'type', 2);
        else
            return $model;
    }
}