<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2020-12-09 14:16:54
 * @Last Modified time: 2020-12-09 14:22:29
 */
namespace app\admin\model\evaluation;

use crmeb\traits\ModelTrait;
use crmeb\basic\BaseModel;
use app\models\store\StoreWish;
use app\models\user\UserRelation;

/**
 * TODO 点赞收藏model
 * Class StoreWishRelation
 * @package app\models\store
 */
class StoreWishRelation extends BaseModel
{
    /**
     * 模型名称
     * @var string
     */
    protected $name = 'store_wish_relation';

    use ModelTrait;

    /**
     * 获取用户点赞所有产品的个数
     * @param $uid
     * @return int|string
     */
    public static function getUserIdLike($uid = 0){
        $count = self::where('uid',$uid)->where('type','like')->count();
        return $count;
    }

    /**
     * 获取用户收藏所有产品的个数
     * @param $uid
     * @return int|string
     */
    public static function getUserIdCollect($uid = 0){
        $count = self::where('uid',$uid)->where('type','collect')->count();
        return $count;
    }

     /**
     * 获取用户收藏所有产品的个数
     * @param $uid
     * @return int|string
     */
    public static function getWishIdWant($wishId = 0){
        $count = self::where('wish_id',$wishId)->where('type','want_test')->count();
        return $count;
    }

         /**
     * 获取用户收藏所有产品的个数
     * @param $uid
     * @return int|string
     */
    public static function getWishIdAttention($wishId = 0){
        $count = self::where('wish_id',$wishId)->where('type','attention')->count();
        return $count;
    }

    /**
     * TODO 获取秒杀产品收藏
     * @param $uid
     * @param int $first
     * @param int $limit
     * @return array
     */
    public static function getIsWishIdWantStatus($wishId, $uid)
    {
        $count = self::where('wish_id',$wishId)->where('type','want_test')->count();
        return $count;
    }

    /**
     * 添加点赞 收藏 想去 
     * @param $wishId
     * @param $uid
     * @param $relationType
     * @param string $category
     * @return bool
     */
    public static function wishRelation($uid,$wishId,$category)
    {
        if(!$wishId) return self::setErrorInfo('心愿不存在!');
        $category = strtolower($category);
        $data = ['uid'=>$uid,'wish_id'=>$wishId,'type'=>$category];
        if(self::be($data)) return true;
        $data['add_time'] = time();
        self::create($data);
        return true;
    }

    /**
     * 批量 添加点赞 收藏
     * @param $wishIdS
     * @param $uid
     * @param $relationType
     * @param string $category
     * @return bool
     */
    public static function wishRelationAll($wishIdS,$uid,$relationType,$category = 'wish'){
        $res = true;
        if(is_array($wishIdS)){
            self::beginTrans();
            foreach ($wishIdS as $wishId){
                $res = $res && self::wishRelation($wishId,$uid,$relationType,$category);
            }
            if($res){
                foreach ($wishIdS as $wishId){
                    event('StoreProductUserOperationConfirmAfter',[$category, $wishId, $relationType, $uid]);
                }
            }
            self::checkTrans($res);
            return $res;
        }
        return $res;
    }

    /**
     * 取消 点赞 收藏 想去
     * @param $wishId
     * @param $uid
     * @param $relationType
     * @param string $category
     * @return bool
     */
    public static function unWishRelation($uid,$wishId,$category = 'wish')
    {
        if(!$wishId) return self::setErrorInfo('产品不存在!');
        $category = strtolower($category);
        self::where('uid', $uid)->where('wish_id', $wishId)->where('type', $category)->delete();
        return true;
    }

    public static function wishRelationNum($wishId,$relationType,$category = 'wish')
    {
        $relationType = strtolower($relationType);
        $category = strtolower($category);
        return self::where('type',$relationType)->where('wish_id',$wishId)->where('category',$category)->count();
    }

    public static function isProductRelation($wish_id,$uid,$relationType,$category = 'wish')
    {
        $type = strtolower($relationType);
        $category = strtolower($category);
        return self::be(compact('wish_id','uid','type','category'));
    }


    public static function isWishRelation($wishId,$uid,$relationType)
    {
        $status = false;
        $type = strtolower($relationType);
        $count =  self::where('type',$relationType)->where('wish_id',$wishId)->where('uid',$uid)->count();
        if ($count >= 1) return true;
        return $status;
    }

    /*
     * 获取某个用户收藏产品
     * @param int uid 用户id
     * @param int $first 行数
     * @param int $limit 展示行数
     * @return array
     * */
    public static function getUserCollectProduct($uid,$page,$limit)
    {
        if(!$limit) return [];
        if($page){
            $list = self::where('A.uid',$uid)
                ->field('B.id pid,A.category,B.store_name,B.price,B.ot_price,B.sales,B.image,B.is_del,B.is_show')
                ->alias('A')
                ->where('A.type','collect')/*->where('A.category','wish')*/
                ->order('A.add_time DESC')
                ->join('store_wish B','A.wish_id = B.id')
                ->page($page, $limit)
                ->select();
        }else{
            $list = self::where('A.uid',$uid)
                ->field('B.id pid,A.category,B.store_name,B.price,B.ot_price,B.sales,B.image,B.is_del,B.is_show')->alias('A')
                ->where('A.type','collect')/*->where('A.category','wish')*/
                ->order('A.add_time DESC')->join('store_wish B','A.wish_id = B.id')
                ->select();
        }
        if(!$list) return [];
        $list = $list->toArray();
        foreach ($list as $k=>$wish){
            if($wish['pid']){
                $list[$k]['is_fail'] = $wish['is_del'] && $wish['is_show'];
            }else{
                unset($list[$k]);
            }
        }
        return $list;
    }

    /**
     * TODO 获取普通产品收藏
     * @param $uid
     * @param int $first
     * @param int $limit
     * @return array
     */
    public static function getWishRelation($wishid,$uid,$type)
    {
        $model = new self;
        $model = $model->alias('A');
        $model = $model->join('user B','A.uid = B.uid');
        $model = $model->where('A.wish_id',$wishid);
        $model = $model->field('A.uid,B.avatar,B.nickname');
        $model = $model->where('A.type',$type);
        $model = $model->order('A.add_time DESC');
        $list = $model->select();
        if($list) {
            $list = $list->toArray();
            foreach ($list as &$relation) {
                $relation['is_follow'] = boolval(UserRelation::getIsUserFollows($uid,$relation['uid'])); //跟随数
            }
            return $list;
        }else{ return [];}
    }
    /**
     * TODO 获取秒杀产品收藏
     * @param $uid
     * @param int $first
     * @param int $limit
     * @return array
     */
    public static function getSeckillRelation($uid, $first = 0,$limit = 8)
    {
        $model = new self;
        $model = $model->alias('A');
        $model = $model->join('StoreSeckill B','A.wish_id = B.id');
        $model = $model->where('A.uid',$uid);
        $model = $model->field('B.id pid,B.title store_name,B.price,B.ot_price,B.sales,B.image,B.is_del,B.is_show,A.category,A.add_time');
        $model = $model->where('A.type','collect');
        $model = $model->where('A.category','wish_seckill');
        $model = $model->order('A.add_time DESC');
        $model = $model->limit($first,$limit);
        $list = $model->select();
        if($list) return $list->toArray();
        else return [];
    }

     /**
     * TODO 获取想测
     * @param $userId
     * @param $uid
     * @param int $first
     * @param int $limit
     * @return array
     */
    public static function getWantToTestList($userId = 0 ,$uid = 0, $first = 0,$limit = 8)
    {
        //判断查询的id\
        $query_id = $uid !=0 ? $uid : $userId;
        $model = new self;
        $model = $model->alias('A');
        $model = $model->join('StoreWishList B','B.id = A.wish_id');
        $model = $model->where('A.uid',$query_id);
        $model = $model->field('B.id,B.product_id,B.name,B.image,B.is_located,B.status');
        $model = $model->where('A.type','want_test');
        $model = $model->order('A.add_time DESC');
        $model = $model->limit($first,$limit);
        $list = $model->select();
        if(!$list) return [];
        $list = $list->toArray();
        foreach ($list as $k=>$wish){
            $type = $wish['product_id'] !=0 && $wish['is_located'] != 0 ? 2 : 1; //数据类型、1=心愿 2=评测
            //查看是否有关联了产品ID
            if ($wish['product_id'] != null ) {
                $product = StoreProduct::getValidProduct($wish['product_id'],'store_name,image,price,vip_price');
                $list[$k]['yw_index'] = StoreYwIndex::getYwIndex($wish['product_id']);
                $list[$k]['name'] =  $product['store_name']; 
                $list[$k]['image'] =  is_string($product['image']) ? $product['image'] : '';
                $list[$k]['price'] =  $product['price'];                 
                $list[$k]['vip_price'] =  $product['vip_price']; 
            }else{
                $list[$k]['name'] = $wish['name']; 
                $list[$k]['image'] =  is_string($wish['image']) ? json_decode($wish['image'], true)[0] : [];
            }
            $list[$k]['is_located'] = boolval($wish['is_located']); //是否入驻
            $list[$k]['status'] = $wish['status']; //状态 
            $list[$k]['type'] = $type; //类型
            $list[$k]['is_want_to_test'] = true;
            $list[$k]['want_to_test_number'] = self::getIsWishIdWantStatus($wish['id'],$query_id); //想测人数
        }
        return $list;
    }

}