<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2020-09-10 17:30:36
 * @Last Modified time: 2021-12-23 11:24:55
 */
namespace app\admin\model\evaluation;

use app\admin\model\system\SystemStore;

use crmeb\basic\BaseModel;
use crmeb\traits\ModelTrait;
use think\facade\Route as Url;
use app\admin\model\user\{User};

/**
 * '问卷调查：问题Model
 * Class StoreBillboard
 * @package app\admin\model\store
 */
class StoreActivityApply extends BaseModel
{
	 /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'store_evaluation_activity_apply';

    use ModelTrait;


    /**
     * 处理where条件
     * @param $where
     * @param $model
     * @return mixed
     */
    public static function getActivityWhere($where, $model, $aler = '', $join = '')
    {
        if (isset($where['activity_id']) && $where['activity_id'] != '') {
            $model = $model->where('evaluation_activity_id', $where['activity_id']);
        }
        return $model;
    }


    public static function getJoinList($activity_id, $page, $limit){
        $where['activity_id'] = $activity_id;
        $list = self::getActivityWhere($where, self::alias('a')->join('user r', 'r.uid=a.uid', 'LEFT'), 'a.', 'r')
            ->field('a.*,r.nickname,r.phone,r.spread_uid')->page((int)$page, (int)$limit)->select();
        count($list) && $list = $list->toArray();
        foreach ($list as &$item) {
            $item['add_time'] = date('Y-m-d H:i:s', (int)$item['add_time']);
            $item['complete_time'] = date('Y-m-d H:i:s', (int) $item['add_time']);
            $item['is_lottery'] = true;
            $item['is_management'] = false;
            $item['status_name'] = '评测完成';
        }
        return $list;
    }
}
