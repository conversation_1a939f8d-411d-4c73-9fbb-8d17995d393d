<?php


namespace app\admin\model\evaluation;

use app\admin\model\system\SystemStore;

use crmeb\basic\BaseModel;
use crmeb\traits\ModelTrait;
use think\facade\Route as Url;
use app\admin\model\user\{User};

/**
 * '评论：活动Model
 * Class StoreBillboard
 * @package app\admin\model\store
 */
class StoreComment extends BaseModel
{
	 /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'store_comment';

    use ModelTrait;

    public static function CommentCount()
    {
        $data['passed'] = self::statusByWhere(1, new self())->where(['is_del' => 0])->count();
        $data['pending'] = self::statusByWhere(2, new self())->where(['is_del' => 0])->count();
        $data['deleted'] = self::statusByWhere(3, new self())->where(['is_del' => 1])->count();
        return $data;
    }


    public static function CommentList($where)
    {
        $model = self::getCommentWhere($where, self::alias('a')
            ->join('user r', 'r.uid=a.uid', 'LEFT'), 'a.', 'r')
            ->field('a.*,r.nickname,r.phone,r.spread_uid,r.avatar');
        $model = $model->order('a.id desc');
        $data = ($data = $model->page((int)$where['page'], (int)$where['limit'])->select()) && count($data) ? $data->toArray() : [];
        foreach ($data as &$item) {
            if ($item['status'] == 0 && $item['is_del'] == 0) {
                $status_name = '待审核';
            } elseif ($item['status'] == 1 && $item['is_del'] == 0) {
                $status_name = '已审核';
            } elseif ($item['status'] == 2 && $item['is_del'] == 0) {
                $status_name = '待审核';
            } elseif ($item['is_del'] == 1 || $item['del_time'] != 0) {
                $status_name = '已删除';
            }
            $item['status_name'] = $status_name;
            // 查找父ID用户
	        $type_name = '一级评论';
	        if ($item['pid'] && $item['pid'] > 0) {
	            $pActivityComment = self::find($item['pid']);
	            if ($pActivityComment) {
	               $type_name = '回复:'.User::where('uid', $pActivityComment['uid'])->value('nickname');;
	            }
	        }else{
	        	$type_name = '一级评论';
	        }
            $item['type_name'] = $type_name;
        }
        $count = self::getCommentWhere($where, self::alias('a')->join('user r', 'r.uid=a.uid', 'LEFT'), 'a.', 'r')->count();
        return compact('count', 'data');
    }


    public static function getCommentList($where, $page, $limit){
        $model = self::getCommentWhere($where, self::alias('a')
            ->join('user r', 'r.uid=a.uid', 'LEFT'), 'a.', 'r')
            ->field('a.*,r.nickname,r.phone,r.spread_uid,r.avatar');
        $model = $model->order('a.id desc');
        $data = ($data = $model->page((int)$page, (int)$limit)->select()) && count($data) ? $data->toArray() : [];
        foreach ($data as &$item) {
            if ($item['status'] == 0 && $item['is_del'] == 0) {
                $status_name = '待审核';
            } elseif ($item['status'] == 1 && $item['is_del'] == 0) {
                $status_name = '已审核';
            } elseif ($item['status'] == 2 && $item['is_del'] == 0) {
                $status_name = '待审核';
            } elseif ($item['is_del'] == 1 || $item['del_time'] != 0) {
                $status_name = '已删除';
            }
            $item['status_name'] = $status_name;
            // 查找父ID用户
            $type_name = '一级评论';
            if ($item['pid'] && $item['pid'] > 0) {
                $pActivityComment = self::find($item['pid']);
                if ($pActivityComment) {
                   $type_name = '回复:'.User::where('uid', $pActivityComment['uid'])->value('nickname');;
                }
            }else{
                $type_name = '一级评论';
            }
            $item['type_name'] = $type_name;
            $item['add_time'] = $item['add_time'] ? date('Y-m-d H:i:s', $item['add_time']) : '';
        }
        return $data;
    }

    /**
     * 处理where条件
     * @param $where
     * @param $model
     * @return mixed
     */
    public static function getCommentWhere($where, $model, $aler = '', $join = '')
    {
        // $model = $model->where('is_system_del', 0);
        if (isset($where['status']) && $where['status'] != '') {
            $model = self::statusByWhere($where['status'], $model, $aler);
        }
        if (isset($where['is_del']) && $where['is_del'] != '' && $where['is_del'] != -1) $model = $model->where($aler . 'is_del', $where['is_del']);
        if (isset($where['keywords']) && $where['keywords'] != '') {
            $model = $model->where($aler . 'comment' . ($join ? '|' . $join . '.nickname|' . $join . '.uid|' . $join . '.phone' : ''), 'LIKE', "%$where[keywords]%");
        }
        if (isset($where['data']) && $where['data'] !== '') {
            $model = self::getModelTime($where, $model, $aler . 'add_time');
        }
        return $model;
    }


    public static function statusByWhere($status, $model = null, $alert = '')
    {
        if ($model == null) $model = new self;
        if ('' === $status)
            return $model;
        else if ($status == 0)//待发布
            return $model->where($alert . 'status', 0)->where($alert . 'is_del', 0);
        else if ($status == 1)//已发布
            return $model->where($alert . 'status', 1)->where($alert . 'is_del', 0);
        else if ($status == 2)//待审核
            return $model->where($alert . 'status', 2)->where($alert . 'is_del', 0);
        else if ($status == 3)//已删除
            return $model->where($alert . 'is_del', 1);
        else
            return $model;
    }
}
