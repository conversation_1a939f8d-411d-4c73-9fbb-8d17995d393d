<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2020-09-29 17:29:43
 * @Last Modified time: 2020-09-29 17:32:04
 */
namespace app\admin\model\evaluation;

use app\admin\model\system\SystemStore;

use crmeb\basic\BaseModel;
use crmeb\traits\ModelTrait;
use think\facade\Route as Url;
use app\admin\model\user\{User};

/**
 * '中奖时间段Model
 * Class StoreBillboard
 * @package app\admin\model\store
 */
class StorePeriod extends BaseModel
{
	 /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'evaluation_activity_winning_time';

    use ModelTrait;
}
