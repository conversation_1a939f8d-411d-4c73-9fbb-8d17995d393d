<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2020-12-14 14:45:47
 * @Last Modified time: 2020-12-28 15:38:41
 */
namespace app\admin\model\evaluation;

use crmeb\traits\ModelTrait;
use crmeb\basic\BaseModel;

/**
 * Class StoreLabelData
 * @package app\admin\model\evaluation
 */
class StoreLabelData extends BaseModel
{

    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'system_label_data';

    use ModelTrait;

    /**
     * 异步获取分类列表
     * @param $where
     * @return array
     */
    public static function LabelList($where)
    {

        $model = self::systemPage($where);
        $data = ($data = $model->page((int)$where['page'], (int)$where['limit'])->order('id desc')->select()) && count($data) ? $data->toArray() : [];
        if (!empty($data)) {
           foreach ($data as $k=>$v) {
               $data[$k]['type'] = $v['type'] == 1 ? '后台录入' : '用户录入';
               $data[$k]['add_time'] = $v['add_time'] ? date('Y-m-d H:i:s', $v['add_time']) : '';
                if ($v['status'] == 1 && $v['is_del'] == 0) {
                    $status_name = '已发布';
                } elseif ($v['status'] == 2 && $v['is_del'] == 0) {
                    $status_name = '待审核';
                } elseif ($v['is_del'] == 1 || $v['del_time'] != 0) {
                    $status_name = '已删除';
                }
                $data[$k]['status'] = $status_name;
                $label_cate_id = StoreLabelCate::where('label_id',$v['id'])->column('cate_id');
                $cateName = StoreLabelCategory::where('id', 'IN', $label_cate_id)->column('title');
                $product_type = is_array($cateName) ? implode('/', $cateName) : '';
                $data[$k]['type_name'] = $product_type ?: '暂无分类';
           }
        }
        $count = self::systemPage($where)->count();
        return compact('count', 'data');
    }

    public static function LabelCount()
    {
        $data['passed'] = self::statusByWhere(1, new self())->where(['is_del' => 0])->count();
        $data['pending'] = self::statusByWhere(2, new self())->where(['is_del' => 0])->count();
        $data['deleted'] = self::statusByWhere(3, new self())->where(['is_del' => 1])->count();
        //标签类型
        $data['system'] = self::typeByWhere(1, new self())->where(['is_del' => 0])->count();
        $data['users'] = self::typeByWhere(2, new self())->where(['is_del' => 0])->count();
        return $data;
    }


    /**
     * @param $where
     * @return array
     */
    public static function systemPage($where)
    {
        $model = new self;
        $model = $model->where('is_del',0);
        if ($where['keywords'] != '') $model = $model->where('name', 'LIKE', "%$where[keywords]%");
        if (isset($where['status']) && $where['status'] != '') {
            $model = self::statusByWhere($where['status'], new self());
        }
        if (isset($where['type']) && $where['type'] != '') {
            $model = self::typeByWhere($where['type'], new self());
        }
        if (isset($where['data']) && $where['data'] !== '') {
            $model = self::getModelTime($where, $model, $aler . 'add_time');
        }
        return $model;
    }

    /**
     * 获取顶级分类
     * @return array
     */
    public static function getLabel()
    {
        return self::where('is_show', 1)->column('name', 'id');
    }

    public static function delLabel($id)
    {
        $name  = self::where('id', $id)->value('name');
        $count = StoreScoringItems::where('name', $name)->count();
        if ($count)
            return self::setErrorInfo('此标签已存在评测关联数据，暂时无法删除');
        else {
            return self::edit(['is_del'=>1],$id,'id');
        }
    }

    public static function statusByWhere($status, $model = null)
    {
        if ($model == null) $model = new self;
        if ('' === $status)
            return $model;
        else if ($status == 0)//全部
            return $model->where('status', 0)->where('is_del', 0);
        else if ($status == 1)//已发布
            return $model->where('status', 1)->where('is_del', 0);
        else if ($status == 2)//待审核
            return $model->where('status', 2)->where('is_del', 0);
        else if ($status == 3)//已删除
            return $model->where('is_del', 1);
        else
            return $model;
    }

    public static function typeByWhere($type, $model = null)
    {
        if ($model == null) $model = new self;
        if ('' === $type)
            return $model;
        else if ($type == 1)//系统
            return $model->where('type', 1)->where('is_del', 0);
        else if ($type == 2)//用户
            return $model->where('type', 2)->where('is_del', 0);
        else
            return $model;
    }
}