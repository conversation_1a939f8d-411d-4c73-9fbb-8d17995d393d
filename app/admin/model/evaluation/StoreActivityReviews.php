<?php

namespace app\admin\model\evaluation;

use crmeb\basic\BaseModel;
use crmeb\traits\ModelTrait;
use think\facade\Route as Url;
use app\admin\model\user\{User};
use app\admin\model\evaluation\StoreCategory as CategoryModel;

/**
 * '问卷调查：答案项Model
 * Class StoreBillboard
 * @package app\admin\model\store
 */
class StoreActivityReviews extends BaseModel
{
	 /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'store_evaluation_activity_reviews';

    use ModelTrait;

    /**
     * 分级排序列表
     * @param null $model
     * @return array
     */
    public static function getTierList($model = null)
    {
        if ($model === null) $model = new self();
        return $model->where('is_del', 0)->where('status', 1)->select()->toArray();
    }

    public static function ArticleList($where)
    {
        $model = self::getArticleWhere($where, self::alias('a')
            ->join('store_evaluation_activity s', 's.id=a.evaluation_activity_id', 'LEFT'), 'a.', 's')
            ->field('a.*,s.name,s.image');
        $model = $model->order('a.id desc');
        $data = ($data = $model->page((int)$where['page'], (int)$where['limit'])->select()) && count($data) ? $data->toArray() : [];
        foreach ($data as &$item) {
            $evaluation_images = is_string($item['image']) ? json_decode($item['image'], true) : [];
            $item['image'] = [];
            if (!empty($evaluation_images)) {
            	$item['image'] = $evaluation_images[0];
            }
            if ($item['status'] == 0 && $item['is_del'] == 0) {
                $status_name = '待发布';
            } elseif ($item['status'] == 1 && $item['is_del'] == 0) {
                $status_name = '已发布';
            } elseif ($item['status'] == 2 && $item['is_del'] == 0) {
                $status_name = '待审核';
            } elseif ($item['is_del'] == 1 || $item['del_time'] != 0) {
                $status_name = '已删除';
            }
            $item['status_name'] = $status_name;
            $item['add_time'] = $item['add_time'] ? date('Y-m-d H:i:s', $item['add_time']) : '';
        }
        $count = self::getArticleWhere($where, self::alias('a')->join('store_evaluation_activity s', 's.id=a.evaluation_activity_id', 'LEFT'), 'a.', 's')->count();
        return compact('count', 'data');
    }

        /**
     * 处理where条件
     * @param $where
     * @param $model
     * @return mixed
     */
    public static function getArticleWhere($where, $model, $aler = '', $join = '')
    {
        // $model = $model->where('is_system_del', 0);
        if (isset($where['status']) && $where['status'] != '') {
            $model = self::statusByWhere($where['status'], $model, $aler);
        }
        if (isset($where['is_del']) && $where['is_del'] != '' && $where['is_del'] != -1) $model = $model->where($aler . 'is_del', $where['is_del']);
        if (isset($where['data']) && $where['data'] !== '') {
            $model = self::getModelTime($where, $model, $aler . 'add_time');
        }
        return $model;
    }


    public static function statusByWhere($status, $model = null, $alert = '')
    {
        if ($model == null) $model = new self;
        if ('' === $status)
            return $model;
        else if ($status == 0)//待发布
            return $model->where($alert . 'status', 0)->where($alert . 'is_del', 0);
        else if ($status == 1)//已发布
            return $model->where($alert . 'status', 1)->where($alert . 'is_del', 0);
        else if ($status == 2)//待审核
            return $model->where($alert . 'status', 2)->where($alert . 'is_del', 0);
        else if ($status == 3)//已删除
            return $model->where($alert . 'is_del', 1);
        else
            return $model;
    }
}
