<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2020-08-31 16:09:37
 * @Last Modified time: 2020-12-08 15:25:02
 */
namespace app\admin\model\evaluation;

use app\admin\model\system\SystemStore;

use crmeb\basic\BaseModel;
use crmeb\traits\ModelTrait;
use think\facade\Route as Url;
use app\admin\model\wechat\WechatUser;
use app\admin\model\store\StoreProduct;
use app\models\routine\RoutineTemplate;
use app\admin\model\user\{User, UserBill};
use app\admin\model\evaluation\StoreCategory as CategoryModel;
use app\admin\model\ump\{StoreCouponUser, StorePink};
use crmeb\services\{PHPExcelService, WechatTemplateService};

/**
 * 订单管理Model
 * Class StoreKanban
 * @package app\admin\model\store
 */
class StoreKanban extends BaseModel
{
    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'store_evaluation';

    use ModelTrait;
    
    /**
     * 分级排序列表
     * @param null $model
     * @return array
     */
    public static function getTierList($model = null)
    {
        if ($model === null) $model = new self();
        return $model->where('is_del', 0)->where('status', 1)->select()->toArray();
    }

    public static function KanbanCount()
    {
        $data['tobereleased'] = self::statusByWhere(0, new self())->where(['is_system_del' => 0])->count();
        $data['pending'] = self::statusByWhere(2, new self())->where(['is_system_del' => 0])->count();
        $data['published'] = self::statusByWhere(1, new self())->where(['is_system_del' => 0])->count();
        $data['deleted'] = self::statusByWhere(3, new self())->where(['is_system_del' => 0])->count();
        return $data;
    }

    public static function KanbanList($where)
    {
        $model = self::getOrderWhere($where, self::alias('a')
            ->join('user r', 'r.uid=a.uid', 'LEFT'), 'a.', 'r')
            ->field('a.*,r.nickname,r.phone,r.spread_uid');
        $model = $model->order('a.id desc');
        $data = ($data = $model->page((int)$where['page'], (int)$where['limit'])->select()) && count($data) ? $data->toArray() : [];
        foreach ($data as &$item) {
            $item['title'] = $item['main_title'].'-'.$item['sub_title'];
            $item['slider_image'] = is_string($item['slider_image']) ? json_decode($item['slider_image'], true) : [];
            $type_id = explode(',', $item['cate_id']);
            if ($item['cate_id'] != "") {
            	$item['type_name'] = implode('/', array_column(CategoryModel::whereIn('id', $type_id)->field('cate_name')->select()->toArray(), 'cate_name'));
            }
            if ($item['status'] == 0 && $item['is_del'] == 0) {
                $status_name = '待发布';
            } elseif ($item['status'] == 1 && $item['is_del'] == 0) {
                $status_name = '已发布';
            } elseif ($item['status'] == 2 && $item['is_del'] == 0) {
                $status_name = '待审核';
            } elseif ($item['is_del'] == 1 || $item['del_time'] != 0) {
                $status_name = '已删除';
            }
            $item['status_name'] = $status_name;
            //自动计算
            // $item['yw_index'] = StoreYwIndex::getYwIndex($product['product_id']);
            $item['attention'] = rand(0,100);
            $item['evaluation_number'] = rand(0,100);
            $item['add_time'] = $item['add_time'] ? date('Y-m-d H:i:s', $item['add_time']) : '';
        }
        $count = self::getOrderWhere($where, self::alias('a')->join('user r', 'r.uid=a.uid', 'LEFT'), 'a.', 'r')->count();
        return compact('count', 'data');
    }

        /**
     * 处理where条件
     * @param $where
     * @param $model
     * @return mixed
     */
    public static function getOrderWhere($where, $model, $aler = '', $join = '')
    {
        $model = $model->where('is_system_del', 0);
        if (isset($where['status']) && $where['status'] != '') {
            $model = self::statusByWhere($where['status'], $model, $aler);
        }
        if (isset($where['is_del']) && $where['is_del'] != '' && $where['is_del'] != -1) $model = $model->where($aler . 'is_del', $where['is_del']);
        // if (isset($where['combination_id'])) {
        //     if ($where['combination_id'] == '普通订单') {
        //         $model = $model->where($aler . 'combination_id', 0)->where($aler . 'seckill_id', 0)->where($aler . 'bargain_id', 0);
        //     }
        //     if ($where['combination_id'] == '拼团订单') {
        //         $model = $model->where($aler . 'combination_id', ">", 0)->where($aler . 'pink_id', ">", 0);
        //     }
        //     if ($where['combination_id'] == '秒杀订单') {
        //         $model = $model->where($aler . 'seckill_id', ">", 0);
        //     }
        //     if ($where['combination_id'] == '砍价订单') {
        //         $model = $model->where($aler . 'bargain_id', ">", 0);
        //     }
        // }
        // if (isset($where['real_name']) && $where['real_name'] != '') {
        //     $model = $model->where($aler . 'order_id|' . $aler . 'real_name|' . $aler . 'user_phone' . ($join ? '|' . $join . '.nickname|' . $join . '.uid|' . $join . '.phone' : ''), 'LIKE', "%$where[real_name]%");
        // }
        if (isset($where['data']) && $where['data'] !== '') {
            $model = self::getModelTime($where, $model, $aler . 'add_time');
        }
        return $model;
    }


    public static function statusByWhere($status, $model = null, $alert = '')
    {
        if ($model == null) $model = new self;
        if ('' === $status)
            return $model;
        else if ($status == 0)//待发布
            return $model->where($alert . 'status', 0)->where($alert . 'is_del', 0);
        else if ($status == 1)//已发布
            return $model->where($alert . 'status', 1)->where($alert . 'is_del', 0);
        else if ($status == 2)//待审核
            return $model->where($alert . 'status', 2)->where($alert . 'is_del', 0);
        else if ($status == 3)//已删除
            return $model->where($alert . 'is_del', 1);
        else
            return $model;
    }
}