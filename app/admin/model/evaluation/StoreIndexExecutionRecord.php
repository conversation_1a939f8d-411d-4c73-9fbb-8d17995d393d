<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2021-01-07 15:03:55
 * @Last Modified time: 2021-01-07 16:43:38
 */
namespace app\admin\model\evaluation;

use crmeb\basic\BaseModel;
use crmeb\traits\ModelTrait;
/**
 * 源未指数统计程序运行记录Model
 * Class StoreIndexStatistics
 * @package app\admin\model\evaluation
 */
class StoreIndexExecutionRecord extends BaseModel
{
	 /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'store_index_execution_record';

    use ModelTrait;

    public static function RecordList($where){

        $model = self::order('id desc')->field('*,FROM_UNIXTIME(add_time,"%Y-%m-%d %H:%i:%s") as add_time');
        $data = ($data = $model->page((int)$where['page'], (int)$where['limit'])->select()) && count($data) ? $data->toArray() : [];
        $count = self::count();
        return compact('count', 'data');
    }
}

