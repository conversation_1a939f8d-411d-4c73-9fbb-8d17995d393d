<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2020-09-02 15:03:07
 * @Last Modified time: 2020-09-19 15:19:01
 */
namespace app\admin\model\evaluation;

use app\admin\model\system\SystemStore;

use crmeb\basic\BaseModel;
use crmeb\traits\ModelTrait;
use think\facade\Route as Url;
use app\admin\model\user\{User, UserBill};
use app\admin\model\evaluation\StoreCategory as CategoryModel;
use app\admin\model\evaluation\StoreBillboard as BillboardModel;

/**
 * 评测维度Model
 * Class StoreBillboard
 * @package app\admin\model\store
 */
class StoreDimension extends BaseModel
{
    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'store_evaluation_dimension';

    use ModelTrait;

    protected function getCartIdAttr($value)
    {
        return json_decode($value, true);
    }
    

    public static function DimensionList($where)
    {
        $model = self::where('is_system_del', 0);
        $model = $model->order('id desc');
        $data = ($data = $model->page((int)$where['page'], (int)$where['limit'])->select()) && count($data) ? $data->toArray() : [];
        foreach ($data as &$item) {
            $type_id = explode(',', $item['cate_id']);
            if ($item['cate_id'] != "") {
            	$item['type_name'] = implode('/', array_column(CategoryModel::whereIn('id', $type_id)->field('cate_name')->select()->toArray(), 'cate_name'));
            }
            $item['add_time'] = $item['add_time'] ? date('Y-m-d H:i:s', $item['add_time']) : '';
            $item['status_name'] = '使用中';
            if ($where['type'] == 3) {
                if ($where['operating_id'] == 0) {
                    $item['checked'] = false;
                }else{
                    $item['checked'] = false;
                    $billboard = BillboardModel::where('id','In',$where['operating_id'])->value('dimension_id');
                    $bids= explode(',', $billboard);
                    if (count($bids) > 0 && in_array($item['id'], $bids)) {
                        $item['checked'] = true;
                    }
                }
            }
        }
        $count = self::getDimensionWhere($where, self::alias('a'))->count();
        return compact('count', 'data');
    }

    /**
     * 处理where条件
     * @param $where
     * @param $model
     * @return mixed
     */
    public static function getDimensionWhere($where, $model)
    {
        $model = $model->where('is_system_del', 0);
        if (isset($where['status']) && $where['status'] != '') {
            $model = self::statusByWhere($where['status'], $model, $aler);
        }
        if (isset($where['is_del']) && $where['is_del'] != '' && $where['is_del'] != -1) $model = $model->where($aler . 'is_del', $where['is_del']);
        if (isset($where['data']) && $where['data'] !== '') {
            $model = self::getModelTime($where, $model, $aler . 'add_time');
        }
        return $model;
    }

}