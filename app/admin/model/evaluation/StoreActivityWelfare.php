<?php

namespace app\admin\model\evaluation;

use app\admin\model\system\SystemStore;

use crmeb\basic\BaseModel;
use crmeb\traits\ModelTrait;
use think\facade\Route as Url;
use app\admin\model\user\{User};
use app\admin\model\evaluation\StoreCategory as CategoryModel;

/**
 * '问卷调查：答案项Model
 * Class StoreBillboard
 * @package app\admin\model\store
 */
class StoreActivityWelfare extends BaseModel
{
	 /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'evaluation_activity_welfare';

    use ModelTrait;
}
