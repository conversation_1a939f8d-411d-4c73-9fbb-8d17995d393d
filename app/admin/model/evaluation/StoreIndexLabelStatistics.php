<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2020-12-27 13:35:47
 * @Last Modified time: 2021-01-07 15:51:35
 */
namespace app\admin\model\evaluation;

use crmeb\basic\BaseModel;
use crmeb\traits\ModelTrait;
/**
 * 源未指数统计日志Model
 * Class StoreIndexStatistics
 * @package app\admin\model\evaluation
 */
class StoreIndexLabelStatistics extends BaseModel
{
	 /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'store_index_label_statistics';

    use ModelTrait;


    public static function getLabelList($index_id, $page, $limit){
        $where['index_id'] = $index_id;
        $model = self::getLabelWhere($where, self::alias('a')            
            ->join('store_product r', 'r.id=a.product_id', 'LEFT'), 'a.', 'r')
            ->field('a.*,r.store_name as title');
        $model = $model->order('a.id desc');
        return  ($data = $model->page((int)$page, (int)$limit)->select()) && count($data) ? $data->toArray() : [];
    }


        /**
     * 处理where条件
     * @param $where
     * @param $model
     * @return mixed
     */
    public static function getLabelWhere($where, $model, $aler = '', $join = '')
    {
        if (isset($where['index_id']) && $where['index_id'] != '') {
            $model = $model->where($aler . 'index_id', $where['index_id']);
        }
        return $model;
    }
}