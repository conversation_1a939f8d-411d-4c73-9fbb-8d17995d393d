<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2020-12-09 13:46:37
 * @Last Modified time: 2021-01-27 10:40:59
 */
namespace app\admin\model\evaluation;

use crmeb\basic\BaseModel;
use crmeb\traits\ModelTrait;
use  app\admin\model\store\StoreProduct;
use app\admin\model\store\StoreCategory as CategoryModel;

/**
 * 心愿单管理 model
 * Class StoreWish
 * @package app\admin\model\evaluation
 */
class StoreWish extends BaseModel
{

    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'store_wish_list';

    use ModelTrait;

    public function getDescriptionAttr($value)
    {
        return htmlspecialchars_decode($value);
    }

    public static function WishCount()
    {
        $data['erreicht'] = self::statusByWhere(1, new self())->where(['is_del' => 0])->count();
        $data['unreviewed'] = self::statusByWhere(2, new self())->where(['is_del' => 0])->count();
        $data['to_be_evaluated'] = self::statusByWhere(3, new self())->where(['is_del' => 0])->count();
        $data['tested'] = self::statusByWhere(4, new self())->where(['is_del' => 0])->count();
        $data['deleted'] = self::statusByWhere(5, new self())->where(['is_del' => 1])->count();
        return $data;
    }


    public static function statusByWhere($status, $model = null, $alert = '')
    {
        if ($model == null) $model = new self;
        if ('' === $status)
            return $model;
        else if ($status == 1)//已达成
            return $model->where($alert . 'status', 1)->where($alert . 'is_del', 0);
        else if ($status == 2)//待测评
            return $model->where($alert . 'status', 2)->where($alert . 'is_del', 0);
        else if ($status == 3)//已测评
            return $model->where($alert . 'status', 3)->where($alert . 'is_del', 0);
        else if ($status == 4)//已测评
            return $model->where($alert . 'status', 4)->where($alert . 'is_del', 0);
        else if ($status == 5)//已删除
            return $model->where($alert . 'is_del', 1);
        else
            return $model;
    }

    public static function WishList($where)
    {
        $model = self::getWishWhere($where, self::alias('a')
            ->join('user r', 'r.uid=a.uid', 'LEFT'), 'a.', 'r')
            ->field('a.*,r.nickname,r.phone,r.spread_uid,r.avatar');
        $model = $model->order('a.id desc');
        $data = ($data = $model->page((int)$where['page'], (int)$where['limit'])->select()) && count($data) ? $data->toArray() : [];
        foreach ($data as &$item) {
            if ($item['status'] == 2 && $item['is_del'] == 0) {
                $status_name = '待审核';
            } elseif ($item['status'] == 1 && $item['is_del'] == 0) {
                $status_name = '已达成';
            } elseif ($item['status'] == 3 && $item['is_del'] == 0) {
                $status_name = '待测评';
            } elseif ($item['status'] == 4 && $item['is_del'] == 0) {
                $status_name = '已测评';
            } elseif ($item['is_del'] == 1 || $item['del_time'] != 0) {
                $status_name = '已删除';
            }
            $item['status_name'] = $status_name;
            $item['type']  = $item['type'] == 1 ? '用户' : '后台';//创建心愿类型
            $item['is_located'] = $item['is_located'] == 1 ? '已入驻' : '未入驻';
            $item['want_test_number'] = StoreWishRelation::getWishIdWant($item['id']);
            $prodoct_cate_id = StoreProduct::where('id',$item['product_id'])->value('cate_id');
            $cate_id  = $prodoct_cate_id ? explode(',', $prodoct_cate_id) : [];
            $cateName = CategoryModel::where('id', 'IN', $cate_id)->column('cate_name', 'id');
            $product_type = is_array($cateName) ? implode('/', $cateName) : '';
            $item['product_type'] = $item['product_id'] !=0 ? $product_type : '暂无分类';
            $item['add_time'] = $item['add_time'] ? date('Y-m-d H:i:s', $item['add_time']) : '';
        }
        $count = self::getWishWhere($where, self::alias('a')->join('user r', 'r.uid=a.uid', 'LEFT'), 'a.', 'r')->count();
        return compact('count', 'data');
    }

    public static function getWishList($where, $page, $limit){
        $model = self::getWishWhere($where, self::alias('a')
            ->join('user r', 'r.uid=a.uid', 'LEFT'), 'a.', 'r')
            ->field('a.*,r.nickname,r.phone,r.spread_uid,r.avatar');
        $model = $model->order('a.id desc');
        $data = ($data = $model->page((int)$page, (int)$limit)->select()) && count($data) ? $data->toArray() : [];
        foreach ($data as &$item) {
            if ($item['status'] == 0 && $item['is_del'] == 0) {
                $status_name = '待审核';
            } elseif ($item['status'] == 1 && $item['is_del'] == 0) {
                $status_name = '已审核';
            } elseif ($item['status'] == 2 && $item['is_del'] == 0) {
                $status_name = '待审核';
            } elseif ($item['is_del'] == 1 || $item['del_time'] != 0) {
                $status_name = '已删除';
            }
            $item['status_name'] = $status_name;
            $item['type_name'] = $type_name;
            $item['add_time'] = $item['add_time'] ? date('Y-m-d H:i:s', $item['add_time']) : '';
        }
        return $data;
    }

    /**
     * 处理where条件
     * @param $where
     * @param $model
     * @return mixed
     */
    public static function getWishWhere($where, $model, $aler = '', $join = '')
    {
        if (isset($where['status']) && $where['status'] != '') {
            $model = self::statusByWhere($where['status'], $model, $aler);
        }
        if (isset($where['is_del']) && $where['is_del'] != '' && $where['is_del'] != -1) $model = $model->where($aler . 'is_del', $where['is_del']);
        if (isset($where['keywords']) && $where['keywords'] != '') {
            $model = $model->where($aler . 'name|position|desc' . ($join ? '|' . $join . '.nickname|' . $join . '.uid|' . $join . '.phone' : ''), 'LIKE', "%$where[keywords]%");
        }
        if (isset($where['data']) && $where['data'] !== '') {
            $model = self::getModelTime($where, $model, $aler . 'add_time');
        }
        return $model;
    }
}