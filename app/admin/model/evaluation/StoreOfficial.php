<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2020-09-03 14:04:41
 * @Last Modified time: 2020-09-03 16:40:09
 */
namespace app\admin\model\evaluation;

use app\admin\model\system\SystemStore;

use crmeb\basic\BaseModel;
use crmeb\traits\ModelTrait;
use think\facade\Route as Url;
use app\admin\model\wechat\WechatUser;
use app\admin\model\store\StoreProduct;
use app\models\routine\RoutineTemplate;
use app\admin\model\user\{User, UserBill};
use app\admin\model\evaluation\StoreCategory as CategoryModel;
use app\admin\model\evaluation\StoreBillboard as BillboardModel;
use app\admin\model\ump\{StoreCouponUser, StorePink};
use crmeb\services\{PHPExcelService, WechatTemplateService};

/**
 * 订单管理Model
 * Class StoreActivity
 * @package app\admin\model\store
 */
class StoreOfficial extends BaseModel
{
    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'store_evaluation_official_article';

    use ModelTrait;

    /**
     * 分级排序列表
     * @param null $model
     * @return array
     */
    public static function getTierList($model = null)
    {
        if ($model === null) $model = new self();
        return $model->where('is_del', 0)->where('status', 1)->select()->toArray();
    }

    public static function ArticleList($where)
    {
        $model = self::getOrderWhere($where, self::alias('a')
            ->join('store_evaluation e', 'e.id=a.evaluation_id', 'LEFT'), 'a.', 'e')
            ->field('a.*,e.main_title,e.sub_title,e.image');
        $model = $model->order('a.id desc');
        $data = ($data = $model->page((int)$where['page'], (int)$where['limit'])->select()) && count($data) ? $data->toArray() : [];
        foreach ($data as &$item) {
        	$item['name'] = $item['main_title']. '-' .$item['sub_title'];
            $item['add_time'] = $item['add_time'] ? date('Y-m-d H:i:s', $item['add_time']) : '';
            $item['updated_time'] = $item['updated_time'] ? date('Y-m-d H:i:s', $item['updated_time']) : '';
        }
        $count = self::getOrderWhere($where, self::alias('a')->join('store_evaluation e', 'e.id=a.evaluation_id', 'LEFT'), 'a.', 'e')->count();
        return compact('count', 'data');
    }

     /**
     * 处理where条件
     * @param $where
     * @param $model
     * @return mixed
     */
    public static function getOrderWhere($where, $model, $aler = '', $join = '')
    {
        $model = $model->where($aler . 'is_del', 0)->where($aler . 'is_system_del', 0)->where($join .'.status',1 )->where($join .'.is_del', 0)->where($join .'.is_system_del', 0);
        if (isset($where['evaluation_id']) && $where['evaluation_id'] != 0) {
           $model = $model->where($aler . 'evaluation_id', $where['evaluation_id']);
        }
        if (isset($where['theme_name']) && $where['theme_name'] != '') {
            $model = $model->where($join . '.main_title|' . $join . '.sub_title','LIKE', "%$where[theme_name]%");
        }
        return $model;
    }
}