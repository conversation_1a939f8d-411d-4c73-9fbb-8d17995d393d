<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2020-12-16 15:00:46
 * @Last Modified time: 2020-12-28 13:42:40
 */

namespace app\admin\model\evaluation;

use crmeb\basic\BaseModel;
use crmeb\traits\ModelTrait;
use app\models\store\StoreProduct;

/**
 * 新品推荐 Model
 * Class StoreNewEvaluation
 * @package app\admin\model\evaluation
 */
class StoreNewEvaluation extends BaseModel
{
  /**
   * 数据表主键
   * @var string
   */
  protected $pk = 'id';

  /**
   * 模型名称
   * @var string
   */
  protected $name = 'system_new_evaluations';

  use ModelTrait;

  public static function EvaluationsList($where)
  {
    $model = self::getEvaluationsWhere()->order('id desc');
    $data = ($data = $model->page((int)$where['page'], (int)$where['limit'])->select()) && count($data) ? $data->toArray() : [];
    foreach ($data as &$item) {
      //查找信息
      if ($item['related_id'] != 0) {
        $product = StoreProduct::getValidProduct($item['related_id'], 'store_name,image,price,vip_price');
        if ($product) {
          $item['store_name'] = $item['is_new_title'] != 1  ? $product['store_name'] : $item['title'];
          $item['image'] = $item['is_new_image'] != 1  ? $product['image'] : $item['image'];
          $item['price'] = $product['price'];
          $item['vip_price'] = $product['vip_price'];
        } else {
          $item['store_name'] = $item['title'];
          $item['image'] = $item['image'];
        }
      }
      $item['add_time'] = $item['add_time'] ? date('Y-m-d H:i:s', $item['add_time']) : '';
      $item['updated_time'] = $item['updated_time'] ? date('Y-m-d H:i:s', $item['updated_time']) : '';
    }
    $count = self::getEvaluationsWhere($where, self::alias('a'))->count();
    return compact('count', 'data');
  }

  /**
   * 处理where条件
   * @param $where
   * @param $model
   * @return mixed
   */
  public static function getEvaluationsWhere()
  {
    $model = new self();
    return  $model->where('is_system_del', 0);
  }


  /* 新品推荐隐藏显示
     * @param $id
     * @param $show
     * @return bool
     */
  public static function setEvaluationsShow($id, $show)
  {
    self::beginTrans();
    $res1 = true;
    $res2 = self::where('id', $id)->update(['is_show' => $show]);
    $res = $res1 && $res2;
    self::checkTrans($res);
    return $res;
  }
}
