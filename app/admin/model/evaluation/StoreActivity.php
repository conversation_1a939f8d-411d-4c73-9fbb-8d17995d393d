<?php

namespace app\admin\model\evaluation;

use crmeb\basic\BaseModel;
use crmeb\traits\ModelTrait;
use app\admin\model\evaluation\{
    StoreKanban as KanbanModel,
    StoreActivityWelfare as WelfareModel
};
use app\admin\model\system\SystemStore;
use app\admin\model\welfare\StoreWelfare as WarehouseModel;
use app\admin\model\evaluation\StoreActivityWelfare as ActivityWelfareModel;
use app\admin\model\evaluation\StoreQuestion as QuestionModel;
use app\admin\model\evaluation\StoreAnswer as AnswerModel;
use app\admin\model\evaluation\StorePeriod as PeriodModel;

/**
 * 看板活动管理Model
 * Class StoreActivity
 * @package app\admin\model\store
 */
class StoreActivity extends BaseModel
{
    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'store_evaluation_activity';

    use ModelTrait;

    protected function getCartIdAttr($value)
    {
        return json_decode($value, true);
    }

    /**
     * 分级排序列表
     * @param null $model
     * @return array
     */
    public static function getTierList($model = null)
    {
        if ($model === null) $model = new self();
        return $model->where('is_del', 0)->where('status', 1)->select()->toArray();
    }

    /**
     * 活动关联奖品列表
     * @param null $model
     * @return array
     */
    public static function getPrizeList($activityId)
    {
        $model = new ActivityWelfareModel();
        $data =  $model->field('id,welfare_id,number,received_number')->where('is_del', 0)->where('evaluation_activity_id',$activityId)->select()->toArray();
        foreach ($data as &$welfare) {
            $welfare['name'] = WarehouseModel::where('id',$welfare['welfare_id'])->value('name');
            if ($welfare['received_number'] != 0) {
                $welfare['number'] = $welfare['received_number'];
            }
        }
        return $data;
    }

    /**
     * 活动关联问题列表
     * @param null $model
     * @return array
     */
    public static function getQuestionList($activityId)
    {
        $model = new QuestionModel();
        $data =  $model->field('id,type,name,is_empty,is_upload')->where('is_del', 0)->where('evaluation_activity_id',$activityId)->select()->toArray();
        foreach ($data as &$question) {
            $answer = AnswerModel::field('id,text')->where('evaluation_activity_question_id',$question['id'])->where('is_del', 0)->select()->toArray();
            $question['answer'] = $answer;
        }
        return $data;

    }

    /**
     * 活动关联时间列表
     * @param null $model
     * @return array
     */
    public static function getPeriodList($activityId)
    {
        $model = new PeriodModel();
        return $model->field('id,begin_time,last_time,number,strategy')->where('is_del', 0)->where('evaluation_activity_id',$activityId)->select()->toArray();
    }

    public static function ActivityCount()
    {
        //状态
        $data['tobereleased'] = self::statusByWhere(0, new self())->where(['is_system_del' => 0])->count();
        $data['pending'] = self::statusByWhere(1, new self())->where(['is_system_del' => 0])->count();
        $data['tostart'] = self::statusByWhere(2, new self())->where(['is_system_del' => 0])->count();
        $data['recruiting'] = self::statusByWhere(3, new self())->where(['is_system_del' => 0])->count();
        $data['recruitmentcompleted'] = self::statusByWhere(4, new self())->where(['is_system_del' => 0])->count();
        $data['processing'] = self::statusByWhere(5, new self())->where(['is_system_del' => 0])->count();
        $data['over'] = self::statusByWhere(6, new self())->where(['is_system_del' => 0])->count();
        $data['cancelled'] = self::statusByWhere(5, new self())->where(['is_system_del' => 0])->count();
        $data['deleted'] = self::statusByWhere(6, new self())->where(['is_system_del' => 0])->count();
        return $data;
    }


    public static function ActivityTyepeCount()
    {
        //活动类型
        $data['recruit'] = self::statusByWhere(3, new self())->where(['is_system_del' => 0])->count();
        $data['free'] = self::statusByWhere(3, new self())->where(['is_system_del' => 0])->count();
        return $data;
    }

    public static function ActivityList($where)
    {
        $model = self::getActivityWhere($where, self::alias('a')
            ->join('user r', 'r.uid=a.uid', 'LEFT'), 'a.', 'r')
            ->field('a.*,r.nickname,r.phone,r.spread_uid');
        $model = $model->order('a.id desc');
        $data = ($data = $model->page((int)$where['page'], (int)$where['limit'])->select()) && count($data) ? $data->toArray() : [];
        foreach ($data as &$item) {
            if ($item['status'] == 0 && $item['is_del'] == 0) {
                $status_name = '待发布';
            } elseif ($item['status'] == 1 && $item['is_del'] == 0) {
                $status_name = '已结束';
            } elseif ($item['status'] == 2 && $item['is_del'] == 0) {
                $status_name = '待审核';
            } elseif ($item['status'] == 0 || $item['del_time'] != 0) {
                $status_name = '已发布待开始';
            } elseif ($item['status'] == 0 || $item['del_time'] != 0) {
                $status_name = '招募中';
            } elseif ($item['status'] == 0 || $item['del_time'] != 0) {
                $status_name = '招募完成';
            } elseif ($item['status'] == 0 || $item['del_time'] != 0) {
                $status_name = '进行中';
            } elseif ($item['status'] == 0 || $item['del_time'] != 0) {
                $status_name = '已取消';
            }elseif ($item['is_del'] == 1 || $item['del_time'] != 0) {
                $status_name = '已删除';
            }else{
                $status_name = '状态异常';
            }
            $item['status_name'] = $status_name;
            if ($item['reward_method'] == 1) {
                $reward_name = '赏金';
                $reward_desc = '<br>'.'奖金池：'.$item['bounty_amount'].'元';
            } elseif ($item['reward_method'] == 2) {
                $reward_name = '现场礼品';
                $prize = WelfareModel::field('welfare_id')->where('evaluation_activity_id',$item['id'])->where(['status'=>1,'is_del'=>0])->select()->toArray();
                $prize_id = array_column($prize, 'welfare_id');
                $db_prize = WarehouseModel::field('name')->where('id','in',$prize_id)->where(['status'=>1,'is_del'=>0])->select()->toArray();
                $db_prize_name = array_column($db_prize, 'name');
                $reward_desc = implode('、', $db_prize_name);
            } elseif ($item['reward_method'] == 3) {
                $reward_name = '物流礼品';
                $prize = WelfareModel::field('welfare_id')->where('evaluation_activity_id',$item['id'])->where(['status'=>1,'is_del'=>0])->select()->toArray();
                $prize_id = array_column($prize, 'welfare_id');
                $db_prize = WarehouseModel::field('name')->where('id','in',$prize_id)->where(['status'=>1,'is_del'=>0])->select()->toArray();
                $db_prize_name = array_column($db_prize, 'name');
                $reward_desc = implode('、', $db_prize_name);
            }else{
                $reward_name = '未知';
                $reward_desc = '未知';
            }
            $item['reward_name'] = $reward_name;
            $item['reward_desc'] = $reward_desc != '' ? $reward_desc : '未配置有效奖品';
            $item['image'] = KanbanModel::where('id',$item['evaluation_id'])->value('image');
            $item['type_name'] = $item['type'] == 1 ? '招募评测官' : '现场体验活动';
            $item['add_time'] = $item['add_time'] ? date('Y-m-d H:i:s', $item['add_time']) : '';
            $item['start_time'] = $item['start_time'] ? date('Y-m-d H:i:s', $item['start_time']) : '';
            $item['end_time'] = $item['end_time'] ? date('Y-m-d H:i:s', $item['end_time']) : '';
        }
        $count = self::getActivityWhere($where, self::alias('a')->join('user r', 'r.uid=a.uid', 'LEFT'), 'a.', 'r')->count();
        return compact('count', 'data');
    }

     /**
     * 处理where条件
     * @param $where
     * @param $model
     * @return mixed
     */
    public static function getActivityWhere($where, $model, $aler = '', $join = '')
    {
        $model = $model->where('is_system_del', 0);
        if (isset($where['status']) && $where['status'] != '') {
            $model = self::statusByWhere($where['status'], $model, $aler);
        }
        if (isset($where['type']) && $where['type'] != '') {
            $model = $model->where($aler . 'type', $where['type']);
        }
        if (isset($where['reward_method']) && $where['reward_method'] != '') {
            $model = $model->where($aler . 'reward_method', $where['reward_method']);
        }
        if (isset($where['data']) && $where['data'] !== '') {
            $model = self::getModelTime($where, $model, $aler . 'add_time');
        }
        if (isset($where['subject_name']) && $where['subject_name'] != '') {
            $model = $model->where($aler . 'name', 'LIKE', "%$where[subject_name]%");
        }
        if (isset($where['real_name']) && $where['real_name'] != '') {
            $model = $model->where($aler . 'evaluation_id|' . 'description' . ($join ? '|' . $join . '.nickname|' . $join . '.uid|' . $join . '.phone' : ''), 'LIKE', "%$where[real_name]%");
        }
        return $model;
    }


    public static function statusByWhere($status, $model = null, $alert = '')
    {
        if ($model == null) $model = new self;
        if ('' === $status)
            return $model;
        else if ($status == 0)//全部
            return $model->where($alert . 'is_del', 0);
        else if ($status == 1)//进行中活动
            return $model->where($alert . 'status', 2)->where($alert . 'is_del', 0);
        else if ($status == 2)//报名中活动
            return $model->where($alert . 'status', 1)->where($alert . 'is_del', 0);
        else if ($status == 3)//众筹中活动
            return $model->where($alert . 'status', 0)->where($alert . 'is_del', 0);
        else if ($status == 4)//已结束活动
            return $model->where($alert . 'status', 3)->where($alert . 'is_del', 0);
        else if ($status == 5)//已取消活动
            return $model->where($alert . 'status', 4)->where($alert . 'is_del', 0);
        else if ($status == 6)//已删除【活动回收站】
            return $model->where($alert . 'is_del', 1);
        else
            return $model;
    }
}