<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2020-10-16 13:46:22
 * @Last Modified time: 2020-10-20 17:39:12
 */
namespace app\admin\model\chat;

use app\models\user\User;
use crmeb\traits\ModelTrait;
use crmeb\basic\BaseModel;

/**
 * 客服管理 model
 * Class StoreProduct
 * @package app\admin\model\store
 */
class StoreBuddyLog extends BaseModel
{
    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'store_buddy_log';

    use ModelTrait;


    public static function CommentList($where)
    {
        $model = self::getCommentWhere($where, self::alias('a')
            ->join('user r', 'r.uid=a.uid', 'LEFT'), 'a.', 'r')
            ->field('a.*,r.nickname,r.phone,r.spread_uid,r.avatar');
        $model = $model->order('a.id desc');
        $data = ($data = $model->page((int)$where['page'], (int)$where['limit'])->select()) && count($data) ? $data->toArray() : [];
        foreach ($data as &$item) {
            if ($item['status'] == 0 && $item['is_del'] == 0) {
                $status_name = '待审核';
            } elseif ($item['status'] == 1 && $item['is_del'] == 0) {
                $status_name = '已审核';
            } elseif ($item['status'] == 2 && $item['is_del'] == 0) {
                $status_name = '待审核';
            } elseif ($item['is_del'] == 1 || $item['del_time'] != 0) {
                $status_name = '已删除';
            }
            $item['status_name'] = $status_name;
            // 查找父ID用户
	        $type_name = '';
	        if ($item['type'] == 1 && $item['is_del'] == 0) {
                $type_name = '文字';
            } elseif ($item['type'] == 2 && $item['is_del'] == 0) {
                $type_name = '表情';
            } elseif ($item['type'] == 3 && $item['is_del'] == 0) {
                $type_name = '图片';
            }elseif ($item['type'] == 4 && $item['is_del'] == 0) {
                $type_name = '语音';
            }
            $item['type_name'] = $type_name;
            $item['add_time'] = $item['add_time'] ? date('Y-m-d H:i:s', $item['add_time']) : '';
        }
        $count = self::getCommentWhere($where, self::alias('a')->join('user r', 'r.uid=a.uid', 'LEFT'), 'a.', 'r')->count();
        return compact('count', 'data');
    }


    /**
     * 处理where条件
     * @param $where
     * @param $model
     * @return mixed
     */
    public static function getCommentWhere($where, $model, $aler = '', $join = '')
    {
        if (isset($where['status']) && $where['status'] != '') {
            $model = self::statusByWhere($where['status'], $model, $aler);
        }
        if (isset($where['is_del']) && $where['is_del'] != '' && $where['is_del'] != -1) $model = $model->where($aler . 'is_del', $where['is_del']);
        if (isset($where['keywords']) && $where['keywords'] != '') {
            $model = $model->where($aler . 'comment' . ($join ? '|' . $join . '.nickname|' . $join . '.uid|' . $join . '.phone' : ''), 'LIKE', "%$where[keywords]%");
        }
        if (isset($where['data']) && $where['data'] !== '') {
            $model = self::getModelTime($where, $model, $aler . 'add_time');
        }
        return $model;
    }


    public static function statusByWhere($status, $model = null, $alert = '')
    {
        if ($model == null) $model = new self;
        if ('' === $status)
            return $model;
        else if ($status == 0)//待发布
            return $model->where($alert . 'status', 0)->where($alert . 'is_del', 0);
        else if ($status == 1)//已发布
            return $model->where($alert . 'status', 1)->where($alert . 'is_del', 0);
        else if ($status == 2)//待审核
            return $model->where($alert . 'status', 2)->where($alert . 'is_del', 0);
        else if ($status == 3)//已删除
            return $model->where($alert . 'is_del', 1);
        else
            return $model;
    }
}