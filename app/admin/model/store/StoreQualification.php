<?php

/**
 * @author: xaboy<<EMAIL>>
 * @day: 2017/11/11
 */

namespace app\admin\model\store;

use think\facade\Cache;
use crmeb\basic\BaseModel;
use crmeb\traits\ModelTrait;
use crmeb\services\minishop\Shop;
use crmeb\services\PHPExcelService;

/**
 * 产品管理 model
 * Class StoreWxProduct
 * @package app\admin\model\store
 */
class StoreQualification extends BaseModel
{

  /**
   * 数据表主键
   * @var string
   */
  protected $pk = 'id';

  /**
   * 模型名称
   * @var string
   */
  protected $name = 'store_qualification';

  use ModelTrait;


  public static function registerCheck($type = 1)
  {

    $shop = new  Shop('wechat');
    $result = $shop->status([]);
    if ($result['errcode'] != 0) {
      return [];
    }
    $data = [];
    switch ($type) {
      case 1:
        $num = 0;
        $access_info = $result['data']['access_info'];
        foreach ($access_info as $key => $value) {
          $data[$num]['id'] = $num;
          $data[$num]['field'] = $key;
          $data[$num]['status'] = $value;
          $num++;
        }
        break;
      case 2:
        $num = 0;
        $data = $result['data']['scene_group_list'];
        foreach ($data as $key => $value) {
          if (isset($value['enable_time'])) {
            $data[$num]['enable_time'] = date('Y-m-d H:i:s', $value['enable_time']);
          } else {
            $data[$num]['enable_time'] = '';
          }
          $num++;
        }
        break;
    }
    $count = count($data);
    return compact('count', 'data');
  }


  public static function getShopCateList()
  {
    //缓存缓存
    $cacheName = 'shop_cat_get';
    $cacheTime = 172800;
    if (Cache::has($cacheName)) {
      $data = Cache::get($cacheName);
    } else {
      $shop = new  Shop('wechat');
      $result = $shop->cate_get();
      if ($result['errcode'] != 0) {
        return [];
      }
      $data = $result['third_cat_list'];
      Cache::set($cacheName, $data, $cacheTime);
    }

    return $data;
  }

  public static function DownloadCate()
  {
    $list = self::getShopCateList();
    $export = [];
    foreach ($list as $item) {
      if ($item['qualification_type'] == 0) $item['qualification_type'] = '需要';
      if ($item['qualification_type'] == 1) $item['qualification_type'] = '必填';
      if ($item['qualification_type'] == 2) $item['qualification_type'] = '选填';
      if ($item['product_qualification_type'] == 0) $item['product_qualification_type'] = '需要';
      if ($item['product_qualification_type'] == 1) $item['product_qualification_type'] = '必填';
      if ($item['product_qualification_type'] == 2) $item['product_qualification_type'] = '选填';
      $export[] = [
        $item['third_cat_id'],
        $item['third_cat_name'],
        $item['qualification'],
        $item['qualification_type'],
        $item['product_qualification'],
        $item['product_qualification_type'],
        $item['first_cat_id'],
        $item['first_cat_name'],
        $item['second_cat_id'],
        $item['second_cat_name'],
      ];
    }
    PHPExcelService::instance()->setExcelHeader(['类目ID', '类目名称', '类目资质', '类目资质类型', '商品资质', '商品资质类型', '一级类目ID', '二级类目ID', '二级类目名称'])
      ->setExcelTile('视频号全量类目数据表', '视频号全量类目数据表', ' 生成时间：' . date('Y-m-d H:i:s', time()))
      ->setExcelContent($export)
      ->ExcelSave('视频号全量类目数据表');
    return $data;
  }



  public static function getTierShop($type = 1)
  {

    $shop = new  Shop('wechat');
    $data = [];
    switch ($type) {
      case 1:
        $result = $shop->get_category_list([]);
        if ($result['errcode'] == 0) {
          $data = $result['data'];
        }
        break;
      case 2:
        $result = $shop->get_brand_list([]);
        if ($result['errcode'] == 0) {
          $data = $result['data'];
        }
        break;
    }
    $count = count($data);
    return compact('count', 'data');
  }



  /**
   * 更新状态
   * @param $model
   * @return object
   */
  public static function update_status($id)
  {
    $model = new self();
    $data =  $model->where('id', $id)->find();
    $audit_id = $data['audit_id'];
    $shop = new  Shop('wechat');
    $result = $shop->audit_result($audit_id);
    if ($result['errcode'] != 0) {
      return self::setErrorInfo($result['errmsg']);
    }
    $attributes['sync_time'] = time();
    $attributes['status'] = $result['data']['status'];
    if (isset($result['data']) && isset($result['data']['brand_id'])) {
      $attributes['brand_id'] = $result['data']['brand_id'];
    }
    if (isset($result['data']) && $result['data']['status'] == 9) {
      $attributes['audit_info'] = $result['data']['reject_reason'];
    }
    return self::edit($attributes, $id);
  }

  /**
   * 获取连表MOdel
   * @param $model
   * @return object
   */
  public static function getModelObject($where = [])
  {
    $model = new self();
    if (!empty($where)) {
      $type = $where['type'] ?? 1;
      switch ((int)$type) {
        case 1:
          $model = $model->whereIn('type', [1, 2]);
          break;
        case 2:
          $model = $model->where(['status' => 1]);
          break;
      };
      $model = $model->order('id desc');
    }
    return $model;
  }


  /**
   * 获取产品列表
   * @param $where
   * @return array
   */
  public static function ProductList($where)
  {
    $model = self::getModelObject($where);
    $data = ($data = $model->select()) && count($data) ? $data->toArray() : [];
    foreach ($data as &$item) {
      $item['add_time'] = $item['add_time'] ? date('Y-m-d H:i:s', $item['add_time']) : '';
      $item['sync_time'] = $item['sync_time'] ? date('Y-m-d H:i:s', $item['sync_time']) : '';
    }
    unset($item);
    $count = self::getModelObject($where)->count();
    return compact('count', 'data');
  }


  // 类目审核结果
  public static function synchronizeCategoryAndBrandResults($data)
  {
    $where['audit_id'] = $data['audit_id'];
    $attributes['status'] = $data['status'];
    //品牌
    if ($data['audit_type'] == 1) {
      $attributes['brand_id'] = $data['brand_id'];
    }
    $attributes['audit_info'] = $data['reject_reason'];

    dd($attributes);
    return  self::where($where)->update($attributes);
  }
}
