<?php
/**
 * @author: xaboy<<EMAIL>>
 * @day: 2017/11/11
 */

namespace app\admin\model\store;

use crmeb\services\FormBuilder as Form;
use crmeb\traits\ModelTrait;
use crmeb\basic\BaseModel;
use think\facade\Route as Url;
use crmeb\services\minishop\Shop;
use app\admin\model\store\StoreCategory as CategoryModel;


/**
 * Class StoreCategory
 * @package app\admin\model\store
 */
class StoreCoupon extends BaseModel
{
    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'store_wechat_coupon';

    use ModelTrait;

    /**
     * 获取产品列表
     * @param $where
     * @return array
     */
    public static function addCoupon($data)
    {
        $shop = new  Shop('wechat');
        $result = $shop->coupon_add($data);
        if ($result['errcode'] != 0) {
            return self::setErrorInfo($result['errmsg']);
        }
        $data['name'] = $data['coupon_info']['name'];
        $data['coupon_info'] = json_encode($data['coupon_info']);
        $data['add_time'] = time();
        return self::create($data);
    }


    public static function statusCount()
    {
        $data['not_valid'] = self::where(['status' => 1 ])->count();
        $data['effective'] = self::where(['status'=> 2])->count();
        $data['expired'] = self::where(['status' => 3 ])->count();
        $data['voided'] = self::where(['status' => 4 ])->count();
        return $data;
    }


    public static function typeCount()
    {
        $data['conditional_discounts'] = self::where('type', 1)->count();
        $data['reductions'] = self::where('type',2)->count();
        $data['flat_discount'] = self::where('type', 3)->count();
        $data['direct_reduction'] = self::where('type', 4)->count();
        $data['exchange'] = self::where('type', 5)->count();
        $data['buy_free'] = self::where('type', 6)->count();
        return $data;
    }



    /**
     * 获取产品列表
     * @param $where
     * @return array
     */
    public static function updateCoupon($data,$id)
    {
        $shop = new  Shop('wechat');
        $result = $shop->coupon_update($data);
        if ($result['errcode'] != 0) {
            return self::setErrorInfo($result['errmsg']);
        }
        $data['name'] = $data['coupon_info']['name'];
        $data['coupon_info'] = json_encode($data['coupon_info']);
        $data['renew_time'] = time();
        return self::edit($data,$id);
    }

    /**
     * 更新优惠券状态
     * @param $where
     * @return array
     */
    public static function updateCouponStatus($data,$id)
    {
        $shop = new  Shop('wechat');
        $result = $shop->coupon_update_status($data);
        if ($result['errcode'] != 0) {
            return self::setErrorInfo($result['errmsg']);
        }
        $data['renew_time'] = time();
        return self::edit($data,$id);
    }


    /**
     * 更新优惠券库存
     * @param $data
     * @param $id
     * @return array
     */
    public static function updateCouponStock($data,$id)
    {
        $shop = new  Shop('wechat');
        $result = $shop->coupon_update_stock($data);
        if ($result['errcode'] != 0) {
            return self::setErrorInfo($result['errmsg']);
        }
        $data['stock_info'] = json_encode($data['stock_info']);
        $data['renew_time'] = time();
        return self::edit($data,$id);
    }

    /**
     * 获取产品列表
     * @param $where
     * @return array
     */
    public static function CouponList($where)
    {
        $model = self::getModelObject($where);
        if ($where['excel'] == 0) $model = $model->page((int)$where['page'], (int)$where['limit']);
        $data = ($data = $model->select()) && count($data) ? $data->toArray() : [];
        foreach ($data as &$item) {
            $item['receive_info']  = [];
            $item['valid_info']  = [];
            $coupon_info = json_decode($item['coupon_info'], true);
            $stock_info = json_decode($item['stock_info'], true);
            if (isset($coupon_info['receive_info'])) {
               $item['receive_info'] = $coupon_info['receive_info'];
            }
            if (isset($coupon_info['valid_info'])) {
               $item['valid_info'] = $coupon_info['valid_info'];
            }
            if (isset($stock_info)) {
               $item['stock_info'] = $stock_info;
            }else{
                $item['stock_info'] = [
                    'issued_num'=>0,
                    'receive_num'=>0
                ];
            }
            $item['add_time'] = $item['add_time'] ? date('Y-m-d H:i:s', $item['add_time']) : '';
        }
        $count = self::getModelObject($where)->count();
        return compact('count', 'data');
    }


    /**
     * 获取连表MOdel
     * @param $model
     * @return object
     */
    public static function getModelObject($where = [])
    {
        $model = new self();
        if (!empty($where)) {
            if (isset($where['name']) && $where['name'] != '') {
                $model = $model->where('name', 'LIKE', "%$where[name]%");
            }
            if (isset($where['status']) && $where['status'] != '') {
                $model = $model->where('status',$where['status']);
            }
            if (isset($where['type']) && $where['type'] != '') {
                $model = $model->where('type',$where['type']);
            }
            if (isset($where['data']) && $where['data'] !== '') {
                $model = self::getModelTime($where, $model, 'add_time');
            }
            $model = $model->order('sort desc,id desc');
        }
        return $model;
    }


    /**
     * @param $where
     * @return array
     */
    public static function systemPage($where)
    {
        $model = new self;
        if ($where['status'] != '') $model = $model->where('status', $where['status']);
        if ($where['type'] != '') $model = $model->where('type', $where['type']);
        if ($where['name'] != '') $model = $model->where('name', 'LIKE', "%$where[name]%");
//        if($where['is_del'] != '')  $model = $model->where('is_del',$where['is_del']);
        $model = $model->where('is_del', 0);
        $model = $model->order('id desc');
        return self::page($model, $where);
    }

    /**
     * @param $where
     * @return array
     */
    public static function systemPageCoupon($where)
    {
        $model = new self;
        if ($where['status'] != '') $model = $model->where('status', $where['status']);
        if ($where['name'] != '') $model = $model->where('name', 'LIKE', "%$where[name]%");
//        if($where['is_del'] != '')  $model = $model->where('is_del',$where['is_del']);
        $model = $model->where('is_del', 0);
        $model = $model->where('status', 1);
        $model = $model->order('sort desc,id desc');
        return self::page($model, $where);
    }

    public static function editIsDel($id)
    {
        $data['status'] = 0;
        self::beginTrans();
        $res1 = self::edit($data, $id);
        $res2 = false !== StoreCouponUser::where('cid', $id)->update(['is_fail' => 1]);
        $res3 = false !== StoreCouponIssue::where('cid', $id)->update(['status' => -1]);
        $res = $res1 && $res2 && $res3;
        self::checkTrans($res);
        return $res;

    }

    /**
     * 商品条件折扣券
     * @param $tab_id
     * @return array
     * @throws \FormBuilder\exception\FormBuilderException
     */
    public static function createProductConditionalDiscountsRule($tab_id)
    {
        $formbuider = [];
        // $formbuider[] = Form::select('category_id', '选择品类')->setOptions(function () {
        //     $list = CategoryModel::getTierList(null, 1);
        //     $menus = [];
        //     foreach ($list as $menu) {
        //         $menus[] = ['value' => $menu['id'], 'label' => $menu['html'] . $menu['cate_name']];
        //     }
        //     return $menus;
        // })->filterable(1)->col(12);
        // $formbuider[] = Form::select('cate_name', '选择分类')->setOptions(function () {
        //     $list = CategoryModel::getTierList(null, 1);
        //     $menus = [];
        //     foreach ($list as $menu) {
        //         $menus[] = ['value' => $menu['id'], 'label' => $menu['html'] . $menu['cate_name']];
        //     }
        //     return $menus;
        // })->filterable(1)->col(12);

        //优惠券基本信息
   

        return $formbuider;
    }

    /**
     *  商品满减券
     * @param $tab_id
     * @return array
     * @throws \FormBuilder\exception\FormBuilderException
     */
    public static function createProductReductionsRule($tab_id)
    {
        $formbuider = [];
        // $formbuider[] = Form::frameImages('image', '活动', Url::buildUrl('admin/ump.StoreCoupon/select', array('fodder' => 'image')))->icon('plus')->width('100%')->height('500px');
        // $formbuider[] = Form::hidden('product_id', 0);
        return $formbuider;
    }


    /**
     *  商品统一折扣券
     * @param $tab_id
     * @return array
     * @throws \FormBuilder\exception\FormBuilderException
     */
    public static function createProductFlatDiscountRule($tab_id)
    {
        $formbuider = [];
        // $formbuider[] = Form::frameImages('image', '活动', Url::buildUrl('admin/ump.StoreCoupon/select', array('fodder' => 'image')))->icon('plus')->width('100%')->height('500px');
        // $formbuider[] = Form::hidden('product_id', 0);
        return $formbuider;
    }

    /**
     *  商品直减券
     * @param $tab_id
     * @return array
     * @throws \FormBuilder\exception\FormBuilderException
     */
    public static function createProductDirectReductionRule($tab_id)
    {
        $formbuider = [];
        // $formbuider[] = Form::frameImages('image', '活动', Url::buildUrl('admin/ump.StoreCoupon/select', array('fodder' => 'image')))->icon('plus')->width('100%')->height('500px');
        // $formbuider[] = Form::hidden('product_id', 0);
        return $formbuider;
    }


    /**
     *  商品换购券
     * @param $tab_id
     * @return array
     * @throws \FormBuilder\exception\FormBuilderException
     */
    public static function createProductExchangeRule($tab_id)
    {
        $formbuider = [];
        // $formbuider[] = Form::frameImages('image', '活动', Url::buildUrl('admin/ump.StoreCoupon/select', array('fodder' => 'image')))->icon('plus')->width('100%')->height('500px');
        // $formbuider[] = Form::hidden('product_id', 0);
        return $formbuider;
    }

    /**
     *  商品买赠券
     * @param $tab_id
     * @return array
     * @throws \FormBuilder\exception\FormBuilderException
     */
    public static function createProductBuyFreeRule($tab_id)
    {
        $formbuider = [];
        // $formbuider[] = Form::frameImages('image', '活动', Url::buildUrl('admin/ump.StoreCoupon/select', array('fodder' => 'image')))->icon('plus')->width('100%')->height('500px');
        // $formbuider[] = Form::hidden('product_id', 0);
        return $formbuider;
    }
}