<?php

namespace app\admin\model\store;

use crmeb\basic\BaseModel;
use crmeb\traits\ModelTrait;
use crmeb\services\minishop\Shop;
use app\admin\model\wechat\WechatUser as UserModel;

class StoreCouponUser extends BaseModel
{
  /**
   * 数据表主键
   * @var string
   */
  protected $pk = 'id';

  /**
   * 模型名称
   * @var string
   */
  protected $name = 'store_wechat_coupon_user';

  use ModelTrait;

  public static function tidyCouponList($couponList)
  {
    $time = time();
    foreach ($couponList as &$coupon) {
      $coupon['_add_time'] = date('Y/m/d', $coupon['add_time']);
      $coupon['_end_time'] = date('Y/m/d', $coupon['end_time']);
      $coupon['use_min_price'] = floatval($coupon['use_min_price']);
      $coupon['coupon_price'] = floatval($coupon['coupon_price']);
      if ($coupon['is_fail']) {
        $coupon['_type'] = 0;
        $coupon['_msg'] = '已失效';
      } else if ($coupon['status'] == 1) {
        $coupon['_type'] = 0;
        $coupon['_msg'] = '已使用';
      } else if ($coupon['status'] == 2) {
        $coupon['_type'] = 0;
        $coupon['_msg'] = '已过期';
      } else if ($coupon['add_time'] > $time || $coupon['end_time'] < $time) {
        $coupon['_type'] = 0;
        $coupon['_msg'] = '已过期';
      } else {
        if ($coupon['add_time'] + 3600 * 24 > $time) {
          $coupon['_type'] = 2;
          $coupon['_msg'] = '可使用';
        } else {
          $coupon['_type'] = 1;
          $coupon['_msg'] = '可使用';
        }
      }
      $coupon['integral'] = Db::name('store_coupon')->where(['id' => $coupon['cid']])->value('integral');
    }
    return $couponList;
  }

  /**
   * 获取连表MOdel
   * @param $model
   * @return object
   */
  public static function getModelObject($where = [])
  {
    $model = new self();
    if (!empty($where)) {
      if (isset($where['openid']) && $where['openid'] != '') {
        $model = $model->where('openid', $where['openid']);
      }
      $model = $model->order('id desc');
    }

    return $model;
  }

  /**
   * 获取发放用户优惠券列表
   * @param $where
   * @return array
   */
  public static function userCouponList($where)
  {
    $model = self::getModelObject($where);
    $model = $model->page((int)$where['page'], (int)$where['limit']);
    $data = ($data = $model->select()) && count($data) ? $data->toArray() : [];
    foreach ($data as &$item) {
      $item['create_time'] = $item['created_time'] ? date('Y-m-d H:i:s', $item['created_time']) : '';
      $item['update_time'] = $item['updated_time'] ? date('Y-m-d H:i:s', $item['updated_time']) : '';
      $item['end_time'] = $item['end_time'] ? date('Y-m-d H:i:s', $item['end_time']) : '';
      $item['use_time'] = $item['use_time'] ? date('Y-m-d H:i:s', $item['use_time']) : '';
      $item['start_time'] = $item['start_time'] ? date('Y-m-d H:i:s', $item['start_time']) : '';
      $item['add_time'] = $item['add_time'] ? date('Y-m-d H:i:s', $item['add_time']) : '';
      unset($item['created_time'], $item['updated_time']);
    }
    $count = self::getModelObject($where)->count();
    return compact('count', 'data');
  }


  /**
   * 获取已发放用户列表
   * @param $where
   * @return array
   */
  public static function getReceiveUserList()
  {
    $model =  self::field('uid,openid')
      ->group('openid')
      ->distinct(true);
    $data = ($data = $model->select()) && count($data) ? $data->toArray() : [];
    foreach ($data as &$item) {
      $item['nickname'] = UserModel::where('uid', $item['uid'])->value('nickname');
      $item['openid'] = $item['openid'];
    }
    return $data;
  }


  //获取个人优惠券列表
  public static function getOneCouponsList($where)
  {
    $list = self::where(['uid' => $where['uid']])->page((int)$where['page'], (int)$where['limit'])->select();
    return self::tidyCouponList($list);
  }
  /**
   * 添加用户优惠券
   * @param $where
   * @return array
   */
  public static function addUserCoupon($data)
  {
    $uid = $data['uid'];
    unset($data['uid']);
    //格式化时间
    $data['recv_time'] = strtotime($data['recv_time']);
    $shop = new  Shop('wechat');
    $result = $shop->add_user_coupon($data);
    if ($result['errcode'] = ! 0) {
      return self::setErrorInfo($result['errmsg']);
    }
    $data['uid'] = $uid;
    $data['out_coupon_id'] = $data['user_coupon']['out_coupon_id'];
    $data['out_user_coupon_id'] = $data['user_coupon']['out_user_coupon_id'];
    $data['status'] =  $data['user_coupon']['status'];
    $data['created_time'] = time();
    $data['add_time'] = time();
    unset($data['user_coupon'], $data['recv_time']);
    return self::create($data);
  }


  /**
   * 更新用户优惠券
   * @param $data
   * @param $id
   * @return array
   */
  public static function updateUserCoupon($data, $id)
  {
    $uid = $data['uid'];
    unset($data['uid']);
    //格式化时间
    $data['user_coupon']['ext_info']['use_time'] = $data['user_coupon']['ext_info']['use_time'] ? strtotime($data['user_coupon']['ext_info']['use_time']) : '';
    $data['recv_time'] = strtotime($data['recv_time']);
    $shop = new  Shop('wechat');
    $result = $shop->update_user_coupon($data);
    if ($result['errcode'] = ! 0) {
      return self::setErrorInfo($result['errmsg']);
    }
    //重新拼装
    $data['uid'] = $uid;
    $data['out_coupon_id'] = $data['user_coupon']['out_coupon_id'];
    $data['out_user_coupon_id'] = $data['user_coupon']['out_user_coupon_id'];
    $data['use_time'] =  $data['user_coupon']['ext_info']['use_time'];
    $data['updated_time'] = time();
    unset($data['user_coupon'], $data['recv_time']);
    return self::edit($data, $id);
  }

  /**
   * 手动-更新用户优惠券状态
   * @param $where
   * @return array
   */
  public static function updateUserCouponStatus($data, $id)
  {
    $shop = new  Shop('wechat');
    $result = $shop->update_usercoupon_status($data);
    if ($result['errcode'] = ! 0) {
      return self::setErrorInfo($result['errmsg']);
    }
    $data['updated_time'] = time();
    return self::edit($data, $id);
  }


  /**
   * 用户领券通知
   * @param $where
   * @return array
   */
  public static function  synchronizeCouponUserResults($data)
  {
    //接收用户发券 
    $request_id = $data['request_id']; //请求唯一ID
    $out_coupon_id = $data['out_coupon_id']; //商家端优惠券ID
    $open_id = $data['FromUserName']; //微信用户的 OpenID
    //根据out_coupon_id 查找优惠券 是否有效 并处理 
    $exist_coupon_status = true;
    if (!$exist_coupon_status) {
      $code  = 1090057;
      $coupon_data['out_coupon_id'] = $out_coupon_id;
      return  self::ReturnData($code, $coupon_data);
    }
    //获取优惠券状态
    $coupon_status = true;
    if (!$coupon_status) {
      $code  = 1090058;
      $coupon_data['out_coupon_id'] = $out_coupon_id;
      return  self::ReturnData($code, $coupon_data);
    }
    //是否超出个人限领超限制
    $limit_num_one_person_status = false;
    if (!$limit_num_one_person_status) {
      $code  = 1090030;
      $coupon_data['out_coupon_id'] = $out_coupon_id;
      return  self::ReturnData($code, $coupon_data);
    }
    //处理库存
    $stock_status = true;
    if (!$stock_status) {
      $code  = 1090029;
      $coupon_data['out_coupon_id'] = $out_coupon_id;
      return  self::ReturnData($code, $coupon_data);
    }
    //添加用户优惠券
    $shop = new  Shop('wechat');
    $result = $shop->add_user_coupon($data);
    //更新优惠券库存
    // // 并返回处理结果
    // $result['ret_code'] = 0;
    // $result['ret_msg'] = 0;
    // $result['out_user_coupon_id'] = 0;
    // $result['out_coupon_id'] = 0;
    // $result['request_id'] = 0;
  }

  public  static function ReturnData($code, $coupon_data)
  {
    $result['ret_code'] = $code;
    $result['ret_msg'] = self::get_ret_msg($code);
    if (isset($coupon_data['request_id'])) {
      $result['request_id'] = $coupon_data['request_id'];
    }
    if (isset($coupon_data['out_coupon_id'])) {
      $result['out_coupon_id'] = $coupon_data['out_coupon_id'];
    }
    if (isset($coupon_data['out_user_coupon_id'])) {
      $result['out_user_coupon_id'] = $coupon_data['out_user_coupon_id'];
    }
    return json_encode($result);
  }


  // 视频号场景领券失败展示错误文案
  public  static function get_ret_msg($code)
  {
    switch ($code) {
      case 1090029:
        $text = '抢光了';
        break;
      case 1090030:
        $text = '已达最大领取限额';
        break;
      case 1090031:
        $text = '领券太频繁，请稍后再试';
        break;
      case 1090032:
        $text = '领取失败，请稍后再试';
        break;
      case 1090033:
        $text = '领取失败，请稍后再试';
        break;
      case 1090057:
        $text = '此券无效';
        break;
      case 1090058:
        $text = '领取失败，请稍后再试';
        break;
      default:
        $text = '成功';
        break;
    }

    return $text;
  }
}
