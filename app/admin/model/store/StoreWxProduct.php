<?php

/**
 * @author: xaboy<<EMAIL>>
 * @day: 2017/11/11
 */

namespace app\admin\model\store;

use think\facade\Cache;
use crmeb\basic\BaseModel;
use crmeb\traits\ModelTrait;
use crmeb\services\minishop\Shop;
use crmeb\services\PHPExcelService;

/**
 * 产品管理 model
 * Class StoreWxProduct
 * @package app\admin\model\store
 */
class StoreWxProduct extends BaseModel
{

  /**
   * 数据表主键
   * @var string
   */
  protected $pk = 'id';
  protected $autoWriteTimestamp = true;
  protected $updateTime = 'update_time';


  /**
   * 模型名称
   * @var string
   */
  protected $name = 'store_wx_product';

  use ModelTrait;

  public function getDescriptionAttr($value)
  {
    return htmlspecialchars_decode($value);
  }

  public static function statusCount()
  {
    $data['uploaded'] = self::where(['status' => 5, 'edit_status' => 4, 'is_del' => 0])->count();
    $data['recycle_bin'] = 0;
    $data['logic_delete'] = self::where(['is_del' => 1, 'status' => 9])->count();
    $data['take_down'] = self::where(['status' => 11, 'is_del' => 0])->count();
    $data['sold_out'] = self::where(['status' => 12, 'edit_status' => 4, 'is_del' => 0])->count();
    $data['violation'] = self::where(['status' => 13, 'is_del' => 0])->count();

    return $data;
  }

  public static function editStatusCount()
  {
    $data['edit'] = self::where(['edit_status' => 1, 'is_del' => 0])->count();
    $data['review'] = self::where(['edit_status' => 2, 'is_del' => 0])->count();
    $data['success'] = self::where(['edit_status' => 4, 'is_del' => 0])->count();
    $data['fail'] = self::where(['edit_status' => 3, 'is_del' => 0])->count();
    return $data;
  }

  public static function TypeCount()
  {
    $data['product'] = self::where(['type' => 1])->count();
    $data['special'] = self::where(['type' => 2])->count();
    $data['market'] = self::where(['type' => 3])->count();
    return $data;
  }

  public static function dateTypeCount()
  {
    $data['on_line'] = self::where(['data_type' => 1])->count();
    $data['draft'] = self::where(['data_type' => 2])->count();
    return $data;
  }

  // 商品审核结果
  public static function synchronizeResults($data)
  {
    $where['out_product_id'] = $data['out_product_id'];
    $where['product_id'] = $data['product_id'];
    $attributes['edit_status'] = $data['status']; //审核状态
    if (isset($data['spu_status'])) { //当前商品上下架状态
      $attributes['status'] = $data['spu_status'];
    }
    if (isset($data['reject_reason'])) {
      $audit_info['reject_reason'] =  $data['reason'];
      $audit_info['audit_time'] =  date('Y-m-d H:i:s', time());
      $attributes['audit_info'] = json_encode($audit_info);
    }
    return  self::where($where)->update($attributes);
  }


  // 商品系统下架通知
  public static function synchronizeTakeDownResults($data)
  {
    $where['out_product_id'] = $data['out_product_id'];
    $where['product_id'] = $data['product_id'];
    $attributes['status'] = $data['status'];
    $audit_info['reject_reason'] =  $data['reason'];
    $audit_info['audit_time'] =  date('Y-m-d H:i:s', time());
    $attributes['audit_info'] = json_encode($audit_info);
    return  self::where($where)->update($attributes);
  }

  /**
   * 免审更新商品字段
   * @param $data
   * @return array
   */
  public static function getShopSpu(int $return_type = 0, int $product_id = 0, string $out_product_id = '', int  $status = 1)
  {
    $shop = new  Shop('wechat');
    $result = $shop->spu_get($product_id, $out_product_id, $status);
    if ($result['errcode'] != 0) {
      return self::setErrorInfo($result['errmsg']);
    }
    switch ($return_type) {
      case 1: //sku
        return $result['spu']['skus'];
        break;
      case 2: //商品审核信息
        return $result['spu']['audit_info'];
        break;
      default: //全部
        return $result['spu'];
        break;
    }
  }



  /**
   * 获取产品列表
   * @param $where
   * @return array
   */
  public static function addShop($data)
  {
    $items = $data['items'];
    $skus = $data['skus'];
    unset($data['items']);
    foreach ($data['skus'] as &$sku) {
      unset($sku['value1']);
      unset($sku['detail']);
    }
    $data['title'] = $data['title'];
    $shop = new  Shop('wechat');
    $result = $shop->add($data);
    if ($result['errcode'] != 0) {
      return self::setErrorInfo($result['errmsg']);
    }
    $data['product_id'] = $result['data']['product_id'];
    $data['market_price'] = min(array_column($skus, 'market_price'));
    $data['sale_price'] = min(array_column($skus, 'sale_price'));
    $data['head_img'] = json_encode($data['head_img']);
    $data['stock'] = array_sum(array_column($skus, 'stock'));
    //将返回的数据 ，存入到数据库，方便后期查询
    $data['desc_info'] = json_encode($data['desc_info']);
    $data['skus'] = json_encode($skus);
    $data['items'] = json_encode($items);
    $data['scene_group_list'] = json_encode($data['scene_group_list']);
    $data['qualification_pics'] = json_encode($data['qualification_pics']);
    $data['data_type'] = 1; //数据类型
    $data['edit_status'] = 2; //审核状态
    $data['status'] = 0; //初始状态
    $data['add_time'] = time();
    return self::create($data);
  }

  /**
   * 获取产品列表
   * @param $where
   * @return array
   */
  public static function editShop($data, $id)
  {
    $items = $data['items'];
    $skus = $data['skus'];
    unset($data['items']);
    foreach ($data['skus'] as &$sku) {
      unset($sku['value1']);
      unset($sku['detail']);
    }
    $shop = new  Shop('wechat');
    $result = $shop->spu_update($data);
    if ($result['errcode'] != 0) {
      return self::setErrorInfo($result['errmsg']);
    }
    $data['product_id'] = $result['data']['product_id'];
    $data['market_price'] = min(array_column($skus, 'market_price'));
    $data['sale_price'] = min(array_column($skus, 'sale_price'));
    $data['head_img'] = json_encode($data['head_img']);
    $data['stock'] = array_sum(array_column($skus, 'stock_num'));
    //将返回的数据 ，存入到数据库，方便后期查询
    $data['desc_info'] = json_encode($data['desc_info']);
    $data['skus'] = json_encode($skus);
    $data['items'] = json_encode($items);
    $data['scene_group_list'] = json_encode($data['scene_group_list']);
    $data['qualification_pics'] = json_encode($data['qualification_pics']);
    $data['edit_status'] = 2; //审核状态
    $data['update_time'] = time();
    return self::edit($data, $id);
  }

  /**
   * 免审更新商品字段
   * @param $data
   * @return array
   */
  public static function updateWithoutShop($data, $id)
  {
    $shop = new  Shop('wechat');
    $result = $shop->update_without_audit($data);
    if ($result['errcode'] != 0) {
      return self::setErrorInfo($result['errmsg']);
    }
    $product_skus =  self::where('id', $id)->value('skus');
    $detail = $data['attrs'];
    $data['market_price'] = min(array_column($detail, 'market_price'));
    $data['sale_price'] = min(array_column($detail, 'sale_price'));
    $data['stock'] = array_sum(array_column($detail, 'stock_num'));
    //将返回的数据 ，存入到数据库，方便后期查询
    $old_sku = json_decode($product_skus, true) ?: [];
    foreach ($old_sku as $key => $value) {
      foreach ($detail as $k => $v) {
        if ($v['out_sku_id'] == $value['out_sku_id']) {
          $old_sku[$key]['sale_price'] = $v['sale_price'];
          $old_sku[$key]['market_price'] = $v['market_price'];
          $old_sku[$key]['stock_num'] = $v['stock_num'];
          $old_sku[$key]['sku_code'] = $v['sku_code'];
          $old_sku[$key]['barcode'] = $v['barcode'];
        }
      }
    }
    $data['skus'] = json_encode($old_sku);
    $data['update_time'] = strtotime($result['data']['update_time']);
    unset($data['attrs']);
    return self::edit($data, $id);
  }




  /**
   * 删除商品
   * @param $id
   * @return array
   */
  public static function delShop($id)
  {
    $product_info = self::field('product_id,out_product_id')->where('id', $id)->find();
    $param['product_id'] = $product_info['product_id'];
    $param['out_product_id'] = $product_info['out_product_id'];
    $shop = new  Shop('wechat');
    $result = $shop->del($param);
    if ($result['errcode'] != 0) {
      return self::setErrorInfo($result['errmsg']);
    }
    $data['edit_status'] = 0;
    $data['status'] = 9;
    $data['is_del'] = 1;
    $data['delete_time'] = time();
    $data['update_time'] = time();
    return self::edit($data, $id);
  }


  /**
   * 免审更新商品字段
   * @param $data
   * @return array
   */
  public static function delAuditShop($id)
  {
    $product_info = self::field('product_id,out_product_id')->where('id', $id)->find();
    $param['product_id'] = $product_info['product_id'];
    $param['out_product_id'] = $product_info['out_product_id'];
    $shop = new  Shop('wechat');
    $result = $shop->del_audit($param);
    if ($result['errcode'] != 0) {
      return self::setErrorInfo($result['errmsg']);
    }
    $data['status'] = 0;
    $data['edit_status'] = 1;
    $data['update_time'] = time();
    return self::edit($data, $id);
  }


  /**
   * 上架商品
   * @param $id
   * @return array
   */
  public static function listingShop($id)
  {
    $product_info = self::field('product_id,out_product_id')->where('id', $id)->find();
    $param['product_id'] = $product_info['product_id'];
    $param['out_product_id'] = $product_info['out_product_id'];
    $shop = new  Shop('wechat');
    $result = $shop->listing($param);
    if ($result['errcode'] != 0) {
      return self::setErrorInfo($result['errmsg']);
    }
    $data['status'] = 5;
    $data['update_time'] = time();
    return self::edit($data, $id);
  }


  /**
   * 下架商品
   * @param $data
   * @param $id
   * @return array
   */
  public static function delistingShop($data, $id)
  {
    $product_info = self::field('product_id,out_product_id')->where('id', $id)->find();
    $param['product_id'] = $product_info['product_id'];
    $param['out_product_id'] = $product_info['out_product_id'];
    $shop = new  Shop('wechat');
    $result = $shop->delisting($param);
    if ($result['errcode'] != 0) {
      return self::setErrorInfo($result['errmsg']);
    }
    $data['status'] = 11;
    $data['take_down_time'] = time();
    $data['take_down_reason'] = $data['remark'];
    $data['update_time'] = time();
    return self::edit($data, $id);
  }



  /**
   * 更新状态
   * @param $model
   * @return object
   */
  public static function update_status($id)
  {
    $model = new self();
    $data =  $model->where('id', $id)->find();
    $product_id = $data['product_id'];
    $out_product_id = $data['out_product_id'];
    $shop = new  Shop('wechat');
    $result = $shop->spu_get($product_id, $out_product_id, 1);
    if ($result['errcode'] != 0) {
      return self::setErrorInfo($result['errmsg']);
    }
    $attributes['status'] = $result['spu']['status'];
    $attributes['edit_status'] = $result['spu']['edit_status'];
    $attributes['audit_info'] = json_encode($result['spu']['audit_info'], JSON_UNESCAPED_UNICODE);
    $attributes['update_time'] = strtotime($result['spu']['update_time']);
    return self::edit($attributes, $id);
  }


  public static function registerCheck($type = 1)
  {
    $shop = new  Shop('wechat');
    $result = $shop->status([]);
    if ($result['errcode'] != 0) {
      return [];
    }
    $data = [];
    switch ($type) {
      case 1:
        $num = 0;
        $access_info = $result['data']['access_info'];
        foreach ($access_info as $key => $value) {
          $data[$num]['id'] = $num;
          $data[$num]['field'] = $key;
          $data[$num]['status'] = $value;
          $num++;
        }
        break;
      case 2:
        $num = 0;
        $data = $result['data']['scene_group_list'];
        foreach ($data as $key => $value) {
          if (isset($value['enable_time'])) {
            $data[$num]['enable_time'] = date('Y-m-d H:i:s', $value['enable_time']);
          } else {
            $data[$num]['enable_time'] = '';
          }
          $num++;
        }
        break;
    }
    $count = count($data);
    return compact('count', 'data');
  }


  public static function getShopCateList()
  {
    //缓存缓存
    $cacheName = 'shop_cat_get';
    $cacheTime = 172800;
    if (Cache::has($cacheName)) {
      $data = Cache::get($cacheName);
    } else {
      $shop = new  Shop('wechat');
      $result = $shop->img_upload();
      if ($result['errcode'] != 0) {
        return [];
      }
      $data = $result['third_cat_list'];
      Cache::set($cacheName, $data, $cacheTime);
    }

    return $data;
  }

  public static function DownloadCate()
  {
    $list = self::getShopCateList();
    $export = [];
    foreach ($list as $item) {
      if ($item['qualification_type'] == 0) $item['qualification_type'] = '需要';
      if ($item['qualification_type'] == 1) $item['qualification_type'] = '必填';
      if ($item['qualification_type'] == 2) $item['qualification_type'] = '选填';
      if ($item['product_qualification_type'] == 0) $item['product_qualification_type'] = '需要';
      if ($item['product_qualification_type'] == 1) $item['product_qualification_type'] = '必填';
      if ($item['product_qualification_type'] == 2) $item['product_qualification_type'] = '选填';
      $export[] = [
        $item['third_cat_id'],
        $item['third_cat_name'],
        $item['qualification'],
        $item['qualification_type'],
        $item['product_qualification'],
        $item['product_qualification_type'],
        $item['first_cat_id'],
        $item['first_cat_name'],
        $item['second_cat_id'],
        $item['second_cat_name'],
      ];
    }
    PHPExcelService::instance()->setExcelHeader(['类目ID', '类目名称', '类目资质', '类目资质类型', '商品资质', '商品资质类型', '一级类目ID', '二级类目ID', '二级类目名称'])
      ->setExcelTile('视频号全量类目数据表', '视频号全量类目数据表', ' 生成时间：' . date('Y-m-d H:i:s', time()))
      ->setExcelContent($export)
      ->ExcelSave('视频号全量类目数据表');
    return $data;
  }



  public static function getTierShop($type = 1)
  {

    $shop = new  Shop('wechat');
    switch ($type) {
      case 1:
        $result = $shop->get_category_list([]);
        if ($result['errcode'] != 0) {
          return ['data' => []];
        }
        $data = $result['data'];
        break;
      case 2:
        $result = $shop->get_brand_list([]);
        if ($result['errcode'] != 0) {
          return ['data' => []];
        }
        $data = $result['data'];
        break;
    }
    $count = count($data);
    return compact('count', 'data');
  }


  /**
   * 获取连表MOdel
   * @param $model
   * @return object
   */
  public static function getModelObject($where = [])
  {
    $model = new self();
    if (!empty($where)) {
      if (isset($where['type']) && $where['type'] != '') {
        $model = $model->where('type', $where['type']);
      }
      if (isset($where['title']) && $where['title'] != '') {
        $model = $model->where('title|keyword|id', 'LIKE', "%$where[title]%");
      }
      if (isset($where['status']) && $where['status'] != '') {
        $model = $model->where('status', $where['status']);
      }
      if (isset($where['edit_status']) && $where['edit_status'] != '') {
        $model = $model->where('edit_status', $where['edit_status']);
      }
      if (isset($where['data_type']) && $where['data_type'] != '') {
        $model = $model->where('data_type', $where['data_type']);
      }
      if (isset($where['data']) && $where['data'] !== '') {
        $model = self::getModelTime($where, $model, 'add_time');
      }
      if (isset($where['update_data']) && $where['update_data'] !== '') {
        $model = self::getModelTime($where, $model, 'update_time', 'update_data');
      }
      $model = isset($where['status']) && $where['status'] != '' && $where['status'] == 9 ? $model->where('is_del', 1) : $model->where('is_del', 0);
      $model = $model->order('sort desc,id desc');
    }
    return $model;
  }

  /**
   * 获取产品列表
   * @param $where
   * @return array
   */
  public static function ProductList($where)
  {
    $need_edit_spu = 0;
    $attributes['page'] =  (int)$where['page'];
    $attributes['page_size'] =  100;
    $attributes['need_edit_spu'] =  $need_edit_spu;
    $new_attributes = [];
    $product_model =  new Self();
    $shop = new  Shop('wechat');
    $result = $shop->spu_list($attributes);
    $db_count = self::count();
    if ($result['errcode'] == 0) {
      $wx_count = $result['total_num'];
      if ($wx_count > $db_count) {
        foreach ($result['spus'] as $key => $value) {
          $product_id = $value['product_id'];
          $out_product_id = $value['out_product_id'];
          $db_product = self::where('product_id', $product_id)->where('out_product_id', $out_product_id)->find();
          if ($db_product) { //更新
            $db_product->status = $value['status'];
            $db_product->edit_status = $value['edit_status'];
            $db_product->audit_info = json_encode($value['audit_info']);
            $db_product->update_time = strtotime($value['update_time']);
            $db_product->save();
          } else {
            //新增
            if (isset($value['title'])) {
              $new_attributes[$key]['title'] = $value['title'];
            }
            if (isset($value['third_cat_id'])) {
              $new_attributes[$key]['third_cat_id'] = $value['third_cat_id'];
            }
            if (isset($value['brand_id'])) {
              $new_attributes[$key]['brand_id'] = $value['brand_id'];
            }
            if (isset($value['path'])) {
              $new_attributes[$key]['path'] = $value['path'];
            }
            if (isset($value['direct_path'])) {
              $new_attributes[$key]['direct_path'] = $value['direct_path'];
            }
            if (isset($value['skus']) && !empty($value['skus'])) {
              $new_attributes[$key]['market_price'] = min(array_column($value['skus'], 'market_price'));
              $new_attributes[$key]['sale_price'] = min(array_column($value['skus'], 'sale_price'));
              $new_attributes[$key]['stock'] = array_sum(array_column($value['skus'], 'stock_num'));

              $skus = $value['skus'];
              if (count($skus) >= 1) {
                $attr_value = StoreProductAttr::where('product_id', $value['out_product_id'])->find();
                if ($attr_value) {
                  $items[0]['value'] = $attr_value['attr_name'];
                  $items[0]['detailValue'] = '';
                  $items[0]['attrHidden'] = false;
                  $items[0]['detail'] = $attr_value['attr_values'];
                  $new_attributes[$key]['items'] = $items;
                }
              }
            }
            $new_attributes[$key]['data_type'] = $value['status'] == 5 ? 1 : 2;
            $new_attributes[$key]['product_id'] = $value['product_id'];
            $new_attributes[$key]['out_product_id'] = $value['out_product_id'];
            $new_attributes[$key]['head_img'] = json_encode($value['head_img']);
            $new_attributes[$key]['desc_info'] = json_encode($value['desc_info']);
            $new_attributes[$key]['skus'] = json_encode($value['skus']);
            $new_attributes[$key]['qualification_pics'] = json_encode($value['qualification_pics']);
            $new_attributes[$key]['scene_group_list'] = json_encode($value['scene_group_list']);
            $new_attributes[$key]['audit_info'] = json_encode($value['audit_info']);
            $new_attributes[$key]['status'] = $value['status'];
            $new_attributes[$key]['edit_status'] = $value['edit_status'];
            $new_attributes[$key]['add_time'] = strtotime($value['create_time']);
            $new_attributes[$key]['update_time'] = strtotime($value['update_time']);
          }
        }
      }
    }
    if (!empty($new_attributes)) {
      $product_model->saveAll($new_attributes);
    }
    $model = self::getModelObject($where);
    if ($where['excel'] == 0) $model = $model->page((int)$where['page'], (int)$where['limit']);
    $data = ($data = $model->select()) && count($data) ? $data->toArray() : [];
    foreach ($data as &$item) {
      $head_img = is_string($item['head_img'])  ? json_decode($item['head_img'], true) : [];
      $item['head_img'] = !empty($head_img) ? $head_img[0] : '';
      $item['take_down_reason'] = is_string($item['take_down_reason']) ? json_decode($item['take_down_reason'], true) : [];
      $item['audit_info'] = is_string($item['audit_info']) ? json_decode($item['audit_info'], true) : [];
      $item['add_time'] = $item['add_time'] ? date('Y-m-d H:i:s', $item['add_time']) : '';
      $item['take_down_time'] = $item['take_down_time'] ? date('Y-m-d H:i:s', $item['take_down_time']) : '';
      $item['delete_time'] = $item['delete_time'] ? date('Y-m-d H:i:s', $item['delete_time']) : '';
    }
    $count = self::getModelObject($where)->count();
    return compact('count', 'data');
  }

  //编辑库存
  public static function changeStock($stock, $productId)
  {
    return self::edit(compact('stock'), $productId);
  }

  /**
   * TODO 获取某个字段值
   * @param $id
   * @param string $field
   * @return mixed
   */
  public static function getProductField($id, $field = 'store_name')
  {
    return self::where('id', $id)->value($field);
  }
}
