<?php
/**
 * @author: xaboy<<EMAIL>>
 * @day: 2017/11/11
 */
namespace app\admin\model\store;

use crmeb\basic\BaseModel;
use crmeb\traits\ModelTrait;
use crmeb\services\minishop\Shop;
use crmeb\services\xhs\Shop as Xshop;

/**
 * 渠道课程商品配置 model
 * Class StoreChannelProduct
 * @package app\admin\model\store
 */
class StoreChannelProduct extends BaseModel
{
    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'store_channels_product';

    use ModelTrait;

    protected function getTypeAttr($value)
    {
        $name = '';
        switch ($value) {
            case 1:
                $name = '视频号';
                break;
            case 2:
                $name = '抖音';
                break;
            case 3:
                $name = '小红书';
                break;
        }
        return $name;
    }

    public static function getChannelProductList($where)
    {
        $model = self::getProductWhere($where, self::alias('a'))
            ->field('a.*');
        $model = $model->order('a.id desc');
        $data = ($data = $model->page((int)$where['page'], (int)$where['limit'])->select()) && count($data) ? $data->toArray() : [];
        foreach ($data as &$item) {
            $item['add_time'] = $item['add_time'] ? date('Y-m-d H:i:s', $item['add_time']) : '';
        }
        $count = self::getProductWhere($where, self::alias('a'))->count();
        return compact('count', 'data');
    }


    /**
     * 处理where条件
     * @param $where
     * @param $model
     * @return mixed
     */
    public static function getProductWhere($where, $model, $aler = '', $join = '')
    {
        $model = $model->where('is_del',0);
        if (isset($where['type']) && $where['type'] != '') {
            $model = $model->where('type',$where['type']);
        }
        if (isset($where['title']) && $where['title'] != '') {
            $model = $model->where($aler . 'title', 'LIKE', "%$where[title]%");
        }
        if (isset($where['keywords']) && $where['keywords'] != '') {
            $model = $model->where($aler . 'channel_product_id|channel_product_sku_id', 'LIKE', "%$where[keywords]%");
        }
        if ($where['add_time'] != '') {
            list($startTime, $endTime) = explode(' - ', $where['add_time']);
            $model = $model->where($aler. 'add_time', '>', strtotime($startTime));
            $model = $model->where($aler. 'add_time', '<', strtotime($endTime) + 24 * 3600);
        }
        return $model;
    }

    /**
     * 获取第三方渠道商品信息
     * @param $where
     * @return array
     */
    public static function getChannelProductSkuList($where)
    {
        $search_channel_product_id = $where['channel_product_id'];
    	$type = $where['type'];
        $data = [];
    	switch ($type) {
    		case 1:  //视频号
    	    	$shop = new  Shop('wechat');
        		$all_product = $shop->ec_product_list_get(0,''); //获取所有产品
                if ($all_product && $all_product['errcode'] == 0 && $all_product['errmsg'] == "ok" && !empty($all_product['product_ids'])) {
                    $all_product_ids = $all_product['product_ids'];
                    foreach ($all_product_ids as $key => $product_id) {
                        $product_detail = $shop->ec_product_get($product_id); 
                        if ($product_detail) {
                            $data[$key] = self::TidyData(1,$product_detail['product']);
                        }
                    }
                }
    			break;
    		case 3:  //小红书
    	        $shop = new  Xshop('ark');
        		$result = $shop->getDetailSkuList();
        		$all_product = $result['data'];
                if ($all_product) {
                    foreach ($all_product as $key => $product_info) {
                        $data[$key] = self::TidyData(2,$product_info);
                    }
                }
    			break;
    		default:
    			return[];
    			break;
    	}  
        if ($search_channel_product_id != "") {
          foreach ($data as $sk => $sv) {
                if (mb_stripos($sv['id'], $search_channel_product_id) === false && mb_stripos($sv['name'], $search_channel_product_id) === false
                ) {
                    unset($data[$sk]);
                    continue;
                }
            }
        }
    	$count = count($data);
        return compact('count', 'data');
    }


    /**
     * 格式化数据
     * @param $model
     * @return object
     */
    public static function TidyData($type,$productResult)
    {
    	$data = [];
        if (empty($productResult)) return $data;
    	switch ($type) {
    		case 1: //视频号
    		   	$data['id'] = $productResult['product_id'];
    			$data['name'] = $productResult['title'];
    			$data['skus'] = $productResult['skus'];
    			break;
    		case 2: //小红书
    		    $data['id'] = $productResult['item']['id'];
    			$data['name'] = $productResult['item']['name'];
    			$data['skus'] = [['sku_id'=>$productResult['sku']['id'],'thumb_img'=>$productResult['sku']['specImage']]];
    			break;
    	}
    	return $data;
    }
}