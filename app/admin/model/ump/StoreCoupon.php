<?php
/**
 * @author: xaboy<<EMAIL>>
 * @day: 2017/11/11
 */

namespace app\admin\model\ump;

use crmeb\services\FormBuilder as Form;
use crmeb\traits\ModelTrait;
use crmeb\basic\BaseModel;
use think\facade\Route as Url;
use app\admin\model\store\StoreCategory as CategoryModel;


/**
 * Class StoreCategory
 * @package app\admin\model\store
 */
class StoreCoupon extends BaseModel
{
    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'store_coupon';

    use ModelTrait;

    /**
     * @param $where
     * @return array
     */
    public static function systemPage($where)
    {
        $model = new self;
        if ($where['status'] != '') $model = $model->where('status', $where['status']);
        if ($where['type'] != '') $model = $model->where('type', $where['type']);
        if ($where['title'] != '') $model = $model->where('title', 'LIKE', "%$where[title]%");
//        if($where['is_del'] != '')  $model = $model->where('is_del',$where['is_del']);
        $model = $model->where('is_del', 0);
        $model = $model->order('id desc');
        return self::page($model, $where);
    }

    /**
     * @param $where
     * @return array
     */
    public static function systemPageCoupon($where)
    {
        $model = new self;
        if ($where['status'] != '') $model = $model->where('status', $where['status']);
        if ($where['title'] != '') $model = $model->where('title', 'LIKE', "%$where[title]%");
//        if($where['is_del'] != '')  $model = $model->where('is_del',$where['is_del']);
        $model = $model->where('is_del', 0);
        $model = $model->where('status', 1);
        $model = $model->order('sort desc,id desc');
        return self::page($model, $where);
    }


    public static function CouponList($where)
    {
        $model = new self;
        if ($where['title'] != '') $model = $model->where('title', 'LIKE', "%$where[title]%");
        $model = $model->where('is_del', 0);
        $model = $model->where('status', 1);
        $model = $model->order('sort desc,id desc');
        $model = $model->page((int)$where['page'], (int)$where['limit']);
        $data = ($data = $model->select()) && count($data) ? $data->toArray() : [];
        $count = count($data);
        return compact('count', 'data');
    }



    public static function editIsDel($id)
    {
        $data['status'] = 0;
        self::beginTrans();
        $res1 = self::edit($data, $id);
        $res2 = false !== StoreCouponUser::where('cid', $id)->update(['is_fail' => 1]);
        $res3 = false !== StoreCouponIssue::where('cid', $id)->update(['status' => -1,'receive_stauts'=> -1]);
        $res = $res1 && $res2 && $res3;
        self::checkTrans($res);
        return $res;

    }

    /**
     * 品类券
     * @param $tab_id
     * @return array
     * @throws \FormBuilder\exception\FormBuilderException
     */
    public static function createClassRule($tab_id)
    {
        $formbuider = [];
        $formbuider[] = Form::select('category_id', '选择品类')->setOptions(function () {
            $list = CategoryModel::getTierList(null, 1);
            $menus = [];
            foreach ($list as $menu) {
                $menus[] = ['value' => $menu['id'], 'label' => $menu['html'] . $menu['cate_name']];
            }
            return $menus;
        })->filterable(1)->col(12);
        return $formbuider;
    }

    /**
     * 商品券
     * @param $tab_id
     * @return array
     * @throws \FormBuilder\exception\FormBuilderException
     */
    public static function createProductRule($tab_id)
    {
        $formbuider = [];
        $formbuider[] = Form::frameImageOne('image', '选择商品', Url::buildUrl('6GqvmHa8HRGHoQEQ/ump.StoreCoupon/select', array('fodder' => 'image')))->icon('plus')->width('100%')->height('500px');
        $formbuider[] = Form::hidden('product_id', 0);
        return $formbuider;
    }


    /**
     * 课程券
     * @param $tab_id
     * @return array
     * @throws \FormBuilder\exception\FormBuilderException
     */
    public static function createSpecialRule($tab_id)
    {
        $formbuider = [];
        $formbuider[] = Form::select('product_type','选择分类','0')->options([['label'=>'通用课程','value'=>'0'],['label'=>'专用课程','value'=>'1']]);
        $formbuider[] = Form::frameImageOne('image', '选择专用课程', Url::buildUrl('6GqvmHa8HRGHoQEQ/ump.StoreCoupon/select_special', array('fodder' => 'image')))->icon('plus')->width('100%')->height('500px');
        $formbuider[] = Form::hidden('product_id', 0);
        return $formbuider;
    }
}