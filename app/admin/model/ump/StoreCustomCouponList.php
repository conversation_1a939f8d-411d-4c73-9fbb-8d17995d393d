<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2021-08-12 15:36:48
 * @Last Modified time: 2021-08-16 13:30:57
 */
namespace app\admin\model\ump;

use crmeb\traits\ModelTrait;
use think\Model;

/**
 * @mixin think\Model
 */
class StoreCustomCouponList extends Model
{
    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'store_custom_coupon_issue';

    use ModelTrait;

    /**
     * @param $where
     * @return array
     */
    public static function systemPage($where)
    {
        $model = new self;
        $model = $model->alias('A');
        $model = $model->join('store_activity_promo_code code','A.cid = code.id');
        $model = $model->field('A.id,A.code,A.price,A.add_time,A.use_time,A.fail_time,A.end_time,A.is_fail,A.is_del,A.status,code.title as code_group');
        $model = $model->where('A.is_del',0);
        $model = $model->order('A.id DESC');
        if ($where['name'] != '') $model = $model->where('A.code', 'LIKE', "%$where[name]%");
        return self::page($model, $where);
    }
}
