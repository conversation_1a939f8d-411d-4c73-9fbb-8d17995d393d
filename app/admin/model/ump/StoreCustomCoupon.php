<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2021-08-12 15:09:55
 * @Last Modified time: 2021-08-16 13:44:37
 */

namespace app\admin\model\ump;

use crmeb\basic\BaseModel;
use crmeb\traits\ModelTrait;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\IOFactory;


/**
 * @mixin think\Model
 */
class StoreCustomCoupon extends BaseModel
{
    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'store_custom_coupon';

    use ModelTrait;

    public static function getCouponList($where)
    {
        $model = self::getCouponWhere($where, self::alias('a'))
            ->field('a.*');
        $model = $model->order('a.id desc');
        $data = ($data = $model->page((int)$where['page'], (int)$where['limit'])->select()) && count($data) ? $data->toArray() : [];
        foreach ($data as &$item) {
            if ($item['remain_count'] > 0) {
                $status_name = '已开启';
            }else{
                 $status_name = '已用完';
            }
            $item['status_name'] = $status_name;
            $item['valid_time'] = $item['valid_time'] ? date('Y-m-d H:i:s', $item['valid_time']) : '';
            $item['add_time'] = $item['add_time'] ? date('Y-m-d H:i:s', $item['add_time']) : '';
        }
        $count = self::statusByWhere($where, self::alias('a')->count());
        return compact('count', 'data');
    }

    public static function exportCode($id)
    {
        $model = new self;
        $data = ($data = $model->field('id,title')->where('id',$id)->where('is_del',0)->where('status',1)->where('valid_time','>',time())->select()) && count($data) ? $data->toArray() : [];
        //根据卡券组，获取其下所有有效未使用的code
        $coupon_list = [];
        $title = '';
        foreach ($data as $key => $code) {
            $title = $code['title'];
            $coupon_list = ($code_data =  StoreCustomCouponList::field('code,pwd')->where('cid',$code['id'])->where('status',0)->where('is_del',0)->where('is_fail',0)->select()) && count($code_data) ? $code_data->toArray() : [];
        }
        self::SaveExcel($title,$coupon_list);
    }


    public static function statusByWhere($status, $model = null, $alert = '')
    {
        if ($model == null) $model = new self;
        if ('' === $status)
            return $model;
        else if ($status == 1)//已审核
            return $model->where($alert . 'status', 1)->where($alert . 'is_del', 0);
        else if ($status == 2)//待审核
            return $model->where($alert . 'status', 2)->where($alert . 'is_del', 0);
        else if ($status == 3)//已删除
            return $model->where($alert . 'is_del', 1);
        else
            return $model;
    }


  /**
     * 处理where条件
     * @param $where
     * @param $model
     * @return mixed
     */
    public static function getCouponWhere($where, $model, $aler = '', $join = '')
    {
        if (isset($where['status']) && $where['status'] != '') {
            $model = self::statusByWhere($where['status'], $model, $aler);
        }
        if (isset($where['keywords']) && $where['keywords'] != '') {
            $model = $model->where($aler . 'title|number|price', 'LIKE', "%$where[keywords]%");
        }
        return $model;
    }

    public static function editIsDel($id,$type)
    {   
        $data['status'] = 0;
        if ($type == 1) {
            $data['is_del']  = 1;
            $attributes = [];
            $attributes['is_del'] = 1;
        }else{
            $attributes = [];
            $attributes['is_fail'] = [];
            $attributes['fail_time'] = time();
        }
        self::beginTrans();
        $res1 = self::edit($data, $id);
        $res2 = false !== StoreCustomCouponList::where('cid', $id)->update($attributes);
        $res = $res1 && $res2;
        self::checkTrans($res);
        return $res;

    }

    /*
     * 保存并下载excel
     * $list array
     * return
     */
    public static function SaveExcel($title,$list)
    {
        $export = [];
        foreach ($list as $index => $item) {
            $export[] = [
                $item['code'],
                $item['pwd'],
            ];
        }  
        # 声明Execl对象
        $objExcel = new Spreadsheet();
        $fileName = $title.'-'.date('Ymd-Hi') . '.xlsx';
        $objWriter = IOFactory::createWriter($objExcel, 'Xlsx');
        $objActSheet = $objExcel->getActiveSheet(0);
        $objActSheet->setTitle('课程激活码');//设置excel的标题
        $keys= ['卡号','密码'];
        array_unshift($export,$keys);
        $objActSheet->fromArray($export);
        ob_end_clean();
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . $fileName . '"');
        header('Cache-Control: max-age=0');
        $objWriter->save('php://output');
        exit;
    }

}
