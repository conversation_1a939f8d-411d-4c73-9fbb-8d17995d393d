<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2020-08-05 14:53:37
 * @Last Modified time: 2020-08-05 15:23:28
 */
namespace app\admin\model\sms;

use app\admin\model\system\SystemConfig;
use crmeb\basic\BaseModel;
use crmeb\services\sms\Sms;

/**
 * @mixin think\Model
 */
class SmsTemplate extends BaseModel
{
	 /**
     * 模型名称
     * @var string
     */
    protected $name = 'sms_templates';
	/**
     * 短信状态
     * @var array
     */
    protected static $resultcode = ['100' => '成功', '130' => '失败', '131' => '空号', '132' => '停机', '133' => '关机', '134' => '无状态'];

    protected function getAddTimeAttr($value)
    {
        return $value ? date('Y-m-d H:i:s', $value) : '';
    }

    public static function vaildWhere($where)
    {
        $model = new static();
        if(isset($where['status'])&&$where['status']!='') $model = $model->where('status', $where['status']);
        if(isset($where['title'])&&$where['title']!='') $model = $model->where('title|content|sign_name|templateid','like',"%$where[title]%");
        return $model;
    }

    /**
     * 获取短信模版列表
     * @param $where
     * @return array
     */
    public static function getTemplateList($where)
    {
        $data = self::vaildWhere($where)->page((int)$where['page'], (int)$where['limit'])->select();
        $count = self::vaildWhere($where)->count();
        return compact('count', 'data');
    }
}