<?php

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date:   2022-12-19 10:23:54
 * @Last Modified time: 2022-12-19 14:28:39
 */
namespace app\admin\model\space;

use crmeb\basic\BaseModel;
use crmeb\traits\ModelTrait;

/**
 * 乐队空间：发帖标签 model
 * Class StoreBandCity
 * @package app\admin\model\market
 */
class StoreBandPostsTags extends BaseModel
{

    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'store_band_space_posts_tags';

    use ModelTrait;

    public static function createSystemTags($bandSpaceId = 0,$tags_arr)
    {
    	if(!$tags_arr) return true;
    	$system_tags = [];
        // 实例化对象
        foreach ($tags_arr as $key => $tag) {
        	$system_tags[$key]['name'] = $tag;
        	$system_tags[$key]['band_space_id'] = $bandSpaceId;
        	$system_tags[$key]['is_system'] = 1;
        	$system_tags[$key]['add_time'] = time();
        }
        $object = self::insertAll($system_tags);
        if(!$object) return false;
        return true;
    }

}