<?php

namespace app\admin\model\space;

use crmeb\traits\ModelTrait;
use crmeb\basic\BaseModel;

/**
 * Class StoreBandMaterialCategory
 * @package app\admin\model\store
 */
class StoreBandMaterialCategory extends BaseModel
{

    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'store_band_space_material_category';

    use ModelTrait;

    /**
     * 异步获取分类列表
     * @param $where
     * @return array
     */
    public static function CategoryList($where)
    {
        $data = ($data = self::systemPage($where, true)->page((int)$where['page'], (int)$where['limit'])->select()) && count($data) ? $data->toArray() : [];
        foreach ($data as &$item) {
            $item['num'] = StoreBandMaterial::where('cate_id',$item['id'])->where('is_del',0)->where('status',1)->count();
        }
        $count = self::systemPage($where, true)->count();
        return compact('count', 'data');
    }

    /**
     * @param $where
     * @return array
     */
    public static function systemPage($where, $isAjax = false)
    {
        $model = new self;
        $model = $model->where('is_del', 0);
        if ($where['is_show'] != '') $model = $model->where('is_show', $where['is_show']);
        if ($where['cate_name'] != '') $model = $model->where('cate_name', 'LIKE', "%$where[cate_name]%");
        if ($isAjax === true) {
            if (isset($where['order']) && $where['order'] != '') {
                $model = $model->order(self::setOrder($where['order']));
            } else {
                $model = $model->order('sort desc,id desc');
            }
            return $model;
        }
        return self::page($model, function ($item) {
        }, $where);
    }

    /**
     * 分级排序列表
     * @param null $model
     * @param int $type
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public static function getTierList($model = null, $type = 0)
    {
        if ($model === null) $model = new self();
        if ($type == 1) return sort_material_list_tier($model->where('is_show',1)->where('is_del',0)->order('sort desc,id desc')->select()->toArray());
        return sort_material_list_tier($model->order('sort desc,id desc')->select()->toArray());
    }

    public static function delCategory($id)
    {
        $count = StoreBandMaterial::where('cate_id', $id)->where('status',1)->count();
        if ($count)
            return self::setErrorInfo('请先删除分类下的空间资料');
        else {
            return self::where('id', $id)->update(['is_del' => 1,'is_show' => 0]);
        }
    }

    /**
     * 产品分类隐藏显示
     * @param $id
     * @param $show
     * @return bool
     */
    public static function setCategoryShow($id, $show)
    {
        $count = self::where('id', $id)->count();
        if (!$count) return self::setErrorInfo('参数错误');
        $count = self::where('id', $id)->where('is_show', $show)->count();
        if ($count) return true;
        self::beginTrans();
        $res1 = true;
        $res2 = self::where('id', $id)->update(['is_show' => $show]);
        $res = $res1 && $res2;
        self::checkTrans($res);
        return $res;
    }
}