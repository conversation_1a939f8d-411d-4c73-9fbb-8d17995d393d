<?php

/**
 * @Author: <PERSON><PERSON> x<PERSON><PERSON><PERSON>
 * @Date:   2022-12-08 14:09:59
 * @Last Modified time: 2022-12-22 14:35:17
 */

namespace app\admin\model\space;

use crmeb\basic\BaseModel;
use app\models\market\StoreCart;
use crmeb\services\PHPExcelService;
use crmeb\traits\ModelTrait;
use app\admin\model\order\StoreOrder;
use app\admin\model\special\Special;
use app\admin\model\special\SpecialTask;
use app\admin\model\ump\{
    StoreBargain, StoreCombination, StoreSeckill
};

/**
 * 产品管理 model
 * Class StoreActivity
 * @package app\admin\model\market
 */
class StoreBandMaterial extends BaseModel
{

    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'store_band_space_material';

    use ModelTrait;

    /**
     * 异步获取分类列表
     * @param $where
     * @return array
     */
    public static function BandMaterialList($where)
    {
        $data = ($data = self::systemPage($where, true)->page((int)$where['page'], (int)$where['limit'])->select()) && count($data) ? $data->toArray() : [];
        foreach ($data as &$item) {
            $item['cate_name'] = StoreBandMaterialCategory::where('id',$item['cate_id'])->value('cate_name');
            if (in_array($item['file_type'], [1,2,3])){
               if ($item['type'] == 1) {
                     $item['image'] = Special::where('id',$item['special_id'])->value('image');
               }elseif ($item['type'] == 2) {
                    $item['image'] = SpecialTask::where('id',$item['source_id'])->value('image');
               }elseif ($item['type'] ==3) {
                   if ($item['file_type'] == 1) {
                        $item['image'] =$item['file'];
                   }else{
                         $item['image'] = "";
                   }
               }
            }
        }
        $count = self::systemPage($where, true)->count();
        return compact('count', 'data');
    }

    /**
     * @param $where
     * @return array
     */
    public static function systemPage($where, $isAjax = false)
    {
        $model = new self;
        $model = $model->where('is_del', 0);
        if ($where['cate_id'] != '') $model = $model->where('cate_id', $where['cate_id']);
        if ($where['band_space_id'] != '') $model = $model->where('band_space_id', $where['band_space_id']);
        if ($where['file_name'] != '') $model = $model->where('file_name', 'LIKE', "%$where[file_name]%");
        if ($isAjax === true) {
            if (isset($where['order']) && $where['order'] != '') {
                $model = $model->order(self::setOrder($where['order']));
            } else {
                $model = $model->order('sort desc,id desc');
            }
            return $model;
        }
        return self::page($model, function ($item) {
        }, $where);
    }
    
    // todo
    // ['special_id'=>$course['id'],'cate_id'=>$cateId,'band_space_id'=>$band_space];
    public static function saveMaterial($type=1,$valueList)
    {
        BaseModel::beginTrans();
        $res1 = true;
        $res2 = true;
        $cate_id = $valueList['cate_id'];
        $band_space_id = $valueList['band_space_id'];
        if ($type == 1) { //课程
            $data = [];
            $courseList = $valueList['check_special_sure'];
            // 循环处理
            foreach ($courseList as $key => $course) {
                foreach ($cate_id as $key => $cateId) {
                    foreach ($band_space_id as $key => $bandSpaceId) {
                        $origin_record =  self::where('type',1)->where('cate_id',$cateId)->where('band_space_id',$bandSpaceId)->where('special_id',$course['id'])->where('is_del',0)->where('status',1)->find();
                        if ($origin_record) {
                            $data['file_name'] = $course['title'];
                            $data['sort'] = $course['sort'];
                            $res1 = $origin_record->update($data);
                        }else{
                            $data['type'] = 1;
                            $data['cate_id'] = $cateId;
                            $data['band_space_id'] = $bandSpaceId;
                            $data['special_id'] = $course['id'];
                            $data['file_type'] = $course['file_type'];
                            $data['file_name'] = $course['title'];
                            $data['sort'] = $course['sort'];
                            $data['status'] =1;
                            $data['add_time'] =time();
                            $res2 = self::create($data);
                        }
                    }
                }
            }
        }elseif ($type == 2) { //素材
            $data = [];
            $materialList = $valueList['check_source_sure'];
            // 循环处理
            foreach ($materialList as $key => $material) {
                foreach ($cate_id as $key => $cateId) {
                    foreach ($band_space_id as $key => $bandSpaceId) {
                        $origin_record =  self::where('type',2)->where('cate_id',$cateId)->where('band_space_id',$bandSpaceId)->where('special_id',$material['id'])->where('is_del',0)->where('status',1)->find();
                        if ($origin_record) {
                            $data['file_name'] = $material['title'];
                            $data['sort'] = $material['sort'];
                            $res1 = $origin_record->update($data);
                        }else{
                            $data['type'] = 1;
                            $data['cate_id'] = $cateId;
                            $data['band_space_id'] = $bandSpaceId;
                            $data['source_id'] = $material['id'];
                            $data['file_type'] = $material['type'];
                            $data['file_name'] = $material['title'];
                            $data['sort'] = $material['sort'];
                            $data['status'] =1;
                            $data['add_time'] =time();
                            $res2 = self::create($data);
                        }
                    }
                }
            }
        }elseif ($type == 3) {
            // 循环处理
            foreach ($cate_id as $key => $cateId) {
                foreach ($band_space_id as $key => $bandSpaceId) {
                    // 判断素材类型
                    $origin_record =  self::where('type',2)->where('file_type',$valueList['source_type'])->where('cate_id',$cateId)->where('band_space_id',$bandSpaceId)->where('file',$valueList['file'])->where('is_del',0)->where('status',1)->find();
                    if ($origin_record) {
                        $data['file_name'] = $valueList['file_name'];
                        $data['sort'] = $valueList['sort'];
                        $res1 = $origin_record->update($data);
                    }else{
                        $data['type'] = 2;
                        $data['cate_id'] = $cateId;
                        $data['band_space_id'] = $bandSpaceId;
                        $data['file_type'] = $valueList['source_type'];
                        $data['file_name'] = $valueList['file_name'];
                        $data['file'] = $valueList['file'];
                        $data['sort'] = $valueList['sort'];
                        $data['status'] =1;
                        $data['add_time'] =time();
                        $res2 = self::create($data);
                    }
                }
            }
        }
        $res = $res1 && $res2;
        BaseModel::checkTrans($res);
        return $res;
    }

    public static function delBandMaterial($id)
    {
        return self::where('id', $id)->update(['is_del' => 1,'status' => 0]);
    }

    /**
     * 状态
     * @param $id
     * @param $status
     * @return bool
     */
    public static function setBandMaterialStatus($id, $status)
    {
        $count = self::where('id', $id)->count();
        if (!$count) return self::setErrorInfo('参数错误');
        $count = self::where('id', $id)->where('status', $status)->count();
        if ($count) return true;
        self::beginTrans();
        $res1 = true;
        $res2 = self::where('id', $id)->update(['status' => $status]);
        $res = $res1 && $res2;
        self::checkTrans($res);
        return $res;
    }
}