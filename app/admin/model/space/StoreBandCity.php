<?php

/**
 * @Author: <PERSON><PERSON> x<PERSON><PERSON><PERSON>
 * @Date:   2022-12-09 10:50:00
 * @Last Modified time: 2022-12-21 10:46:05
 */
namespace app\admin\model\space;

use crmeb\basic\BaseModel;
use crmeb\traits\ModelTrait;

/**
 * 产品管理 model
 * Class StoreBandCity
 * @package app\admin\model\market
 */
class StoreBandCity extends BaseModel
{

    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'store_band_space_city';

    use ModelTrait;

    /**
     * 异步获取分类列表
     * @param $where
     * @return array
     */
    public static function CityList($where)
    {
        $data = ($data = self::systemPage($where, true)->page((int)$where['page'], (int)$where['limit'])->select()) && count($data) ? $data->toArray() : [];
        foreach ($data as &$item) {
            $item['num'] = StoreBandMaterial::where('cate_id',$item['id'])->where('is_del',0)->where('status',1)->count();
        }
        $count = self::systemPage($where, true)->count();
        return compact('count', 'data');
    }

    /**
     * @param $where
     * @return array
     */
    public static function systemPage($where, $isAjax = false)
    {
        $model = new self;
        $model = $model->where('is_del', 0);
        if ($where['is_show'] != '') $model = $model->where('is_show', $where['is_show']);
        if ($where['name'] != '') $model = $model->where('name', 'LIKE', "%$where[name]%");
        if ($isAjax === true) {
            if (isset($where['order']) && $where['order'] != '') {
                $model = $model->order(self::setOrder($where['order']));
            } else {
                $model = $model->order('sort desc,id desc');
            }
            return $model;
        }
        return self::page($model, function ($item) {
        }, $where);
    }

    public static function delCity($id)
    {
        $count = StoreBand::where('city_id', $id)->where('status',1)->count();
        if ($count)
            return self::setErrorInfo('请先删除此城市下的乐队');
        else {
            return self::where('id', $id)->update(['is_del' => 1,'is_show' => 0]);
        }
    }

    /**
     * 乐队活动举办城市隐藏显示
     * @param $id
     * @param $show
     * @return bool
     */
    public static function setCityShow($id, $show)
    {
        $count = self::where('id', $id)->count();
        if (!$count) return self::setErrorInfo('参数错误');
        $count = self::where('id', $id)->where('is_show', $show)->count();
        if ($count) return true;
        self::beginTrans();
        $res1 = true;
        $res2 = self::where('id', $id)->update(['is_show' => $show]);
        $res = $res1 && $res2;
        self::checkTrans($res);
        return $res;
    }
}