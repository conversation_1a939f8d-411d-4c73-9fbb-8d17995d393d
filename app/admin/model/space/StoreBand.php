<?php

/**
 * @Author: <PERSON>
 * @Date:   2022-11-30 17:27:50
 * @Last Modified time: 2022-12-21 16:01:10
 */

namespace app\admin\model\space;

use crmeb\basic\BaseModel;
use app\models\market\StoreCart;
use crmeb\services\PHPExcelService;
use crmeb\traits\ModelTrait;
use app\admin\model\user\User;
use app\admin\model\order\StoreOrder;
use app\admin\model\market\StoreCategory as CategoryModel;
use app\admin\model\market\StoreActivity;
use app\admin\model\system\SystemTeacher;

/**
 * 产品管理 model
 * Class StoreActivity
 * @package app\admin\model\market
 */
class StoreBand extends BaseModel
{

  /**
   * 数据表主键
   * @var string
   */
  protected $pk = 'id';

  /**
   * 模型名称
   * @var string
   */
  protected $name = 'store_band_space';

  use ModelTrait;

  public function getDescriptionAttr($value)
  {
    return htmlspecialchars_decode($value);
  }

  /**
   * 获取连表MOdel
   * @param $model
   * @return object
   */
  public static function getModelObject($where = [])
  {
    $model = new self();
    if (!empty($where)) {
      $type = $where['type'] ?? 0;
      switch ((int)$type) {
        case 1:
          $model = $model->where(['is_show' => 1, 'is_del' => 0, 'status' => 1]);
          break;
        case 2:
          $model = $model->where(['is_show' => 1, 'is_del' => 0, 'status' => 2]);
          break;
        case 3:
          $model = $model->where(['is_show' => 1, 'is_cancel' => 1, 'is_del' => 0, 'status' => 3]);
          break;
        case 4:
          $model = $model->where(['is_show' => 1, 'is_del' => 0, 'status' => 4]);
          break;
        case 5:
          $model = $model->where(['is_del' => 1]);
          break;
      };
      if (isset($where['store_name']) && $where['store_name'] != '') {
        $model = $model->where('store_name|keyword|id', 'LIKE', "%$where[store_name]%");
      }
      // if (isset($where['cate_id']) && trim($where['cate_id']) != '') {
      //     $model = $model->whereIn('id', function ($query) use ($where) {
      //         $query->name('store_activity_cate')->where('cate_id', $where['cate_id'])->field('activity_id')->select();
      //     });
      // }
      if (isset($where['order']) && $where['order'] != '') {
        $model = $model->order(self::setOrder($where['order']));
      } else {
        $model = $model->order('sort desc,id desc');
      }
    }
    return $model;
  }

  /**根据cateid查询产品 拼sql语句
   * @param $cateid
   * @return string
   */
  protected static function getCateSql($cateid)
  {
    $lcateid = $cateid . ',%'; //匹配最前面的cateid
    $ccatid = '%,' . $cateid . ',%'; //匹配中间的cateid
    $ratidid = '%,' . $cateid; //匹配后面的cateid
    return " `cate_id` LIKE '$lcateid' OR `cate_id` LIKE '$ccatid' OR `cate_id` LIKE '$ratidid' OR `cate_id`=$cateid";
  }

  /** 如果有子分类查询子分类获取拼接查询sql
   * @param $cateid
   * @return string
   */
  protected static function getPidSql($cateid)
  {

    $sql = self::getCateSql($cateid);
    $ids = CategoryModel::where('pid', $cateid)->column('id', 'id');
    //查询如果有子分类获取子分类查询sql语句
    if ($ids) foreach ($ids as $v) $sql .= " OR " . self::getcatesql($v);
    return $sql;
  }

  /**
   * 获取产品列表
   * @param $where
   * @return array
   */
  public static function ActivityList($where)
  {
    $model = self::getModelObject($where);
    if ($where['excel'] == 0) $model = $model->page((int)$where['page'], (int)$where['limit']);
    $data = ($data = $model->select()) && count($data) ? $data->toArray() : [];
    foreach ($data as &$item) {
      // $item['stock'] = self::getStock($item['id']) > 0 ? self::getStock($item['id']) : $item['stock'];//库存
      // $item['stock_attr'] = self::getStock($item['id']) > 0 ? true : false;//库存
      // $item['sales_attr'] = self::getSales($item['id']);//属性销量
      // // $item['activity_time'] =  $item['activity_time'] ? date('Y-m-d', $item['activity_time']) : '';
      // $item['end_activity_time'] =  $item['end_activity_time'] ? date('Y-m-d', $item['end_activity_time']) : '';
      // $item['crowdfunding_started_time'] =  $item['crowdfunding_started_time'] ? date('Y-m-d', $item['crowdfunding_started_time']) : '';
      // $item['crowdfunding_ends_time'] =  $item['crowdfunding_ends_time'] ? date('Y-m-d', $item['crowdfunding_ends_time']) : '';
      // $item['started_time'] =  $item['started_time'] ? date('Y-m-d', $item['started_time']) : '';
      // $item['ends_time'] =  $item['ends_time'] ? date('Y-m-d', $item['ends_time']) : '';
      $item['city_name'] = StoreBandCity::where('id', $item['city_id'])->value('name');
      $item['activity_info'] = StoreActivity::where('id', $item['activity_id'])->find();
      // 指导老师
      $teacherId = StoreBandSpaceAssociatedTeaching::where('type', 1)->where('band_space_id', $item['id'])->select()->toArray();
      $assistantList = array();
      if ($teacherId) {
        foreach ($teacherId as $k => $v) {
          $task_list = SystemTeacher::field('uid')->where(['id' => $v['teacher_id'], 'is_del' => 0])->find();
          if ($task_list) {
            $userInfo = User::where('uid', $task_list['uid'])->field(['avatar', 'nickname'])->find();
            if ($userInfo) {
              $task_list['nickname'] = $userInfo['nickname'];
              $task_list['avatar'] = $userInfo['avatar'];
            }
            array_push($assistantList, $task_list);
          } else {
            array_splice($teacherId, $k, 1);
            continue;
          }
        }
      }
      $item['assistant'] = $assistantList;
      // 助理
      $instructorTeacherId = StoreBandSpaceAssociatedTeaching::where('type', 2)->where('band_space_id', $item['id'])->select()->toArray();
      $instructorCheckList = array();
      if ($instructorTeacherId) {
        foreach ($instructorTeacherId as $k => $v) {
          $task_list = SystemTeacher::field('uid')->where(['id' => $v['teacher_id'], 'is_del' => 0])->find();
          if ($task_list) {
            $userInfo = User::where('uid', $task_list['uid'])->field(['avatar', 'nickname'])->find();
            if ($userInfo) {
              $task_list['nickname'] = $userInfo['nickname'];
              $task_list['avatar'] = $userInfo['avatar'];
            }
            array_push($instructorCheckList, $task_list);
          } else {
            array_splice($instructorTeacherId, $k, 1);
            continue;
          }
        }
      }
      $item['instructor'] = $instructorCheckList;
      // 成团
      $groupList = StoreBandGroupOfStudents::where('band_space_id', $item['id'])->where('is_apply_quit', 0)->where('status', 1)->select()->toArray();
      foreach ($groupList as $key => $student) {
        $userInfo = User::where('uid', $student['uid'])->field(['avatar', 'nickname'])->find();
        if ($userInfo) {
          $groupList[$key]['nickname'] = $userInfo['nickname'];
          $groupList[$key]['avatar'] = $userInfo['avatar'];
        }
      }
      $item['group_number'] = count($groupList);
      $item['groupList'] = $groupList;
    }
    unset($item);
    if ($where['excel'] == 1) {
      $export = [];
      foreach ($data as $index => $item) {
        $export[] = [
          $item['store_name'],
          $item['store_info'],
          $item['cate_name'],
          '￥' . $item['price'],
          $item['stock'],
          $item['sales'],
          $item['like'],
          $item['collect']
        ];
      }
      PHPExcelService::instance()->setExcelHeader(['产品名称', '产品简介', '产品分类', '价格', '库存', '销量', '点赞人数', '收藏人数'])
        ->setExcelTile('产品导出', '产品信息' . time(), ' 生成时间：' . date('Y-m-d H:i:s', time()))
        ->setExcelContent($export)
        ->ExcelSave('产品导出');
    }
    $count = self::getModelObject($where)->count();
    return compact('count', 'data');
  }

  public static function setWhereType($model, $type)
  {
    switch ($type) {
      case 1:
        $data = ['is_show' => 1, 'status' => 1, 'is_del' => 0];
        break;
      case 2:
        $data = ['is_show' => 1, 'status' => 2, 'is_del' => 0];
        break;
      case 3:
        $data = ['stauts' => 3];
        break;
      case 4:
        $data = ['is_cancel' => 1];
        break;
      case 5:
        $data = ['is_del' => 1];
        break;
    }
    if (isset($data)) $model = $model->where($data);
    return $model;
  }

  public static function getTierList($model = null)
  {
    if ($model === null) $model = new self();
    return sort_material_list_tier($model->where('is_del', 0)->order('sort desc,id desc')->select()->toArray());
  }

  //获取活动
  public static function getActivityLists()
  {
    $model = new self();
    return $model->field('id,store_name')->where('is_del', 0)->select()->toArray();
  }


  /**
   * 设置查询条件
   * @param array $where
   * @return array
   */
  public static function setWhere($where)
  {
    $time['data'] = '';
    if (isset($where['start_time']) && $where['start_time'] != '' && isset($where['end_time']) && $where['end_time'] != '') {
      $time['data'] = $where['start_time'] . ' - ' . $where['end_time'];
    } else {
      $time['data'] = isset($where['data']) ? $where['data'] : '';
    }
    $model = self::getModelTime($time, StoreCart::alias('a')->join('store_activity b', 'a.activity_id=b.id'), 'a.add_time');
    if (isset($where['title']) && $where['title'] != '') {
      $model = $model->where('b.store_name|b.id', 'like', "%$where[title]%");
    }
    return $model;
  }


  /*
     * 处理二维数组排序
     * $arrays 需要处理的数组
     * $sort_key 需要处理的key名
     * $sort_order 排序方式
     * $sort_type 类型 可不填写
     */
  public static function my_sort($arrays, $sort_key, $sort_order = SORT_ASC, $sort_type = SORT_NUMERIC)
  {
    if (is_array($arrays)) {
      foreach ($arrays as $array) {
        if (is_array($array)) {
          $key_arrays[] = $array[$sort_key];
        } else {
          return false;
        }
      }
    }
    if (isset($key_arrays)) {
      array_multisort($key_arrays, $sort_order, $sort_type, $arrays);
      return $arrays;
    }
    return false;
  }

  /**
   * TODO 获取某个字段值
   * @param $id
   * @param string $field
   * @return mixed
   */
  public static function getActivityField($id, $field = 'store_name')
  {
    return self::where('id', $id)->value($field);
  }

  /**
   * TODO 获取某个字段值
   * @param $id
   * @param string $field
   * @return mixed
   */
  public static function getActivityList()
  {
    $model = self::where('is_del', 0)->field('store_name,id');
    $data = ($data = $model->select()) && count($data) ? $data->toArray() : [];
    foreach ($data as $key => $value) {
      $data[$key]['name'] = $value['store_name'];
      $data[$key]['value'] = $value['id'];
    }
    return $data;
  }
}
