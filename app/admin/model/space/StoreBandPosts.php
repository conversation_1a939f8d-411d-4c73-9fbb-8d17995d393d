<?php

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date:   2022-12-19 15:23:24
 * @Last Modified time: 2022-12-19 16:42:55
 */
namespace app\admin\model\space;

use crmeb\basic\BaseModel;
use crmeb\traits\ModelTrait;

/**
 * 乐队空间：发帖标签 model
 * Class StoreBandCity
 * @package app\admin\model\market
 */
class StoreBandPosts extends BaseModel
{

    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'store_band_space_posts';

    use ModelTrait;

    public static function postCount()
    {
        $data['reviewed'] = self::statusByWhere(1, new self())->count();
        $data['unreviewed'] = self::statusByWhere(2, new self())->count();
        $data['deleted'] = self::statusByWhere(3, new self())->count();
        return $data;
    }


    public static function statusByWhere($status, $model = null, $alert = '')
    {
        if ($model == null) $model = new self;
        if ('' === $status)
            return $model;
        else if ($status == 1)//已审核
            return $model->where($alert . 'status', 1)->where($alert . 'is_del', 0);
        else if ($status == 2)//待审核
            return $model->where($alert . 'status', 2)->where($alert . 'is_del', 0);
        else if ($status == 3)//已删除
            return $model->where($alert . 'is_del', 1);
        else
            return $model;
    }

    public static function postList($where)
    {
        $model = self::getpostWhere($where, self::alias('a')
            ->join('user r', 'r.uid=a.uid', 'LEFT'), 'a.', 'r')
            ->field('a.*,r.nickname,r.phone,r.spread_uid,r.avatar');
        $model = $model->order('a.id desc');
        $data = ($data = $model->page((int)$where['page'], (int)$where['limit'])->select()) && count($data) ? $data->toArray() : [];
        foreach ($data as &$item) {
            $status_name = '';
            if ($item['status'] == 2 && $item['is_del'] == 0) {
                $status_name = '待审核';
            } elseif ($item['status'] == 1 && $item['is_del'] == 0) {
                $status_name = '已审核';
            } elseif ($item['is_del'] == 1) {
                $status_name = '已删除';
            }
            // 寻找封面图
            $item['message_cover'] = 1;
            $item['status_name'] = $status_name;
            // $item['type_name'] = $item['message_type'] == 1 ? '作业' : '普通评论';
            $item['add_time'] = $item['add_time'] ? date('Y-m-d H:i:s', $item['add_time']) : '';
        }
        $count = self::getpostWhere($where, self::alias('a')->join('user r', 'r.uid=a.uid', 'LEFT'), 'a.', 'r')->count();
        return compact('count', 'data');
    }

    public static function getpostList($where, $page, $limit){
        $model = self::getpostWhere($where, self::alias('a')
            ->join('user r', 'r.uid=a.uid', 'LEFT'), 'a.', 'r')
            ->field('a.*,r.nickname,r.phone,r.spread_uid,r.avatar');
        $model = $model->order('a.id desc');
        $data = ($data = $model->page((int)$page, (int)$limit)->select()) && count($data) ? $data->toArray() : [];
        foreach ($data as &$item) {
            if ($item['status'] == 2 && $item['is_del'] == 0) {
                $status_name = '待审核';
            } elseif ($item['status'] == 1 && $item['is_del'] == 0) {
                $status_name = '已审核';
            } elseif ($item['is_del'] == 1) {
                $status_name = '已删除';
            }
            $item['status_name'] = $status_name;
            $item['add_time'] = $item['add_time'] ? date('Y-m-d H:i:s', $item['add_time']) : '';
        }
        return $data;
    }

    /**
     * 处理where条件
     * @param $where
     * @param $model
     * @return mixed
     */
    public static function getpostWhere($where, $model, $aler = '', $join = '')
    {
        if (isset($where['status']) && $where['status'] != '') {
            $model = self::statusByWhere($where['status'], $model, $aler);
        }
        if (isset($where['status']) && $where['status'] == '') {
            $model = $model->where('is_del', 0);
        }
        if (isset($where['is_del']) && $where['is_del'] != '' && $where['is_del'] != -1) $model = $model->where($aler . 'is_del', $where['is_del']);
        if (isset($where['keywords']) && $where['keywords'] != '') {
            $model = $model->where($aler . 'title|position|comment' . ($join ? '|' . $join . '.nickname|' . $join . '.uid|' . $join . '.phone' : ''), 'LIKE', "%$where[keywords]%");
        }
        if (isset($where['data']) && $where['data'] !== '') {
            $model = self::getModelTime($where, $model, $aler . 'add_time');
        }
        return $model;
    }
}