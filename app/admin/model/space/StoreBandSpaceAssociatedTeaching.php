<?php

/**
 * @Author: <PERSON><PERSON> x<PERSON><PERSON><PERSON>
 * @Date:   2022-12-19 10:31:01
 * @Last Modified time: 2022-12-19 15:16:02
 */
namespace app\admin\model\space;

use crmeb\basic\BaseModel;
use crmeb\traits\ModelTrait;

/**
 * 乐队空间：关联教师与助理 model
 * Class StoreBandCity
 * @package app\admin\model\market
 */
class StoreBandSpaceAssociatedTeaching extends BaseModel
{

    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'store_band_space_associated_teaching';

    use ModelTrait;

  /**
   * TODO 批量添加关联的教师信息
   * @param int $type  状态 1=添加 2=更新
   * @param int $spaceId  $spaceId 空间id
   * @param int $instructorIds  $instructorIds 指导老师id组
   * @param int $assistantIds  $assistantIds 助理id组
   * @return bool|object
   */
  public static function saveTeaching($type, $spaceId, $instructorIds = [], $assistantIds = [])
  {
    self::beginTrans();
    try {
      $instructor_res = true;
      $assistant_res = true;
      // 指导老师
      $instructo_data = [];
      foreach ($instructorIds as $key => $teacher) {
        $instructo_data[$key]['type'] = 1;
        $instructo_data[$key]['band_space_id'] =  (int) $spaceId;
        $instructo_data[$key]['teacher_id'] = (int) $teacher['id'];
        $instructo_data[$key]['sort'] = $key + 1;
        $instructo_data[$key]['status'] = 1;
        $instructo_data[$key]['add_time'] = time();
      }
      //助理
      $assistant_data = [];
      foreach ($assistantIds as $key => $assistant) {
        $assistant_data[$key]['type'] = 2;
        $assistant_data[$key]['band_space_id'] =  (int) $spaceId;
        $assistant_data[$key]['teacher_id'] = (int) $assistant['id'];
        $assistant_data[$key]['sort'] = $key + 1;
        $assistant_data[$key]['status'] = 1;
        $assistant_data[$key]['add_time'] = time();
      }
      if ($type == 2) { //编辑状态 清空之前的记录
         self::where('band_space_id', $spaceId)->delete();
      }
      $instructor_res  = false !== self::insertAll($instructo_data);
      $assistant_res = false !==  self::insertAll($assistant_data);
      if (!$instructor_res || !$assistant_res) return self::setErrorInfo('生成失败!', true);
      self::commitTrans();
      return true;
    } catch (\Exception $e) {
      self::rollbackTrans();
      return self::setErrorInfo('生成关联指导老师与处理关系：' . $e->getMessage());
    }
  }
}