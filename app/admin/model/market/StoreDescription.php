<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2021-07-19 10:31:32
 * @Last Modified time: 2022-07-14 09:49:04
 */

namespace app\admin\model\market;

use crmeb\basic\BaseModel;
use crmeb\traits\ModelTrait;

class StoreDescription extends BaseModel
{

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'store_activity_description';

    use ModelTrait;

    /**
     * 获取详情
     * @param $activity_id
     * @param int $type
     * @return mixed
     */
    public static function getDescription($activity_id, $type = 0)
    {
        return self::where('activity_id', $activity_id)->where('type', $type)->value('description');
    }

    /**
     * 添加或者修改详情
     * @param string $description
     * @param int $activity_id
     * @param int $type
     * @return bool|\think\Model|static
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function saveDescription(string $description = '', int $activity_id = 0, int $type = 0)
    {
        $description = htmlspecialchars($description);
        if ($activity_id) {
            $info = self::where(['activity_id' => $activity_id, 'type' => $type])->find();
            if ($info) {
                $info->description = $description;
                return $info->save();
            }
        }
        return self::create(compact('description', 'activity_id', 'type'));
    }
}