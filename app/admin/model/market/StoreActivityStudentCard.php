<?php

/**
 * @Author: <PERSON>
 * @Date:   2022-09-20 10:47:55
 * @Last Modified time: 2022-09-20 15:10:49
 */

namespace app\admin\model\market;

use crmeb\basic\BaseModel;
use crmeb\traits\ModelTrait;

/**
 * 活动规则
 * @mixin think\Model
 */
class StoreActivityStudentCard extends BaseModel
{
  /**
   * 模型名称
   * @var string
   */
  protected $name = 'user_student_card';

  use ModelTrait;
  // $list = StoreActivityStudentCard::select();
  // $export = [];
  // foreach ($list as $index => $item) {
  //     $sex = $item['sex'];
  //     if ($sex == 1) $sex_name = '男';
  //     else if ($sex == 2) $sex_name = '女';
  //     else $sex_name = '未知';
  //     $WechatUserInfo = User::where('uid', $item['uid'])->value('nickname') ?: '暂无信息';
  //     $platform_users = $item['uid'].'/'.$WechatUserInfo.'/'.$item['phone'];
  //     $add_time = date('Y-m-d H:i:s',$item['add_time']);
  //     $learning_experience = json_decode($item['learning_experience'], true);
  //     $str2 = [];
  //     foreach ($learning_experience as $key => $value) {
  //         $str2[] = '学习经历:'  . $value['content'];
  //     }
  //     $learning_experience = implode('<br>',$str2);
  //     $skills = json_decode($item['skills'], true);
  //     $str = [];
  //     foreach ($skills as $key => $value) {
  //         $str[] = '分类名:' . $value['cate_name'] . '乐器名：'. $value['content'];
  //     }
  //     $skills = implode('<br>',$str);
  //     $export[] = [
  //         $platform_users,
  //         $item['real_name'],
  //         $item['age'],
  //         $sex_name,
  //         $item['career'],
  //         $learning_experience,
  //         $skills,
  //         $item['video_works'],
  //         $item['video_links'],
  //         $add_time
  //     ];
  // }  
  // PHPExcelService::instance()->setExcelHeader(['联系人信息', '学员姓名','年龄','性别', '职业','音乐基础','擅长乐器','作品链接', '外链作品链接', '报名时间'])
  //     ->setExcelTile('报名学生资料卡导出', '学生卡信息', ' 生成时间：' . date('Y-m-d H:i:s', time()))
  //     ->setExcelContent($export)
  //     ->ExcelSave();
}
