<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2021-06-28 15:19:36
 * @Last Modified time: 2021-07-19 13:58:08
 */
namespace app\admin\model\market;


use crmeb\basic\BaseModel;
use crmeb\traits\ModelTrait;

class StoreOrderCartInfo extends BaseModel
{

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'store_activity_order_cart_info';

    use ModelTrait;

    /** 获取订单产品列表
     * @param $oid
     * @return array
     */
    public static function getProductNameList($oid)
    {
        $cartInfo = self::where('oid',$oid)->select();
        $goodsName = [];
        foreach ($cartInfo as $cart){
            if(isset($cart['cart_info']['productInfo'])){
                $suk = isset($cart['cart_info']['productInfo']['attrInfo']) ? '('.$cart['cart_info']['productInfo']['attrInfo']['suk'].')' : '';
                $goodsN<PERSON>[] = $cart['cart_info']['productInfo']['store_name'].$suk;
            }else{
                $goodsName[] = '';
            }
        }
        return $goodsName;
    }

}