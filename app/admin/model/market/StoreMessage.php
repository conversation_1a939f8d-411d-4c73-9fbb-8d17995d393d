<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2021-08-01 16:10:45
 * @Last Modified time: 2022-09-07 15:11:47
 */

namespace app\admin\model\market;

use crmeb\basic\BaseModel;
use crmeb\traits\ModelTrait;
use app\admin\model\user\{User};
use  app\admin\model\market\StoreActivity;
use app\admin\model\market\StoreCategory as CategoryModel;
use crmeb\services\{PHPExcelService};

/**
 * 心愿单管理 model
 * Class StoreMessage
 * @package app\admin\model\market
 */
class StoreMessage extends BaseModel
{

  /**
   * 数据表主键
   * @var string
   */
  protected $pk = 'id';

  /**
   * 模型名称
   * @var string
   */
  protected $name = 'store_activity_message';

  use ModelTrait;

  public static function MessageCount()
  {
    $data['reviewed'] = self::statusByWhere(1, new self())->count();
    $data['unreviewed'] = self::statusByWhere(2, new self())->count();
    $data['deleted'] = self::statusByWhere(3, new self())->count();
    return $data;
  }


  public static function statusByWhere($status, $model = null, $alert = '')
  {
    if ($model == null) $model = new self;
    if ('' === $status)
      return $model;
    else if ($status == 1) //已审核
      return $model->where($alert . 'status', 1)->where($alert . 'is_del', 0);
    else if ($status == 2) //待审核
      return $model->where($alert . 'status', 2)->where($alert . 'is_del', 0);
    else if ($status == 3) //已删除
      return $model->where($alert . 'is_del', 1);
    else
      return $model;
  }

  public static function MessageList($where)
  {
    $model = self::getMessageWhere($where, self::alias('a')
      ->join('user r', 'r.uid=a.uid', 'LEFT'), 'a.', 'r')
      ->field('a.*,r.nickname,r.phone,r.spread_uid,r.avatar');
    $model = $model->order('a.id desc');
    $data = ($data = $model->page((int)$where['page'], (int)$where['limit'])->select()) && count($data) ? $data->toArray() : [];
    foreach ($data as &$item) {
      if ($item['status'] == 2 && $item['is_del'] == 0) {
        $status_name = '待审核';
      } elseif ($item['status'] == 1 && $item['is_del'] == 0) {
        $status_name = '已审核';
      } elseif ($item['is_del'] == 1) {
        $status_name = '已删除';
      }
      $item['status_name'] = $status_name;
      $item['add_time'] = $item['add_time'] ? date('Y-m-d H:i:s', $item['add_time']) : '';
    }
    if (isset($where['excel']) && $where['excel'] == 1) {
      self::SaveExcel($data);
    }
    $count = self::getMessageWhere($where, self::alias('a')->join('user r', 'r.uid=a.uid', 'LEFT'), 'a.', 'r')->count();
    return compact('count', 'data');
  }

  public static function getMessageList($where, $page, $limit)
  {
    $model = self::getMessageWhere($where, self::alias('a')
      ->join('user r', 'r.uid=a.uid', 'LEFT'), 'a.', 'r')
      ->field('a.*,r.nickname,r.phone,r.spread_uid,r.avatar');
    $model = $model->order('a.id desc');
    $data = ($data = $model->page((int)$page, (int)$limit)->select()) && count($data) ? $data->toArray() : [];
    foreach ($data as &$item) {
      if ($item['status'] == 2 && $item['is_del'] == 0) {
        $status_name = '待审核';
      } elseif ($item['status'] == 1 && $item['is_del'] == 0) {
        $status_name = '已审核';
      } elseif ($item['is_del'] == 1) {
        $status_name = '已删除';
      }
      $item['status_name'] = $status_name;
      $item['add_time'] = $item['add_time'] ? date('Y-m-d H:i:s', $item['add_time']) : '';
    }
    return $data;
  }

  /**
   * 处理where条件
   * @param $where
   * @param $model
   * @return mixed
   */
  public static function getMessageWhere($where, $model, $aler = '', $join = '')
  {
    if (isset($where['status']) && $where['status'] != '') {
      $model = self::statusByWhere($where['status'], $model, $aler);
    }
    if (isset($where['status']) && $where['status'] == '') {
      $model = $model->where('is_del', 0);
    }
    if (isset($where['is_del']) && $where['is_del'] != '' && $where['is_del'] != -1) $model = $model->where($aler . 'is_del', $where['is_del']);
    if (isset($where['keywords']) && $where['keywords'] != '') {
      $model = $model->where($aler . 'title|position|comment' . ($join ? '|' . $join . '.nickname|' . $join . '.uid|' . $join . '.phone' : ''), 'LIKE', "%$where[keywords]%");
    }
    if (isset($where['data']) && $where['data'] !== '') {
      $model = self::getModelTime($where, $model, $aler . 'add_time');
    }
    return $model;
  }

  /*
     * 保存并下载excel
     * $list array
     * return
     */
  public static function SaveExcel($list)
  {
    $export = [];
    foreach ($list as $index => $item) {
      $WechatUserInfo = User::where('uid', $item['uid'])->value('nickname') ?: '暂无信息';
      $platform_users = $item['uid'] . '/' . $WechatUserInfo . '/' . $item['phone'];
      $export[] = [
        $platform_users,
        $item['title'],
        $item['comment'],
        $item['add_time'],
      ];
    }
    PHPExcelService::instance()->setExcelHeader(['用户信息', '标题', '评论内容', '评论时间'])
      ->setExcelTile('活动留言评论名单', '名单信息', ' 生成时间：' . date('Y-m-d H:i:s', time()))
      ->setExcelContent($export)
      ->ExcelSave('活动留言评论名单');
  }
}
