<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2021-08-12 15:09:55
 * @Last Modified time: 2021-08-16 13:44:37
 */

namespace app\admin\model\market;

use crmeb\traits\ModelTrait;
use crmeb\basic\BaseModel;
use crmeb\services\{PHPExcelService};

/**
 * @mixin think\Model
 */
class StorePromoCode extends BaseModel
{
  /**
   * 数据表主键
   * @var string
   */
  protected $pk = 'id';

  /**
   * 模型名称
   * @var string
   */
  protected $name = 'store_activity_promo_code';

  use ModelTrait;

  public static function CodeList($where)
  {
    $model = self::getCodeWhere($where, self::alias('a'))
      ->field('a.*');
    $model = $model->order('a.id desc');
    $data = ($data = $model->page((int)$where['page'], (int)$where['limit'])->select()) && count($data) ? $data->toArray() : [];
    foreach ($data as &$item) {
      if (time() < $item['valid_time']) {
        if ($item['status'] == 1 && $item['is_del'] == 0) {
          $status_name = '已开启';
        } elseif ($item['status'] == 0 && $item['is_del'] == 0) {
          $status_name = '已关闭';
        } elseif ($item['is_del'] == 1) {
          $status_name = '已删除';
        }
      } else {
        $status_name = '已失效';
      }
      $item['status_name'] = $status_name;
      $item['valid_time'] = $item['valid_time'] ? date('Y-m-d H:i:s', $item['valid_time']) : '';
      $item['add_time'] = $item['add_time'] ? date('Y-m-d H:i:s', $item['add_time']) : '';
    }
    $count = self::statusByWhere($where, self::alias('a')->count());
    return compact('count', 'data');
  }

  public static function exportCode($ids)
  {
    $model = new self;
    $data = ($data = $model->field('id')->where('id', 'in', $ids)->where('is_del', 0)->where('status', 0)->where('valid_time', '>', time())->select()) && count($data) ? $data->toArray() : [];
    //根据优惠码组，获取其下所有有效未使用的code
    $code_list = [];
    foreach ($data as $key => $code) {
      $code_list = ($code_data =  StorePromoCodeList::where('cid', $code['id'])->where('status', 0)->where('is_del', 0)->where('is_fail', 0)->select()) && count($code_data) ? $code_data->toArray() : [];
    }
    if (!$code_list) return self::setErrorInfo('导出失败');
    self::SaveExcel($code_list);
  }


  public static function statusByWhere($status, $model = null, $alert = '')
  {
    if ($model == null) $model = new self;
    if ('' === $status)
      return $model;
    else if ($status == 1) //已审核
      return $model->where($alert . 'status', 1)->where($alert . 'is_del', 0);
    else if ($status == 2) //待审核
      return $model->where($alert . 'status', 2)->where($alert . 'is_del', 0);
    else if ($status == 3) //已删除
      return $model->where($alert . 'is_del', 1);
    else
      return $model;
  }


  /**
   * 处理where条件
   * @param $where
   * @param $model
   * @return mixed
   */
  public static function getCodeWhere($where, $model, $aler = '', $join = '')
  {
    if (isset($where['status']) && $where['status'] != '') {
      $model = self::statusByWhere($where['status'], $model, $aler);
    }
    if (isset($where['status']) && $where['status'] == '') {
      $model = $model->where('is_del', 0);
    }
    if (isset($where['keywords']) && $where['keywords'] != '') {
      $model = $model->where($aler . 'title|number|price', 'LIKE', "%$where[keywords]%");
    }
    return $model;
  }

  public static function editIsDel($id, $type)
  {
    $data['status'] = 0;
    if ($type == 1) {
      $data['is_del']  = 1;
      $attributes = [];
      $attributes['is_del'] = 1;
    } else {
      $attributes = [];
      $attributes['is_fail'] = [];
      $attributes['fail_time'] = time();
    }
    self::beginTrans();
    $res1 = self::edit($data, $id);
    $res2 = false !== StorePromoCodeList::where('cid', $id)->update($attributes);
    $res = $res1 && $res2;
    self::checkTrans($res);
    return $res;
  }

  /*
     * 保存并下载excel
     * $list array
     * return
     */
  public static function SaveExcel($list)
  {
    $export = [];
    foreach ($list as $index => $item) {
      $export[] = [
        $item['code'],
        $item['price'],
      ];
    }
    $title = '优惠码导出';
    PHPExcelService::instance()->setExcelHeader(['优惠码', '优惠金额'])
      ->setExcelTile($title, '订单信息' . time(), ' 生成时间：' . date('Y-m-d H:i:s', time()))
      ->setExcelContent($export)
      ->ExcelSave($title);
  }
}
