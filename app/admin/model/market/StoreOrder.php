<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2021-06-28 15:19:07
 */

namespace app\admin\model\market;

use app\admin\model\system\SystemStore;
use crmeb\basic\BaseModel;
use crmeb\traits\ModelTrait;
use think\facade\Route as Url;
use app\models\store\StoreCart;
use app\admin\model\wechat\WechatUser;
use app\admin\model\store\StoreProduct;
use app\models\routine\RoutineTemplate;
use app\admin\model\user\{User, UserBill};
use app\admin\model\ump\{StoreCouponUser, StorePink};
use crmeb\services\{PHPExcelService, WechatTemplateService};

/**
 * 订单管理Model
 * Class StoreOrder
 * @package app\admin\model\store
 */
class StoreOrder extends BaseModel
{
  /**
   * 数据表主键
   * @var string
   */
  protected $pk = 'id';

  /**
   * 模型名称
   * @var string
   */
  protected $name = 'store_activity_order';

  use ModelTrait;

  protected function getCartIdAttr($value)
  {
    return json_decode($value, true);
  }


  public static function orderCount()
  {
    $data['to_write_off'] = self::statusByWhere(1, new self())->where(['is_system_del' => 0])->count();
    $data['be_paid_deposit'] = self::statusByWhere(3, new self())->where(['is_system_del' => 0])->count();
    $data['pending_payment'] = self::statusByWhere(0, new self())->where(['is_system_del' => 0])->whereIn('order_type', [0, 1, 2])->count();
    $data['completed'] = self::statusByWhere(2, new self())->where(['is_system_del' => 0])->count();
    $data['tk'] = self::statusByWhere(-1, new self())->where(['is_system_del' => 0])->count();
    $data['yt'] = self::statusByWhere(-2, new self())->where(['is_system_del' => 0])->count();
    $data['del'] = self::statusByWhere(-4, new self())->where(['is_system_del' => 0])->count();
    $data['deposit'] = self::where('order_type', 2)->count();
    $data['full_amount'] = self::where('order_type', 1)->count();
    $data['advance_amount'] = self::where('order_type', 3)->count();
    return $data;
  }


  public static function OrderList($where)
  {
    $model = self::getOrderWhere(
      $where,
      self::alias('a')->join('user r', 'r.uid=a.uid', 'LEFT')->join('store_activity_order_cart_info c', 'a.id=c.oid'),
      'a.',
      'r',
      'c'
    )
      ->field('a.*,r.nickname,r.phone,r.spread_uid,c.activity_id');
    if ($where['order'] != '') {
      $model = $model->order(self::setOrder($where['order']));
    } else {
      $model = $model->order('a.id desc');
    }
    if (isset($where['excel']) && $where['excel'] == 1) {
      $data = ($data = $model->select()) && count($data) ? $data->toArray() : [];
    } else {
      $data = ($data = $model->page((int)$where['page'], (int)$where['limit'])->select()) && count($data) ? $data->toArray() : [];
    }
    $spreadUser = [];
    $storePink = [];
    $systemStore = [];
    $spreadIds = array_unique(array_column($data, 'spread_uid'));
    if ($spreadIds) {
      $spreadUser = User::where('uid', 'IN', $spreadIds)->column('nickname', 'uid');
    }
    $orderIds = array_column($data, 'id');
    if ($orderIds) {
      $storePink = StorePink::where('order_id_key', 'IN', $orderIds)->column('status', 'order_id_key');
    }
    $storeIds = array_unique(array_column($data, 'store_id'));
    if ($storeIds) {
      $systemStore = SystemStore::where('id', 'IN', $storeIds)->column('name', 'id');
    }
    foreach ($data as &$item) {
      $_info = StoreOrderCartInfo::where('oid', $item['id'])->field('cart_info')->select();
      $_info = count($_info) ? $_info->toArray() : [];
      foreach ($_info as $k => $v) {
        $cart_info = json_decode($v['cart_info'], true);
        if (!isset($cart_info['activityInfo'])) $cart_info['activityInfo'] = [];
        $_info[$k]['cart_info'] = $cart_info;
        unset($cart_info);
      }
      $item['_info'] = $_info;
      $item['spread_nickname'] = $spreadUser[$item['spread_uid']] ?? '';
      $item['invite_nickname'] = User::where('uid', $item['invite_uid'])->value('nickname');
      $item['invite_order_id'] = self::where('id', $item['invite_oid'])->value('order_id');
      $item['add_time'] = $item['add_time'] ? date('Y-m-d H:i:s', $item['add_time']) : '';
      $item['store_name'] = $systemStore[$item['store_id']] ?? '';
      $item['actual_payment'] = $item['order_type'] == 2 && $item['deposit_paid'] == 1 && $item['paid'] == 1 ? bcadd($item['deposit_pay_price'], $item['pay_price']) : 0;
      $item['actual_payment'] = $item['order_type'] == 2 && $item['deposit_paid'] == 1 && $item['paid'] == 0 ? $item['deposit_pay_price'] : 0;
      if ($item['shipping_type'] == 1) {
        $item['pink_name'] = '[普通订单]';
        $item['color'] = '#895612';
      } else if ($item['shipping_type'] == 2) {
        $item['pink_name'] = '[核销订单]';
        $item['color'] = '#8956E8';
      }
      if ($item['order_type'] == 1) {  //全款订单
        //支付方式
        if ($item['pay_type'] == 'weixin' || $item['pay_type'] == 'bytedance.weixin') {
          $item['pay_type_name'] = '全款：微信支付';
        } elseif ($item['pay_type'] == 'alipay' || $item['pay_type'] == 'bytedance.alipay') {
          $item['pay_type_name'] = '全款：支付宝支付';
        } elseif ($item['pay_type'] == 'yue') {
          $item['pay_type_name'] = '全款：余额支付';
        }
        //支付时间
        $item['merge_pay_time'] = $item['pay_time'] > 0 ? date('Y-m-d H:i:s', $item['pay_time']) : '';
        if ($item['paid'] == 0  && $item['status'] == 0) {
          $item['status_name'] = '未支付';
          $item['_status'] = 0;
        } else if ($item['paid'] == 1 && $item['status'] == 1 && $item['refund_status'] == 0) {
          $item['status_name'] = '待核销';
          $item['_status'] = 1;
        } else if ($item['paid'] == 1 && $item['status'] == 2 && $item['refund_status'] == 0) {
          $item['status_name'] = '交易完成';
          $item['_status'] = 2;
        } else if ($item['paid'] == 1 && $item['refund_status'] == 1) {
          $refundReasonTime = date('Y-m-d H:i', $item['refund_reason_time']);
          $refundReasonWapImg = json_decode($item['refund_reason_wap_img'], true);
          $refundReasonWapImg = $refundReasonWapImg ? $refundReasonWapImg : [];
          $img = '';
          if (count($refundReasonWapImg)) {
            foreach ($refundReasonWapImg as $itemImg) {
              if (strlen(trim($itemImg)))
                $img .= '<img style="height:50px;" src="' . $itemImg . '" />';
            }
          }
          if (!strlen(trim($img))) $img = '无';
          if (isset($where['excel']) && $where['excel'] == 1) {
            $refundImageStr = implode(',', $refundReasonWapImg);
            $item['status_name'] = <<<TEXT
退款原因:{$item['refund_reason_wap']} 
备注说明：{$item['refund_reason_wap_explain']}
退款时间：{$refundReasonTime}
凭证连接：{$refundImageStr}
TEXT;
            unset($refundImageStr);
            $item['_status'] = 3;
          } else {
            $item['status_name'] = <<<HTML
<b style="color:#f124c7">申请退款</b><br/>
<span>退款原因：{$item['refund_reason_wap']}</span><br/>
<span>备注说明：{$item['refund_reason_wap_explain']}</span><br/>
<span>退款时间：{$refundReasonTime}</span><br/>
<span>退款凭证：{$img}</span>
HTML;
            $item['_status'] = 4;
          }
        } else if ($item['paid'] == 1 && $item['refund_status'] == 2) {
          $item['_status'] = 4;
          $item['status_name'] = '已退款';
        }
        $item['actual_pay_price'] =  $item['paid'] == 1 ? $item['pay_price'] : 0;
      } else if ($item['order_type'] == 2) { //定金—+尾款
        //支付方式
        if ($item['deposit_pay_type'] == 'weixin' || $item['deposit_pay_type'] == 'bytedance.weixin') {
          $item['pay_type_name'] = '全款：微信支付';
          if ($item['pay_type'] == 'weixin' || $item['pay_type'] == 'bytedance.weixin') {
            $item['pay_type_name'] = $item['pay_type_name'] . '
尾款：微信支付';
          } elseif ($item['pay_type'] == 'alipay'  || $item['pay_type'] == 'bytedance.alipay') {
            $item['pay_type_name'] = $item['pay_type_name'] . '
尾款：支付宝支付';
          } elseif ($item['pay_type'] == 'yue') {
            $item['pay_type_name'] = $item['pay_type_name'] . '
尾款：余额支付';
          }
        } elseif ($item['deposit_pay_type'] == 'alipay'  || $item['deposit_pay_type'] == 'bytedance.alipay') {
          $item['pay_type_name'] = '全款：支付宝支付';
          if ($item['pay_type'] == 'weixin' || $item['pay_type'] == 'bytedance.weixin') {
            $item['pay_type_name'] = $item['pay_type_name'] . '
尾款：微信支付';
          } elseif ($item['pay_type'] == 'alipay'  || $item['pay_type'] == 'bytedance.alipay') {
            $item['pay_type_name'] = $item['pay_type_name'] . '
尾款：支付宝支付';
          } elseif ($item['pay_type'] == 'yue') {
            $item['pay_type_name'] = $item['pay_type_name'] . '
尾款：余额支付';
          }
        } elseif ($item['deposit_pay_type'] == 'yue') {
          $item['pay_type_name'] = '全款：余额支付';
          if ($item['pay_type'] == 'weixin' || $item['pay_type'] == 'bytedance.weixin') {
            $item['pay_type_name'] = $item['pay_type_name'] . '
尾款：微信支付';
          } elseif ($item['pay_type'] == 'alipay'  || $item['pay_type'] == 'bytedance.alipay') {
            $item['pay_type_name'] = $item['pay_type_name'] . '
尾款：支付宝支付';
          } elseif ($item['pay_type'] == 'yue') {
            $item['pay_type_name'] = $item['pay_type_name'] . '
尾款：余额支付';
          }
        }
        $deposit_pay_time = $item['deposit_pay_time'] > 0 ? date('Y-m-d H:i:s', $item['deposit_pay_time']) : '';
        $pay_time = $item['pay_time'] > 0 ? date('Y-m-d H:i:s', $item['pay_time']) : '';
        $change_pay_price = $item['paid'] == 1 ? $item['pay_price'] : 0;
        $change_deposit_pay_price = $item['deposit_paid'] == 1 ? $item['deposit_pay_price'] : 0;
        //实际支付价格
        $item['actual_pay_price'] = bcadd($change_pay_price, $change_deposit_pay_price, 2);
        //支付时间
        $item['merge_pay_time'] = $deposit_pay_time . '/' . $pay_time;
        if ($item['deposit_paid'] == 0  && $item['paid'] == 0 && $item['status'] == 0 && $item['is_del'] == 0) {
          $item['status_name'] = '未支付';
          $item['_status'] = 0;
        } else if ($item['deposit_paid'] == 1 && $item['paid'] == 0 && $item['status'] == 0  && $item['refund_status'] == 0 && $item['is_del'] == 0) {
          $item['status_name'] =  '待支付尾款';
          $item['_status'] = 5;
        } else if ($item['deposit_paid'] == 1 && $item['paid'] == 0 && $item['status'] == 0  && $item['refund_status'] == 0 && $item['is_del'] == 1 && $item['is_cancel'] == 1) {
          $item['status_name'] =  '订单取消-待支付尾款';
          $item['_status'] = 5;
        } else if ($item['status'] == 1 && $item['refund_status'] == 0 && $item['is_del'] == 0) {
          $item['status_name'] = '待核销';
          $item['_status'] = 1;
        } else if ($item['status'] == 2 && $item['refund_status'] == 0 && $item['is_del'] == 0) {
          $item['status_name'] = '交易完成';
          $item['_status'] = 2;
        } else if ($item['status'] == 3 && $item['refund_status'] == 0 && $item['is_del'] == 0) {
          $item['status_name'] = '交易完成,由系统执行';
          $item['_status'] = 6;
        } else if ($item['refund_status'] == 1 && $item['is_del'] == 0) {
          $item['_status'] = 3;
          $refundReasonTime = date('Y-m-d H:i', $item['refund_reason_time']);
          $refundReasonWapImg = json_decode($item['refund_reason_wap_img'], true);
          $refundReasonWapImg = $refundReasonWapImg ? $refundReasonWapImg : [];
          $img = '';
          if (count($refundReasonWapImg)) {
            foreach ($refundReasonWapImg as $itemImg) {
              if (strlen(trim($itemImg)))
                $img .= '<img style="height:50px;" src="' . $itemImg . '" />';
            }
          }
          if (!strlen(trim($img))) $img = '无';
          if (isset($where['excel']) && $where['excel'] == 1) {
            $refundImageStr = implode(',', $refundReasonWapImg);
            $item['status_name'] = <<<TEXT
退款原因:{$item['refund_reason_wap']} 
备注说明：{$item['refund_reason_wap_explain']}
退款时间：{$refundReasonTime}
凭证连接：{$refundImageStr}
TEXT;
            unset($refundImageStr);
          } else {
            $item['status_name'] = <<<HTML
<b style="color:#f124c7">申请退款</b><br/>
<span>退款原因：{$item['refund_reason_wap']}</span><br/>
<span>备注说明：{$item['refund_reason_wap_explain']}</span><br/>
<span>退款时间：{$refundReasonTime}</span><br/>
<span>退款凭证：{$img}</span>
HTML;
          }
        } else if ($item['refund_status'] == 2 && $item['is_del'] == 0) {
          $item['status_name'] = '已退款';
          $item['_status'] = 4;
        } else if ($item['refund_status'] == 2 && $item['is_cancel'] == 1 && $item['order_type'] == 2 && $item['paid'] == 0) {
          $item['status_name'] = '已退定金';
          $item['_status'] = 4;
        }
      } else if ($item['order_type'] == 3) { //定金—+尾款
        $item['status_name'] = '预报名成功';
        $item['pink_name'] = '[预报名订单]';
        $item['color'] = '#8956E8';
        $item['_status'] = 5;
        $item['actual_pay_price'] = 0;
        $item['pay_type_name'] = '字节跳动担保支付发起';
        $item['merge_pay_time'] = $item['deposit_add_time'] > 0 ? date('Y-m-d H:i:s', $item['deposit_add_time']) : '';
      }
    }
    if (isset($where['excel']) && $where['excel'] == 1) {
      self::SaveExcel($data);
    }
    $count = self::getOrderWhere(
      $where,
      self::alias('a')->join('user r', 'r.uid=a.uid', 'LEFT')->join('store_activity_order_cart_info c', 'a.id=c.oid'),
      'a.',
      'r',
      'c'
    )->count();
    return compact('count', 'data');
  }

  /*
     * 保存并下载excel
     * $list array
     * return
     */
  public static function SaveExcel($list)
  {
    $export = [];
    foreach ($list as $index => $item) {
      $_info = StoreOrderCartInfo::where('oid', $item['id'])->column('cart_info');
      $goodsName = [];
      $cart_sum = 0;
      foreach ($_info as $k => $v) {
        $v = json_decode($v, true);
        $suk = '';
        if (isset($v['activityInfo']['attrInfo'])) {
          if (isset($v['activityInfo']['attrInfo']['suk'])) {
            $suk = '(' . $v['activityInfo']['attrInfo']['suk'] . ')';
          }
        }
        $goodsName[] = implode(
          ' ',
          [
            $v['activityInfo']['store_name'],
            $suk,
            "[{$v['cart_num']} * {$v['truePrice']}]",
          ]
        );
        $cart_sum += $v['cart_num'];
      }
      $item['cartInfo'] = $_info;
      $sex = WechatUser::where('uid', $item['uid'])->value('sex');
      if ($sex == 1) $sex_name = '男';
      else if ($sex == 2) $sex_name = '女';
      else $sex_name = '未知';
      $WechatUserInfo = User::where('uid', $item['uid'])->value('nickname') ?: '暂无信息';
      $platform_users = $item['uid'] . '/' . $WechatUserInfo . '/' . $item['phone'];
      // 虚拟数据
      // $item['invite_uid'] = 99;
      // $item['invite_oid'] = 99;
      // $item['student_id'] = 60;
      $invite_users = [];
      if ($item['invite_uid'] != 0) {
        $invite_info  = User::where('uid', $item['invite_uid'])->field('nickname,phone')->find();
        $invite_info_order_id = $item['invite_oid'] != 0 ? self::where('id', $item['invite_oid'])->value('order_id') : '';
        $invite_users = [
          'ID:' . $item['invite_uid'],
          '昵称:' . $invite_info['nickname'],
          '手机号:' . $invite_info['phone'],
          '关联的订单号:' . $invite_info_order_id
        ];
      }
      $application_users = [];
      if ($item['student_id'] != 0) {
        $student_info = StoreActivityStudentCard::where('id', $item['student_id'])->find();
        if ($student_info) {
          $learning_experience = is_string($student_info['learning_experience'])  ? json_decode($student_info['learning_experience'], true) : [];
          $str = [];
          foreach ($learning_experience as $key => $value) {
            $str[] = '学习经历:'  . $value['content'];
          }
          $learning_experience = implode('', $str);
          $skills = is_string($student_info['skills'])  ?  json_decode($student_info['skills'], true)  : [];
          $str1 = [];
          foreach ($skills as $key => $value) {
            $str1[] = '分类名:' . $value['cate_name'] . '乐器名：' . $value['content'];
          }
          $skills = implode('', $str1);

          $application_users = [
            '学员姓名:' . $student_info['real_name'],
            '年龄:' . $student_info['age'],
            '性别:' . $student_info['sex'] == 1  ? '男' : '女',
            '职业:' . $student_info['career'],
            '音乐基础:' . $learning_experience,
            '擅长乐器:' . $skills,
            '作品链接:' . $student_info['video_works'],
            '外链作品链接:' . $student_info['video_links'],
          ];
        }
      }
      $export[] = [
        $item['order_id'],
        implode(", ", $goodsName),
        $platform_users,
        implode(", ", $application_users),
        $item['real_name'],
        $item['user_phone'],
        $item['spare_contact'],
        implode(", ", $invite_users),
        (string) $cart_sum,
        (string) $item['total_price'],
        (string)$item['actual_pay_price'],
        (string) $item['pay_type_name'],
        $item['merge_pay_time'] != ""  ? $item['merge_pay_time'] : '',
        $item['status_name'],
        $item['add_time'],
        $item['channel_name'],
        $item['questionnaire'],
        $item['mark']
      ];
    }

    $title = '活动订单导出';
    PHPExcelService::instance()->setExcelHeader([
      '订单号',
      '活动信息',
      '用户信息',
      '报名信息',
      '联系人姓名',
      '联系人电话',
      '备用联系方式',
      '邀请人信息',
      '总件数',
      '总价格',
      '实际支付',
      '支付状态',
      '支付时间',
      '订单状态',
      '下单时间',
      '渠道信息',
      '调查问卷',
      '用户备注'
    ])
      ->setExcelTile($title, '订单信息' . time(), ' 生成时间：' . date('Y-m-d H:i:s', time()))
      ->setExcelContent($export)
      ->ExcelSave($title);
  }

  /**
   * @param $where
   * @return array
   */
  public static function systemPage($where, $userid = false)
  {
    $model = self::getOrderWhere($where, self::alias('a')
      ->join('user r', 'r.uid=a.uid', 'LEFT'), 'a.', 'r')
      ->field('a.*,r.nickname');
    if ($where['order']) {
      $model = $model->order('a.' . $where['order']);
    } else {
      $model = $model->order('a.id desc');
    }
    if ($where['export'] == 1) {
      $list = $model->select()->toArray();
      $export = [];
      foreach ($list as $index => $item) {
        if ($item['pay_type'] == 'weixin') {
          $payType = '微信支付';
        } elseif ($item['pay_type'] == 'alipay') {
          $payType = '支付宝支付';
        } elseif ($item['pay_type'] == 'yue') {
          $payType = '余额支付';
        } elseif ($item['pay_type'] == 'offline') {
          $payType = '线下支付';
        } else {
          $payType = '其他支付';
        }
        $_info = StoreOrderCartInfo::where('oid', $item['id'])->column('cart_info', 'oid');
        $goodsName = [];
        foreach ($_info as $k => $v) {
          $v = json_decode($v, true);
          $goodsName[] = implode(
            [
              $v['activityInfo']['store_name'],
              isset($v['activityInfo']['attrInfo']) ? '(' . $v['activityInfo']['attrInfo']['suk'] . ')' : '',
              "[{$v['cart_num']} * {$v['truePrice']}]"
            ],
            ' '
          );
        }
        $item['cartInfo'] = $_info;
        $export[] = [
          $item['order_id'],
          $payType,
          $item['total_num'],
          $item['total_price'],
          $item['total_postage'],
          $item['pay_price'],
          $item['refund_price'],
          $item['mark'],
          $item['remark'],
          [$item['real_name'], $item['user_phone'], $item['user_address']],
          $goodsName,
          [$item['paid'] == 1 ? '已支付' : '未支付', '支付时间: ' . ($item['pay_time'] > 0 ? date('Y/md H:i', $item['pay_time']) : '暂无')]

        ];
        $list[$index] = $item;
      }
      PHPExcelService::instance()->setExcelHeader(['订单号', '支付方式', '商品总数', '商品总价', '邮费', '支付金额', '退款金额', '用户备注', '管理员备注', '收货人信息', '商品信息', '支付状态'])
        ->setExcelTile('订单导出', '订单信息' . time(), ' 生成时间：' . date('Y-m-d H:i:s', time()))
        ->setExcelContent($export)
        ->ExcelSave('订单导出');
    }
    return self::page($model, function ($item) {
      $_info = StoreOrderCartInfo::where('oid', $item['id'])->field('cart_info')->select();
      foreach ($_info as $k => $v) {
        $_info[$k]['cart_info'] = json_decode($v['cart_info'], true);
      }
      $item['_info'] = $_info;
      $item['pink_name'] = '[普通订单]';
      $item['color'] = '#895612';
    }, $where);
  }

  public static function statusByWhere($status, $model = null, $alert = '')
  {
    if ($model == null) $model = new self;
    if ('' === $status)
      return $model;
    else if ($status == 0) //未支付
      return $model->where($alert . 'status', 0)->where($alert . 'refund_status', 0)->where($alert . 'is_del', 0)->where('paid', 0)->where('deposit_paid', 0);
    else if ($status == 1) //已支付 待核销
      return $model->where($alert . 'status', 1)->where($alert . 'refund_status', 0)->where($alert . 'is_del', 0);
    else if ($status == 2) //已支付  交易完成
      return $model->where($alert . 'status', 2)->where($alert . 'refund_status', 0)->where($alert . 'is_del', 0);
    else if ($status == 3) //已支付定金 未支付尾款  
      return $model->where($alert . 'status', 0)->where($alert . 'refund_status', 0)->where($alert . 'is_del', 0)->where('paid', 0)->where('deposit_paid', 1);
    else if ($status == -1) //退款中
      return $model->where($alert . 'refund_status', 1)->where($alert . 'is_del', 0);
    else if ($status == -2) //已退款
      return $model->where($alert . 'refund_status', 2)->where($alert . 'is_del', 0);
    else if ($status == -3) //退款
      return $model->where($alert . 'refund_status', 'in', '1,2')->where($alert . 'is_del', 0);
    else if ($status == -4) //已删除
      return $model->where($alert . 'is_del', 1);
    else
      return $model;
  }

  public static function timeQuantumWhere($startTime = null, $endTime = null, $model = null)
  {
    if ($model === null) $model = new self;
    if ($startTime != null && $endTime != null)
      $model = $model->where('add_time', '>', strtotime($startTime))->where('add_time', '<', strtotime($endTime));
    return $model;
  }

  public static function changeOrderId($orderId)
  {
    $ymd = substr($orderId, 2, 8);
    $key = substr($orderId, 16);
    return 'wx' . $ymd . date('His') . $key;
  }

  /**
   * 线下付款
   * @param $id
   * @return $this
   */
  public static function updateOffline($id)
  {
    $count = self::where('id', $id)->count();
    if (!$count) return self::setErrorInfo('订单不存在');
    $count = self::where('id', $id)->where('paid', 0)->count();
    if (!$count) return self::setErrorInfo('订单已支付');
    $res = self::where('id', $id)->update(['paid' => 1, 'pay_time' => time()]);
    return $res;
  }

  /**
   * TODO 公众号退款发送模板消息
   * @param $oid
   * $oid 订单id  key
   */
  public static function refundTemplate($data, $oid)
  {
    $order = self::where('id', $oid)->find();
    WechatTemplateService::sendTemplate(WechatUser::where('uid', $order['uid'])->value('openid'), WechatTemplateService::ORDER_REFUND_STATUS, [
      'first' => '亲，您购买的商品已退款,本次退款' . $data['refund_price'] . '金额',
      'keyword1' => $order['order_id'],
      'keyword2' => $order['pay_price'],
      'keyword3' => date('Y-m-d H:i:s', $order['add_time']),
      'remark' => '点击查看订单详情'
    ], Url::buildUrl('/order/detail/' . $order['order_id'])->suffix('')->domain(true)->build());
  }

  /**
   * TODO 小程序余额退款模板消息
   * @param $oid
   * @return bool|mixed
   * @throws \think\db\exception\DataNotFoundException
   * @throws \think\db\exception\ModelNotFoundException
   * @throws \think\exception\DbException
   */
  public static function refundRoutineTemplate($oid)
  {
    return RoutineTemplate::sendOrderRefundSuccess();
  }

  /**
   * 处理where条件
   * @param $where
   * @param $model
   * @return mixed
   */
  public static function getOrderWhere($where, $model, $aler = '', $join = '', $joins = '')
  {
    $model = $model->where('is_system_del', 0);
    if (isset($where['status']) && $where['status'] != '') {
      $model = self::statusByWhere($where['status'], $model, $aler);
    }
    if (isset($where['is_del']) && $where['is_del'] != '' && $where['is_del'] != -1) $model = $model->where($aler . 'is_del', $where['is_del']);
    if (isset($where['pay_type'])) {
      switch ($where['pay_type']) {
        case 1:
          $model = $model->where($aler . 'pay_type', 'weixin');
          break;
        case 2:
          $model = $model->where($aler . 'pay_type', 'yue');
          break;
      }
    }
    if (isset($where['order_type'])) {
      switch ($where['order_type']) {
        case 1:
          $model = $model->where($aler . 'order_type', 1);
          break;
        case 2:
          $model = $model->where($aler . 'order_type', 2);
          break;
        case 3:
          $model = $model->where($aler . 'order_type', 3);
          break;
      }
    }
    if (isset($where['real_name']) && $where['real_name'] != '') {
      $model = $model->where($aler . 'order_id|' . $aler . 'registration_id|' . $aler . 'deposit_order_id|' . $aler . 'final_payment_order_id|' . $aler . 'real_name|' . $aler . 'user_phone' . ($join ? '|' . $join . '.nickname|' . $join . '.uid|' . $join . '.phone' : ''), 'LIKE', "%$where[real_name]%");
    }
    //活动id
    if (isset($where['activity_id']) && $where['activity_id'] !== '') {
      $model = $model->whereIn($joins . '.activity_id', [$where['activity_id']]);
    }
    if (isset($where['data']) && $where['data'] !== '') {
      $model = self::getModelTime($where, $model, $aler . 'add_time');
    }
    return $model;
  }

  public static function getBadge($where)
  {
    $price = self::getOrderPrice($where);
    return [
      [
        'name' => '订单数量',
        'field' => '件',
        'count' => $price['count_sum'],
        'background_color' => 'layui-bg-blue',
        'col' => 2
      ],
      [
        'name' => '售出活动商品',
        'field' => '件',
        'count' => $price['total_num'],
        'background_color' => 'layui-bg-blue',
        'col' => 2
      ],
      [
        'name' => '订单金额',
        'field' => '元',
        'count' => $price['pay_price'],
        'background_color' => 'layui-bg-blue',
        'col' => 2
      ],
      [
        'name' => '退款金额',
        'field' => '元',
        'count' => $price['refund_price'],
        'background_color' => 'layui-bg-blue',
        'col' => 2
      ],
      [
        'name' => '微信支付金额',
        'field' => '元',
        'count' => $price['pay_price_wx'],
        'background_color' => 'layui-bg-blue',
        'col' => 2
      ],
      [
        'name' => '余额支付金额',
        'field' => '元',
        'count' => $price['pay_price_yue'],
        'background_color' => 'layui-bg-blue',
        'col' => 2
      ],
      [
        'name' => '运费金额',
        'field' => '元',
        'count' => $price['pay_postage'],
        'background_color' => 'layui-bg-blue',
        'col' => 2
      ],
      [
        'name' => '分佣金额',
        'field' => '元',
        'count' => $price['brokerage'],
        'background_color' => 'layui-bg-blue',
        'col' => 2
      ],
      [
        'name' => '线下支付金额',
        'field' => '元',
        'count' => $price['pay_price_offline'],
        'background_color' => 'layui-bg-blue',
        'col' => 2
      ],
      [
        'name' => '积分抵扣',
        'field' => '分',
        'count' => $price['use_integral'] . '(抵扣金额:￥' . $price['deduction_price'] . ')',
        'background_color' => 'layui-bg-blue',
        'col' => 2
      ]
    ];
  }

  /**
   * 处理订单金额
   * @param $where
   * @return array
   */
  public static function getOrderPrice($where)
  {
    $where['is_del'] = 0; //删除订单不统计
    $model = new self;
    $price = [];
    $price['pay_price'] = 0; //支付金额
    $price['refund_price'] = 0; //退款金额
    $price['pay_price_wx'] = 0; //微信支付金额
    $price['pay_price_ali'] = 0; //支付宝支付金额
    $price['pay_price_yue'] = 0; //余额支付金额
    $price['pay_price_offline'] = 0; //线下支付金额
    $price['pay_price_other'] = 0; //其他支付金额
    $price['deduction_price'] = 0; //抵扣金额
    $price['total_num'] = 0; //商品总数
    $price['count_sum'] = 0; //商品总数
    $price['brokerage'] = 0;
    $price['pay_postage'] = 0;
    $whereData = ['is_del' => 0];
    if ($where['status'] == '') {
      $whereData['paid'] = 1;
      $whereData['refund_status'] = 0;
    }
    $ids = self::getOrderWhere($where, $model)->where($whereData)->column('id');
    if (count($ids)) {
      $price['brokerage'] = UserBill::where(['category' => 'now_money', 'type' => 'brokerage'])->where('link_id', 'in', $ids)->sum('number');
    }
    $price['refund_price'] = self::getOrderWhere($where, $model)->where(['is_del' => 0, 'paid' => 1, 'refund_status' => 2])->sum('refund_price');
    $sumNumber = self::getOrderWhere($where, $model)->where($whereData)->field([
      'sum(total_num) as sum_total_num',
      'count(id) as count_sum',
      'sum(pay_price) as sum_pay_price',
      'sum(pay_postage) as sum_pay_postage',
      'sum(deduction_price) as sum_deduction_price'
    ])->find();
    if ($sumNumber) {
      $price['count_sum'] = $sumNumber['count_sum'];
      $price['total_num'] = $sumNumber['sum_total_num'];
      $price['pay_price'] = $sumNumber['sum_pay_price'];
      $price['pay_postage'] = $sumNumber['sum_pay_postage'];
      $price['deduction_price'] = $sumNumber['sum_deduction_price'];
    }
    $list = self::getOrderWhere($where, $model)->where($whereData)->group('pay_type')->column('sum(pay_price) as sum_pay_price,pay_type', 'id');
    foreach ($list as $v) {
      if ($v['pay_type'] == 'weixin') {
        $price['pay_price_wx'] = $v['sum_pay_price'];
      } elseif ($v['pay_type'] == 'alipay') {
        $price['pay_price_ali'] = $v['sum_pay_price'];
      } elseif ($v['pay_type'] == 'yue') {
        $price['pay_price_yue'] = $v['sum_pay_price'];
      } elseif ($v['pay_type'] == 'offline') {
        $price['pay_price_offline'] = $v['sum_pay_price'];
      } else {
        $price['pay_price_other'] = $v['sum_pay_price'];
      }
    }
    return $price;
  }

  public static function systemPagePink($where)
  {
    $model = new self;
    $model = self::getOrderWherePink($where, $model);
    $model = $model->order('id desc');

    if ($where['export'] == 1) {
      $list = $model->select()->toArray();
      $export = [];
      foreach ($list as $index => $item) {

        if ($item['pay_type'] == 'weixin') {
          $payType = '微信支付';
        } elseif ($item['pay_type'] == 'alipay') {
          $payType = '支付宝支付';
        } elseif ($item['pay_type'] == 'yue') {
          $payType = '余额支付';
        } elseif ($item['pay_type'] == 'offline') {
          $payType = '线下支付';
        } else {
          $payType = '其他支付';
        }

        $_info = StoreOrderCartInfo::where('oid', $item['id'])->column('cart_info', 'oid');
        $goodsName = [];
        foreach ($_info as $k => $v) {
          $v = json_decode($v, true);
          $goodsName[] = implode(
            [
              $v['activityInfo']['store_name'],
              isset($v['activityInfo']['attrInfo']) ? '(' . $v['activityInfo']['attrInfo']['suk'] . ')' : '',
              "[{$v['cart_num']} * {$v['truePrice']}]"
            ],
            ' '
          );
        }
        $item['cartInfo'] = $_info;
        $export[] = [
          $item['order_id'],
          $payType,
          $item['total_num'],
          $item['total_price'],
          $item['total_postage'],
          $item['pay_price'],
          $item['refund_price'],
          $item['mark'],
          $item['remark'],
          [$item['real_name'], $item['user_phone'], $item['user_address']],
          $goodsName,
          [$item['paid'] == 1 ? '已支付' : '未支付', '支付时间: ' . ($item['pay_time'] > 0 ? date('Y/md H:i', $item['pay_time']) : '暂无')]

        ];
        $list[$index] = $item;
      }
      ExportService::exportCsv($export, '订单导出' . time(), ['订单号', '支付方式', '商品总数', '商品总价', '邮费', '支付金额', '退款金额', '用户备注', '管理员备注', '收货人信息', '商品信息', '支付状态']);
    }

    return self::page($model, function ($item) {
      $item['nickname'] = WechatUser::where('uid', $item['uid'])->value('nickname');
      $_info = StoreOrderCartInfo::where('oid', $item['id'])->field('cart_info')->select();
      foreach ($_info as $k => $v) {
        $_info[$k]['cart_info'] = json_decode($v['cart_info'], true);
      }
      $item['_info'] = $_info;
    }, $where);
  }

  /**
   * 处理where条件
   * @param $where
   * @param $model
   * @return mixed
   */
  public static function getOrderWherePink($where, $model)
  {
    if ($where['status'] != '') $model = $model::statusByWhere($where['status']);
    if ($where['real_name'] != '') {
      $model = $model->where('order_id|real_name|user_phone', 'LIKE', "%$where[real_name]%");
    }
    if ($where['data'] !== '') {
      $model = self::getModelTime($where, $model, 'add_time');
    }
    return $model;
  }

  /**
   * 处理订单金额
   * @param $where
   * @return array
   */
  public static function getOrderPricePink($where)
  {
    $model = new self;
    $price = [];
    $price['pay_price'] = 0; //支付金额
    $price['refund_price'] = 0; //退款金额
    $price['pay_price_wx'] = 0; //微信支付金额
    $price['pay_price_ali'] = 0; //支付宝支付金额
    $price['pay_price_yue'] = 0; //余额支付金额
    $price['pay_price_offline'] = 0; //线下支付金额
    $price['pay_price_other'] = 0; //其他支付金额
    $price['deduction_price'] = 0; //抵扣金额
    $price['total_num'] = 0; //商品总数
    $model = self::getOrderWherePink($where, $model);
    $list = $model->select()->toArray();
    foreach ($list as $v) {
      $price['total_num'] = bcadd($price['total_num'], $v['total_num'], 0);
      $price['pay_price'] = bcadd($price['pay_price'], $v['pay_price'], 2);
      $price['refund_price'] = bcadd($price['refund_price'], $v['refund_price'], 2);
      $price['deduction_price'] = bcadd($price['deduction_price'], $v['deduction_price'], 2);
      if ($v['pay_type'] == 'weixin') {
        $price['pay_price_wx'] = bcadd($price['pay_price_wx'], $v['pay_price'], 2);
      } elseif ($v['pay_type'] == 'alipay') {
        $price['pay_price_ali'] = bcadd($price['pay_price_ali'], $v['pay_price'], 2);
      } elseif ($v['pay_type'] == 'yue') {
        $price['pay_price_yue'] = bcadd($price['pay_price_yue'], $v['pay_price'], 2);
      } elseif ($v['pay_type'] == 'offline') {
        $price['pay_price_offline'] = bcadd($price['pay_price_offline'], $v['pay_price'], 2);
      } else {
        $price['pay_price_other'] = bcadd($price['pay_price_other'], $v['pay_price'], 2);
      }
    }
    return $price;
  }

  /**
   * 获取昨天的订单   首页在使用
   * @param int $preDay
   * @param int $day
   * @return $this|StoreOrder
   */
  public static function isMainYesterdayCount($preDay = 0, $day = 0)
  {
    $model = new self();
    $model = $model->where('add_time', '>', $preDay);
    $model = $model->where('add_time', '<', $day);
    return $model;
  }

  /**
   * 获取用户购买次数
   * @param int $uid
   * @return int|string
   */
  public static function getUserCountPay($uid = 0)
  {
    if (!$uid) return 0;
    return self::where('uid', $uid)->where('paid', 1)->count();
  }

  /**
   * 获取单个用户购买列表
   * @param array $where
   * @return array
   */
  public static function getOneorderList($where)
  {
    return self::where('uid', $where['uid'])
      ->order('add_time desc')
      ->page((int)$where['page'], (int)$where['limit'])
      ->field([
        'order_id,deposit_order_id,final_payment_order_id,real_name,total_num,total_price,pay_price,FROM_UNIXTIME(pay_time,"%Y-%m-%d") as pay_time,paid,pay_type'
      ])->select()
      ->toArray();
  }

  /**
   * 设置订单统计图搜索
   * @param array $where 条件
   * @param null $status
   * @param null $time
   * @return array
   */
  public static function setEchatWhere($where, $status = null, $time = null)
  {
    $model = self::statusByWhere($where['status'])->where('is_system_del', 0);
    if ($status !== null) $where['type'] = $status;
    if ($time === true) $where['data'] = '';
    return self::getModelTime($where, $model);
  }

  /*
     * 获取订单数据统计图
     * $where array
     * $limit int
     * return array
     */
  public static function getEchartsOrder($where, $limit = 20)
  {
    $orderlist = self::setEchatWhere($where)->field(
      'FROM_UNIXTIME(add_time,"%Y-%m-%d") as _add_time,sum(total_num) total_num,count(*) count,sum(total_price) total_price,sum(refund_price) refund_price,group_concat(cart_id SEPARATOR "|") cart_ids'
    )->group('_add_time')->order('_add_time asc')->select();
    count($orderlist) && $orderlist = $orderlist->toArray();
    $legend = ['商品数量', '订单数量', '订单金额', '退款金额'];
    $seriesdata = [
      [
        'name' => $legend[0],
        'type' => 'line',
        'data' => [],
      ],
      [
        'name' => $legend[1],
        'type' => 'line',
        'data' => []
      ],
      [
        'name' => $legend[2],
        'type' => 'line',
        'data' => []
      ],
      [
        'name' => $legend[3],
        'type' => 'line',
        'data' => []
      ]
    ];
    $xdata = [];
    $zoom = '';
    foreach ($orderlist as $item) {
      $xdata[] = $item['_add_time'];
      $seriesdata[0]['data'][] = $item['total_num'];
      $seriesdata[1]['data'][] = $item['count'];
      $seriesdata[2]['data'][] = $item['total_price'];
      $seriesdata[3]['data'][] = $item['refund_price'];
    }
    count($xdata) > $limit && $zoom = $xdata[$limit - 5];
    $badge = self::getOrderBadge($where);
    $bingpaytype = self::setEchatWhere($where)->group('pay_type')->field('count(*) as count,pay_type')->select();
    count($bingpaytype) && $bingpaytype = $bingpaytype->toArray();
    $bing_xdata = ['微信支付', '支付宝支付', '余额支付', '其他支付'];
    $color = ['#ffcccc', '#99cc00', '#fd99cc', '#669966'];
    $bing_data = [];
    foreach ($bingpaytype as $key => $item) {
      if ($item['pay_type'] == 'weixin') {
        $value['name'] = $bing_xdata[0];
      } else if ($item['pay_type'] == 'alipay') {
        $value['name'] = $bing_xdata[1];
      } else if ($item['pay_type'] == 'yue') {
        $value['name'] = $bing_xdata[2];
      } else {
        $value['name'] = $bing_xdata[3];
      }
      $value['value'] = $item['count'];
      $value['itemStyle']['color'] = isset($color[$key]) ? $color[$key] : $color[0];
      $bing_data[] = $value;
    }
    return compact('zoom', 'xdata', 'seriesdata', 'badge', 'legend', 'bing_data', 'bing_xdata');
  }

  public static function getOrderBadge($where)
  {
    return [
      [
        'name' => '拼团订单数量',
        'field' => '个',
        'count' => self::setEchatWhere($where, 2)->count(),
        'content' => '拼团总订单数量',
        'background_color' => 'layui-bg-cyan',
        'sum' => self::setEchatWhere($where, 2, true)->count(),
        'class' => 'fa fa-line-chart',
        'col' => 2
      ],
      [
        'name' => '砍价订单数量',
        'field' => '个',
        'count' => self::setEchatWhere($where, 4)->count(),
        'content' => '砍价总订单数量',
        'background_color' => 'layui-bg-cyan',
        'sum' => self::setEchatWhere($where, 4, true)->count(),
        'class' => 'fa fa-line-chart',
        'col' => 2
      ],
      [
        'name' => '秒杀订单数量',
        'field' => '个',
        'count' => self::setEchatWhere($where, 3)->count(),
        'content' => '秒杀总订单数量',
        'background_color' => 'layui-bg-cyan',
        'sum' => self::setEchatWhere($where, 3, true)->count(),
        'class' => 'fa fa-line-chart',
        'col' => 2
      ],
      [
        'name' => '普通订单数量',
        'field' => '个',
        'count' => self::setEchatWhere($where, 1)->count(),
        'content' => '普通总订单数量',
        'background_color' => 'layui-bg-cyan',
        'sum' => self::setEchatWhere($where, 1, true)->count(),
        'class' => 'fa fa-line-chart',
        'col' => 2,
      ],
      [
        'name' => '使用优惠卷金额',
        'field' => '元',
        'count' => self::setEchatWhere($where)->sum('coupon_price'),
        'content' => '普通总订单数量',
        'background_color' => 'layui-bg-cyan',
        'sum' => self::setEchatWhere($where, null, true)->sum('coupon_price'),
        'class' => 'fa fa-line-chart',
        'col' => 2
      ],
      [
        'name' => '积分消耗数',
        'field' => '个',
        'count' => self::setEchatWhere($where)->sum('use_integral'),
        'content' => '积分消耗总数',
        'background_color' => 'layui-bg-cyan',
        'sum' => self::setEchatWhere($where, null, true)->sum('use_integral'),
        'class' => 'fa fa-line-chart',
        'col' => 2
      ],
      [
        'name' => '积分抵扣金额',
        'field' => '个',
        'count' => self::setEchatWhere($where)->sum('deduction_price'),
        'content' => '积分抵扣总金额',
        'background_color' => 'layui-bg-cyan',
        'sum' => self::setEchatWhere($where, null, true)->sum('deduction_price'),
        'class' => 'fa fa-money',
        'col' => 2
      ],
      [
        'name' => '在线支付金额',
        'field' => '元',
        'count' => self::setEchatWhere($where)->where(['paid' => 1, 'refund_status' => 0])->whereIn('pay_type', ['weixin', 'alipay'])->sum('pay_price'),
        'content' => '在线支付总金额',
        'background_color' => 'layui-bg-cyan',
        'sum' => self::setEchatWhere($where, null, true)->where(['paid' => 1, 'refund_status' => 0])->whereIn('pay_type', ['weixin', 'alipay'])->sum('pay_price'),
        'class' => 'fa fa-weixin',
        'col' => 2
      ],
      [
        'name' => '余额支付金额',
        'field' => '元',
        'count' => self::setEchatWhere($where)->where('pay_type', 'yue')->where(['paid' => 1, 'refund_status' => 0])->sum('pay_price'),
        'content' => '余额支付总金额',
        'background_color' => 'layui-bg-cyan',
        'sum' => self::setEchatWhere($where, null, true)->where(['paid' => 1, 'refund_status' => 0])->where('pay_type', 'yue')->sum('pay_price'),
        'class' => 'fa  fa-balance-scale',
        'col' => 2
      ],
      [
        'name' => '赚取积分',
        'field' => '分',
        'count' => self::setEchatWhere($where)->sum('gain_integral'),
        'content' => '赚取总积分',
        'background_color' => 'layui-bg-cyan',
        'sum' => self::setEchatWhere($where, null, true)->sum('gain_integral'),
        'class' => 'fa fa-gg-circle',
        'col' => 2
      ],
      [
        'name' => '交易额',
        'field' => '元',
        'count' => self::setEchatWhere($where)->where(['paid' => 1, 'refund_status' => 0])->sum('pay_price'),
        'content' => '总交易额',
        'background_color' => 'layui-bg-cyan',
        'sum' => self::setEchatWhere($where, null, true)->where(['paid' => 1, 'refund_status' => 0])->sum('pay_price'),
        'class' => 'fa fa-jpy',
        'col' => 2
      ],
      [
        'name' => '订单商品数量',
        'field' => '元',
        'count' => self::setEchatWhere($where)->sum('total_num'),
        'content' => '订单商品总数量',
        'background_color' => 'layui-bg-cyan',
        'sum' => self::setEchatWhere($where, null, true)->sum('total_num'),
        'class' => 'fa fa-cube',
        'col' => 2
      ]
    ];
  }

  /**
   * 微信 订单发货
   * @param $oid
   * @param array $postageData
   * @throws \think\db\exception\DataNotFoundException
   * @throws \think\db\exception\ModelNotFoundException
   * @throws \think\exception\DbException
   */
  public static function orderPostageAfter()
  {
    //小程序送货模版消息
  }

  /** 收货后发送模版消息
   * @param $order
   */
  public static function orderTakeAfter($order)
  {
    $title = '';
    $cartInfo = StoreOrderCartInfo::where('oid', $order['id'])->column('cart_info', 'oid');

    if (count($cartInfo)) {
      foreach ($cartInfo as $key => &$cart) {
        $cart = json_decode($cart, true);
        $title .= $cart['activityInfo']['store_name'] . ',';
      }
    }
    if (strlen(trim($title)))
      $title = substr($title, 0, bcsub(strlen($title), 1, 0));
    else {
      $cartInfo = StoreCart::alias('a')->where('a.id', 'in', implode(',', json_decode($order['cart_id'], true)))->find();
      $title = StoreProduct::where('id', $cartInfo['product_id'])->value('store_name');
    }

    if ($order['is_channel'] == 1) { //小程序
      RoutineTemplate::sendOrderTakeOver($order, $title);
    } else {
      $openid = WechatUser::where('uid', $order['uid'])->value('openid');
      WechatTemplateService::sendTemplate($openid, WechatTemplateService::ORDER_TAKE_SUCCESS, [
        'first' => '亲，您的订单已收货',
        'keyword1' => $order['order_id'],
        'keyword2' => '已收货',
        'keyword3' => date('Y-m-d H:i:s', time()),
        'keyword4' => $title,
        'remark' => '感谢您的光临！'
      ]);
    }
  }

  /**
   * 不退款发送模板消息
   * @param int $id 订单id
   * @param array $data 退款详情
   * */
  public static function refundNoPrieTemplate($id, $data)
  {
    $order = self::get($id);
    if ($order) return false;
    //小程序模板消息
    $cartInfo = StoreOrderCartInfo::where('oid', $order['id'])->column('product_id', 'oid') ?: [];
    $title = '';
    foreach ($cartInfo as $k => $productId) {
      $store_name = StoreProduct::where('id', $productId)->value('store_name');
      $title .= $store_name . ',';
    }
    if ($order->is_channel == 1) {
      RoutineTemplate::sendOrderRefundFail($order, $title);
    } else {
      WechatTemplateService::sendTemplate(WechatUser::where('uid', $order->uid)->value('openid'), WechatTemplateService::ORDER_REFUND_STATUS, [
        'first' => '很抱歉您的订单退款失败，失败原因：' . $data,
        'keyword1' => $order->order_id,
        'keyword2' => $order->pay_price,
        'keyword3' => date('Y-m-d H:i:s', time()),
        'remark' => '给您带来的不便，请谅解！'
      ], Url::buildUrl('/order/detail/' . $order['order_id'])->suffix('')->domain(true)->build());
    }
  }

  /**
   * 获取订单总数
   * @param int $uid
   * @return int|string
   */
  public static function getOrderCount($uid = 0)
  {
    if (!$uid) return 0;
    return self::where('uid', $uid)->where('paid', 1)->where('refund_status', 0)->where('status', 2)->count();
  }

  /**
   * 获取已支付的订单
   * @param int $is_promoter
   * @return int|string
   */
  public static function getOrderPayCount($is_promoter = 0)
  {
    return self::where('o.paid', 1)->alias('o')->join('User u', 'u.uid=o.uid')->where('u.is_promoter', $is_promoter)->count();
  }

  /**
   * 获取最后一个月已支付的订单
   * @param int $is_promoter
   * @return int|string
   */
  public static function getOrderPayMonthCount($is_promoter = 0)
  {
    return self::where('o.paid', 1)->alias('o')->whereTime('o.pay_time', 'last month')->join('User u', 'u.uid=o.uid')->where('u.is_promoter', $is_promoter)->count();
  }

  /**
   * 订单数量 支付方式
   * @return array
   */
  public static function payTypeCount()
  {
    $where['status'] = 8;
    $where['is_del'] = 0;
    $where['real_name'] = '';
    $where['data'] = '';
    $where['type'] = '';
    $where['order'] = '';
    $where['pay_type'] = 1;
    $weixin = self::getOrderWhere($where, new self)->count();
    $where['pay_type'] = 2;
    $yue = self::getOrderWhere($where, new self)->count();
    $where['pay_type'] = 3;
    $offline = self::getOrderWhere($where, new self)->count();
    $where['pay_type'] = 4;
    $alipay = self::getOrderWhere($where, new self)->count();
    return compact('weixin', 'yue', 'offline', 'alipay');
  }


  /**
   * TODO 参加活动人员的用户ID
   * @param $id
   * @param string $field
   * @return mixed
   */
  public static function getActivityOrderGroup($activity_id, $type)
  {
    $model = new self;
    $model = $model->alias('A');
    $model = $model->join('store_activity_order_cart_info cart', 'A.id = cart.oid');
    $model = $model->field('A.order_id,A.final_payment_order_id,A.uid,A.pay_price,A.deposit_pay_price,A.deposit_add_time,A.deposit_pay_time,A.add_time');
    $model = $model->where('cart.activity_id', $activity_id);
    switch ($type) {
      case 1: //待付款
        # code...
        $model = $model->where('A.deposit_paid', 0)->where('A.paid', 0)->where('A.is_del', 0);
        break;
      case 2: //代付尾款
        # code...
        $model = $model->where('A.order_type', 2)->where('A.deposit_paid', 1)->where('A.paid', 0)->where('A.is_del', 0);
        break;
      case 3: //取消活动
        # code...
        $model = $model->where(function ($query) {
          $query->where('A.paid', 1)->whereOr('A.deposit_paid', 1)->where('A.is_del', 0);
        });
        break;
    }
    $model = $model->group('A.uid');
    $model = $model->order('A.id DESC');
    $data = $model->select();
    if (count($data)) $data = $data->toArray();
    return $data;
  }


  /**
   * 查找活动购物车里的所有活动标题
   * @param $cartId 购物车id
   * @return bool|string
   */
  public static function getActivityTitle($cartId)
  {
    $title = '';
    try {
      $orderCart = StoreOrderCartInfo::where('cart_id', 'in', $cartId)->field('cart_info')->select();
      foreach ($orderCart as $item) {
        $cart_info = json_decode($item['cart_info'], true);
        if (isset($cart_info['activityInfo']['store_name'])) {
          $title .= $cart_info['activityInfo']['store_name'] . '|';
        }
      }
      unset($item);
      if (!$title) {
        $productIds = StoreCart::where('id', 'in', $cartId)->column('product_id');
        $productlist = ($productlist = StoreProduct::getProductField($productIds, 'store_name')) ? $productlist->toArray() : [];
        foreach ($productlist as $item) {
          if (isset($item['store_name'])) $title .= $item['store_name'] . '|';
        }
      }
      if ($title) $title = substr($title, 0, strlen($title) - 1);
      unset($item);
    } catch (\Exception $e) {
    }
    return $title;
  }


  /**
   * 查找活动购物车里的所有活动标题
   * @param $cartId 购物车id
   * @return bool|string
   */
  public static function getActivityId($cartId)
  {
    $activity_id = '';
    try {
      $orderCart = StoreOrderCartInfo::where('cart_id', 'in', $cartId)->field('cart_info')->select();
      foreach ($orderCart as $item) {
        $cart_info = json_decode($item['cart_info'], true);
        if (isset($cart_info['activityInfo']['id'])) {
          $activity_id .= $cart_info['activityInfo']['id'] . '|';
        }
      }
      unset($item);
      if (!$activity_id) {
        $productIds = StoreCart::where('id', 'in', $cartId)->column('product_id');
        $productlist = ($productlist = StoreProduct::getProductField($productIds, 'id')) ? $productlist->toArray() : [];
        foreach ($productlist as $item) {
          if (isset($item['id'])) $activity_id .= $item['id'] . '|';
        }
      }
      if ($activity_id) $activity_id = substr($activity_id, 0, strlen($activity_id) - 1);
      unset($item);
    } catch (\Exception $e) {
    }
    return $activity_id;
  }

  /**
   * 回退报名与众筹人数
   * @param $order 订单信息
   * @return bool
   * @throws \think\db\exception\DataNotFoundException
   * @throws \think\db\exception\ModelNotFoundException
   * @throws \think\exception\DbException
   */
  public static function RegressionStock($order)
  {
    if ($order['status'] == -2 || $order['is_del']) return true;
    $res5 = true;
    $cartInfo = StoreOrderCartInfo::where('cart_id', 'in', $order['cart_id'])->select();
    foreach ($cartInfo as $cart) {
      $cart_info = json_decode($cart['cart_info'], true);
      StoreActivity::incActivityStock($cart_info['cart_num'], $cart['activity_id'], $order['order_type']);
    }
    return $res5;
  }
}
