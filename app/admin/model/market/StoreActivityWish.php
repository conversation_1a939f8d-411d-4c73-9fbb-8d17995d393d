<?php

/**
 * @Author: <PERSON>
 * @Date:   2022-07-16 11:04:13
 * @Last Modified time: 2022-09-07 14:59:04
 */

namespace app\admin\model\market;


use crmeb\basic\BaseModel;
use crmeb\traits\ModelTrait;
use app\admin\model\user\{User};
use app\admin\model\wechat\WechatUser;
use crmeb\services\{PHPExcelService};

class StoreActivityWish extends BaseModel
{

  /**
   * 模型名称
   * @var string
   */
  protected $name = 'store_activity_wishs';

  use ModelTrait;

  /** 获取订单产品列表
   * @param $oid
   * @return array
   */
  public static function getWishList($where)
  {
    $model = self::getWishWhere($where, self::alias('a'))
      ->field('a.*');
    $model = $model->order('a.id desc');

    if (isset($where['excel']) && $where['excel'] == 1) {
      $data = ($data = $model->select()) && count($data) ? $data->toArray() : [];
    } else {
      $data = ($data = $model->page((int)$where['page'], (int)$where['limit'])->select()) && count($data) ? $data->toArray() : [];
    }
    foreach ($data as &$item) {
      $item['add_time'] =  $item['add_time'] != 0 ? date('Y-m-d H:i:s', $item['add_time']) : '';
    }
    if (isset($where['excel']) && $where['excel'] == 1) {
      self::SaveExcel($data);
    }
    $count  = self::getWishWhere($where, self::alias('a'))->count();
    return compact('count', 'data');
  }




  /**
   * 处理where条件
   * @param $where
   * @param $model
   * @return mixed
   */
  public static function getWishWhere($where, $model, $aler = '', $join = '')
  {
    if (isset($where['keywords']) && $where['keywords'] != '') {
      $model = $model->where($aler . 'address|contact_name|phone|comment', 'LIKE', "%$where[keywords]%");
    }
    return $model;
  }

  /*
     * 保存并下载excel
     * $list array
     * return
     */
  public static function SaveExcel($list)
  {
    $export = [];
    foreach ($list as $index => $item) {
      $WechatUserInfo = User::where('uid', $item['uid'])->value('nickname') ?: '暂无信息';
      $platform_users = $item['uid'] . '/' . $WechatUserInfo . '/' . $item['phone'];
      $export[] = [
        $platform_users,
        $item['address'],
        $item['contact_name'],
        $item['phone'],
        $item['comment'],
        $item['add_time'],
      ];
    }
    PHPExcelService::instance()->setExcelHeader(['用户信息', '城市', '联系人', '电话', '备注', '收集时间'])
      ->setExcelTile('活动心愿收集名单导出' . date('YmdHis', time()), '名单信息' . time(), ' 生成时间：' . date('Y-m-d H:i:s', time()))
      ->setExcelContent($export)
      ->ExcelSave('活动心愿收集名单导出');
  }
}
