<?php

/**
 * @Author: <PERSON>
 * @Date:   2022-03-23 11:41:05
 * @Last Modified time: 2022-04-11 16:36:39
 */
namespace app\admin\model\market;

use crmeb\basic\BaseModel;
use crmeb\traits\ModelTrait;
use app\models\routine\RoutineTemplate;

/**
 * TODO 在线运营活动:参与记录Model
 * Class StoreActivityOnlinePartake
 * @package app\models\market
 */
class StoreActivityOnlinePartake extends BaseModel
{
    /**
     * 模型名称
     * @var string
     */
    protected $name = 'store_activity_online_partake';

    use ModelTrait;

    public static function recordCount()
    {
        $data['reviewed'] = self::statusByWhere(1, new self())->count();
        return $data;
    }


    public static function statusByWhere($status, $model = null, $alert = '')
    {
        if ($model == null) $model = new self;
        if ('' === $status)
            return $model;
        else
            return $model;
    }

    public static function getRecordList($where, $page, $limit){
        $model = self::getrecordWhere($where, self::alias('a')
            ->join('user r', 'r.uid=a.uid', 'LEFT'), 'a.', 'r')
            ->field('a.*,r.nickname,r.phone,r.spread_uid,r.avatar');
        $model = $model->order('a.id desc');
        $data = ($data = $model->page((int)$page, (int)$limit)->select()) && count($data) ? $data->toArray() : [];
        foreach ($data as &$item) {
            $item['add_time'] = $item['add_time'] ? date('Y-m-d H:i:s', $item['add_time']) : '';
        }
        $count = self::getrecordWhere($where, self::alias('a')->join('user r', 'r.uid=a.uid', 'LEFT'), 'a.', 'r')->count();
        return compact('count', 'data');
    }

    /**
     * 处理where条件
     * @param $where
     * @param $model
     * @return mixed
     */
    public static function getRecordWhere($where, $model, $aler = '', $join = '')
    {
        if (isset($where['id']) && $where['id'] != '') {
            $model = $model->where($aler . 'activity_id', $where['id']);
        }
        if (isset($where['keywords']) && $where['keywords'] != '') {
                $model = $model->where($aler . 'uid' . ($join ? '|' . $join . '.nickname|' . $join . '.uid|' . $join . '.phone' : ''), 'LIKE', "%$where[keywords]%");
        }
        if (isset($where['data']) && $where['data'] !== '') {
            $model = self::getModelTime($where, $model, $aler . 'add_time');
        }
        return $model;
    }

    public static function PushWxMessage($id){
        $res1 = true;
        $res2 = true;
        self::beginTrans();
        $activity = StoreActivityOnline::where('id',$id)->find();
        if ($activity) {
            $data = ($data = self::where('activity_id',$id)->select()) && count($data) ? $data->toArray() : [];
            $count = 0;
            foreach ($data as &$item) {
                $push_data = is_string($activity['push_message_data']) ? json_decode($activity['push_message_data'], true) : [];
                if (!empty($push_data)) {
                    $push_message_status = RoutineTemplate::sendActivityReserveStarted($item['uid'],$push_data,$activity->url);
                    if ($push_message_status) {
                        $res1 = self::where('activity_id',$id)->where('uid',$item['uid'])->update(['push_message_status'=>1]);
                        $count ++;
                    }
                }
            }
            // 更新推送状态 
            $activity->is_admin_push_message_status =  1;
            $activity->success_push_message_number =  $count;
            $res2 = $activity->save();
        }
        $res = $res1 && $res2;
        self::checkTrans($res);
        return $res;
    }

}