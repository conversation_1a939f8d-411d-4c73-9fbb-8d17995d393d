<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2021-08-12 15:36:48
 * @Last Modified time: 2021-08-16 13:30:57
 */

namespace app\admin\model\market;

use crmeb\traits\ModelTrait;
use think\Model;

/**
 * @mixin think\Model
 */
class StorePromoCodeList extends Model
{
    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'store_activity_promo_code_list';

    use ModelTrait;


    /**
     * @param $where
     * @return array
     */
    public static function systemPage($where)
    {
        $model = new self;
        $model = $model->alias('A');
        $model = $model->join('store_activity_promo_code code','A.cid = code.id');
        $model = $model->field('A.id,A.code,A.price,A.add_time,A.use_time,A.fail_time,A.end_time,A.is_fail,A.is_del,A.status,code.title as code_group');
        $model = $model->where('A.is_del',0);
        $model = $model->order('A.id DESC');
        // if ($where['status'] != '') $model = $model->where('A.status', $where['status']);
        if ($where['name'] != '') $model = $model->where('A.code', 'LIKE', "%$where[name]%");
        return self::page($model, $where);
    }


    public static function editIsDel($id,$type)
    {   
        $data['status'] = 0;
        if ($type == 1) {
            $data['is_del']  = 1;
            $attributes = [];
            $attributes['is_fail'] = 1;
            $attributes['is_del'] = 1;
        }else{
            $attributes = [];
            $attributes['is_fail'] = 1;
            $attributes['fail_time'] = time();
        }
        self::beginTrans();
        $res1 = self::edit($data, $id);
        $res2 = false !== StorePromoCodeList::where('cid', $id)->update($attributes);
        $res = $res1 && $res2;
        self::checkTrans($res);
        return $res;

    }
}
