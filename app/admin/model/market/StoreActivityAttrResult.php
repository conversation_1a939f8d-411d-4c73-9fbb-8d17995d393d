<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2021-07-19 12:16:51
 * @Last Modified time: 2021-07-19 12:17:51
 */
namespace app\admin\model\market;

use crmeb\basic\BaseModel;
use crmeb\traits\ModelTrait;

class StoreActivityAttrResult extends BaseModel
{

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'store_activity_attr_result';

    use ModelTrait;

    protected $insert = ['change_time'];

    protected static function setChangeTimeAttr($value)
    {
        return time();
    }

    protected static function setResultAttr($value)
    {
        return is_array($value) ? json_encode($value) : $value;
    }

    public static function setResult($result, $activity_id, $type = 0)
    {
        $result = self::setResultAttr($result);
        $change_time = self::setChangeTimeAttr(0);
        $count = self::where('activity_id', $activity_id)->where('type', $type)->count();
        $res = true;
        if ($count) $res = self::where('activity_id', $activity_id)->where('type', $type)->delete();
        if ($res) return self::insert(compact('activity_id', 'result', 'change_time', 'type'), true);
        return $res;
    }

    public static function getResult($activityId, int $type = 0)
    {
        return json_decode(self::where('activity_id', $activityId)->where('type', $type)->value('result'), true) ?: ['value' => []];
    }

    public static function clearResult($activityId)
    {
        return self::where('activity_id', $activityId)->delete();
    }

}