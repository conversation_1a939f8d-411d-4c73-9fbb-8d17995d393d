<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2021-06-29 15:34:37
 * @Last Modified time: 2021-07-19 12:16:30
 */
namespace app\admin\model\market;

use crmeb\basic\BaseModel;
use crmeb\services\SystemConfigService;
use crmeb\services\workerman\ChannelService;
use crmeb\traits\ModelTrait;

class StoreActivityAttrValue extends BaseModel
{

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'store_activity_attr_value';

    use ModelTrait;

    protected $insert = ['unique'];

    protected function setSukAttr($value)
    {
        return is_array($value) ? implode(',', $value) : $value;
    }

    protected function setUniqueAttr($value, $data)
    {

        if (is_array($data['suk'])) $data['suk'] = $this->setSukAttr($data['suk']);
        return $data['unique'] ?: self::uniqueId($data['activity_id'] . $data['suk'] . uniqid(true));
    }

    /*
     * 减少销量增加库存
     * */
    public static function incActivityAttrStock($activityId, $unique, $num, $type = 0)
    {
        $activityAttr = self::where('unique', $unique)->where('activity_id', $activityId)->where('type', $type)->field('stock,sales')->find();
        if (!$activityAttr) return true;
        if ($activityAttr->sales > 0) $activityAttr->sales = bcsub($activityAttr->sales, $num, 0);
        if ($activityAttr->sales < 0) $activityAttr->sales = 0;
        $activityAttr->stock = bcadd($activityAttr->stock, $num, 0);
        return $activityAttr->save();
    }

    public static function decActivityAttrStock($activityId, $unique, $num, $type = 0)
    {
        if ($type == 0) {
            $res = self::where('activity_id', $activityId)->where('unique', $unique)->where('type', $type)
                ->dec('stock', $num)->inc('sales', $num)->update();
        } else {
            $res = self::where('activity_id', $activityId)->where('unique', $unique)->where('type', $type)
                ->dec('stock', $num)->dec('quota', $num)->inc('sales', $num)->update();
        }

        if ($res) {
            $stock = self::where('activity_id', $activityId)->where('unique', $unique)->where('type', $type)->value('stock');
            $replenishment_num = sys_config('store_stock') ?? 0;//库存预警界限
            if ($replenishment_num >= $stock) {
                try {
                    ChannelService::instance()->send('STORE_STOCK', ['id' => $activityId]);
                } catch (\Exception $e) {
                }
            }
        }
        return $res;
    }

    /**
     * 获取属性参数
     * @param $activityId
     * @return array|string
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public static function getStoreActivityAttrResult($activityId)
    {
        $activityAttr = StoreActivityAttr::getActivityAttr($activityId);
        if (!$activityAttr) return [];
        $attr = [];
        foreach ($activityAttr as $key => $value) {
            $attr[$key]['value'] = $value['attr_name'];
            $attr[$key]['detailValue'] = '';
            $attr[$key]['attrHidden'] = true;
            $attr[$key]['detail'] = $value['attr_values'];
        }
        $value = attr_format($attr)[1];
        $valueNew = [];
        $count = 0;
        foreach ($value as $key => $item) {
            $detail = $item['detail'];
            sort($item['detail'], SORT_STRING);
            $suk = implode(',', $item['detail']);
            $sukValue = self::where('activity_id', $activityId)->where('type', 0)->where('suk', $suk)->column('bar_code,cost,price,vip_price,stock as sales,image as pic', 'suk');
            if (!count($sukValue)) {
                unset($value[$key]);
            } else {
                $valueNew[$count]['detail'] = $detail;
                $valueNew[$count]['cost'] = $sukValue[$suk]['cost'];
                $valueNew[$count]['price'] = $sukValue[$suk]['price'];
                $valueNew[$count]['vip_price'] = $sukValue[$suk]['vip_price'];
                $valueNew[$count]['sales'] = $sukValue[$suk]['sales'];
                $valueNew[$count]['pic'] = $sukValue[$suk]['pic'];
                $valueNew[$count]['bar_code'] = $sukValue[$suk]['bar_code'] ?? '';
                $valueNew[$count]['check'] = false;
                $count++;
            }
        }
        return ['attr' => $attr, 'value' => $valueNew];
    }


    public static function uniqueId($key)
    {
        return substr(md5($key), 12, 8);
    }

    public static function clearActivityAttrValue($activityId, $type = 0)
    {
        return self::where('activity_id', $activityId)->where('type', $type)->delete();
    }


}
