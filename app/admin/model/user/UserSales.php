<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2020-07-17 13:48:52
 * @Last Modified time: 2022-04-21 14:42:52
 */

namespace app\admin\model\user;

use app\admin\model\system\SystemUserLevel;
use crmeb\traits\ModelTrait;
use crmeb\basic\BaseModel;
use crmeb\services\{PHPExcelService, WechatTemplateService};

/**
 * 用户消费记录管理 model
 * Class User
 * @package app\admin\model\user
 */
class UserSales extends BaseModel
{
  /**
   * 数据表主键
   * @var string
   */
  protected $pk = 'id';

  /**
   * 模型名称
   * @var string
   */
  protected $name = 'user_level_sales';

  use ModelTrait;

  /**
   * 处理where条件
   * @param $where
   * @param $model
   * @return mixed
   */
  public static function getOrderWhere($where, $model, $aler = '', $join = '')
  {
    $model = $model->where('is_system_del', 0);
    if (isset($where['status']) && $where['status'] != '') {
      $model =  $model->where($aler . 'status', $where['status']);
    }
    if (isset($where['pay_type'])) {
      switch ($where['pay_type']) {
        case 1:
          $model = $model->where($aler . 'pay_type', 'weixin');
          break;
        case 2:
          $model = $model->where($aler . 'pay_type', 'weixinh5');
          break;
        case 3:
          $model = $model->where($aler . 'pay_type', 'alipay');
          break;
      }
    }
    if (isset($where['real_name']) && $where['real_name'] != '') {
      $model = $model->where($aler . 'order_id|' . $join . '.nickname|' . $join . '.uid|' . $join . '.phone', 'LIKE', "%$where[real_name]%");
    }
    if (isset($where['data']) && $where['data'] !== '') {
      $model = self::getModelTime($where, $model, $aler . 'add_time');
    }
    return $model;
  }

  public static function orderCount()
  {
    $data['paid'] = self::where(['paid' => 1, 'status' => 1])->count();
    $data['unpaid'] = self::where(['paid' => 0, 'status' => 0])->count();
    return $data;
  }

  public static function payTypeCount()
  {
    $data['jsapi'] = self::where(['is_system_del' => 0, 'pay_type' => 'weixin'])->count();
    $data['h5pay'] = self::where(['is_system_del' => 0, 'pay_type' => 'weixinh5'])->count();
    $data['alipay'] = self::where(['is_system_del' => 0, 'pay_type' => 'alipay'])->count();
    return $data;
  }

  /**
   * 获取单个用户购买列表
   * @param array $where
   * @return array
   */
  public static function getUserSalesList($where)
  {
    return self::where('uid', $where['uid'])
      ->where('paid', 1)
      ->order('add_time desc')
      ->page((int)$where['page'], (int)$where['limit'])
      ->field([
        'order_id,pay_type,pay_price,FROM_UNIXTIME(pay_time,"%Y-%m-%d %H:%i:%s") as pay_time,paid'
      ])->select()
      ->toArray();
  }

  public static function SalesList($where)
  {
    $model = self::getOrderWhere($where, self::alias('a')
      ->join('user r', 'r.uid=a.uid', 'LEFT'), 'a.', 'r')
      ->field('a.*,r.real_name,r.nickname,r.phone,r.spread_uid');
    $model = $model->order('a.id desc');
    if (isset($where['excel']) && $where['excel'] == 1) {
      $data = ($data = $model->select()) && count($data) ? $data->toArray() : [];
    } else {
      $data = ($data = $model->page((int)$where['page'], (int)$where['limit'])->select()) && count($data) ? $data->toArray() : [];
    }
    foreach ($data as &$item) {
      $item['add_time'] = $item['add_time'] ? date('Y-m-d H:i:s', $item['add_time']) : '';
      $item['pay_time'] = $item['pay_time'] ? date('Y-m-d H:i:s', $item['pay_time']) : '';
      switch ($item['pay_type']) {
        case 'weixin':
        case 'bytedance.weixin':
          $item['pay_type_name'] = '微信支付';
          break;
        case 'weixinh5':
          $item['pay_type_name'] = '微信H5支付';
          break;
        case 'alipay':
        case 'bytedance.alipay':
          $item['pay_type_name'] = '支付宝支付';
          break;
      }
      switch ($item['paid']) {
        case 1:
          $item['status_name'] = '已支付';
          break;
        default:
          $item['status_name'] = '未支付';
          break;
      }
    }
    if (isset($where['excel']) && $where['excel'] == 1) {
      self::SaveExcel($data);
    }
    $count = self::getOrderWhere($where, self::alias('a')->join('user r', 'r.uid=a.uid', 'LEFT'), 'a.', 'r')->count();
    return compact('count', 'data');
  }

  /*
     * 保存并下载excel
     * $list array
     * return
     */
  public static function SaveExcel($list)
  {
    $export = [];
    foreach ($list as $index => $item) {
      $export[] = [
        $item['nickname'],
        $item['order_id'],
        $item['level_name'],
        $item['pay_price'],
        $item['pay_type_name'],
        $item['pay_time'],
        $item['status_name'],
        $item['add_time'],
      ];
    }
    PHPExcelService::instance()->setExcelHeader(['购买人', '订单号', '商品信息', '支付金额', '支付方式', '支付时间', '支付状态', '下单时间'])
      ->setExcelTile('购买会员记录' . date('YmdHis', time()), '购买记录信息' . time(), ' 生成时间：' . date('Y-m-d H:i:s', time()))
      ->setExcelContent($export)
      ->ExcelSave('购买会员记录');
  }
}
