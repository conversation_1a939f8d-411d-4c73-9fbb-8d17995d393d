<?php


namespace app\admin\model\user;

use crmeb\traits\ModelTrait;
use crmeb\basic\BaseModel;

class UserPollster extends BaseModel
{

    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'user_pollster';

    // 设置json类型字段
    protected $json = ['answer_sheet'];


    use ModelTrait;

    public static function getAddTime($uid)
    {
        $data = self::where('uid',$uid)->where('is_del',0)->order('id desc')->value('add_time');
        return $data ? date('Y-m-d H:i:s',$data) : '';
    }



    public static function getWriteRecordList($where)
    {
        $model = self::setWhere($where, self::alias('a'))
            ->field('a.*');
        $model = $model->order('a.id desc');
        $data = ($data = $model->page((int)$where['page'], (int)$where['limit'])->select()) && count($data) ? $data->toArray() : [];
        foreach ($data as &$item) {
            $item['add_time'] = $item['add_time'] ? date('Y-m-d H:i:s', $item['add_time']) : '';
        }
        $count = self::setWhere($where, self::alias('a'))->count();
        return compact('count', 'data');
    }

    /**
     * 处理where条件
     * @param $where
     * @param $model
     * @return mixed
     */
    public static function setWhere($where, $model, $aler = '', $join = '')
    {
        $model = $model->where('is_del',0);
        if (isset($where['id'])) {
            $model = $model->where($aler . 'pollster_id', $where['id']);
        }
        if (isset($where['keywords']) && $where['keywords'] != '') {
            $model = $model->where($aler . 'title｜teaching_content|musical_instrument|issue_info', 'LIKE', "%$where[keywords]%");
        }
        return $model;
    }

    /**
     * 获取调查问卷统计数据
     * @param array $where
     * @return array
     */
    public static function getPollsterStatistics($where)
    {
        try {
            // 获取时间范围
            $timeRange = self::getTimeRange($where['data'] ?? '');
            
            // 基础统计数据
            $baseStats = self::getBaseStatistics($where,$timeRange);
            
            // 身份分布统计
            $identityStats = self::getIdentityStatistics($timeRange);
            
            // 年龄分布统计
            $ageStats = self::getAgeStatistics($timeRange);
            
            // 性别分布统计
            $genderStats = self::getGenderStatistics($timeRange);
            
            // 地区分布统计
            $regionStats = self::getRegionStatistics($timeRange);
            
            // 渠道来源统计
            $channelStats = self::getChannelStatistics($timeRange);
            
            // 填写来源统计
            $sourceStats = self::getSourceStatistics($timeRange);
            
            // 时间趋势统计
            $trendStats = self::getTrendStatistics($where['data'] ?? '');
            
            return [
                'code' => 200,
                'msg' => '统计数据获取成功',
                'data' => [
                    'base' => $baseStats,
                    'identity' => $identityStats,
                    'age' => $ageStats,
                    'gender' => $genderStats,
                    'region' => $regionStats,
                    'channel' => $channelStats,
                    'source' => $sourceStats,
                    'trend' => $trendStats
                ]
            ];
        } catch (\Exception $e) {
            return [
                'code' => 500,
                'msg' => '统计数据获取失败：' . $e->getMessage(),
                'data' => [
                    'base' => ['total' => 0, 'today' => 0, 'user_count' => 0],
                    'identity' => [],
                    'age' => [],
                    'gender' => [],
                    'region' => [],
                    'channel' => [],
                    'source' => [],
                    'trend' => []
                ]
            ];
        }
    }

    /**
     * 获取时间范围
     */
    private static function getTimeRange($data)
    {
        if (empty($data)) {
            // 默认最近30天，包含当天
            return [
                'start' => strtotime(date('Y-m-d') . ' 00:00:00'),
                'end' => strtotime(date('Y-m-d') . ' 23:59:59')
            ];
        }
        
        $dates = explode(' - ', $data);
        if (count($dates) == 2) {
            return [
                'start' => strtotime($dates[0] . ' 00:00:00'),
                'end' => strtotime($dates[1] . ' 23:59:59')
            ];
        }
        
        return [
            'start' => strtotime($data . ' 00:00:00'),
            'end' => strtotime($data . ' 23:59:59')
        ];
    }

    /**
     * 获取基础统计数据
     */
    private static function getBaseStatistics($where, $timeRange)
    {
        // 构建基础查询
        $query = self::where('is_del', 0);
        
        // 如果提供了时间范围，则应用时间过滤
        if ($timeRange !== null) {
            $query->where('add_time', '>=', $timeRange['start'])
                  ->where('add_time', '<=', $timeRange['end']);
        }
        
        // 计算总数
        $total = $query->count();
        
        // 计算今日新增数量
        $todayCount = self::where('is_del', 0)
            ->where('add_time', '>=', strtotime('today'))
            ->count();
        
        // 计算用户数量（基于相同的时间范围过滤）
        $userQuery = self::where('is_del', 0);
        if ($timeRange !== null) {
            $userQuery->where('add_time', '>=', $timeRange['start'])
                      ->where('add_time', '<=', $timeRange['end']);
        }
        $userCount = $userQuery->distinct('uid')->count();
        
        return [
            'total' => $total,
            'today' => $todayCount,
            'user_count' => $userCount
        ];
    }

    /**
     * 获取身份分布统计
     */
    private static function getIdentityStatistics($timeRange)
    {
        $data = self::where('is_del', 0)
            ->where('add_time', '>=', $timeRange['start'])
            ->where('add_time', '<=', $timeRange['end'])
            ->field('answer_sheet')
            ->select()
            ->toArray();
        
        $identityCount = [];
        foreach ($data as $item) {
            $answerSheet = $item['answer_sheet'];
            // 处理不同类型的answer_sheet
            if (is_string($answerSheet)) {
                $answerSheet = json_decode($answerSheet, true);
            } elseif (is_object($answerSheet)) {
                // 将stdClass对象转换为数组
                $answerSheet = json_decode(json_encode($answerSheet), true);
            }
            
            if (is_array($answerSheet)) {
                foreach ($answerSheet as $answer) {
                    if (isset($answer['name']) && strpos($answer['name'], '身份') !== false) {
                        if (isset($answer['value_group']) && is_array($answer['value_group'])) {
                            foreach ($answer['value_group'] as $value) {
                                $identity = $value['name'] ?? '未知';
                                $identityCount[$identity] = ($identityCount[$identity] ?? 0) + 1;
                            }
                        }
                    }
                }
            }
        }
        
        return $identityCount;
    }

    /**
     * 获取年龄分布统计
     */
    private static function getAgeStatistics($timeRange)
    {
        $query = self::where('is_del', 0);
        
        // 如果有时间范围条件，则应用时间过滤
        if ($timeRange && isset($timeRange['start']) && isset($timeRange['end'])) {
            $query = $query->where('add_time', '>=', $timeRange['start'])
                          ->where('add_time', '<=', $timeRange['end']);
        }
        
        $data = $query->field('answer_sheet')
                     ->select()
                     ->toArray();
        
        $ageCount = [];
        foreach ($data as $item) {
            $answerSheet = $item['answer_sheet'];
            // 处理不同类型的answer_sheet
            if (is_string($answerSheet)) {
                $answerSheet = json_decode($answerSheet, true);
            } elseif (is_object($answerSheet)) {
                // 将stdClass对象转换为数组
                $answerSheet = json_decode(json_encode($answerSheet), true);
            }
            
            if (is_array($answerSheet)) {
                foreach ($answerSheet as $answer) {
                    if (isset($answer['name']) && strpos($answer['name'], '年龄') !== false) {
                        if (isset($answer['value_group']) && is_array($answer['value_group'])) {
                            foreach ($answer['value_group'] as $value) {
                                $age = $value['name'] ?? '未知';
                                $ageCount[$age] = ($ageCount[$age] ?? 0) + 1;
                            }
                        }
                    }
                }
            }
        }
        
        return $ageCount;
    }

    /**
     * 获取性别分布统计
     */
    private static function getGenderStatistics($timeRange)
    {
        $query = self::where('is_del', 0);
        
        // 如果有时间范围条件，则应用时间过滤
        if ($timeRange && isset($timeRange['start']) && isset($timeRange['end'])) {
            $query = $query->where('add_time', '>=', $timeRange['start'])
                          ->where('add_time', '<=', $timeRange['end']);
        }
        
        $data = $query->field('answer_sheet')
                     ->select()
                     ->toArray();
        
        $genderCount = [];
        foreach ($data as $item) {
            $answerSheet = $item['answer_sheet'];
            // 处理不同类型的answer_sheet
            if (is_string($answerSheet)) {
                $answerSheet = json_decode($answerSheet, true);
            } elseif (is_object($answerSheet)) {
                // 将stdClass对象转换为数组
                $answerSheet = json_decode(json_encode($answerSheet), true);
            }
            
            if (is_array($answerSheet)) {
                foreach ($answerSheet as $answer) {
                    if (isset($answer['name']) && strpos($answer['name'], '性别') !== false) {
                        if (isset($answer['value_group']) && is_array($answer['value_group'])) {
                            foreach ($answer['value_group'] as $value) {
                                $gender = $value['name'] ?? '未知';
                                $genderCount[$gender] = ($genderCount[$gender] ?? 0) + 1;
                            }
                        }
                    }
                }
            }
        }
        
        return $genderCount;
    }

    /**
     * 获取地区分布统计
     */
    private static function getRegionStatistics($timeRange)
    {
        $data = self::where('is_del', 0)
            ->where('add_time', '>=', $timeRange['start'])
            ->where('add_time', '<=', $timeRange['end'])
            ->field('answer_sheet')
            ->select()
            ->toArray();
        
        $regionCount = [];
        foreach ($data as $item) {
            $answerSheet = $item['answer_sheet'];
            // 处理不同类型的answer_sheet
            if (is_string($answerSheet)) {
                $answerSheet = json_decode($answerSheet, true);
            } elseif (is_object($answerSheet)) {
                // 将stdClass对象转换为数组
                $answerSheet = json_decode(json_encode($answerSheet), true);
            }
            
            if (is_array($answerSheet)) {
                foreach ($answerSheet as $answer) {
                    if (isset($answer['name']) && (strpos($answer['name'], '工作') !== false || strpos($answer['name'], '地区') !== false)) {
                        if (isset($answer['value_group']) && is_array($answer['value_group'])) {
                            foreach ($answer['value_group'] as $value) {
                                if (isset($value['value']) && is_string($value['value'])) {
                                    $address = $value['value'];
                                    $region = '';
                                    
                                    // 改进的地区解析逻辑
                                    if (preg_match('/^(.*?)(省|市|自治区|特别行政区)/', $address, $matches)) {
                                        $region = $matches[1];
                                    } elseif (preg_match('/^(北京|上海|天津|重庆)/', $address, $matches)) {
                                        $region = $matches[1];
                                    } else {
                                        // 提取前2-3个字符作为地区
                                        $region = mb_substr($address, 0, 3);
                                        if (mb_strlen($region) > 2 && mb_substr($region, -1) === '市') {
                                            $region = mb_substr($region, 0, -1);
                                        }
                                    }
                                    
                                    if (!empty($region) && $region !== '国内' && $region !== '国外') {
                                        $regionCount[$region] = ($regionCount[$region] ?? 0) + 1;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        
        return $regionCount;
    }

    /**
     * 获取渠道来源统计
     */
    private static function getChannelStatistics($timeRange)
    {
        $data = self::where('is_del', 0)
            ->where('add_time', '>=', $timeRange['start'])
            ->where('add_time', '<=', $timeRange['end'])
            ->field('answer_sheet')
            ->select()
            ->toArray();
        
        $channelCount = [];
        foreach ($data as $item) {
            $answerSheet = $item['answer_sheet'];
            // 处理不同类型的answer_sheet
            if (is_string($answerSheet)) {
                $answerSheet = json_decode($answerSheet, true);
            } elseif (is_object($answerSheet)) {
                // 将stdClass对象转换为数组
                $answerSheet = json_decode(json_encode($answerSheet), true);
            }
            
            if (is_array($answerSheet)) {
                foreach ($answerSheet as $answer) {
                    if (isset($answer['name']) && strpos($answer['name'], '了解') !== false) {
                        if (isset($answer['value_group']) && is_array($answer['value_group'])) {
                            foreach ($answer['value_group'] as $value) {
                                $channel = $value['name'] ?? '未知';
                                $channelCount[$channel] = ($channelCount[$channel] ?? 0) + 1;
                            }
                        }
                    }
                }
            }
        }
        
        return $channelCount;
    }

    /**
     * 获取时间趋势统计
     */
    private static function getTrendStatistics($data)
    {
        $timeRange = self::getTimeRange($data);
        // 修复天数计算，确保包含开始和结束日期
        $days = ceil(($timeRange['end'] - $timeRange['start']) / 86400) + 1;
        
        $trendData = [];
        for ($i = 0; $i < $days; $i++) {
            $date = date('Y-m-d', $timeRange['start'] + $i * 86400);
            $dayStart = strtotime($date . ' 00:00:00');
            $dayEnd = strtotime($date . ' 23:59:59');
            
            $count = self::where('is_del', 0)
                ->where('add_time', '>=', $dayStart)
                ->where('add_time', '<=', $dayEnd)
                ->count();
            
            $trendData[] = [
                'date' => $date,
                'count' => $count
            ];
        }
        
        return $trendData;
    }

    /**
     * 获取来源分布统计
     */
    private static function getSourceStatistics($timeRange)
    {
        $query = self::where('is_del', 0);
        
        // 如果有时间范围条件，则应用时间过滤
        if ($timeRange && isset($timeRange['start']) && isset($timeRange['end'])) {
            $query = $query->where('add_time', '>=', $timeRange['start'])
                          ->where('add_time', '<=', $timeRange['end']);
        }
        
        $data = $query->field('type, interlink_id, COUNT(*) as count')
                     ->group('type, interlink_id')
                     ->select()
                     ->toArray();
        
        $sourceCount = [];
        $typeNames = [
            1 => '系统',
            2 => '商品', 
            3 => '课程'
        ];
        
        foreach ($data as $item) {
            $typeName = $typeNames[$item['type']] ?? '未知类型';
            $sourceName = $typeName;
            
            // 根据类型获取具体名称
            if ($item['type'] == 2 && $item['interlink_id'] > 0) {
                // 查询商品名称
                $product = \app\admin\model\store\StoreProduct::where('id', $item['interlink_id'])
                    ->field('store_name')
                    ->find();
                if ($product) {
                    $sourceName = '【商品】-' . $product['store_name'];
                } else {
                    $sourceName = $typeName . '(ID:' . $item['interlink_id'] . ')';
                }
            } elseif ($item['type'] == 3 && $item['interlink_id'] > 0) {
                // 查询课程标题
                $special = \app\admin\model\special\Special::where('id', $item['interlink_id'])
                    ->field('title')
                    ->find();
                if ($special) {
                    $sourceName = '【课程】' . $special['title'];
                } else {
                    $sourceName = $typeName . '(ID:' . $item['interlink_id'] . ')';
                }
            } elseif ($item['interlink_id'] > 0) {
                $sourceName = $typeName . '(ID:' . $item['interlink_id'] . ')';
            }
            
            $sourceCount[$sourceName] = $item['count'];
        }
        
        return $sourceCount;
    }
}