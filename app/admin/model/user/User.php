<?php

/**
 * @author: xaboy<<EMAIL>>
 * @day: 2017/11/11
 */

namespace app\admin\model\user;

use crmeb\basic\BaseModel;
use crmeb\traits\ModelTrait;
use crmeb\services\PHPExcelService;
use app\admin\model\special\Special;
use app\admin\model\special\SpecialBuy;
use app\admin\model\special\SpecialRecord;
use app\admin\model\order\StoreOrder;
use app\admin\model\wechat\WechatUser;
use app\admin\model\store\StoreCouponUser;
use app\admin\model\special\SpecialMessage;
use app\admin\model\system\SystemUserLevel;
use app\admin\model\order\StoreOrderCartInfo;

/**
 * 用户管理 model
 * Class User
 * @package app\admin\model\user
 */
class User extends BaseModel
{

  /**
   * 数据表主键
   * @var string
   */
  protected $pk = 'uid';

  /**
   * 模型名称
   * @var string
   */
  protected $name = 'user';

  use ModelTrait;

  /*
     * 获取和提现金额
     * @param array $uid
     * @return float
     * */
  public static function getextractPrice($uid, $where = [])
  {
    if (is_array($uid)) {
      if (!count($uid)) return 0;
    } else
      $uid = [$uid];
    $brokerage = UserBill::getBrokerage($uid, 'now_money', 'brokerage', $where); //获取总佣金
    $recharge = UserBill::getBrokerage($uid, 'now_money', 'recharge', $where); //累计充值
    $extractTotalPrice = UserExtract::userExtractTotalPrice($uid, 1, $where); //累计提现
    if ($brokerage > $extractTotalPrice) {
      $orderYuePrice = self::getModelTime($where, StoreOrder::where('uid', 'in', $uid)->where(['is_del' => 0, 'paid' => 1]))->sum('pay_price'); //余额累计消费
      $systemAdd = UserBill::getBrokerage($uid, 'now_money', 'system_add', $where); //后台添加余额
      $yueCount = bcadd($recharge, $systemAdd, 2); // 后台添加余额 + 累计充值  = 非佣金的总金额
      $orderYuePrice = $yueCount > $orderYuePrice ? 0 : bcsub($orderYuePrice, $yueCount, 2); // 余额累计消费（使用佣金消费的金额）
      $brokerage = bcsub($brokerage, $extractTotalPrice, 2); //减去已提现金额
      $extract_price = UserExtract::userExtractTotalPrice($uid, 0, $where);
      $brokerage = $extract_price < $brokerage ? bcsub($brokerage, $extract_price, 2) : 0; //减去审核中的提现金额
      $brokerage = $brokerage > $orderYuePrice ? bcsub($brokerage, $orderYuePrice, 2) : 0; //减掉余额支付
    } else {
      $brokerage = 0;
    }
    $num = (float)bcsub($brokerage, $extractTotalPrice, 2);
    return $num > 0 ? $num : 0; //可提现
  }

  /**
   * @param $where
   * @return array
   */
  public static function systemPage($where)
  {
    $model = new self;
    if ($where['status'] != '') $model = $model->where('status', $where['status']);
    if ($where['is_promoter'] != '') $model = $model->where('is_promoter', $where['is_promoter']);
    if (isset($where['user_type']) && $where['user_type'] != '') $model = $model->where('user_type', $where['user_type']);
    if ($where['nickname'] != '') $model = $model->where('nickname|uid', 'like', "%$where[nickname]%");
    $model = $model->order('uid desc');
    return self::page($model, function ($item) {
      if ($item['spread_uid']) {
        $item['spread_uid_nickname'] = self::where('uid', $item['spread_uid'])->value('nickname');
      } else {
        $item['spread_uid_nickname'] = '无';
      }
    }, $where);
  }

  /*
     * 设置搜索条件
     *
     */
  public static function setWhere($where)
  {
    if ($where['order'] != '') {
      $model = self::order(self::setOrder($where['order']));
    } else {
      $model = self::order('u.uid desc');
    }
    if ($where['role'] !== '') {
      if ($where['role'] == '1') $model = $model->where('role', '>', 0);
    }
    if ($where['user_time_type'] == 'visitno' && $where['user_time'] != '') {
      list($startTime, $endTime) = explode(' - ', $where['user_time']);
      $endTime = strtotime($endTime) + 24 * 3600;
      $model = $model->where("u.last_time < " . strtotime($startTime) . " OR u.last_time > " . $endTime);
    }
    if ($where['user_time_type'] == 'visit' && $where['user_time'] != '') {
      list($startTime, $endTime) = explode(' - ', $where['user_time']);
      $model = $model->where('u.last_time', '>', strtotime($startTime));
      $model = $model->where('u.last_time', '<', strtotime($endTime) + 24 * 3600);
    }
    if ($where['user_time_type'] == 'add_time' && $where['user_time'] != '') {
      list($startTime, $endTime) = explode(' - ', $where['user_time']);
      $model = $model->where('u.add_time', '>', strtotime($startTime));
      $model = $model->where('u.add_time', '<', strtotime($endTime) + 24 * 3600);
    }
    if ($where['pay_count'] !== '') {
      if ($where['pay_count'] == '-1') $model = $model->where('pay_count', 0);
      else $model = $model->where('pay_count', '>', $where['pay_count']);
    }
    if ($where['comment_count'] !== '') {
      if ($where['comment_count'] == '-1') $model = $model->where('comment_count', 0);
      else $model = $model->where('comment_count', '>', $where['comment_count']);
    }
    if ($where['study_time_count'] !== '') {
      if ($where['study_time_count'] == '-1') $model = $model->where('study_time_count', 0);
      else $model = $model->where('study_time_count', '>', $where['study_time_count']);
    }
    if ($where['user_type'] != '') {
      $model = $model->where('u.user_type', $where['user_type']);
    }
    if (isset($where['app_name']) && $where['app_name'] != '') {
      $model = $model->where('u.app_name', $where['app_name']);
    }
    if ($where['country'] != '') {
      if ($where['country'] == 'domestic') $model = $model->where('w.country', '中国');
      else if ($where['country'] == 'abroad') $model = $model->where('w.country', '<>', '中国');
    }
    if ($where['level'] !== '') {
      $model = $model->where('level', $where['level'])->where('clean_time', 0);
    }
    if ($where['group_id'] !== '') {
      $model = $model->where('group_id', $where['group_id']);
    }
    // 调查问卷 
    if ($where['create_pollster_status'] == 1) { //是否填写
      $model = $model->join('UserPollster p', 'u.uid=p.uid')->group('p.uid');
    }
    if ($where['create_pollster_time'] != '') {
      list($startTime, $endTime) = explode(' - ', $where['create_pollster_time']);
      $model = $model->join('UserPollster pp', 'u.uid=pp.uid');
      $model = $model->where('p.add_time', '>', strtotime($startTime));
      $model = $model->where('p.add_time', '<', strtotime($endTime) + 24 * 3600);
    }
    return $model;
  }

  /**
   * 异步获取当前用户 信息
   * @param $where
   * @return array
   */
  public static function getUserList($where)
  {
    $model = self::setWherePage(self::setWhere($where), $where, ['w.sex', 'w.province', 'w.city', 'u.status', 'u.is_promoter'], ['u.nickname', 'u.uid', 'u.phone', 'w.unionid']);
    $list = $model->alias('u')
      ->join('WechatUser w', 'u.uid=w.uid')
      ->field('u.*')
      ->page((int)$where['page'], (int)$where['limit'])
      ->select()
      ->each(function ($item) {
        $item['add_time'] = date('Y-m-d H:i:s', $item['add_time']);
        if ($item['last_time']) $item['last_time'] = date('Y-m-d H:i:s', $item['last_time']); //最近一次访问日期
        else $item['last_time'] = '无访问'; //最近一次访问日期
        self::edit(['pay_count' => StoreOrder::getUserCountPay($item['uid'])], $item['uid']);
        $item['extract_count_price'] = UserExtract::getUserCountPrice($item['uid']); //累计提现
        if ($item['spread_uid']) {
          $item['spread_uid_nickname'] = self::where('uid', $item['spread_uid'])->value('nickname') . '/' . $item['spread_uid'];
        } else {
          $item['spread_uid_nickname'] = '无';
        }
        if ($item['openid'] != '' && $item['routine_openid'] != '') {
          $item['user_type'] = '通用';
        } else if ($item['user_type'] == 'routine') {
          $item['user_type'] = '小程序';
        } else if ($item['user_type'] == 'bytedance') {
          $item['user_type'] = '字节小程序';
        } else if ($item['user_type']  == 'wechat') {
          $item['user_type'] = '公众号';
        } else if ($item['user_type'] == 'h5') {
          $item['user_type'] = 'H5';
        } else $item['user_type'] = '其他';
        $item['app_name'] = self::getAppName($item['app_name']);
        if ($item['sex'] == 1) {
          $item['sex'] = '男';
        } else if ($item['sex'] == 2) {
          $item['sex'] = '女';
        } else $item['sex'] = '保密';
        $item['vip_name'] = '';
        $levelinfo = UserLevel::where('uid', $item['uid'])->where('is_del', 0)->order('grade desc')->field('level_id,is_forever,valid_time')->find();
        if ($levelinfo) {
          if ($levelinfo['is_forever']) $item['vip_name'] = SystemUserLevel::where('id', $levelinfo['level_id'])->value('name');
          else if (time() < $levelinfo['valid_time']) $item['vip_name'] = SystemUserLevel::where('id', $levelinfo['level_id'])->value('name');
        }
        $create_user_pollster_at =  UserPollster::where('uid', $item['uid'])->where('is_del', 0)->order('id desc')->value('add_time');
        $item['pollster_time'] = $create_user_pollster_at ? date('Y-m-d H:i:s', $create_user_pollster_at) : '';
        $item['unionid'] = WechatUser::where('uid', $item['uid'])->value('unionid');
        // 二次数字计算 
        $item['comment_count'] = $item['comment_count'];
        $item['study_time_count'] = secondChanage($item['study_time_count']);
      }); //->toArray();
    $count = self::setWherePage(self::setWhere($where), $where, ['w.sex', 'w.province', 'w.city', 'u.status', 'u.is_promoter'], ['u.nickname', 'u.uid', 'u.phone', 'w.unionid'])->alias('u')->join('WechatUser w', 'u.uid=w.uid')->count();
    return ['count' => $count, 'data' => $list];
  }


  /**
   * 异步获取当前用户 信息
   * @param $where
   * @return array
   */
  public static function ExportUserList($uids)
  {
    $list = self::alias('u')->field('u.*')
      ->where('uid', 'in', $uids)
      ->select();
    self::SaveExcel($list);
  }

  /**
   *  修改用户状态
   * @param $uids 用户uid
   * @param $status 修改状态
   * @return array
   */
  public static function destrSyatus($uids, $status)
  {
    if (empty($uids) && !is_array($uids)) return false;
    if ($status == '') return false;
    self::beginTrans();
    try {
      $res = self::where('uid', 'in', $uids)->update(['status' => $status]);
      self::checkTrans($res);
      return true;
    } catch (\Exception $e) {
      self::rollbackTrans();
      return Json::fail($e->getMessage());
    }
  }

  /*
     *  获取某季度,某年某年后的时间戳
     *
     * self::getMonth('n',1) 获取当前季度的上个季度的时间戳
     * self::getMonth('n') 获取当前季度的时间戳
     */
  public static function getMonth($time = '', $ceil = 0)
  {
    if (empty($time)) {
      $firstday = date("Y-m-01", time());
      $lastday = date("Y-m-d", strtotime("$firstday +1 month -1 day"));
    } else if ($time == 'n') {
      if ($ceil != 0)
        $season = ceil(date('n') / 3) - $ceil;
      else
        $season = ceil(date('n') / 3);
      $firstday = date('Y-m-01', mktime(0, 0, 0, ($season - 1) * 3 + 1, 1, date('Y')));
      $lastday = date('Y-m-t', mktime(0, 0, 0, $season * 3, 1, date('Y')));
    } else if ($time == 'y') {
      $firstday = date('Y-01-01');
      $lastday = date('Y-12-31');
    } else if ($time == 'h') {
      $firstday = date('Y-m-d', strtotime('this week +' . $ceil . ' day')) . ' 00:00:00';
      $lastday = date('Y-m-d', strtotime('this week +' . ($ceil + 1) . ' day')) . ' 23:59:59';
    }
    return array($firstday, $lastday);
  }

  public static function getcount()
  {
    return self::count();
  }

  /*
    *获取用户某个时间段的消费信息
    *
    * reutrn Array || number
    */
  public static function consume($where, $status = '', $keep = '')
  {
    $model = new self;
    $user_id = [];
    if (is_array($where)) {
      if ($where['is_promoter'] != '') $model = $model->where('is_promoter', $where['is_promoter']);
      if ($where['status'] != '') $model = $model->where('status', $where['status']);
      switch ($where['date']) {
        case null:
        case 'today':
        case 'week':
        case 'year':
          if ($where['date'] == null) {
            $where['date'] = 'month';
          }
          if ($keep) {
            $model = $model->whereTime('add_time', $where['date'])->whereTime('last_time', $where['date']);
          } else {
            $model = $model->whereTime('add_time', $where['date']);
          }
          break;
        case 'quarter':
          $quarter = self::getMonth('n');
          $startTime = strtotime($quarter[0]);
          $endTime = strtotime($quarter[1]);
          if ($keep) {
            $model = $model->where('add_time', '>', $startTime)->where('add_time', '<', $endTime)->where('last_time', '>', $startTime)->where('last_time', '<', $endTime);
          } else {
            $model = $model->where('add_time', '>', $startTime)->where('add_time', '<', $endTime);
          }
          break;
        default:
          //自定义时间
          if (strstr($where['date'], '-') !== FALSE) {
            list($startTime, $endTime) = explode('-', $where['date']);
            $model = $model->where('add_time', '>', strtotime($startTime))->where('add_time', '<', bcadd(strtotime($endTime), 86400, 0));
          } else {
            $model = $model->whereTime('add_time', 'month');
          }
          break;
      }
    } else {
      if (is_array($status)) {
        $model = $model->where('add_time', '>', $status[0])->where('add_time', '<', $status[1]);
      }
    }
    if ($keep === true) {
      return $model->count();
    }
    if ($status === 'default') {
      return $model->group('from_unixtime(add_time,\'%Y-%m-%d\')')->field('count(uid) num,from_unixtime(add_time,\'%Y-%m-%d\') add_time,uid')->select()->toArray();
    }
    if ($status === 'grouping') {
      return $model->group('user_type')->field('user_type')->select()->toArray();
    }
    $uid = $model->field('uid')->select()->toArray();
    foreach ($uid as $val) {
      $user_id[] = $val['uid'];
    }
    if (empty($user_id)) {
      $user_id = [0];
    }
    if ($status === 'xiaofei') {
      $list = UserBill::where('uid', 'in', $user_id)
        ->group('type')
        ->field('sum(number) as top_number,title')
        ->select()
        ->toArray();
      $series = [
        'name' => isset($list[0]['title']) ? $list[0]['title'] : '',
        'type' => 'pie',
        'radius' => ['40%', '50%'],
        'data' => []
      ];
      foreach ($list as $key => $val) {
        $series['data'][$key]['value'] = $val['top_number'];
        $series['data'][$key]['name'] = $val['title'];
      }
      return $series;
    } else if ($status === 'form') {
      $list = WechatUser::where('uid', 'in', $user_id)->group('city')->field('count(city) as top_city,city')->limit(0, 10)->select()->toArray();
      $count = self::getcount();
      $option = [
        'legend_date' => [],
        'series_date' => []
      ];
      foreach ($list as $key => $val) {
        $num = $count != 0 ? (bcdiv($val['top_city'], $count, 2)) * 100 : 0;
        $t = ['name' => $num . '%  ' . (empty($val['city']) ? '未知' : $val['city']), 'icon' => 'circle'];
        $option['legend_date'][$key] = $t;
        $option['series_date'][$key] = ['value' => $num, 'name' => $t['name']];
      }
      return $option;
    } else {
      $number = UserBill::where('uid', 'in', $user_id)->where('type', 'pay_product')->sum('number');
      return $number;
    }
  }

  /*
     * 获取 用户某个时间段的钱数或者TOP20排行
     *
     * return Array  || number
     */
  public static function getUserSpend($date, $status = '')
  {
    $model = new self();
    $model = $model->alias('A');
    switch ($date) {
      case null:
      case 'today':
      case 'week':
      case 'year':
        if ($date == null) $date = 'month';
        $model = $model->whereTime('A.add_time', $date);
        break;
      case 'quarter':
        list($startTime, $endTime) = User::getMonth('n');
        $model = $model->where('A.add_time', '>', strtotime($startTime));
        $model = $model->where('A.add_time', '<', bcadd(strtotime($endTime), 86400, 0));
        break;
      default:
        list($startTime, $endTime) = explode('-', $date);
        $model = $model->where('A.add_time', '>', strtotime($startTime));
        $model = $model->where('A.add_time', '<', bcadd(strtotime($endTime), 86400, 0));
        break;
    }
    if ($status === true) {
      return $model->join('user_bill B', 'B.uid=A.uid')->where('B.type', 'pay_product')->where('B.pm', 0)->sum('B.number');
    }
    $list = $model->join('user_bill B', 'B.uid=A.uid')
      ->where('B.type', 'pay_product')
      ->where('B.pm', 0)
      ->field('sum(B.number) as totel_number,A.nickname,A.avatar,A.now_money,A.uid,A.add_time')
      ->order('totel_number desc')
      ->limit(0, 20)
      ->select()
      ->toArray();
    if (!isset($list[0]['totel_number'])) {
      $list = [];
    }
    return $list;
  }

  /*
     * 获取 相对于上月或者其他的数据
     *
     * return Array
     */
  public static function getPostNumber($date, $status = false, $field = 'A.add_time', $t = '消费')
  {
    $model = new self();
    if (!$status) $model = $model->alias('A');
    switch ($date) {
      case null:
      case 'today':
      case 'week':
      case 'year':
        if ($date == null) {
          $date = 'last month';
          $title = '相比上月用户' . $t . '增长';
        }
        if ($date == 'today') {
          $date = 'yesterday';
          $title = '相比昨天用户' . $t . '增长';
        }
        if ($date == 'week') {
          $date = 'last week';
          $title = '相比上周用户' . $t . '增长';
        }
        if ($date == 'year') {
          $date = 'last year';
          $title = '相比去年用户' . $t . '增长';
        }
        $model = $model->whereTime($field, $date);
        break;
      case 'quarter':
        $title = '相比上季度用户' . $t . '增长';
        list($startTime, $endTime) = User::getMonth('n', 1);
        $model = $model->where($field, '>', $startTime);
        $model = $model->where($field, '<', $endTime);
        break;
      default:
        list($startTime, $endTime) = explode('-', $date);
        $title = '相比' . $startTime . '-' . $endTime . '时间段用户' . $t . '增长';
        $Time = strtotime($endTime) - strtotime($startTime);
        $model = $model->where($field, '>', strtotime($startTime) + $Time);
        $model = $model->where($field, '<', strtotime($endTime) + $Time);
        break;
    }
    if ($status) {
      return [$model->count(), $title];
    }
    $number = $model->join('user_bill B', 'B.uid=A.uid')->where('B.type', 'pay_product')->where('B.pm', 0)->sum('B.number');
    return [$number, $title];
  }

  //获取用户新增,头部信息
  public static function getBadgeList($where)
  {
    $user_count = self::setWherePage(self::getModelTime($where, new self), $where, ['is_promoter', 'status'])->count();
    $user_count_old = self::getOldDate($where)->count();
    $store_brokerage_statu = sys_config('store_brokerage_statu');
    if ($store_brokerage_statu == 1)
      $fenxiao = self::setWherePage(self::getModelTime($where, new self), $where, ['is_promoter', 'status'])->where('spread_uid', '<>', 0)->count();
    else
      $fenxiao = self::count();
    $fenxiao_count = self::getOldDate($where)->where('spread_uid', '<>', 0)->count();
    $newFemxiao_count = bcsub($fenxiao, $fenxiao_count, 0);
    $order_count = bcsub($user_count, $user_count_old, 0);
    return [
      [
        'name' => '会员人数',
        'field' => '个',
        'count' => $user_count,
        'content' => '会员总人数',
        'background_color' => 'layui-bg-blue',
        'sum' => self::count(),
        'class' => 'fa fa-bar-chart',
      ],
      [
        'name' => '会员增长',
        'field' => '个',
        'count' => $order_count,
        'content' => '会员增长率',
        'background_color' => 'layui-bg-cyan',
        'sum' => $user_count_old ? bcdiv($order_count, $user_count_old, 2) * 100 : 0,
        'class' => 'fa fa-line-chart',
      ],
      [
        'name' => '分销人数',
        'field' => '个',
        'count' => $fenxiao,
        'content' => '分销总人数',
        'background_color' => 'layui-bg-green',
        'sum' => $store_brokerage_statu == 1 ? self::where('spread_uid', '<>', 0)->count() : $fenxiao,
        'class' => 'fa fa-bar-chart',
      ],
      [
        'name' => '分销增长',
        'field' => '个',
        'count' => $newFemxiao_count,
        'content' => '分销总人数',
        'background_color' => 'layui-bg-orange',
        'sum' => $fenxiao_count ? bcdiv($newFemxiao_count, $fenxiao_count, 2) * 100 : 0,
        'class' => 'fa fa-cube',
      ],
    ];
  }

  /*
     * 获取会员增长曲线图和分布图
     *  $where 查询条件
     *  $limit 显示条数,是否有滚动条
     */
  public static function getUserChartList($where, $limit = 20)
  {
    $list = self::setWherePage(self::getModelTime($where, new self), $where, ['is_promoter', 'status'])
      ->where('add_time', '<>', 0)
      ->field('FROM_UNIXTIME(add_time,"%Y-%m-%d") as _add_time,count(uid) as num')
      ->order('_add_time asc')
      ->group('_add_time')
      ->select();
    count($list) && $list = $list->toArray();
    $seriesdata = [];
    $xdata = [];
    $Zoom = '';
    foreach ($list as $item) {
      $seriesdata[] = $item['num'];
      $xdata[] = $item['_add_time'];
    }
    (count($xdata) > $limit) && $Zoom = $xdata[$limit - 5];
    //多次购物会员数量饼状图
    $count = self::setWherePage(self::getModelTime($where, new self), $where, ['is_promoter'])->count();
    $user_count = self::setWherePage(self::getModelTime($where, self::alias('a')->join('store_order r', 'r.uid=a.uid'), 'a.add_time'), $where, ['is_promoter'])
      ->where('r.paid', 1)->count('a.uid');
    $shop_xdata = ['多次购买数量占比', '无购买数量占比'];
    $shop_data = [];
    $count > 0 && $shop_data = [
      [
        'value' => bcdiv($user_count, $count, 2) * 100,
        'name' => $shop_xdata[0],
        'itemStyle' => [
          'color' => '#D789FF',
        ]
      ],
      [
        'value' => bcdiv($count - $user_count, $count, 2) * 100,
        'name' => $shop_xdata[1],
        'itemStyle' => [
          'color' => '#7EF0FB',
        ]
      ]
    ];
    return compact('shop_data', 'shop_xdata', 'seriesdata', 'Zoom');
  }

  //获取$date的前一天或者其他的时间段
  public static function getOldDate($where, $moedls = null)
  {
    $model = $moedls === null ? self::setWherePage(new self(), $where, ['is_promoter', 'status']) : $moedls;
    switch ($where['data']) {
      case 'today':
        $model = $model->whereTime('add_time', 'yesterday');
        break;
      case 'week':
        $model = $model->whereTime('add_time', 'last week');
        break;
      case 'month':
        $model = $model->whereTime('add_time', 'last month');
        break;
      case 'year':
        $model = $model->whereTime('add_time', 'last year');
        break;
      case 'quarter':
        $time = self::getMonth('n', 1);
        $model = $model->where('add_time', 'between', $time);
        break;
    }
    return $model;
  }

  //获取用户属性和性别分布图
  public static function getEchartsData($where)
  {
    $model = self::alias('a');
    $data = self::getModelTime($where, $model, 'a.add_time')
      ->join('wechat_user r', 'r.uid=a.uid')
      ->group('r.province')
      ->field('count(r.province) as count,province')
      ->order('count desc')
      ->limit(15)
      ->select();
    if (count($data)) $data = $data->toArray();
    $legdata = [];
    $dataList = [];
    foreach ($data as $value) {
      $value['province'] == '' && $value['province'] = '未知省份';
      $legdata[] = $value['province'];
      $dataList[] = $value['count'];
    }
    $model = self::alias('a');
    $sex = self::getModelTime($where, $model, 'a.add_time')
      ->join('wechat_user r', 'r.uid=a.uid')
      ->group('r.sex')
      ->field('count(r.uid) as count,sex')
      ->order('count desc')
      ->select();
    if (count($sex)) $sex = $sex->toArray();
    $sexlegdata = ['男', '女', '未知'];
    $sexcount = self::getModelTime($where, new self())->count();
    $sexList = [];
    $color = ['#FB7773', '#81BCFE', '#91F3FE'];
    foreach ($sex as $key => $item) {
      if ($item['sex'] == 1) {
        $item_date['name'] = '男';
      } else if ($item['sex'] == 2) {
        $item_date['name'] = '女';
      } else {
        $item_date['name'] = '未知性别';
      }
      $item_date['value'] = bcdiv($item['count'], $sexcount, 2) * 100;
      $item_date['itemStyle']['color'] = $color[$key];
      $sexList[] = $item_date;
    }
    return compact('sexList', 'sexlegdata', 'legdata', 'dataList');
  }

  //获取佣金记录列表
  public static function getCommissionList($where)
  {
    $model = self::setCommissionWhere($where);
    if ($where['excel'])
      $list = $model->select();
    else
      $list = $model->page((int)$where['page'], (int)$where['limit'])->select();
    count($list) && $list = $list->toArray();
    $export = [];
    foreach ($list as &$value) {
      //退款退的佣金 -
      $refund_commission = UserBill::where(['uid' => $value['uid'], 'category' => 'now_money', 'type' => 'brokerage'])
        ->where('pm', 0)
        ->sum('number');
      if ($value['sum_number'] > $refund_commission)
        $value['sum_number'] = bcsub($value['sum_number'], $refund_commission, 2);
      else
        $value['sum_number'] = 0;
      $value['ex_price'] = UserExtract::where('uid', $value['uid'])->sum('extract_price');
      $value['extract_price'] = UserExtract::where('uid', $value['uid'])->where('status', 1)->sum('extract_price');
      $cashPrice = UserExtract::where('uid', $value['uid'])->where('status', 0)->sum('extract_price');
      $value['money'] = bcsub($value['ex_price'], $value['extract_price'], 2);
      $value['money'] = bcsub($value['money'], $cashPrice, 2);
      $export[] = [
        $value['nickname'],
        $value['sum_number'],
        $value['now_money'],
        $value['brokerage_price'],
        $value['extract_price'],
      ];
    }
    if ($where['excel']) {


      PHPExcelService::instance()->setExcelHeader(['昵称/姓名', '总佣金金额', '账户余额', '账户佣金', '提现到账佣金'])
        ->setExcelTile('拥金记录', '拥金记录' . time(), ' 生成时间：' . date('Y-m-d H:i:s', time()))
        ->setExcelContent($export)
        ->ExcelSave('拥金记录');
    }
    $count = self::setCommissionWhere($where)->count();
    return ['data' => $list, 'count' => $count];
  }

  //获取佣金记录列表的查询条件
  public static function setCommissionWhere($where)
  {
    $models = self::setWherePage(self::alias('A'), $where, [], ['A.nickname', 'A.uid'])
      ->join('user_bill B', 'B.uid=A.uid')
      ->group('A.uid')
      ->where('B.type', 'brokerage')
      ->where('B.pm', 1)
      ->where('B.category', 'now_money')
      ->field('sum(B.number) as sum_number,A.nickname,A.uid,A.now_money,A.brokerage_price');
    if ($where['order'] == '') {
      $models = $models->order('sum_number desc');
    } else {
      $models = $models->order($where['order'] == 1 ? 'sum_number desc' : 'sum_number asc');
    }
    if ($where['price_max'] != '' && $where['price_min'] != '') {
      $models = $models->where('now_money', 'between', [$where['price_max'], $where['price_min']]);
    }
    return $models;
  }

  /**获取用户详细信息
   * @param $uid
   * @return array
   */
  public static function getUserInfos($uid)
  {
    $userInfo = self::where('uid', $uid)->find();
    if (!$userInfo) exception('读取用户信息失败!');
    return $userInfo->toArray();
  }

  //获取某人用户推广信息
  public static function getUserinfo($uid)
  {
    $userinfo = self::where('uid', $uid)->field('nickname,spread_uid,now_money,add_time')->find()->toArray();
    $userinfo['number'] = (float)UserBill::where('category', 'now_money')->where('uid', $uid)->where('pm', 1)->where('type', 'brokerage')->sum('number');
    //退款退的佣金 -
    $refund_commission = UserBill::where(['uid' => $uid, 'category' => 'now_money', 'type' => 'brokerage'])
      ->where('pm', 0)
      ->sum('number');
    if ($userinfo['number'] > $refund_commission)
      $userinfo['number'] = bcsub($userinfo['number'], $refund_commission, 2);
    else
      $userinfo['number'] = 0;
    $userinfo['spread_name'] = $userinfo['spread_uid'] ? self::where('uid', $userinfo['spread_uid'])->value('nickname') : '';
    return $userinfo;
  }

  //获取某用户的详细信息
  public static function getUserDetailed($uid)
  {
    $key_field = ['real_name', 'phone', 'province', 'city', 'district', 'detail', 'post_code'];
    $Address = ($thisAddress = UserAddress::where('uid', $uid)->where('is_default', 1)->field($key_field)->find()) ?
      $thisAddress :
      UserAddress::where('uid', $uid)->field($key_field)->find();
    $UserInfo = self::get($uid);
    return [
      ['col' => 12, 'name' => '默认收货地址', 'value' => $thisAddress ? '收货人:' . $thisAddress['real_name'] . '邮编:' . $thisAddress['post_code'] . ' 收货人电话:' . $thisAddress['phone'] . ' 地址:' . $thisAddress['province'] . ' ' . $thisAddress['city'] . ' ' . $thisAddress['district'] . ' ' . $thisAddress['detail'] : ''],
      //            ['name'=>'微信OpenID','value'=>WechatUser::where('uid', $uid)->value('openid'),'col'=>8],
      ['name' => '手机号码', 'value' => $UserInfo['phone']],
      //            ['name'=>'ID','value'=>$uid],
      ['name' => '姓名', 'value' => ''],
      ['name' => '昵称', 'value' => $UserInfo['nickname']],
      ['name' => '邮箱', 'value' => ''],
      ['name' => '生日', 'value' => ''],
      ['name' => '积分', 'value' => $UserInfo['integral']],
      ['name' => '上级推广人', 'value' => $UserInfo['spread_uid'] ? self::where('uid', $UserInfo['spread_uid'])->value('nickname') : ''],
      ['name' => '账户余额', 'value' => $UserInfo['now_money']],
      ['name' => '佣金总收入', 'value' => UserBill::where('category', 'now_money')->where('type', 'brokerage')->where('uid', $uid)->sum('number')],
      ['name' => '提现总金额', 'value' => UserExtract::where('uid', $uid)->where('status', 1)->sum('extract_price')],
    ];
  }



  //获取某用户的详细信息
  public static function getUserStudentCard($uid)
  {
    $UserInfo = self::get($uid);
    $user_pollster =  UserPollster::where('uid', $uid)->where('is_del', 0)->order('id desc')->find();
    $answer_option  = $user_pollster ?  (array) $user_pollster->answer_sheet : [];
    $user_pollster_phone = '';
    $identity = [];
    $user_pollster_sex = '';
    $city = '';
    $age = '';
    $from = [];
    foreach ($answer_option as $key => $value) {
      if ($value['name'] == '请问您的身份是') {
        $identity_option = [$value['value_group'][0]['name']];
        $answer_option = [];
        $answer_options = $value['value_group'][0]['answer_options'];
        foreach ($answer_options as $k => $v) {
          if (!empty($v['value_group'])) {
            $names = implode(',', array_column($v['value_group'], 'name'));
          } else {
            if ($v['name'] == '联系方式') {
              $user_pollster_phone =  $v['value'];
            }
            $names = isset($v['value']) ? $v['value'] : '';
          }
          $answer_option[$k] = $v['name'] . '=' . $names;
        }
        $identity  = array_merge($identity_option, $answer_option);
      }
      if ($value['name'] == '性别') {
        $user_pollster_sex = $value['value_group'][0]['name'];
      }
      if ($value['name'] == '请问您的年龄是') {
        $age = $value['value_group'][0]['name'];
      }
      if ($value['name'] == '请问您在哪里工作') { //城市
        $city = $value['value_group'][0]['name'] . '-' . $value['value_group'][0]['value'];
      }
      if ($value['name'] == '您是从哪里了解到我们的课程的（多选）') { //渠道来源
        $chanel = $value['value_group'];
        foreach ($chanel as $k => $v) {
          if ($v['name'] == '其他(填空)') {
            $from[$k] = $v['name'] . '-' . $v['value'];
          } else {
            $from[$k] = $v['name'];
          }
        }
      }
    }
    $sex = WechatUser::where('uid', $uid)->value('sex');
    if ($sex == 1) $user_sex_name = '男';
    else if ($sex == 2) $user_sex_name = '女';
    else $user_sex_name = '未知';
    $phone = $user_pollster_phone ?: $UserInfo['phone'];
    $sex_name = $user_pollster_sex ?: $user_sex_name;
    $comments =  self::getAllComments($uid);
    $watchs =  secondChanage(rand(100, 10000));
    return [
      ['name' => '手机号码', 'value' => $user_pollster_phone, 'col' => 2],
      ['name' => '微信UnionId', 'value' => WechatUser::where('uid', $uid)->value('unionid'), 'col' => 3],
      ['name' => '昵称', 'value' => $UserInfo['nickname'], 'col' => 2],
      ['name' => '性别', 'value' => $sex_name, 'col' => 2],
      ['name' => '年龄', 'value' => $age, 'col' => 2],
      ['col' => 12, 'name' => '身份', 'value' =>  $identity ?  implode(',', $identity) : ''],
      ['col' => 6, 'name' => '工作城市', 'value' =>  $city ?  $city : ''],
      ['col' => 6, 'name' => '渠道来源', 'value' => $from ?  implode(',', $from) : ''],
      ['col' => 6, 'name' => '课程评论', 'value' => $comments],
      ['col' => 6, 'name' => '课程评论观看总时长', 'value' => $watchs],
    ];
  }

  //获取某用户的订单个数,消费明细
  public static function getHeaderList($uid)
  {
    return [
      [
        'title' => '总计订单',
        'value' => StoreOrder::where('uid', $uid)->count(),
        'key' => '笔',
        'class' => '',
      ],
      [
        'title' => '总消费金额',
        'value' => StoreOrder::where('uid', $uid)->where('paid', 1)->sum('total_price'),
        'key' => '元',
        'class' => '',
      ],
      [
        'title' => '本月订单',
        'value' => StoreOrder::where('uid', $uid)->whereTime('add_time', 'month')->count(),
        'key' => '笔',
        'class' => '',
      ],
      [
        'title' => '本月消费金额',
        'value' => StoreOrder::where('uid', $uid)->where('paid', 1)->whereTime('add_time', 'month')->sum('total_price'),
        'key' => '元',
        'class' => '',
      ]
    ];
  }

  /*
     * 获取 会员 订单个数,积分明细,优惠劵明细
     *
     * $uid 用户id;
     *
     * return array
     */
  public static function getCountInfo($uid)
  {
    $order_count = StoreOrder::where('uid', $uid)->count();
    $integral_count = UserBill::where('uid', $uid)->where('category', 'integral')->where('type', 'in', 'deduction,system_add')->count();
    $sign_count = UserBill::where('type', 'sign')->where('uid', $uid)->where('category', 'integral')->count();
    $balanceChang_count = UserBill::where('category', 'now_money')->where('uid', $uid)
      ->where('type', 'in', 'system_add,pay_product,extract,pay_product_refund,system_sub')
      ->count();
    $coupon_count = StoreCouponUser::where('uid', $uid)->count();
    $spread_count = self::where('spread_uid', $uid)->count();
    $courses_count = SpecialRecord::where('uid', $uid)->order('updated_time desc')->count();
    return compact('order_count', 'integral_count', 'sign_count', 'balanceChang_count', 'coupon_count', 'courses_count', 'spread_count');
  }

  /*
     * 获取 会员业务的
     * 购物会员统计
     *  会员访问量
     *
     * 曲线图
     *
     * $where 查询条件
     *
     * return array
     */
  public static function getUserBusinessChart($where, $limit = 20)
  {
    //获取购物会员人数趋势图
    $list = self::getModelTime($where, self::where('a.status', 1)->alias('a')->join('store_order r', 'r.uid=a.uid'), 'a.add_time')
      ->where('r.paid', 1)
      ->where('a.is_promoter', 0)
      ->where('a.add_time', '<>', 0)
      ->field('FROM_UNIXTIME(a.add_time,"%Y-%m-%d") as _add_time,count(r.uid) as count_user')
      ->group('_add_time')
      ->order('_add_time asc')
      ->select();
    count($list) && $list = $list->toArray();
    $seriesdata = [];
    $xdata = [];
    $zoom = '';
    foreach ($list as $item) {
      $seriesdata[] = $item['count_user'];
      $xdata[] = $item['_add_time'];
    }
    count($xdata) > $limit && $zoom = $xdata[$limit - 5];
    //会员访问量
    $visit = self::getModelTime($where, self::alias('a')->join('store_visit t', 't.uid=a.uid'), 't.add_time')
      ->where('a.is_promoter', 0)
      ->field('FROM_UNIXTIME(t.add_time,"%Y-%m-%d") as _add_time,count(t.uid) as count_user')
      ->group('_add_time')
      ->order('_add_time asc')
      ->select();
    count($visit) && $visit = $visit->toArray();
    $visit_data = [];
    $visit_xdata = [];
    $visit_zoom = '';
    foreach ($visit as $item) {
      $visit_data[] = $item['count_user'];
      $visit_xdata[] = $item['_add_time'];
    }
    count($visit_xdata) > $limit && $visit_zoom = $visit_xdata[$limit - 5];
    //多次购物会员数量饼状图
    $count = self::getModelTime($where, self::where('is_promoter', 0))->count();
    $user_count = self::getModelTime($where, self::alias('a')->join('store_order r', 'r.uid=a.uid'), 'a.add_time')
      ->where('a.is_promoter', 0)
      ->where('r.paid', 1)
      ->group('a.uid')
      ->count();
    $shop_xdata = ['多次购买数量占比', '无购买数量占比'];
    $shop_data = [];
    $count > 0 && $shop_data = [
      [
        'value' => bcdiv($user_count, $count, 2) * 100,
        'name' => $shop_xdata[0],
        'itemStyle' => [
          'color' => '#D789FF',
        ]
      ],
      [
        'value' => bcdiv($count - $user_count, $count, 2) * 100,
        'name' => $shop_xdata[1],
        'itemStyle' => [
          'color' => '#7EF0FB',
        ]
      ]
    ];
    return compact('seriesdata', 'xdata', 'zoom', 'visit_data', 'visit_xdata', 'visit_zoom', 'shop_data', 'shop_xdata');
  }

  /*
     * 获取用户
     * 积分排行
     * 会员余额排行榜
     * 分销商佣金总额排行榜
     * 购物笔数排行榜
     * 购物金额排行榜
     * 分销商佣金提现排行榜
     * 上月消费排行榜
     * $limit 查询多少条
     * return array
     */
  public static function getUserTop10List($limit = 10, $is_promoter = 0)
  {
    //积分排行
    $integral = self::where('status', 1)
      ->where('is_promoter', $is_promoter)
      ->order('integral desc')
      ->field('nickname,phone,integral,FROM_UNIXTIME(add_time,"%Y-%m-%d") as add_time')
      ->limit($limit)
      ->select();
    count($integral) && $integral = $integral->toArray();
    //会员余额排行榜
    $now_money = self::where('status', 1)
      ->where('is_promoter', $is_promoter)
      ->order('now_money desc')
      ->field('nickname,phone,now_money,FROM_UNIXTIME(add_time,"%Y-%m-%d") as add_time')
      ->limit($limit)
      ->select();
    count($now_money) && $now_money = $now_money->toArray();
    //购物笔数排行榜
    $orderPayCount = StoreOrder::getOrderPayCount($is_promoter);
    if ($orderPayCount) {
      $shopcount = self::alias('a')
        ->join('store_order r', 'r.uid=a.uid')
        ->where('r.paid', 1)
        ->where('a.is_promoter', $is_promoter)
        ->group('r.uid')
        ->field('a.nickname,a.phone,count(r.uid) as sum_count,FROM_UNIXTIME(a.add_time,"%Y-%m-%d") as add_time')
        ->order('sum_count desc')
        ->limit($limit)
        ->select();
    } else $shopcount = [];
    count($shopcount) && $shopcount = $shopcount->toArray();
    //购物金额排行榜
    if ($orderPayCount) {
      $order = self::alias('a')
        ->join('store_order r', 'r.uid=a.uid')
        ->where('r.paid', 1)
        ->where('a.is_promoter', $is_promoter)
        ->group('r.uid')
        ->field('a.nickname,a.phone,sum(r.pay_price) as sum_price,FROM_UNIXTIME(a.add_time,"%Y-%m-%d") as add_time,r.uid')
        ->order('sum_price desc')
        ->limit($limit)
        ->select();
    } else $order = [];
    count($order) && $order = $order->toArray();
    //上月消费排行
    $orderPayCount = StoreOrder::getOrderPayMonthCount($is_promoter);
    if ($orderPayCount) {
      $lastorder = self::alias('a')
        ->join('store_order r', 'r.uid=a.uid')
        ->where('r.paid', 1)
        ->where('a.is_promoter', $is_promoter)
        //                ->whereTime('r.pay_time','last month')
        ->where('r.pay_time', 'between', [strtotime(date('Y-m', strtotime('-1 month'))), strtotime(date('Y-m'))])
        ->group('r.uid')
        ->field('a.nickname,a.phone,sum(r.pay_price) as sum_price,FROM_UNIXTIME(a.add_time,"%Y-%m-%d") as add_time,r.uid')
        ->order('sum_price desc')
        ->limit($limit)
        ->select();
    } else $lastorder = [];
    count($lastorder) && $lastorder = $lastorder->toArray();
    return compact('integral', 'now_money', 'shopcount', 'order', 'lastorder');
  }

  /*
     * 获取 会员业务
     * 会员总余额 会员总积分
     * $where 查询条件
     *
     * return array
     */
  public static function getUserBusinesHeade($where)
  {
    return [
      [
        'name' => '会员总余额',
        'field' => '元',
        'count' => self::getModelTime($where, self::where('status', 1))->sum('now_money'),
        'background_color' => 'layui-bg-cyan',
        'col' => 6,
      ],
      [
        'name' => '会员总积分',
        'field' => '分',
        'count' => self::getModelTime($where, self::where('status', 1))->sum('integral'),
        'background_color' => 'layui-bg-cyan',
        'col' => 6
      ]
    ];
  }

  /*
     * 分销会员头部信息查询获取
     *
     * 分销商总佣金余额
     * 分销商总提现佣金
     * 本月分销商业务佣金
     * 本月分销商佣金提现金额
     * 上月分销商业务佣金
     * 上月分销商佣金提现金额
     * $where array 时间条件
     *
     * return array
     */
  public static function getDistributionBadgeList($where)
  {
    $timeList = [
      'today' => implode(' - ', [date('Y/m/d'), date('Y/m/d', strtotime('+1 day'))]),
      'week' => implode(' - ', [
        date('Y/m/d', (time() - ((date('w') == 0 ? 7 : date('w')) - 1) * 24 * 3600)),
        date('Y-m-d', (time() + (7 - (date('w') == 0 ? 7 : date('w'))) * 24 * 3600))
      ]),
      'month' => implode(' - ', [date('Y/m') . '/01', date('Y/m') . '/' . date('t')]),
      'quarter' => implode(' - ', [
        date('Y') . '/' . (ceil((date('n')) / 3) * 3 - 3 + 1) . '/01',
        date('Y') . '/' . (ceil((date('n')) / 3) * 3) . '/' . date('t', mktime(0, 0, 0, (ceil((date('n')) / 3) * 3), 1, date('Y')))
      ]),
      'year' => implode(' - ', [
        date('Y') . '/01/01',
        date('Y/m/d', strtotime(date('Y') . '/01/01 + 1year -1 day'))
      ])
    ];
    $timeKey = ['today', 'week', 'month', 'quarter', 'year'];
    if (in_array($where['data'], $timeKey)) $where['data'] = $timeList[$where['data']];
    $uid = self::name('user')->where('status', 1)->where('is_promoter', 1)->whereOr('spread_uid', '<>', 0)->column('uid', 'uid');
    return [
      [
        'name' => '分销商总佣金',
        'field' => '元',
        'count' => count($uid) ? self::getModelTime($where, UserBill::where('category', 'now_money')->where('type', 'brokerage'))->where('uid', 'in', implode(',', $uid))->sum('number') : 0,
        'background_color' => 'layui-bg-cyan',
        'col' => 3,
      ],
      [
        'name' => '分销商总佣金余额',
        'field' => '元',
        'count' => self::getModelTime($where, self::where('status', 1)->where('is_promoter', 1))->sum('now_money'),
        'background_color' => 'layui-bg-cyan',
        'col' => 3,
      ],
      [
        'name' => '分销商总提现佣金',
        'field' => '元',
        'count' => self::getModelTime($where, UserExtract::where('status', 1))->sum('extract_price'),
        'background_color' => 'layui-bg-cyan',
        'col' => 3,
      ],
      [
        'name' => '本月分销商业务佣金',
        'field' => '元',
        'count' => count($uid) ? self::getModelTime(['data' => 'month'], UserBill::where('category', 'now_money')->where('type', 'brokerage'))
          ->where('uid', 'in', implode(',', $uid))->sum('number') : 0,
        'background_color' => 'layui-bg-cyan',
        'col' => 3,
      ],
      [
        'name' => '本月分销商佣金提现金额',
        'field' => '元',
        'count' => count($uid) ? self::getModelTime(['data' => 'month'], UserExtract::where('status', 1))
          ->where('uid', 'in', implode(',', $uid))->sum('extract_price') : 0,
        'background_color' => 'layui-bg-cyan',
        'col' => 4,
      ],
      [
        'name' => '上月分销商业务佣金',
        'field' => '元',
        'count' => count($uid) ? self::getOldDate(['data' => 'year'], UserBill::where('category', 'now_money')->where('uid', 'in', implode(',', $uid))->where('type', 'brokerage'))->sum('number') : 0,
        'background_color' => 'layui-bg-cyan',
        'col' => 4,
      ],
      [
        'name' => '上月分销商佣金提现金额',
        'field' => '元',
        'count' => count($uid) ? self::getOldDate(['data' => 'year'], UserBill::where('category', 'now_money')->where('uid', 'in', implode(',', $uid))->where('type', 'brokerage'))->sum('number') : 0,
        'background_color' => 'layui-bg-cyan',
        'col' => 4,
      ],
    ];
  }

  /*
     * 分销会员
     * 分销数量 饼状图
     * 分销商会员访问量 曲线
     * 获取购物会员人数趋势图 曲线
     * 多次购物分销会员数量 饼状图
     * $where array 条件
     * $limit int n条数据后出拖动条
     * return array
     */
  public static function getUserDistributionChart($where, $limit = 20)
  {
    //分销数量
    $fenbu_user = self::getModelTime($where, new self)->field('count(uid) as num')->group('is_promoter')->select();
    count($fenbu_user) && $fenbu_user = $fenbu_user->toArray();
    $sum_user = 0;
    $fenbu_data = [];
    $fenbu_xdata = ['分销商', '非分销商'];
    $color = ['#81BCFE', '#91F3FE'];
    foreach ($fenbu_user as $item) {
      $sum_user += $item['num'];
    }
    foreach ($fenbu_user as $key => $item) {
      $value['value'] = bcdiv($item['num'], $sum_user, 2) * 100;
      $value['name'] = isset($fenbu_xdata[$key]) ? $fenbu_xdata[$key] . '  %' . $value['value'] : '';
      $value['itemStyle']['color'] = $color[$key];
      $fenbu_data[] = $value;
    }
    //分销商会员访问量
    $visit = self::getModelTime($where, self::alias('a')->join('store_visit t', 't.uid=a.uid'), 't.add_time')
      ->where('a.is_promoter', 1)
      ->field(['FROM_UNIXTIME(t.add_time,"%Y-%m-%d") as _add_time', 'count(t.uid) as count_user'])
      ->group('_add_time')
      ->order('_add_time asc')
      ->select();
    //        echo self::getLastSql();die;
    count($visit) && $visit = $visit->toArray();
    $visit_data = [];
    $visit_xdata = [];
    $visit_zoom = '';
    foreach ($visit as $item) {
      $visit_data[] = $item['count_user'];
      $visit_xdata[] = $item['_add_time'];
    }
    count($visit_xdata) > $limit && $visit_zoom = $visit_xdata[$limit - 5];
    //获取购物会员人数趋势图
    $list = self::getModelTime($where, self::where('a.status', 1)->alias('a')->join('store_order r', 'r.uid=a.uid'), 'a.add_time')
      ->where('r.paid', 1)
      ->where('a.is_promoter', 1)
      ->where('a.add_time', '<>', 0)
      ->field('FROM_UNIXTIME(a.add_time,"%Y-%m-%d") as _add_time,count(r.uid) as count_user')
      ->group('_add_time')
      ->order('_add_time asc')
      ->select();
    count($list) && $list = $list->toArray();
    $seriesdata = [];
    $xdata = [];
    $zoom = '';
    foreach ($list as $item) {
      $seriesdata[] = $item['count_user'];
      $xdata[] = $item['_add_time'];
    }
    count($xdata) > $limit && $zoom = $xdata[$limit - 5];
    //多次购物分销会员数量饼状图
    $count = self::getModelTime($where, self::where('is_promoter', 1))->count();
    $user_count = self::getModelTime($where, self::alias('a')
      ->join('store_order r', 'r.uid=a.uid'), 'a.add_time')
      ->where('a.is_promoter', 1)
      ->where('r.paid', 1)
      ->group('a.uid')
      ->count();
    $shop_xdata = ['多次购买数量占比', '无购买数量占比'];
    $shop_data = [];
    $count > 0 && $shop_data = [
      [
        'value' => bcdiv($user_count, $count, 2) * 100,
        'name' => $shop_xdata[0] . $user_count . '人',
        'itemStyle' => [
          'color' => '#D789FF',
        ]
      ],
      [
        'value' => bcdiv($count - $user_count, $count, 2) * 100,
        'name' => $shop_xdata[1] . ($count - $user_count) . '人',
        'itemStyle' => [
          'color' => '#7EF0FB',
        ]
      ]
    ];
    return compact('fenbu_data', 'fenbu_xdata', 'visit_data', 'visit_xdata', 'visit_zoom', 'seriesdata', 'xdata', 'zoom', 'shop_xdata', 'shop_data');
  }

  /*
     * 分销商佣金提现排行榜
     * 分销商佣金总额排行榜
     * $limit 截取条数
     * return array
     */
  public static function getUserDistributionTop10List($limit)
  {
    //分销商佣金提现排行榜
    $extract = self::alias('a')
      ->join('user_extract t', 'a.uid=t.uid')
      ->where('t.status', 1)
      ->where('a.is_promoter', 1)
      ->group('t.uid')
      ->field('a.nickname,a.phone,sum(t.extract_price) as sum_price,FROM_UNIXTIME(a.add_time,"%Y-%m-%d") as add_time,t.uid')
      ->order('sum_price desc')
      ->limit($limit)
      ->select();
    count($extract) && $extract = $extract->toArray();
    //分销商佣金总额排行榜
    $commission = UserBill::alias('l')
      ->join('user a', 'l.uid=a.uid')
      ->where('l.status', 1)
      ->where('l.category', 'now_money')
      ->where('l.type', 'brokerage')
      ->where('a.is_promoter', 1)
      ->group('l.uid')
      ->field('a.nickname,a.phone,sum(number) as sum_number,FROM_UNIXTIME(a.add_time,"%Y-%m-%d") as add_time')
      ->order('sum_number desc')
      ->limit($limit)
      ->select();
    count($commission) && $commission = $commission->toArray();
    return compact('extract', 'commission');
  }

  public static function getSpreadList($uid, $page, $limit)
  {
    $list = self::where('spread_uid', $uid)->field('uid,nickname,now_money,integral,add_time')
      ->order('uid desc')->page((int)$page, (int)$limit)->select();
    count($list) && $list = $list->toArray();
    foreach ($list as &$item) {
      $item['add_time'] = date('Y-m-d H:i:s', $item['add_time']);
    }
    return $list;
  }

  /**
   * 一级分销
   * @param $orderInfo
   * @return bool
   */
  public static function backOrderBrokerage($orderInfo)
  {
    $userInfo = User::getUserInfo($orderInfo['uid']);
    if (!$userInfo || !$userInfo['spread_uid']) return true;
    $storeBrokerageStatu = sys_config('store_brokerage_statu') ?: 1; //获取后台分销类型
    if ($storeBrokerageStatu == 1) {
      if (!User::be(['uid' => $userInfo['spread_uid'], 'is_promoter' => 1])) return true;
    }
    $brokerageRatio = (sys_config('store_brokerage_ratio') ?: 0) / 100;
    if ($brokerageRatio <= 0) return true;
    $cost = isset($orderInfo['cost']) ? $orderInfo['cost'] : 0; //成本价
    if ($cost > $orderInfo['pay_price']) return true; //成本价大于支付价格时直接返回
    $brokeragePrice = bcmul(bcsub($orderInfo['pay_price'], $cost, 2), $brokerageRatio, 2);
    //返佣之后余额
    $spNow_money = User::where('uid', $userInfo['spread_uid'])->value('now_money');
    $balance = bcadd($spNow_money, $brokeragePrice, 2);
    if ($brokeragePrice <= 0) return true;
    $mark = $userInfo['nickname'] . '成功消费' . floatval($orderInfo['pay_price']) . '元,奖励推广佣金' . floatval($brokeragePrice);
    self::beginTrans();
    $res1 = UserBill::income('获得推广佣金', $userInfo['spread_uid'], 'now_money', 'brokerage', $brokeragePrice, $orderInfo['id'], $balance, $mark);
    $res2 = self::bcInc($userInfo['spread_uid'], 'now_money', $brokeragePrice, 'uid');
    $res = $res1 && $res2;
    self::checkTrans($res);
    if ($res) self::backOrderBrokerageTwo($orderInfo);
    return $res;
  }

  /**
   * 二级推广
   * @param $orderInfo
   * @return bool
   */
  public static function backOrderBrokerageTwo($orderInfo)
  {
    $userInfo = User::getUserInfo($orderInfo['uid']);
    $userInfoTwo = User::getUserInfo($userInfo['spread_uid']);
    if (!$userInfoTwo || !$userInfoTwo['spread_uid']) return true;
    $storeBrokerageStatu = sys_config('store_brokerage_statu') ?: 1; //获取后台分销类型
    if ($storeBrokerageStatu == 1) {
      if (!User::be(['uid' => $userInfoTwo['spread_uid'], 'is_promoter' => 1])) return true;
    }
    $brokerageRatio = (sys_config('store_brokerage_two') ?: 0) / 100;
    if ($brokerageRatio <= 0) return true;
    $cost = isset($orderInfo['cost']) ? $orderInfo['cost'] : 0; //成本价
    if ($cost > $orderInfo['pay_price']) return true; //成本价大于支付价格时直接返回
    $brokeragePrice = bcmul(bcsub($orderInfo['pay_price'], $cost, 2), $brokerageRatio, 2);
    //返佣之后余额
    $spNow_money = User::where('uid', $userInfo['spread_uid'])->value('now_money');
    $balance = bcadd($spNow_money, $brokeragePrice, 2);
    if ($brokeragePrice <= 0) return true;
    $mark = '二级推广人' . $userInfo['nickname'] . '成功消费' . floatval($orderInfo['pay_price']) . '元,奖励推广佣金' . floatval($brokeragePrice);
    self::beginTrans();
    $res1 = UserBill::income('获得推广佣金', $userInfoTwo['spread_uid'], 'now_money', 'brokerage', $brokeragePrice, $orderInfo['id'], $balance, $mark);
    $res2 = self::bcInc($userInfoTwo['spread_uid'], 'now_money', $brokeragePrice, 'uid');
    $res = $res1 && $res2;
    self::checkTrans($res);
    return $res;
  }

  /**
   * 获取app 名称
   * 
   * @param $link
   * @return string
   */
  public static  function getAppName($str = '')
  {
    switch ($str) {
      case 'devtools':
        $name = "字节跳动开发者工具";
        break;
      case 'Toutiao':
        $name = "今日头条";
        break;
      case 'Toutiao_Pro':
        $name = "今日头条专业版";
        break;
      case 'news_article_lite':
        $name = "今日头条极速版";
        break;
      case 'Douyin':
        $name = "抖音";
        break;
      case 'douyin_lite':
        $name = "抖音极速版";
        break;
      case 'live_stream':
        $name = "抖音火山版";
        break;
      case 'Tiktok':
        $name = "TikTok";
        break;
      case 'Musically':
        $name = "TikTok.M";
        break;
      case 'XiGua':
        $name = "西瓜视频";
        break;
      case 'PPX':
        $name = "皮皮虾";
        break;
      case 'automobile':
        $name = "懂车帝";
        break;
      case 'DeliverManager':
        $name = "巨量引擎";
        break;
      case 'Reading':
        $name = "番茄小说";
        break;
      case 'xiaohe':
        $name = "小荷";
        break;
      default:
        $name = '';
    }
    return $name;
  }

  /**
   * 获取app 名称
   * 
   * @param $link
   * @return string
   */
  public static  function getAllComments($uid)
  {
    return  SpecialMessage::where('uid', $uid)->where('is_del', 0)->where('status', 1)->count();
  }

  /*
     * 保存并下载excel
     * $list array
     * return
     */
  public static function SaveExcel($list)
  {
    $export = [];
    foreach ($list as $index => $item) {
      $comments = self::getAllComments($item['uid']);
      $special_order_data = SpecialBuy::field('A.id,B.title,from_unixtime(A.add_time,\'%Y-%m-%d\') add_time')->alias('A')->join('special B', 'B.id=A.special_id')->where('A.uid', $item['uid']);
      $special_order_list = ($special_order_data = $special_order_data->select()) && count($special_order_data) ? $special_order_data->toArray() : [];
      $unionid = WechatUser::where('uid', $item['uid'])->value('unionid') ?: '';
      $user_pollster =  UserPollster::where('uid', $item['uid'])->where('is_del', 0)->order('id desc')->find();
      $answer_option  = $user_pollster ?  (array) $user_pollster->answer_sheet : [];
      $user_pollster_phone = '';
      $identity = '';
      $user_pollster_sex = '';
      $city = '';
      $age = '';
      $from = [];
      $answer = [];
      foreach ($answer_option as $key => $value) {
        if (strpos($value['name'], "身份") !== false) {
          $identity = $value['value_group'][0]['name'];
          $answer_option = [];
          $is_child_question = $value['value_group'][0]['is_child_question'];
          $answer_options = $value['value_group'][0]['answer_options'];
          foreach ($answer_options as $k => $v) {
            if (!empty($v['value_group'])) {
              $names = implode(',', array_column($v['value_group'], 'name'));
            } else {
              if (strpos($v['name'], "联系") !== false) {
                if (!$is_child_question) {
                  $user_pollster_phone =  $v['content'];
                  unset($v['content']);
                } else {
                  $user_pollster_phone =  $v['value'];
                  unset($v['value']);
                }
              }
              if (!$is_child_question) {
                $names = isset($v['content']) ? $v['content'] : '';
              } else {
                $names = isset($v['value']) ? $v['value'] : '';
              }
            }
            $answer_option[$k] = $names;
          }
          $answer  = $answer_option;
        }
        if (strpos($value['name'], "性别") !== false) {
          $user_pollster_sex = $value['value_group'][0]['name'];
        }
        if (strpos($value['name'], "年龄") !== false) {
          $age = $value['value_group'][0]['name'];
        }
        if (strpos($value['name'], "工作") !== false) { //城市
          $city = $value['value_group'][0]['name'] . '-' . $value['value_group'][0]['value'];
        }
        if (strpos($value['name'], "了解") !== false) { //渠道来源
          $chanel = $value['value_group'];
          foreach ($chanel as $k => $v) {
            if (strpos($v['name'], "填写") !== false) {
              $from[$k] = $v['value'];
            } else {
              $from[$k] = $v['name'];
            }
          }
        }
      }
      $goodsName = [];
      // 商品
      foreach ($special_order_list as $kk => $vv) {
        $goodsName[]  = mb_substr($vv['title'], 0, 11, 'UTF-8');
      }
      $sex = WechatUser::where('uid', $item['uid'])->value('sex');
      if ($sex == 1) $user_sex_name = '男';
      else if ($sex == 2) $user_sex_name = '女';
      else $user_sex_name = '未知';
      $phone = $user_pollster_phone ? $user_pollster_phone : $item['phone'];
      $sex_name = $user_pollster_sex ? $user_pollster_sex :  $user_sex_name;
      $identity_value = implode(',', $answer);
      // 默认导出格式
      $export[] = [
        $phone,
        $unionid,
        $item['nickname'],
        $sex_name,
        $identity,
        $identity_value,
        $city,
        $age,
        implode(", ", $goodsName),
        implode(", ", $from),
        (string) $item['comment_count'],
        $comments
      ];
    }
    $title = '用户分层-数据导出';
    PHPExcelService::instance()->setExcelHeader([
      '手机号',
      'union id',
      '昵称',
      '性别',
      '身份',
      '答案',
      '城市',
      '年龄段',
      '购买内容',
      '来源',
      '用户评论总数'
    ])
      ->setExcelTile($title, '分层信息', ' 生成时间：' . date('Y-m-d H:i:s', time()))
      ->setExcelContent($export)
      ->ExcelSave($title);
  }
}
