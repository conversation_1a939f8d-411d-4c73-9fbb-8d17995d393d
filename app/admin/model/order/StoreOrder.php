<?php

/**
 *
 * @author: xaboy<<EMAIL>>
 * @day: 2017/11/11
 */

namespace app\admin\model\order;

use Carbon\Carbon;
use think\facade\Db;
use think\facade\Cache;
use crmeb\basic\BaseModel;
use crmeb\traits\ModelTrait;
use think\facade\Route as Url;
use app\models\store\StoreCart;
use crmeb\services\minishop\Shop;
use app\admin\model\wechat\WechatUser;
use app\admin\model\special\{Special};
use app\admin\model\system\SystemStore;
use app\admin\model\store\StoreProduct;
use app\models\routine\RoutineTemplate;
use app\admin\model\user\{User, UserBill, UserAddress};
use crmeb\services\FileService as FileClass;
use app\admin\model\ump\{StoreCouponUser, StorePink};
use app\admin\model\special\{SpecialActivity, SpecialActivityInvitationRecord};
use crmeb\services\{OrderStatusService, PHPExcelService, WechatTemplateService};
use app\admin\model\system\ShippingTemplates;
use app\admin\model\system\ShippingTemplatesFree;
use app\admin\model\system\ShippingTemplatesRegion;

/**
 * 订单管理Model
 * Class StoreOrder
 * @package app\admin\model\store
 */
class StoreOrder extends BaseModel
{
  /**
   * 数据表主键
   * @var string
   */
  protected $pk = 'id';

  /**
   * 模型名称
   * @var string
   */
  protected $name = 'store_order';

  use ModelTrait;

  protected function getCartIdAttr($value)
  {
    return json_decode($value, true);
  }


  public static function orderCount()
  {
    $data['ys'] = self::statusByWhere(9, new self())->where(['is_system_del' => 0])->count();
    $data['wz'] = self::statusByWhere(0, new self())->where(['is_system_del' => 0])->count();
    $data['wf'] = self::statusByWhere(1, new self())->where(['is_system_del' => 0, 'shipping_type' => 1])->count();
    $data['ds'] = self::statusByWhere(2, new self())->where(['is_system_del' => 0, 'shipping_type' => 1])->count();
    $data['dp'] = self::statusByWhere(3, new self())->where(['is_system_del' => 0])->count();
    $data['jy'] = self::statusByWhere(4, new self())->where(['is_system_del' => 0])->count();
    $data['tk'] = self::statusByWhere(-1, new self())->where(['is_system_del' => 0])->count();
    $data['yt'] = self::statusByWhere(-2, new self())->where(['is_system_del' => 0])->count();
    $data['del'] = self::statusByWhere(-4, new self())->where(['is_system_del' => 0])->count();
    $data['write_off'] = self::statusByWhere(5, new self())->where(['is_system_del' => 0])->count();
    $data['general'] = self::where(['pink_id' => 0, 'combination_id' => 0, 'seckill_id' => 0, 'bargain_id' => 0, 'is_system_del' => 0])->whereIn('type', [0, 1, 2])->count();
    $data['pink'] = self::where('pink_id|combination_id', '>', 0)->where('is_system_del', 0)->whereIn('type', [0, 1, 2])->count();
    $data['seckill'] = self::where('seckill_id', '>', 0)->where('is_system_del', 0)->whereIn('type', [0, 1, 2])->count();
    $data['bargain'] = self::where('bargain_id', '>', 0)->where('is_system_del', 0)->whereIn('type', [0, 1, 2])->count();
    $data['shop_amount'] = self::where('type', 5)->count();
    return $data;
  }


  public static function orderWxCount()
  {
    $data['ys'] = self::statusWxByWhere(9, new self())->where(['is_system_del' => 0])->count();
    $data['wz'] = self::statusWxByWhere(0, new self())->where(['is_system_del' => 0])->count();
    $data['wf'] = self::statusWxByWhere(1, new self())->where(['is_system_del' => 0, 'shipping_type' => 1])->count();
    $data['ds'] = self::statusWxByWhere(2, new self())->where(['is_system_del' => 0, 'shipping_type' => 1])->count();
    $data['dp'] = self::statusWxByWhere(3, new self())->where(['is_system_del' => 0])->count();
    $data['jy'] = self::statusWxByWhere(4, new self())->where(['is_system_del' => 0])->count();
    $data['tk'] = self::statusWxByWhere(-1, new self())->where(['is_system_del' => 0])->count();
    $data['yt'] = self::statusWxByWhere(-2, new self())->where(['is_system_del' => 0])->count();
    $data['del'] = self::statusWxByWhere(-4, new self())->where(['is_system_del' => 0])->count();
    $data['write_off'] = self::statusWxByWhere(5, new self())->where(['is_system_del' => 0])->count();
    $data['shop_amount'] = self::where('type', 5)->count();
    return $data;
  }

  public static function specialOrderCount($type = 0)
  {
    // dd($data, $type);
    $data['wz'] = self::statusSpecialByWhere(0, new self(), '', $type)->count();
    $data['wf'] = self::statusSpecialByWhere(1, new self(), '', $type)->count();
    $data['pt'] = self::statusSpecialByWhere(6, new self(), '', $type)->count();
    $data['lw'] = self::statusSpecialByWhere(7, new self(), '', $type)->count();
    $data['test'] = self::statusSpecialByWhere(8, new self(), '', $type)->count();
    $data['ysc'] = self::statusSpecialByWhere(-5, new self(), '', $type)->count();
    $data['tk'] = self::statusSpecialByWhere(-1, new self(), '', $type)->count();
    $data['yt'] = self::statusSpecialByWhere(-2, new self(), '', $type)->count();
    return $data;
  }


  public static function sharerList()
  {
    $model = WechatUser::where('is_live_room_sharer', 1)->where('user_type', 'routine')->field('uid,nickname as name,routine_openid as value');
    $data = ($data = $model->select()) && count($data) ? $data->toArray() : [];
    return $data;
  }

  public static function promoterList()
  {
    $cacheName = 'shop_promoter_list';
    $cacheTime = 172800;
    if (Cache::has($cacheName)) {
      $data = Cache::get($cacheName);
    } else {
      $shop = new  Shop('wechat');
      $result = $shop->promoter_list();
      if ($result['errcode'] != 0) {
        return [];
      }
      $data = $result['promoters'];
      Cache::set($cacheName, $data, $cacheTime);
    }
    foreach ($data as $key => $promoter) {
      $data[$key]['value'] = $promoter['promoter_id'];
      $data[$key]['name'] = $promoter['finder_nickname'];
      unset($data[$key]['finder_nickname'], $data[$key]['promoter_id']);
    }
    return $data;
  }


  public static function getDeliveryCompanyList()
  {
    $cacheName = 'get_delivery_company_list';
    $cacheTime = 172800;
    if (Cache::has($cacheName)) {
      $data = Cache::get($cacheName);
    } else {
      $shop = new  Shop('wechat');
      $result = $shop->delivery_get_company_list([]);
      if ($result['errcode'] != 0) {
        return [];
      }
      $data = $result['company_list'];
      Cache::set($cacheName, $data, $cacheTime);
    }
    return $data;
  }

  // 订单确认收货
  public static function deliveryRecieve($order)
  {
    $data['openid'] = WechatUser::where('uid', $order['uid'])->value('routine_openid');
    $data['order_id '] = $order['wx_shop_order_id'];
    $data['out_order_id'] = $order['order_id'];
    $shop = new  Shop('wechat');
    $result =   $shop->delivery_recieve($data);
    if ($result['errcode'] != 0) {
      return self::setErrorInfo($result['errmsg']);
    }
    return true;
  }

  //订单发货
  public static function shopDeliverySend($data, $id)
  {

    $order = self::get($id);
    $data['openid'] = WechatUser::where('uid', $order['uid'])->value('routine_openid');
    $data['order_id']  = $order['wx_shop_order_id'];
    $data['finish_all_delivery'] = $data['finish_all_delivery'];
    $data['delivery_list'] = $data['delivery_list'];
    if ($data['finish_all_delivery'] == 1) {
      $data['ship_done_time'] = date('Y-m-d H:i:s', time());
    }
    $shop = new  Shop('wechat');
    $result =   $shop->delivery_send($data);
    if ($result['errcode'] != 0) {
      return self::setErrorInfo($result['errmsg']);
    }
    return true;
  }


  public static function addAftersale($data, $order, $id)
  {
    $refund_reason_wap = $data['refund_reason_wap'];
    $data['out_order_id'] = $order['order_id'];
    $data['out_aftersale_id'] = 'aftersale_' . $order['order_id'];
    $data['openid'] = WechatUser::where('uid', $order['uid'])->value('routine_openid');
    $data['create_time'] = date('Y-m-d H:i:s', time());
    $data['path'] = '/pages/aftersale.html?out_aftersale_id=' . $order['order_id'];
    $data['refund'] = $order['pay_price'];
    $data['refund_reason'] = $order['refund_reason_wap'];
    //拼接商品信息
    $_info = StoreOrderCartInfo::where('oid', $order['id'])->field('cart_info')->select();
    $_info = count($_info) ? $_info->toArray() : [];
    foreach ($_info as $k => $v) {
      $cart_info = json_decode($v['cart_info'], true);
      if (!isset($cart_info['productInfo'])) $cart_info['productInfo'] = [];
      $_info[$k]['cart_info'] = $cart_info;
      unset($cart_info);
    }
    $product_infos = [];
    foreach ($_info as $key => $product) {
      $product_infos[$key]['out_product_id'] = $product['cart_info']['id'];
      $product_infos[$key]['out_sku_id'] = $product['cart_info']['product_attr_unique'];
      $product_infos[$key]['product_cnt'] = $product['cart_info']['cart_num'];
    }
    $data['product_infos'] = $product_infos;
    $shop = new  Shop('wechat');
    $result = $shop->aftersale_add($data);
    if ($result['errcode'] != 0) {
      return self::setErrorInfo($result['errmsg']);
    }
    unset($data['type'], $data['finish_all_aftersale'], $data['status']);
    //销毁变量
    $data['refund_status'] = 1;
    $data['refund_reason_wap'] = $refund_reason_wap;
    $data['refund_reason_time'] = time();
    return  self::edit($data, $id);
  }


  public static function updateAftersaleStatus($order, $status, $is_all = 1)
  {

    $data['out_order_id'] = $order['order_id'];
    $data['openid'] = WechatUser::where('uid', $order['uid'])->value('routine_openid');
    $data['out_aftersale_id'] = 'aftersale_' . $order['order_id'];
    $data['status'] = $status;
    $data['finish_all_aftersale'] = $is_all;
    $shop = new  Shop('wechat');
    $result = $shop->aftersale_update($data);
    if ($result['errcode'] != 0) {
      return self::setErrorInfo($result['errmsg']);
    }
    return true;
  }



  public static function getWxOrderInfo($orderId)
  {
    $shop = new  Shop('wechat');
    $result = $shop->order_get($orderId);
    if ($result['errcode'] != 0) {
      return self::setErrorInfo($result['errmsg']);
    }
    $data = $result['order'];
    return $data;
  }


  public static function OrderList($where)
  {
    switch ($where['search_type']) {
      case 1:
        $model = self::getOrderWhere($where, self::alias('a')
          ->join('user r', 'r.uid=a.uid', 'LEFT'), 'a.', 'r')
          ->field('a.*,r.nickname,r.phone,r.spread_uid');
        break;
      case 2:
      case 3:
        //实例化
        $shop = new Shop('wechat');
        //公用参数
        $params['page'] = (int)$where['page'];
        $params['limit'] = (int)$where['limit'];
        $params['start_pay_time'] = "2020-03-25 12:05:25";
        $params['end_pay_time'] = "2020-03-25 12:05:28";
        if ($where['search_type'] == 2) {
          $params['promoter_id'] = $where['promoter_id'];
          $result = $shop->get_list_by_finder($params);
        } else {
          $params['sharer_openid'] = $where['sharer_openid'];
          $result = $shop->get_list_by_sharer($params);
        }
        if ($result['errcode'] != 0) {
          $result['orders'] = [];
          $result['total_num'] = 0;
        }
        $out_order_ids = array_column($result['orders'], 'out_order_id');
        $model = self::alias('a')->whereIn('order_id', $out_order_ids)->join('user r', 'r.uid=a.uid', 'LEFT')->field('a.*,r.nickname,r.phone,r.spread_uid');
        $count = $result['total_num'];
        break;
    }
    if ($where['order'] != '') {
      $model = $model->order(self::setOrder($where['order']));
    } else {
      $model = $model->order('a.id desc');
    }
    if ((isset($where['excel']) && in_array($where['excel'], [1, 2, 3, 4]))) {
      $data = ($data = $model->select()) && count($data) ? $data->toArray() : [];
    } else {
      $data = ($data = $model->page((int)$where['page'], (int)$where['limit'])->select()) && count($data) ? $data->toArray() : [];
    }
    $spreadUser = [];
    $storePink = [];
    $systemStore = [];
    $spreadIds = array_unique(array_column($data, 'spread_uid'));
    if ($spreadIds) {
      $spreadUser = User::where('uid', 'IN', $spreadIds)->column('nickname', 'uid');
    }
    $orderIds = array_column($data, 'id');
    if ($orderIds) {
      $storePink = StorePink::where('order_id_key', 'IN', $orderIds)->column('status', 'order_id_key');
    }
    $storeIds = array_unique(array_column($data, 'store_id'));
    if ($storeIds) {
      $systemStore = SystemStore::where('id', 'IN', $storeIds)->column('name', 'id');
    }
    $count = 0;
    foreach ($data as &$item) {
      if (in_array($item['type'], [3, 6])) {
        $_info = Special::whereIn('id', $item['cart_id'])->field('id,image,title,money')->select();
        $_info = count($_info) ? $_info->toArray() : [];
        foreach ($_info as $k => $v) {
          $cart_info['cart_num'] = 1;
          $cart_info['truePrice'] = $v['money'];
          $cart_info['productInfo'] = ['id' => $v['id'], 'image' => $v['image'], 'store_name' => $v['title']];
          $_info[$k]['cart_info'] = $cart_info;
          unset($cart_info);
        }
      } else {
        $_info = StoreOrderCartInfo::where('oid', $item['id'])->field('cart_info')->select();
        $_info = count($_info) ? $_info->toArray() : [];

        foreach ($_info as $k => $v) {
          $cart_info = json_decode($v['cart_info'], true);
          if (!isset($cart_info['productInfo'])) $cart_info['productInfo'] = [];

          $user_address = explode(' ', $item['user_address']);
          // 获取省、市、区部分
          $province = $user_address[0] ?? '';
          $addr = UserAddress::where('uid', $item['uid'])->where('province', $province)->find();
          $payPostage = self::getOrderPostagePriceGroup([$cart_info], $addr)['storePostage'];

          $cart_info['truePostage'] = $payPostage;
          $_info[$k]['cart_info'] = $cart_info;
          unset($cart_info);
        }
      }
      $item['_info'] = $_info;
      $item['spread_nickname'] = $spreadUser[$item['spread_uid']] ?? '';
      $item['back_integral'] = $item['back_integral'] ?: 0;
      $item['store_name'] = $systemStore[$item['store_id']] ?? '';
      if ($item['pink_id'] || $item['combination_id']) {
        $pinkStatus = $storePink[$item['id']] ?? '';
        switch ($pinkStatus) {
          case 1:
            $item['pink_name'] = '[拼团订单]正在进行中';
            $item['color'] = '#f00';
            break;
          case 2:
            $item['pink_name'] = '[拼团订单]已完成';
            $item['color'] = '#00f';
            break;
          case 3:
            $item['pink_name'] = '[拼团订单]未完成';
            $item['color'] = '#f0f';
            break;
          default:
            $item['pink_name'] = '[拼团订单]历史订单';
            $item['color'] = '#457856';
            break;
        }
      } elseif ($item['seckill_id']) {
        $item['pink_name'] = '[秒杀订单]';
        $item['color'] = '#32c5e9';
      } elseif ($item['bargain_id']) {
        $item['pink_name'] = '[砍价订单]';
        $item['color'] = '#12c5e9';
      } elseif ($item['wx_shop_order_id']) {
        $item['pink_name'] = '[微信商店订单]';
        $item['color'] = '#F29325';
      } else {
        if ($item['shipping_type'] == 1) {
          $item['pink_name'] = '[普通订单]';
          $item['color'] = '#895612';
        } else if ($item['shipping_type'] == 2) {
          $item['pink_name'] = '[核销订单]';
          $item['color'] = '#8956E8';
        }
      }
      if ($item['paid'] == 1) {
        switch ($item['pay_type']) {
          case 'weixin':
          case 'bytedance.weixin':
            $item['pay_type_name'] = '微信支付';
            break;
          case 'alipay':
          case 'bytedance.alipay':
            $item['pay_type_name'] = '支付宝支付';
            break;
          case 'bytedance.douyin':
            $item['pay_type_name'] = '抖音支付';
            break;
          case 'yue':
            $item['pay_type_name'] = '余额支付';
            break;
          case 'offline':
            $item['pay_type_name'] = '线下支付';
            break;
          default:
            $item['pay_type_name'] = '其他支付';
            break;
        }
      } else {
        switch ($item['pay_type']) {
          case 'weixin':
          case 'bytedance.weixin':
            $item['pay_type_name'] = '微信支付';
            break;
          case 'alipay':
          case 'bytedance.alipay':
            $item['pay_type_name'] = '支付宝支付';
            break;
          case 'bytedance.douyin':
            $item['pay_type_name'] = '抖音支付';
            break;
          case 'yue':
            $item['pay_type_name'] = '余额支付';
            break;
          case 'offline':
            $item['pay_type_name'] = '线下支付';
            $item['pay_type_info'] = 1;
            break;
          case 'bytedance':
            $item['pay_type_name'] = '发起字节跳动担保支付';
            break;
        }
      }
      if ($item['paid'] == 0 && $item['status'] == 0) {
        $item['status_name'] = '未支付';
      } else if ($item['paid'] == 1 && $item['status'] == 0 && $item['shipping_type'] == 1 && $item['refund_status'] == 0) {
        $item['status_name'] = '未发货';
      } else if ($item['paid'] == 1 && $item['status'] == 0 && $item['shipping_type'] == 2 && $item['refund_status'] == 0) {
        $item['status_name'] = '未核销';
      } else if ($item['paid'] == 1 && $item['status'] == 1 && $item['shipping_type'] == 1 && $item['refund_status'] == 0) {
        $item['status_name'] = '待收货';
      } else if ($item['paid'] == 1 && $item['status'] == 1 && $item['shipping_type'] == 2 && $item['refund_status'] == 0) {
        $item['status_name'] = '未核销';
      } else if ($item['paid'] == 1 && $item['status'] == 2 && $item['refund_status'] == 0) {
        $item['status_name'] = '待评价';
      } else if ($item['paid'] == 1 && $item['status'] == 3 && $item['refund_status'] == 0) {
        $item['status_name'] = '已完成';
      } else if ($item['paid'] == 1 && $item['refund_status'] == 1) {
        $item['status_name'] = '申请退款';
      } else if ($item['paid'] == 1 && $item['refund_status'] == 2) {
        $item['status_name'] = '已退款';
      }
      if ($item['paid'] == 1 && $item['status'] == 0 && $item['shipping_type'] == 2) {
        $item['_status'] = 0;
      } else if ($item['paid'] == 0 && $item['status'] == 0 && $item['refund_status'] == 0) {
        $item['_status'] = 1;
      } else if ($item['paid'] == 1 && $item['status'] == 0 && $item['refund_status'] == 0) {
        $item['_status'] = 2;
      } else if ($item['paid'] == 1 && $item['refund_status'] == 1) {
        $item['_status'] = 3;
      } else if ($item['paid'] == 1 && $item['status'] == 1 && $item['refund_status'] == 0) {
        $item['_status'] = 4;
      } else if ($item['paid'] == 1 && $item['status'] == 2 && $item['refund_status'] == 0) {
        $item['_status'] = 5;
      } else if ($item['paid'] == 1 && $item['status'] == 3 && $item['refund_status'] == 0) {
        $item['_status'] = 6;
      } else if ($item['paid'] == 1 && $item['refund_status'] == 2) {
        $item['_status'] = 7;
      }
      if ($where['search_type'] == 1) {
        $count = self::getOrderWhere($where, self::alias('a')->join('user r', 'r.uid=a.uid', 'LEFT'), 'a.', 'r')->count();
      }
      $item['add_time'] = $item['add_time'] ? date('Y-m-d H:i:s', $item['add_time']) : '';
    }
    if (isset($where['excel']) && in_array($where['excel'], [1, 2, 3, 4])) {
      self::SaveExcel($where['excel'], $data, $where);
    }
    return compact('count', 'data');
  }

  public static function SpecialOrderList($where)
  {
    $model = self::getSpecialOrderWhere($where, self::alias('a')->join('user r', 'r.uid=a.uid', 'LEFT'), 'a.', 'r')->field('a.*,r.nickname,r.phone');
    if ($where['order'] != '') {
      $model = $model->order(self::setOrder($where['order']));
    } else {
      $model = $model->order('a.id desc');
    }
    if (isset($where['excel']) && in_array($where['excel'], [1, 2])) {
      $data = ($data = $model->select()) && count($data) ? $data->toArray() : [];
    } else {
      $data = ($data = $model->page((int)$where['page'], (int)$where['limit'])->select()) && count($data) ? $data->toArray() : [];
    }
    foreach ($data as &$item) {
      switch ($item['type']) {
        case 3:
          $item['_info'] =  is_array($item['cart_id']) ? Special::whereIn('id', $item['cart_id'])->find() : Special::where('id', $item['cart_id'])->find();
          if ($item['pink_id']) {
            $item['pink_name'] = '[拼团订单]';
            $item['color'] = '#895612';
          } else if ($item['is_gift']) {
            if (bccomp($item['pay_price'], '0.00', 2) == 0) {
              $item['pink_name'] = '[测试订单]';
              $item['color'] = '#895612';
            } else {
              $item['pink_name'] = '[送礼物订单]';
              $item['color'] = '#895612';
            }
          } else if (!$item['is_gift'] && !$item['pink_id'] && $item['gift_order_id']) {
            $item['pink_name'] = '[领礼物订单]';
            $item['color'] = '#895612';
          } else if (bccomp($item['pay_price'], '1.00', 2) <= 0 && !$item['gift_order_id']) {
            $item['pink_name'] = '[测试订单]';
            $item['color'] = '#895612';
          } else {
            $item['pink_name'] = '[课程订单]';
            $item['color'] = '#895612';
          }
          if (!$item['_info']) {
            $item['_info']['title'] = '专题被删除';
          } else {
            $len = strlen($item['_info']['title']);
            $item['_info']['title'] = $len > 16 ? mb_substr($item['_info']['title'], 0, 16) . '...' : $item['_info']['title'];
          }
          break;
      }
      if ($item['paid'] == 1) {
        switch ($item['pay_type']) {
          case 'weixin':
          case 'bytedance.weixin':
            $item['pay_type_name'] = '微信支付';
            break;
          case 'alipay':
          case 'bytedance.alipay':
            $item['pay_type_name'] = '支付宝支付';
            break;
          case 'bytedance.douyin':
            $item['pay_type_name'] = '抖音支付';
            break;
          case 'yue':
            $item['pay_type_name'] = '余额支付';
            break;
          case 'offline':
            $item['pay_type_name'] = '线下支付';
            break;
          default:
            $item['pay_type_name'] = '其他支付';
            break;
        }
      } else {
        switch ($item['pay_type']) {
          case 'offline':
            $item['pay_type_name'] = '线下支付';
            $item['pay_type_info'] = 1;
            break;
          case 'bytedance':
            $item['pay_type_name'] = '发起字节跳动担保支付';
            break;
          case 'weixin':
          case 'bytedance.weixin':
            $item['pay_type_name'] = '微信支付';
            break;
          case 'alipay':
          case 'bytedance.alipay':
            $item['pay_type_name'] = '支付宝支付';
            break;
          case 'bytedance.douyin':
            $item['pay_type_name'] = '抖音支付';
            break;
          case 'yue':
            $item['pay_type_name'] = '余额支付';
            break;
          default:
            $item['pay_type_name'] = '未支付';
            break;
        }
      }
      if ($item['paid'] == 0 && $item['status'] == 0) {
        $item['status_name'] = '未支付';
      } else if ($item['paid'] == 1 && $item['status'] == 0 && $item['refund_status'] == 0 && $item['type'] != 2) {
        $item['status_name'] = '已支付';
      } else if ($item['paid'] == 1 && $item['status'] == 0 && $item['refund_status'] == 0 && $item['type'] == 2) {
        $item['status_name'] = '待发货';
      } else if ($item['paid'] == 1 && $item['status'] == 1 && $item['refund_status'] == 0 && $item['type'] == 2) {
        $item['status_name'] = '待收货';
      } else if ($item['paid'] == 1 && $item['status'] == 2 && $item['refund_status'] == 0 && $item['type'] == 2) {
        $item['status_name'] = '已完成';
      } else if ($item['paid'] == 1 && $item['refund_status'] == 1) {
        $item['status_name'] = <<<HTML
<b style="color:#f124c7">申请退款</b><br/>
HTML;
      } else if ($item['paid'] == 1 && $item['refund_status'] == 2) {
        $item['status_name'] = '已退款';
      }
      if ($item['paid'] == 0 && $item['status'] == 0 && $item['refund_status'] == 0) {
        $item['_status'] = 1;
      } else if ($item['paid'] == 1 && $item['status'] == 0 && $item['refund_status'] == 0) {
        $item['_status'] = 2;
      } else if ($item['paid'] == 1 && $item['refund_status'] == 1) {
        $item['_status'] = 3;
      } else if ($item['paid'] == 1 && $item['status'] == 1 && $item['refund_status'] == 0) {
        $item['_status'] = 4;
      } else if ($item['paid'] == 1 && $item['status'] == 2 && $item['refund_status'] == 0) {
        $item['_status'] = 5;
      } else if ($item['paid'] == 1 && $item['status'] == 3 && $item['refund_status'] == 0) {
        $item['_status'] = 6;
      } else if ($item['paid'] == 1 && $item['refund_status'] == 2) {
        $item['_status'] = 7;
      }
      $item['spread_name'] = '';
      $item['spread_name_two'] = '';
      $item['add_time'] = $item['add_time'] ? date('Y-m-d H:i:s', $item['add_time']) : '';
      if ($item['type'] == 0 || $item['type'] == 1) {
        if ($item['link_pay_uid']) {
          $spread_name = User::where('uid', $item['link_pay_uid'])->value('nickname');
          $item['spread_name'] = $spread_name ? $spread_name . '/' . $item['link_pay_uid'] : '无';
          $spread_uid_two = User::where('uid', $item['link_pay_uid'])->value('spread_uid');
          if ($spread_uid_two) {
            $spread_name_two = User::where('uid', $spread_uid_two)->value('nickname');
            $item['spread_name_two'] = $spread_name_two ? $spread_name_two . '/' . $spread_uid_two : '无';
          } else {
            $item['spread_name_two'] = '无';
          }
        } else if ($item['spread_uid']) {
          $spread_name = User::where('uid', $item['spread_uid'])->value('nickname');
          $item['spread_name'] = $spread_name ? $spread_name . '/' . $item['spread_uid'] : '无';
          $spread_uid_two = User::where('uid', $item['spread_uid'])->value('spread_uid');
          if ($spread_uid_two) {
            $spread_name_two = User::where('uid', $spread_uid_two)->value('nickname');
            $item['spread_name_two'] = $spread_name_two ? $spread_name_two . '/' . $spread_uid_two : '无';
          } else {
            $item['spread_name_two'] = '无';
          }
        } else {
          $item['spread_name'] = '无';
          $item['spread_name_two'] = '无';
        }
      } else {
        $item['spread_name'] = '不参与分销';
        $item['spread_name_two'] = '不参与分销';
      }

      $link_invite_id = $item['link_invite_id'];
      $record  = SpecialActivityInvitationRecord::where('id', $link_invite_id)->find();
      if ($record) {
        $activity_title = SpecialActivity::where('id', $record['activity_id'])->value('title');
        $activity_name = $activity_title ? mb_substr($activity_title, 0, 13) . '...' : '';
        $item['link_invite_info'] = <<<HTML
<b style="color:#f124c7">$activity_name</b><br/>
邀请记录：<b>$link_invite_id</b><br/>
HTML;
      }
    }
    if (isset($where['excel']) && $where['excel'] == 1) {
      self::SaveSpecialExcel($data);
    }
    $count = self::getSpecialOrderWhere($where, self::alias('a')->join('user r', 'r.uid=a.uid', 'LEFT'), 'a.', 'r')->count();
    return compact('count', 'data');
  }
  /*
     * 保存并下载excel
     * $list array
     * return
     */
  public static function SaveExcel($type = 1, $list = [], $where = [])
  {
    $export = [];
    foreach ($list as $index => $item) {
      if (isset($where['product_id']) && $where['product_id'] != 0) {
        $_info = StoreOrderCartInfo::where('oid', $item['id'])->where('product_id', $where['product_id'])->column('cart_info');
      } else {
        $_info = StoreOrderCartInfo::where('oid', $item['id'])->column('cart_info');
      }
      $goodsName = "";
      $cart_sum = 0;
      $total_price = 0;
      $total_postage = 0;
      foreach ($_info as $k => $v) {
        $v = json_decode($v, true);
        $suk = '';
        if (isset($v['productInfo']['attrInfo'])) {
          if (isset($v['productInfo']['attrInfo']['suk'])) {
            $suk = '(' . $v['productInfo']['attrInfo']['suk'] . ')';
          }
        }
        $user_address = explode(' ', $item['user_address']);
        // 获取省、市、区部分
        $province = $user_address[0] ?? '';
        $addr = UserAddress::where('uid', $item['uid'])->where('province', $province)->find();
        $payPostage = self::getOrderPostagePriceGroup([$v], $addr)['storePostage'];
        if ($type == 2) {
          $goodsName .= implode(
            ' ',
            [
              $v['productInfo']['store_name'],
              $suk,
              "[{$v['cart_num']} * {$v['productInfo']['ot_price']}]"
            ]
          );
          $total_price = bcadd($total_price, bcmul($v['cart_num'], $v['productInfo']['ot_price'], 2), 2);
          $total_postage = $payPostage;
        } else {
          $goodsName .= implode(
            ' ',
            [
              $v['productInfo']['store_name'],
              $suk,
              "[{$v['cart_num']} * {$v['truePrice']}]"
            ]
          );
          $total_postage += $payPostage;
        }
        $cart_sum += $v['cart_num'];
      }
      $item['cartInfo'] = $_info;
      $sex = WechatUser::where('uid', $item['uid'])->value('sex');
      if ($sex == 1) $sex_name = '男';
      else if ($sex == 2) $sex_name = '女';
      else $sex_name = '未知';
      $WechatUserInfo = User::where('uid', $item['uid'])->value('nickname') ?: '暂无信息';
      $platform_users = $item['uid'] . '/' . $WechatUserInfo . '/' . $item['phone'];
      $user_address = explode(' ', $item['user_address']);
      // 获取省、市、区部分
      $province = $user_address[0] ?? '收件人省：异常';
      $city = $user_address[1] ?? '收件人省：异常';
      $district = $user_address[2] ?? '收件人区：异常';
      $detailAddress = !empty($user_address) ? '收件人详细地址：异常' : '';
      if (count($user_address) > 3) {
        $detailAddress = implode(' ', array_slice($user_address, 3));
      }
      if ($type == 2) {
        // 后浪导出格式
        $export[] = [
          $item['order_id'],
          $item['real_name'],
          $province,
          $city,
          $district,
          $detailAddress,
          $item['user_phone'],
          $goodsName,
          $item['total_num'],
          $total_price,
          $total_postage,
          $item['pay_time'] > 0 ? date('Y/m-d H:i:s', $item['pay_time']) : '暂无',
          $item['delivery_id'],
          $item['delivery_name'],
        ];
      } else if ($type == 3) {
        $note =  $item['pay_time'] > 0 ? '购买时间：' . date('Y-m-d H:i:s', $item['pay_time']) . '承诺发货时间：' . getNextMonday9AM(date('Y-m-d H:i:s', $item['pay_time'])) : '未购买';
        // 花城导出格式
        $export[] = [
          $item['order_id'],
          '北京源未文化',
          $item['real_name'],
          $item['user_phone'],
          $province,
          $city,
          $district,
          $detailAddress,
          '978-7-5749-0293-0',
          '',
          $cart_sum,
          $total_postage,
          $note,
        ];
      } else if ($type == 4) {
        // 花城订单状态：导出格式  原始单号  订单状态  付款时间  发货时间  收货地区  物流公司  物流单号  商品编码  商品名称  实发数量
        $time = StoreOrderStatus::where('oid', $item['id'])->where('change_type', 'delivery_goods')->order('change_time desc')->value('change_time');
        $delivery_time = $time ? Carbon::createFromTimestamp($time)->format('Y-m-d H:i:s') : '暂未发货';
        $status_name = $item['status_name'] ?? '';
        if ($status_name != "") {
          $allowed_status = ['待评价'];
          if (in_array($status_name, $allowed_status)) {
            $status_name = '已完成';
          }
        }
        if ($delivery_time != "暂未发货") {
          if ($status_name == '待评价') {
            $status_name = '已完成';
          } else {
            if (!in_array($status_name, ['退款中', '已退款'])) {
              // Check if 7 days have passed since the delivery time
              $delivery_time_carbon = Carbon::createFromFormat('Y-m-d H:i:s', $delivery_time);
              $is_max_7_day = Carbon::now()->diffInDays($delivery_time_carbon) >= 7;

              if ($is_max_7_day) {
                $status_name = '已完成';
              }
            }
          }
        }
        $export[] = [
          $item['order_id'],
          $status_name,
          date('Y-m-d H:i:s', $item['pay_time']),
          $delivery_time,
          $province . '-' . $city . '-' . $district,
          $item['delivery_name'],
          $item['delivery_id'],
          '978-7-5749-0293-0',
          '如果古典乐被戳破',
          $cart_sum,
          $total_postage,
        ];
      } else {
        // 默认导出格式
        $export[] = [
          $item['order_id'],
          $platform_users,
          $sex_name,
          $item['phone'],
          $item['real_name'],
          $item['user_phone'],
          $item['user_address'],
          '',
          $cart_sum,
          $item['total_price'],
          $item['pay_price'],
          $total_postage,
          $item['coupon_price'],
          isset($item['pay_type_name']) ? $item['pay_type_name'] : '',
          $item['pay_time'] > 0 ? date('Y/m-d H:i', $item['pay_time']) : '暂无',
          isset($item['status_name']) ? $item['status_name'] : '',
          $item['add_time'],
          $item['mark']
        ];
      }
    }
    switch ($type) {
      case 2:

        $title = '后浪-书籍订单导出';
        PHPExcelService::instance()->setCustomizeExcelHeader([
          '订单号',
          '收件人',
          '省',
          '市',
          '区',
          '收货地址',
          '手机',
          '商品名称',
          '货品数量',
          '定价',
          '邮费',
          '购买时间',
          '快递单号',
          '快递公司'
        ])
          ->setCustomizeExcelTile($title)
          ->setCustomizeExcelContent($export)
          ->ExcelSave($title);
        break;

      case 3:
        $title = '花城&源未-书籍待发货订单导出';
        PHPExcelService::instance()->setCustomizeExcelHeader([
          '原始单号',
          '店铺',
          '收件人姓名',
          '收件人手机',
          '收件人省',
          '收件人市',
          '收件人区/县',
          '收件人详细地址',
          '商品编码',
          '商品标题',
          '商品数量',
          '邮费',
          '备注',
        ])
          ->setCustomizeExcelTile($title)
          ->setCustomizeExcelContent($export)
          ->ExcelSave($title);
        break;

      case 4:
        $title = '花城&源未-书籍订单明细导出';
        PHPExcelService::instance()->setCustomizeExcelHeader([
          '原始单号',
          '订单状态',
          '付款时间',
          '发货时间',
          '收货地区',
          '物流公司',
          '物流单号',
          '商品编码',
          '商品名称',
          '实发数量',
          '邮费'
        ])
          ->setCustomizeExcelTile($title)
          ->setCustomizeExcelContent($export)
          ->ExcelSave($title);
        break;

      default:
        $title = '订单导出';
        PHPExcelService::instance()->setExcelHeader([
          '订单号',
          '用户信息',
          '性别',
          '电话',
          '收货人姓名',
          '收货人电话',
          '收货地址',
          '商品信息',
          '商品总件数',
          '总价格',
          '实际支付',
          '邮费',
          '优惠金额',
          '支付状态',
          '支付时间',
          '订单状态',
          '下单时间',
          '用户备注'
        ])
          ->setExcelTile($title, '订单信息' . time(), ' 生成时间：' . date('Y-m-d H:i:s', time()))
          ->setExcelContent($export)
          ->ExcelSave($title);
        break;
    }
  }

  /**
   * 保存并下载excel
   * $list array
   * return
   */
  public static function SaveSpecialExcel($list)
  {
    $export = [];
    foreach ($list as $index => $item) {
      $_info = $item['_info'];
      $goodsName = $_info['title'];
      $WechatUserInfo = User::where('uid', $item['uid'])->value('nickname') ?: '暂无信息';
      $platform_users = $item['uid'] . '/' . $WechatUserInfo . '/' . $item['phone'];
      $user_address = explode(' ', $item['user_address']);
      // 默认导出格式
      $export[] = [
        $item['order_id'],
        $platform_users,
        $item['real_name'],
        $item['mark'],
        $goodsName,
        $item['total_price'],
        $item['pay_price'],
        $item['coupon_price'],
        isset($item['pay_type_name']) ? $item['pay_type_name'] : '',
        $item['pay_time'] > 0 ? date('Y/m-d H:i', $item['pay_time']) : '暂无',
        isset($item['status_name']) ? $item['status_name'] : '',
        $item['add_time'],
        $item['mark']
      ];
    }
    $header = [
      '订单号',
      '用户信息',
      '购课人姓名',
      '购课人电话',
      '课程信息',
      '总价格',
      '实际支付',
      '优惠金额',
      '支付状态',
      '支付时间',
      '订单状态',
      '下单时间',
      '用户备注'
    ];
    $name = '订单信息';
    $is_save = true;
    $suffix = 'xlsx';
    $info = date('Y-m-d H:i:s', time());
    $title =  '课程订单导出-' . date('YmdHis', time());
    $title = '课程订单导出-' . date('YmdHis', time());

    PHPExcelService::instance()->setExcelHeader($header)
      ->setExcelTile($title, $name, $info)
      ->setExcelContent($export)
      ->ExcelSave($title);

    $path = self::siteUrl() . $path;
    return [$path];
  }

  /**
   * @param $where
   * @return array
   */
  public static function systemPage($where, $userid = false)
  {
    $model = self::getOrderWhere($where, self::alias('a')
      ->join('user r', 'r.uid=a.uid', 'LEFT'), 'a.', 'r')
      ->field('a.*,r.nickname');
    if ($where['order']) {
      $model = $model->order('a.' . $where['order']);
    } else {
      $model = $model->order('a.id desc');
    }
    if ($where['export'] == 1) {
      $list = $model->select()->toArray();
      $export = [];
      foreach ($list as $index => $item) {
        if ($item['pay_type'] == 'weixin' || $item['pay_type'] == 'bytedance.weixin') {
          $payType = '微信支付';
        } elseif ($item['pay_type'] == 'alipay' || $item['pay_type'] == 'bytedance.alipay') {
          $payType = '支付宝支付';
        } elseif ($item['pay_type'] == 'bytedance.douyin') {
          $payType = '抖音支付';
        } elseif ($item['pay_type'] == 'yue') {
          $payType = '余额支付';
        } elseif ($item['pay_type'] == 'offline') {
          $payType = '线下支付';
        } else {
          $payType = '其他支付';
        }
        $_info = StoreOrderCartInfo::where('oid', $item['id'])->column('cart_info', 'oid');
        $goodsName = [];
        foreach ($_info as $k => $v) {
          $v = json_decode($v, true);
          $goodsName[] = implode(
            [
              $v['productInfo']['store_name'],
              isset($v['productInfo']['attrInfo']) ? '(' . $v['productInfo']['attrInfo']['suk'] . ')' : '',
              "[{$v['cart_num']} * {$v['truePrice']}]"
            ],
            ' '
          );
        }
        $item['cartInfo'] = $_info;
        $export[] = [
          $item['order_id'],
          $payType,
          $item['total_num'],
          $item['total_price'],
          $item['total_postage'],
          $item['pay_price'],
          $item['refund_price'],
          $item['mark'],
          $item['remark'],
          [$item['real_name'], $item['user_phone'], $item['user_address']],
          $goodsName,
          [$item['paid'] == 1 ? '已支付' : '未支付', '支付时间: ' . ($item['pay_time'] > 0 ? date('Y/md H:i', $item['pay_time']) : '暂无')]

        ];
        $list[$index] = $item;
      }
      PHPExcelService::instance()->setExcelHeader(['订单号', '支付方式', '商品总数', '商品总价', '邮费', '支付金额', '退款金额', '用户备注', '管理员备注', '收货人信息', '商品信息', '支付状态'])
        ->setExcelTile('订单导出', '订单信息' . time(), ' 生成时间：' . date('Y-m-d H:i:s', time()))
        ->setExcelContent($export)
        ->ExcelSave();
    }
    return self::page($model, function ($item) {
      $_info = StoreOrderCartInfo::where('oid', $item['id'])->field('cart_info')->select();
      foreach ($_info as $k => $v) {
        $_info[$k]['cart_info'] = json_decode($v['cart_info'], true);
      }
      $item['_info'] = $_info;
      if ($item['pink_id'] && $item['combination_id']) {
        $pinkStatus = StorePink::where('order_id_key', $item['id'])->value('status');
        switch ($pinkStatus) {
          case 1:
            $item['pink_name'] = '[拼团订单]正在进行中';
            $item['color'] = '#f00';
            break;
          case 2:
            $item['pink_name'] = '[拼团订单]已完成';
            $item['color'] = '#00f';
            break;
          case 3:
            $item['pink_name'] = '[拼团订单]未完成';
            $item['color'] = '#f0f';
            break;
          default:
            $item['pink_name'] = '[拼团订单]历史订单';
            $item['color'] = '#457856';
            break;
        }
      } else {
        if ($item['seckill_id']) {
          $item['pink_name'] = '[秒杀订单]';
          $item['color'] = '#32c5e9';
        } elseif ($item['bargain_id']) {
          $item['pink_name'] = '[砍价订单]';
          $item['color'] = '#12c5e9';
        } elseif ($item['wx_shop_order_id']) {
          $item['pink_name'] = '[微信小商店订单]' . $item['type'] == 5 ?  '普通商品' : '视频专题';
          $item['color'] = '#12c5e9';
        } else {
          $item['pink_name'] = '[普通订单]';
          $item['color'] = '#895612';
        }
      }
    }, $where);
  }

  public static function statusByWhere($status, $model = null, $alert = '')
  {
    if ($model == null) $model = new self;
    if ('' === $status)
      return $model;
    else if ($status == 8)
      return $model;
    else if ($status == 0) //未支付
      return $model->whereIn('type', [0, 1, 2, 5])->where($alert . 'paid', 0)->where($alert . 'status', 0)->where($alert . 'refund_status', 0)->where($alert . 'is_del', 0);
    else if ($status == 1) //已支付 未发货
      return $model->whereIn('type', [0, 1, 2, 5])->where($alert . 'paid', 1)->where($alert . 'status', 0)->where($alert . 'shipping_type', 1)->where($alert . 'refund_status', 0)->where($alert . 'is_del', 0);
    else if ($status == 2) //已支付  待收货
      return $model->whereIn('type', [0, 1, 2, 3, 5])->where($alert . 'paid', 1)->where($alert . 'status', 1)->where($alert . 'refund_status', 0)->where($alert . 'is_del', 0);
    else if ($status == 3) // 已支付  已收货  待评价
      return $model->whereIn('type', [0, 1, 2, 3, 5])->where($alert . 'paid', 1)->where($alert . 'status', 2)->where($alert . 'refund_status', 0)->where($alert . 'is_del', 0);
    else if ($status == 4) // 交易完成
      return $model->whereIn('type', [0, 1, 2, 5])->where($alert . 'paid', 1)->where($alert . 'status', 3)->where($alert . 'refund_status', 0)->where($alert . 'is_del', 0);
    else if ($status == 5) //已支付  待核销
      return $model->whereIn('type', [0, 1, 2, 5])->where($alert . 'paid', 1)->where($alert . 'status', 0)->where($alert . 'shipping_type', 2)->where($alert . 'refund_status', 0)->where($alert . 'is_del', 0);
    else if ($status == 6) //课程订单
      return $model->whereIn('type', [0, 1, 2, 5])->where($alert . 'combination_id', 0)->where($alert . 'is_del', 0)->where($alert . 'is_gift', 0);
    else if ($status == 7) // 礼物订单
      return $model->whereIn('type', [0, 1, 2, 5])->where($alert . 'combination_id', 0)->where($alert . 'is_del', 0)->where($alert . 'is_gift', '>', 0);
    else if ($status == 9) //已卖出
      return $model->whereIn('type', [0, 1, 2, 5])->where($alert . 'paid', 1)->where($alert . 'refund_status', 0)->where($alert . 'is_del', 0);
    else if ($status == -1) //退款中
      return $model->whereIn('type', [0, 1, 2, 5])->where($alert . 'paid', 1)->where($alert . 'refund_status', 1)->where($alert . 'is_del', 0);
    else if ($status == -2) //已退款
      return $model->whereIn('type', [0, 1, 2, 5])->where($alert . 'paid', 1)->where($alert . 'refund_status', 2)->where($alert . 'is_del', 0);
    else if ($status == -3) //退款
      return $model->whereIn('type', [0, 1, 2, 5])->where($alert . 'paid', 1)->where($alert . 'refund_status', 'in', '1,2')->where($alert . 'is_del', 0);
    else if ($status == -4) //已删除
      return $model->whereIn('type', [0, 1, 2, 5])->where($alert . 'is_del', 1);
    else
      return $model;
  }

  public static function statusWxByWhere($status, $model = null, $alert = '')
  {
    if ($model == null) $model = new self;
    if ('' === $status)
      return $model;
    else if ($status == 8)
      return $model;
    else if ($status == 0) //未支付
      return $model->whereIn('type', [5, 6])->where($alert . 'paid', 0)->where($alert . 'status', 0)->where($alert . 'refund_status', 0)->where($alert . 'is_del', 0);
    else if ($status == 1) //已支付 未发货
      return $model->whereIn('type', [5, 6])->where($alert . 'paid', 1)->where($alert . 'status', 0)->where($alert . 'shipping_type', 1)->where($alert . 'refund_status', 0)->where($alert . 'is_del', 0);
    else if ($status == 2) //已支付  待收货
      return $model->whereIn('type', [5, 6])->where($alert . 'paid', 1)->where($alert . 'status', 1)->where($alert . 'shipping_type', 1)->where($alert . 'refund_status', 0)->where($alert . 'is_del', 0);
    else if ($status == 3) // 已支付  已收货  待评价
      return $model->whereIn('type', [5, 6])->where($alert . 'paid', 1)->where($alert . 'status', 2)->where($alert . 'refund_status', 0)->where($alert . 'is_del', 0);
    else if ($status == 4) // 交易完成
      return $model->whereIn('type', [5, 6])->where($alert . 'paid', 1)->where($alert . 'status', 3)->where($alert . 'refund_status', 0)->where($alert . 'is_del', 0);
    else if ($status == 5) //已支付  待核销
      return $model->whereIn('type', [5, 6])->where($alert . 'paid', 1)->where($alert . 'status', 0)->where($alert . 'shipping_type', 2)->where($alert . 'refund_status', 0)->where($alert . 'is_del', 0);
    else if ($status == 6) //课程订单
      return $model->whereIn('type', [5, 6])->where($alert . 'combination_id', 0)->where($alert . 'is_del', 0)->where($alert . 'is_gift', 0);
    else if ($status == 7) // 礼物订单
      return $model->whereIn('type', [5, 6])->where($alert . 'combination_id', 0)->where($alert . 'is_del', 0)->where($alert . 'is_gift', '>', 0);
    else if ($status == 9) //已卖出
      return $model->whereIn('type', [5, 6])->where($alert . 'paid', 1)->where($alert . 'refund_status', 0)->where($alert . 'is_del', 0);
    else if ($status == -1) //退款中
      return $model->whereIn('type', [5, 6])->where($alert . 'paid', 1)->where($alert . 'refund_status', 1)->where($alert . 'is_del', 0);
    else if ($status == -2) //已退款
      return $model->whereIn('type', [5, 6])->where($alert . 'paid', 1)->where($alert . 'refund_status', 2)->where($alert . 'is_del', 0);
    else if ($status == -3) //退款
      return $model->whereIn('type', [5, 6])->where($alert . 'paid', 1)->where($alert . 'refund_status', 'in', '1,2')->where($alert . 'is_del', 0);
    else if ($status == -4) //已删除
      return $model->whereIn('type', [5, 6])->where($alert . 'is_del', 1);
    else
      return $model;
  }

  public static function statusSpecialByWhere($status, $model = null, $alert = '')
  {
    if ($model == null) $model = new self;
    if ('' === $status)
      return $model;
    else if ($status == 0) //未支付
      return $model->where($alert . 'type', 3)->where($alert . 'paid', 0)->where($alert . 'status', 0)->where($alert . 'refund_status', 0)->where($alert . 'is_del', 0);
    else if ($status == 1) //已支付 未发货
      return $model->where($alert . 'type', 3)->where($alert . 'paid', 1)->where($alert . 'status', 0)->where($alert . 'shipping_type', 1)->where($alert . 'refund_status', 0)->where($alert . 'is_del', 0)->where($alert . 'pay_type', '<>', 'yue')->where('pay_price', '>', '1.00');
    else if ($status == 2) //已支付  待收货
      return $model->where($alert . 'type', 3)->where($alert . 'paid', 1)->where($alert . 'status', 1)->where($alert . 'shipping_type', 1)->where($alert . 'refund_status', 0)->where($alert . 'is_del', 0);
    else if ($status == 3) // 已支付  已收货  待评价
      return $model->where($alert . 'type', 3)->where($alert . 'paid', 1)->where($alert . 'status', 2)->where($alert . 'refund_status', 0)->where($alert . 'is_del', 0);
    else if ($status == 4) // 交易完成
      return $model->where($alert . 'type', 3)->where($alert . 'paid', 1)->where($alert . 'status', 3)->where($alert . 'refund_status', 0)->where($alert . 'is_del', 0);
    else if ($status == 5) //已支付  待核销
      return $model->where($alert . 'type', 3)->where($alert . 'paid', 1)->where($alert . 'status', 0)->where($alert . 'shipping_type', 2)->where($alert . 'refund_status', 0)->where($alert . 'is_del', 0);
    else if ($status == 6) //课程订单
      return $model->where($alert . 'type', 3)->where($alert . 'paid', 1)->where($alert . 'refund_status', 0)->where($alert . 'is_del', 0)->where($alert . 'pay_type', '<>', 'yue')->whereIn($alert . 'is_gift', [0, 1])->where('pay_price', '>', '1.00');
    else if ($status == 7) //领礼物订单
      return $model->where($alert . 'type', 3)->where($alert . 'paid', 1)->where($alert . 'combination_id', 0)->where($alert . 'refund_status', 0)->where($alert . 'pay_type', '<>', 'yue')->where($alert . 'is_del', 0)->where($alert . 'is_system_del', 0)->where($alert . 'is_receive_gift', 1)->where('pay_price', '0.00');
    else if ($status == 8) // 测试订单
      return $model->where($alert . 'type', 3)->where($alert . 'paid', 1)->where($alert . 'refund_status', 0)->where($alert . 'is_del', 0)->where($alert . 'is_receive_gift', 0)->where(function ($query) use ($alert) {
        $query->where($alert . 'pay_price', '<', '1.00')
          ->whereOr([
            [$alert . 'pay_type', '=', 'yue'],
            // [$alert . 'pay_price', '>', '1.00']
          ]);
      });
    else if ($status == 9) //已卖出
      return $model->where($alert . 'type', 3)->where($alert . 'paid', 1)->where($alert . 'refund_status', 0)->where($alert . 'is_del', 0);
    else if ($status == -1) //退款中
      return $model->where($alert . 'type', 3)->where($alert . 'paid', 1)->where($alert . 'refund_status', 1)->where($alert . 'is_del', 0);
    else if ($status == -2) //已退款
      return $model->where($alert . 'type', 3)->where($alert . 'paid', 1)->where($alert . 'refund_status', 2)->where($alert . 'is_del', 0);
    else if ($status == -3) //退款
      return $model->where($alert . 'type', 3)->where($alert . 'paid', 1)->where($alert . 'refund_status', 'in', '1,2')->where($alert . 'is_del', 0);
    else if ($status == -4) //已删除
      return $model->where($alert . 'type', 3)->where($alert . 'is_del', 1);
    else if ($status == -5) //已卖出
      return $model->where($alert . 'type', 3)->where($alert . 'paid', 1)->where($alert . 'refund_status', 0)->where($alert . 'is_del', 0)->where('pay_type', '<>', 'yue')->where($alert . 'pay_price', '>=', '1.00');
    else
      return $model;
  }


  public static function timeQuantumWhere($startTime = null, $endTime = null, $model = null)
  {
    if ($model === null) $model = new self;
    if ($startTime != null && $endTime != null)
      $model = $model->where('add_time', '>', strtotime($startTime))->where('add_time', '<', strtotime($endTime));
    return $model;
  }

  public static function changeOrderId($orderId)
  {
    $ymd = substr($orderId, 2, 8);
    $key = substr($orderId, 16);
    return 'wx' . $ymd . date('His') . $key;
  }

  /**
   * 线下付款
   * @param $id
   * @return $this
   */
  public static function updateOffline($id)
  {
    $count = self::where('id', $id)->count();
    if (!$count) return self::setErrorInfo('订单不存在');
    $count = self::where('id', $id)->where('paid', 0)->count();
    if (!$count) return self::setErrorInfo('订单已支付');
    $res = self::where('id', $id)->update(['paid' => 1, 'pay_time' => time()]);
    return $res;
  }

  /**
   * TODO 公众号退款发送模板消息
   * @param $oid
   * $oid 订单id  key
   */
  public static function refundTemplate($data, $oid)
  {
    $order = self::where('id', $oid)->find();
    WechatTemplateService::sendTemplate(WechatUser::where('uid', $order['uid'])->value('openid'), WechatTemplateService::ORDER_REFUND_STATUS, [
      'first' => '亲，您购买的商品已退款,本次退款' . $data['refund_price'] . '金额',
      'keyword1' => $order['order_id'],
      'keyword2' => $order['pay_price'],
      'keyword3' => date('Y-m-d H:i:s', $order['add_time']),
      'remark' => '点击查看订单详情'
    ], Url::buildUrl('/order/detail/' . $order['order_id'])->suffix('')->domain(true)->build());
  }

  /**
   * TODO 小程序余额退款模板消息
   * @param $oid
   * @return bool|mixed
   * @throws \think\db\exception\DataNotFoundException
   * @throws \think\db\exception\ModelNotFoundException
   * @throws \think\exception\DbException
   */
  public static function refundRoutineTemplate($oid)
  {
    $order = self::where('id', $oid)->find();
    $store_name = self::getProductTitle($order['cart_id']) ?: '****';
    return RoutineTemplate::sendOrderRefundSuccess($order['uid'], $order['order_id'], $store_name, $order['pay_price'], $order['refund_price']); //订阅提醒
  }

  /**
   * 查找活动购物车里的所有活动标题
   * @param $cartId 购物车id
   * @return bool|string
   */
  public static function getProductTitle($cartId)
  {
    $title = '';
    try {
      $orderCart = StoreOrderCartInfo::where('cart_id', 'in', $cartId)->field('cart_info')->select();
      foreach ($orderCart as $item) {
        $cart_info = json_decode($item['cart_info'], true);
        if (isset($cart_info['productInfo']['store_name'])) {
          $title .= $cart_info['productInfo']['store_name'] . '|';
        }
      }
      unset($item);
      if (!$title) {
        $productIds = StoreCart::where('id', 'in', $cartId)->column('product_id');
        $productlist = ($productlist = StoreProduct::getProductField($productIds, 'store_name')) ? $productlist->toArray() : [];
        foreach ($productlist as $item) {
          if (isset($item['store_name'])) $title .= $item['store_name'] . '|';
        }
      }
      if ($title) $title = substr($title, 0, strlen($title) - 1);
      unset($item);
    } catch (\Exception $e) {
    }
    return $title;
  }

  /**
   * 处理where条件
   * @param $where
   * @param $model
   * @return mixed
   */
  public static function getOrderWhere($where, $model, $aler = '', $join = '')
  {
    // $model = $model->where('combination_id',0);
    if ($where['type'] == 5) {
      $model = $model->where('is_system_del', 0)->whereIn('type', [5, 6]);
    } else {
      $model = $model->where('is_system_del', 0)->whereIn('type', [0, 1, 2]);
    }
    if (isset($where['status']) && $where['status'] != '') {
      $model = self::statusByWhere($where['status'], $model, $aler);
    } else {
      $model = $model->where('is_del', 0);
    }
    // 加入 product_id 过滤条件
    if (isset($where['product_id']) && $where['product_id'] != "") {
      $model = $model->join('store_order_cart_info c', 'c.oid =  id', 'LEFT')
        ->where('c.product_id', $where['product_id']);
    }

    if (isset($where['is_del']) && $where['is_del'] != '' && $where['is_del'] != -1) $model = $model->where($aler . 'is_del', $where['is_del']);
    if (isset($where['combination_id'])) {
      if ($where['combination_id'] == '普通订单') {
        $model = $model->where($aler . 'combination_id', 0)->where($aler . 'seckill_id', 0)->where($aler . 'bargain_id', 0);
      }
      if ($where['combination_id'] == '拼团订单') {
        $model = $model->where($aler . 'combination_id', ">", 0)->where($aler . 'pink_id', ">", 0);
      }
      if ($where['combination_id'] == '秒杀订单') {
        $model = $model->where($aler . 'seckill_id', ">", 0);
      }
      if ($where['combination_id'] == '砍价订单') {
        $model = $model->where($aler . 'bargain_id', ">", 0);
      }
    }
    if (isset($where['pay_type'])) {
      switch ($where['pay_type']) {
        case 1:
          $model = $model->whereIn($aler . 'pay_type', ['weixin', 'bytedance.weixin']);
          break;
        case 2:
          $model = $model->whereIn($aler . 'pay_type', ['yue',]);
          break;
        case 3:
          $model = $model->whereIn($aler . 'pay_type', ['offline']);
          break;
        case 4:
          $model = $model->whereIn($aler . 'pay_type', ['alipay', 'bytedance.alipay']);
          break;
      }
    }
    if (isset($where['type'])) {
      switch ($where['type']) {
        case 1:
          $model = $model->where($aler . 'combination_id', 0)->where($aler . 'seckill_id', 0)->where($aler . 'bargain_id', 0);
          break;
        case 2:
          //                    $model = $model->where($aler.'combination_id',">",0)->where($aler.'pink_id',">",0);
          $model = $model->where($aler . 'combination_id', ">", 0);
          break;
        case 3:
          $model = $model->where($aler . 'seckill_id', ">", 0);
          break;
        case 4:
          $model = $model->where($aler . 'bargain_id', ">", 0);
          break;
        case 5:
          $model = $model->where($aler . 'wx_shop_order_id', ">", 0);
          break;
      }
    }

    if (isset($where['real_name']) && $where['real_name'] != '') {
      $model = $model->where($aler . 'order_id|' . $aler . 'real_name|' . $aler . 'user_phone' . ($join ? '|' . $join . '.nickname|' . $join . '.uid|' . $join . '.phone' : ''), 'LIKE', "%$where[real_name]%");
    }
    if (isset($where['data']) && $where['data'] !== '') {
      $model = self::getModelTime($where, $model, $aler . 'add_time');
    }
    return $model;
  }

  /**
   * 处理where条件
   * @param $where
   * @param $model
   * @return mixed
   */
  public static function getSpecialOrderWhere($where, $model, $aler = '', $join = '')
  {

    if ($where['status'] != '') $model = self::statusSpecialByWhere($where['status'], $model, $aler, $where['types']);
    $model = $model->where($aler . 'is_del', $where['is_del'])->where($aler . 'is_system_del', $where['is_del']);
    if ($where['real_name'] != '') {
      $model = $model->where($aler . 'order_id|' . $aler . 'real_name|' . $aler . 'user_phone' . ($join ? '|' . $join . '.nickname|' . $join . '.uid|' . $join . '.phone' : ''), 'LIKE', "%$where[real_name]%");
    }
    if (isset($where['special_id']) && $where['special_id'] != '') {
      $model = $model->where($aler . 'cart_id', $where['special_id']);
    }

    $model = $model->where($aler . 'type', $where['types']);
    if ($where['type'] != '') {
      switch ($where['type']) {
        case 6:
          $model = $model->where($aler . 'type', 3)->where($aler . 'paid', 1)->where($aler . 'refund_status', 0)->where($aler . 'is_del', 0)->where('pay_type', '<>', 'yue')->whereIn($aler . 'is_gift', [0, 1])->where('pay_price', '>', '1.00');
          break;
        case 7:
          // 领礼物：条件就是：支付价格是0。00
          $model = $model->where($aler . 'type', 3)->where($aler . 'paid', 1)->where($aler . 'refund_status', 0)->where($aler . 'combination_id', 0)->where($aler . 'is_del', 0)->where($aler . 'is_system_del', 0)->where('is_receive_gift', 1)->where($aler . 'pay_price', '0.00');
          break;
        case 8:
          // 测试订单
          $model = $model->where($aler . 'type', 3)
            ->where($aler . 'paid', 1)
            ->where($aler . 'refund_status', 0)
            ->where($aler . 'combination_id', 0)
            ->where($aler . 'is_del', 0)
            ->where($aler . 'is_system_del', 0)
            ->where('is_receive_gift', 0)
            ->where(function ($query) use ($aler) {
              $query->where($aler . 'pay_price', '<', '1.00')
                ->whereOr([
                  [$aler . 'pay_type', '=', 'yue'],
                  // [$aler . 'pay_price', '>', '1.00']
                ]);
            });
          break;
      }
    }
    if ($where['data'] !== '') {
      $model = self::getModelTime($where, $model, $aler . 'add_time');
    }
    return $model;
  }

  public static function getBadge($where, $type = 1)
  {
    $price = $type == 2 ? self::getWxOrderPrice($where) : self::getOrderPrice($where);
    return [
      [
        'name' => '订单数量',
        'field' => '条',
        'count' => $price['count_sum'],
        'background_color' => 'layui-bg-blue',
        'col' => 2
      ],
      [
        'name' => '售出商品',
        'field' => '件',
        'count' => $price['total_num'],
        'background_color' => 'layui-bg-blue',
        'col' => 2
      ],
      [
        'name' => '订单金额',
        'field' => '元',
        'count' => $price['pay_price'],
        'background_color' => 'layui-bg-blue',
        'col' => 2
      ],
      [
        'name' => '退款金额',
        'field' => '元',
        'count' => $price['refund_price'],
        'background_color' => 'layui-bg-blue',
        'col' => 2
      ],
      [
        'name' => '微信支付金额',
        'field' => '元',
        'count' => $price['pay_price_wx'],
        'background_color' => 'layui-bg-blue',
        'col' => 2
      ],
      [
        'name' => '余额支付金额',
        'field' => '元',
        'count' => $price['pay_price_yue'],
        'background_color' => 'layui-bg-blue',
        'col' => 2
      ],
      [
        'name' => '运费金额',
        'field' => '元',
        'count' => $price['pay_postage'],
        'background_color' => 'layui-bg-blue',
        'col' => 2
      ],
      [
        'name' => '分佣金额',
        'field' => '元',
        'count' => $price['brokerage'],
        'background_color' => 'layui-bg-blue',
        'col' => 2
      ],
      [
        'name' => '线下支付金额',
        'field' => '元',
        'count' => $price['pay_price_offline'],
        'background_color' => 'layui-bg-blue',
        'col' => 2
      ],
      [
        'name' => '积分抵扣',
        'field' => '分',
        'count' => $price['use_integral'] . '(抵扣金额:￥' . $price['deduction_price'] . ')',
        'background_color' => 'layui-bg-blue',
        'col' => 2
      ],
      [
        'name' => '退回积分',
        'field' => '元',
        'count' => $price['back_integral'],
        'background_color' => 'layui-bg-blue',
        'col' => 2
      ]
    ];
  }

  public static function getSpecialBadge($where)
  {
    $price = self::getSpecialOrderPrice($where);
    switch ($where['types']) {
      case 1:
        $name = '售出商品';
        break;
      case 2:
        $name = '售出虚拟商品';
        break;
      case 3:
        $name = '售出课程';
        break;
    }
    return [
      [
        'name' => '订单数量',
        'field' => '件',
        'count' => $price['order_sum'],
        'background_color' => 'layui-bg-blue',
        'col' => 2
      ],
      [
        'name' => $name,
        'field' => '件',
        'count' => $price['total_num'],
        'background_color' => 'layui-bg-blue',
        'col' => 2
      ],
      [
        'name' => '订单金额',
        'field' => '元',
        'count' => $price['pay_price'],
        'background_color' => 'layui-bg-blue',
        'col' => 2
      ],
      [
        'name' => '退款金额',
        'field' => '元',
        'count' => $price['refund_price'],
        'background_color' => 'layui-bg-blue',
        'col' => 2
      ],
      [
        'name' => '微信支付金额',
        'field' => '元',
        'count' => $price['pay_price_wx'],
        'background_color' => 'layui-bg-blue',
        'col' => 2
      ],
      [
        'name' => '余额支付金额',
        'field' => '元',
        'count' => $price['pay_price_yue'],
        'background_color' => 'layui-bg-blue',
        'col' => 2
      ],
      [
        'name' => '线下支付金额',
        'field' => '元',
        'count' => $price['pay_price_offline'],
        'background_color' => 'layui-bg-blue',
        'col' => 2
      ]
    ];
  }

  /**
   * 处理订单金额
   * @param $where
   * @return array
   */
  public static function getOrderPrice($where)
  {
    $where['is_del'] = 0; //删除订单不统计
    $model = new self;
    $price = [];
    $price['pay_price'] = 0; //支付金额
    $price['refund_price'] = 0; //退款金额
    $price['pay_price_wx'] = 0; //微信支付金额
    $price['pay_price_ali'] = 0; //支付宝支付金额
    $price['pay_price_yue'] = 0; //余额支付金额
    $price['pay_price_offline'] = 0; //线下支付金额
    $price['pay_price_other'] = 0; //其他支付金额
    $price['use_integral'] = 0; //用户使用积分
    $price['back_integral'] = 0; //退积分总数
    $price['deduction_price'] = 0; //抵扣金额
    $price['total_num'] = 0; //商品总数
    $price['count_sum'] = 0; //商品总数
    $price['brokerage'] = 0;
    $price['pay_postage'] = 0;
    $whereData = ['is_del' => 0];
    if ($where['status'] == '') {
      $whereData['paid'] = 1;
      $whereData['refund_status'] = 0;
    }
    $ids = self::getOrderWhere($where, $model)->where($whereData)->column('id');
    if (count($ids)) {
      $price['brokerage'] = UserBill::where(['category' => 'now_money', 'type' => 'brokerage'])->where('link_id', 'in', $ids)->sum('number');
    }
    $price['refund_price'] = self::getOrderWhere($where, $model)->where(['is_del' => 0, 'paid' => 1, 'refund_status' => 2])->sum('refund_price');
    $sumNumber = self::getOrderWhere($where, $model)->where($whereData)->field([
      'sum(total_num) as sum_total_num',
      'count(id) as count_sum',
      'sum(pay_price) as sum_pay_price',
      'sum(pay_postage) as sum_pay_postage',
      'sum(use_integral) as sum_use_integral',
      'sum(back_integral) as sum_back_integral',
      'sum(deduction_price) as sum_deduction_price'
    ])->find();
    if ($sumNumber) {
      $price['count_sum'] = $sumNumber['count_sum'];
      $price['total_num'] = $sumNumber['sum_total_num'];
      $price['pay_price'] = $sumNumber['sum_pay_price'];
      $price['pay_postage'] = $sumNumber['sum_pay_postage'];
      $price['use_integral'] = $sumNumber['sum_use_integral'];
      $price['back_integral'] = $sumNumber['sum_back_integral'];
      $price['deduction_price'] = $sumNumber['sum_deduction_price'];
    }
    $list = self::getOrderWhere($where, $model)->where($whereData)->group('pay_type')->column('sum(pay_price) as sum_pay_price,pay_type', 'id');
    foreach ($list as $v) {
      if ($v['pay_type'] == 'weixin' || $v['pay_type'] == 'bytedance.weixin') {
        $price['pay_price_wx'] = $v['sum_pay_price'];
      } elseif ($v['pay_type'] == 'alipay' || $v['pay_type'] == 'bytedance.alipay') {
        $price['pay_price_ali'] = $v['sum_pay_price'];
      } elseif ($v['pay_type'] == 'yue') {
        $price['pay_price_yue'] = $v['sum_pay_price'];
      } elseif ($v['pay_type'] == 'offline') {
        $price['pay_price_offline'] = $v['sum_pay_price'];
      } else {
        $price['pay_price_other'] = $v['sum_pay_price'];
      }
    }
    return $price;
  }


  /**
   * 处理订单金额
   * @param $where
   * @return array
   */
  public static function getWxOrderPrice($where)
  {
    $where['is_del'] = 0; //删除订单不统计
    $model = new self;
    $price = [];
    $price['pay_price'] = 0; //支付金额
    $price['refund_price'] = 0; //退款金额
    $price['pay_price_wx'] = 0; //微信支付金额
    $price['pay_price_ali'] = 0; //支付宝支付金额
    $price['pay_price_yue'] = 0; //余额支付金额
    $price['pay_price_offline'] = 0; //线下支付金额
    $price['pay_price_other'] = 0; //其他支付金额
    $price['use_integral'] = 0; //用户使用积分
    $price['back_integral'] = 0; //退积分总数
    $price['deduction_price'] = 0; //抵扣金额
    $price['total_num'] = 0; //商品总数
    $price['count_sum'] = 0; //商品总数
    $price['brokerage'] = 0;
    $price['pay_postage'] = 0;
    $whereData = ['is_del' => 0];
    if ($where['status'] == '') {
      $whereData['paid'] = 1;
      $whereData['refund_status'] = 0;
    }
    $ids = self::getOrderWhere($where, $model)->where($whereData)->column('id');
    if (count($ids)) {
      $price['brokerage'] = UserBill::where(['category' => 'now_money', 'type' => 'brokerage'])->where('link_id', 'in', $ids)->sum('number');
    }
    $price['refund_price'] = self::getOrderWhere($where, $model)->where(['is_del' => 0, 'paid' => 1, 'refund_status' => 2])->sum('refund_price');
    $sumNumber = self::getOrderWhere($where, $model)->where($whereData)->field([
      'sum(total_num) as sum_total_num',
      'count(id) as count_sum',
      'sum(pay_price) as sum_pay_price',
      'sum(pay_postage) as sum_pay_postage',
      'sum(use_integral) as sum_use_integral',
      'sum(back_integral) as sum_back_integral',
      'sum(deduction_price) as sum_deduction_price'
    ])->find();
    if ($sumNumber) {
      $price['count_sum'] = $sumNumber['count_sum'];
      $price['total_num'] = $sumNumber['sum_total_num'];
      $price['pay_price'] = $sumNumber['sum_pay_price'];
      $price['pay_postage'] = $sumNumber['sum_pay_postage'];
      $price['use_integral'] = $sumNumber['sum_use_integral'];
      $price['back_integral'] = $sumNumber['sum_back_integral'];
      $price['deduction_price'] = $sumNumber['sum_deduction_price'];
    }
    $list = self::getOrderWhere($where, $model)->where($whereData)->group('pay_type')->column('sum(pay_price) as sum_pay_price,pay_type', 'id');
    foreach ($list as $v) {
      if ($v['pay_type'] == 'weixin' || $v['pay_type'] == 'bytedance.weixin') {
        $price['pay_price_wx'] = $v['sum_pay_price'];
      } elseif ($v['pay_type'] == 'alipay' || $v['pay_type'] == 'bytedance.alipay') {
        $price['pay_price_ali'] = $v['sum_pay_price'];
      } elseif ($v['pay_type'] == 'yue') {
        $price['pay_price_yue'] = $v['sum_pay_price'];
      } elseif ($v['pay_type'] == 'offline') {
        $price['pay_price_offline'] = $v['sum_pay_price'];
      } else {
        $price['pay_price_other'] = $v['sum_pay_price'];
      }
    }
    return $price;
  }

  /**
   * 处理订单金额
   * @param $where
   * @return array
   */
  public static function getSpecialOrderPrice($where)
  {
    $model = new self;
    $price = array();
    $price['pay_price'] = 0; //支付金额
    $price['refund_price'] = 0; //退款金额
    $price['pay_price_wx'] = 0; //微信支付金额
    $price['pay_price_yue'] = 0; //余额支付金额
    $price['pay_price_offline'] = 0; //线下支付金额
    $price['pay_price_zhifubao'] = 0; //支付宝支付金额
    $price['pay_price_other'] = 0; //其他支付金额

    $list = self::getSpecialOrderWhere($where, $model)->field([
      'sum(total_num) as total_num',
      'sum(pay_price) as pay_price',
      'sum(refund_price) as refund_price'
    ])->find()->toArray();

    $price['total_num'] = $list['total_num']; //商品总数
    $price['pay_price'] = $list['pay_price']; //支付金额
    $price['refund_price'] = $list['refund_price']; //退款金额
    $list = self::getSpecialOrderWhere($where, $model)->field('sum(pay_price) as pay_price,pay_type')->group('pay_type')->select()->toArray();
    foreach ($list as $v) {
      if ($v['pay_type'] == 'weixin' || $v['pay_type'] == 'bytedance.weixin') {
        $price['pay_price_wx'] = $v['pay_price'];
      } elseif ($v['pay_type'] == 'yue') {
        $price['pay_price_yue'] = $v['pay_price'];
      } elseif ($v['pay_type'] == 'offline') {
        $price['pay_price_offline'] = $v['pay_price'];
      } elseif ($v['pay_type'] == 'alipay' || $v['pay_type'] == 'bytedance.alipay') {
        $price['pay_price_ali'] = $v['pay_price'];
      } else {
        $price['pay_price_other'] = $v['pay_price'];
      }
    }
    $price['order_sum'] = self::getSpecialOrderWhere($where, $model)->count();
    return $price;
  }


  public static function systemPagePink($where)
  {
    $model = new self;
    $model = self::getOrderWherePink($where, $model);
    $model = $model->order('id desc');

    if ($where['export'] == 1) {
      $list = $model->select()->toArray();
      $export = [];
      foreach ($list as $index => $item) {

        if ($item['pay_type'] == 'weixin' || $item['pay_type'] == 'bytedance.weixin') {
          $payType = '微信支付';
        } elseif ($item['pay_type'] == 'alipay' || $item['pay_type'] == 'bytedance.alipay') {
          $payType = '支付宝支付';
        } elseif ($item['pay_type'] == 'yue') {
          $payType = '余额支付';
        } elseif ($item['pay_type'] == 'offline') {
          $payType = '线下支付';
        } else {
          $payType = '其他支付';
        }

        $_info = StoreOrderCartInfo::where('oid', $item['id'])->column('cart_info', 'oid');
        $goodsName = [];
        foreach ($_info as $k => $v) {
          $v = json_decode($v, true);
          $goodsName[] = implode(
            [
              $v['productInfo']['store_name'],
              isset($v['productInfo']['attrInfo']) ? '(' . $v['productInfo']['attrInfo']['suk'] . ')' : '',
              "[{$v['cart_num']} * {$v['truePrice']}]"
            ],
            ' '
          );
        }
        $item['cartInfo'] = $_info;
        $export[] = [
          $item['order_id'],
          $payType,
          $item['total_num'],
          $item['total_price'],
          $item['total_postage'],
          $item['pay_price'],
          $item['refund_price'],
          $item['mark'],
          $item['remark'],
          [$item['real_name'], $item['user_phone'], $item['user_address']],
          $goodsName,
          [$item['paid'] == 1 ? '已支付' : '未支付', '支付时间: ' . ($item['pay_time'] > 0 ? date('Y/md H:i', $item['pay_time']) : '暂无')]

        ];
        $list[$index] = $item;
      }
      ExportService::exportCsv($export, '订单导出' . time(), ['订单号', '支付方式', '商品总数', '商品总价', '邮费', '支付金额', '退款金额', '用户备注', '管理员备注', '收货人信息', '商品信息', '支付状态']);
    }

    return self::page($model, function ($item) {
      $item['nickname'] = WechatUser::where('uid', $item['uid'])->value('nickname');
      $_info = StoreOrderCartInfo::where('oid', $item['id'])->field('cart_info')->select();
      foreach ($_info as $k => $v) {
        $_info[$k]['cart_info'] = json_decode($v['cart_info'], true);
      }
      $item['_info'] = $_info;
    }, $where);
  }

  /**
   * 处理where条件
   * @param $where
   * @param $model
   * @return mixed
   */
  public static function getOrderWherePink($where, $model)
  {
    $model = $model->where('combination_id', '>', 0);
    if ($where['status'] != '') $model = $model::statusByWhere($where['status']);
    //        if($where['is_del'] != '' && $where['is_del'] != -1) $model = $model->where('is_del',$where['is_del']);
    if ($where['real_name'] != '') {
      $model = $model->where('order_id|real_name|user_phone', 'LIKE', "%$where[real_name]%");
    }
    if ($where['data'] !== '') {
      $model = self::getModelTime($where, $model, 'add_time');
    }
    return $model;
  }

  /**
   * 处理订单金额
   * @param $where
   * @return array
   */
  public static function getOrderPricePink($where)
  {
    $model = new self;
    $price = [];
    $price['pay_price'] = 0; //支付金额
    $price['refund_price'] = 0; //退款金额
    $price['pay_price_wx'] = 0; //微信支付金额
    $price['pay_price_ali'] = 0; //支付宝支付金额
    $price['pay_price_yue'] = 0; //余额支付金额
    $price['pay_price_offline'] = 0; //线下支付金额
    $price['pay_price_other'] = 0; //其他支付金额
    $price['use_integral'] = 0; //用户使用积分
    $price['back_integral'] = 0; //退积分总数
    $price['deduction_price'] = 0; //抵扣金额
    $price['total_num'] = 0; //商品总数
    $model = self::getOrderWherePink($where, $model);
    $list = $model->select()->toArray();
    foreach ($list as $v) {
      $price['total_num'] = bcadd($price['total_num'], $v['total_num'], 0);
      $price['pay_price'] = bcadd($price['pay_price'], $v['pay_price'], 2);
      $price['refund_price'] = bcadd($price['refund_price'], $v['refund_price'], 2);
      $price['use_integral'] = bcadd($price['use_integral'], $v['use_integral'], 2);
      $price['back_integral'] = bcadd($price['back_integral'], $v['back_integral'], 2);
      $price['deduction_price'] = bcadd($price['deduction_price'], $v['deduction_price'], 2);
      if ($v['pay_type'] == 'weixin' || $v['pay_type'] == 'bytedance.weixin') {
        $price['pay_price_wx'] = bcadd($price['pay_price_wx'], $v['pay_price'], 2);
      } elseif ($v['pay_type'] == 'alipay' || $v['pay_type'] == 'bytedance.alipay') {
        $price['pay_price_ali'] = bcadd($price['pay_price_ali'], $v['pay_price'], 2);
      } elseif ($v['pay_type'] == 'yue') {
        $price['pay_price_yue'] = bcadd($price['pay_price_yue'], $v['pay_price'], 2);
      } elseif ($v['pay_type'] == 'offline') {
        $price['pay_price_offline'] = bcadd($price['pay_price_offline'], $v['pay_price'], 2);
      } else {
        $price['pay_price_other'] = bcadd($price['pay_price_other'], $v['pay_price'], 2);
      }
    }
    return $price;
  }

  /**
   * 获取昨天的订单   首页在使用
   * @param int $preDay
   * @param int $day
   * @return $this|StoreOrder
   */
  public static function isMainYesterdayCount($preDay = 0, $day = 0)
  {
    $model = new self();
    $model = $model->where('add_time', '>', $preDay);
    $model = $model->where('add_time', '<', $day);
    return $model;
  }

  /**
   * 获取用户购买次数
   * @param int $uid
   * @return int|string
   */
  public static function getUserCountPay($uid = 0)
  {
    if (!$uid) return 0;
    return self::where('uid', $uid)->where('paid', 1)->count();
  }

  /**
   * 获取单个用户购买列表
   * @param array $where
   * @return array
   */
  public static function getOneorderList($where)
  {
    return self::where('uid', $where['uid'])->where('paid', 1)
      ->order('add_time desc')
      ->page((int)$where['page'], (int)$where['limit'])
      ->field([
        'order_id,type,real_name,total_num,total_price,pay_price,FROM_UNIXTIME(pay_time,"%Y-%m-%d") as pay_time,paid,pay_type'
      ])->select()
      ->toArray();
  }

  /**
   * 设置订单统计图搜索
   * @param array $where 条件
   * @param null $status
   * @param null $time
   * @return array
   */
  public static function setEchatWhere($where, $status = null, $time = null)
  {
    $model = self::statusByWhere($where['status'])->where('is_system_del', 0);
    if ($status !== null) $where['type'] = $status;
    if ($time === true) $where['data'] = '';
    switch ($where['type']) {
      case 1:
        //普通商品
        $model = $model->where('combination_id', 0)->where('seckill_id', 0)->where('bargain_id', 0);
        break;
      case 2:
        //拼团商品
        $model = $model->where('combination_id', ">", 0)->where('pink_id', ">", 0);
        break;
      case 3:
        //秒杀商品
        $model = $model->where('seckill_id', ">", 0);
        break;
      case 4:
        //砍价商品
        $model = $model->where('bargain_id', '>', 0);
        break;
    }
    return self::getModelTime($where, $model);
  }

  /*
     * 获取订单数据统计图
     * $where array
     * $limit int
     * return array
     */
  public static function getEchartsOrder($where, $limit = 20)
  {
    $orderlist = self::setEchatWhere($where)->field(
      'FROM_UNIXTIME(add_time,"%Y-%m-%d") as _add_time,sum(total_num) total_num,count(*) count,sum(total_price) total_price,sum(refund_price) refund_price,group_concat(cart_id SEPARATOR "|") cart_ids'
    )->group('_add_time')->order('_add_time asc')->select();
    count($orderlist) && $orderlist = $orderlist->toArray();
    $legend = ['商品数量', '订单数量', '订单金额', '退款金额'];
    $seriesdata = [
      [
        'name' => $legend[0],
        'type' => 'line',
        'data' => [],
      ],
      [
        'name' => $legend[1],
        'type' => 'line',
        'data' => []
      ],
      [
        'name' => $legend[2],
        'type' => 'line',
        'data' => []
      ],
      [
        'name' => $legend[3],
        'type' => 'line',
        'data' => []
      ]
    ];
    $xdata = [];
    $zoom = '';
    foreach ($orderlist as $item) {
      $xdata[] = $item['_add_time'];
      $seriesdata[0]['data'][] = $item['total_num'];
      $seriesdata[1]['data'][] = $item['count'];
      $seriesdata[2]['data'][] = $item['total_price'];
      $seriesdata[3]['data'][] = $item['refund_price'];
    }
    count($xdata) > $limit && $zoom = $xdata[$limit - 5];
    $badge = self::getOrderBadge($where);
    $bingpaytype = self::setEchatWhere($where)->group('pay_type')->field('count(*) as count,pay_type')->select();
    count($bingpaytype) && $bingpaytype = $bingpaytype->toArray();
    $bing_xdata = ['微信支付', '支付宝支付', '余额支付', '其他支付'];
    $color = ['#ffcccc', '#99cc00', '#fd99cc', '#669966'];
    $bing_data = [];
    foreach ($bingpaytype as $key => $item) {
      if ($item['pay_type'] == 'weixin' || $item['pay_type'] == 'bytedance.weixin') {
        $value['name'] = $bing_xdata[0];
      } else if ($item['pay_type'] == 'alipay' || $item['pay_type'] == 'bytedance.alipay') {
        $value['name'] = $bing_xdata[1];
      } else if ($item['pay_type'] == 'yue') {
        $value['name'] = $bing_xdata[2];
      } else {
        $value['name'] = $bing_xdata[3];
      }
      $value['value'] = $item['count'];
      $value['itemStyle']['color'] = isset($color[$key]) ? $color[$key] : $color[0];
      $bing_data[] = $value;
    }
    return compact('zoom', 'xdata', 'seriesdata', 'badge', 'legend', 'bing_data', 'bing_xdata');
  }

  public static function getOrderBadge($where)
  {
    return [
      [
        'name' => '拼团订单数量',
        'field' => '个',
        'count' => self::setEchatWhere($where, 2)->count(),
        'content' => '拼团总订单数量',
        'background_color' => 'layui-bg-cyan',
        'sum' => self::setEchatWhere($where, 2, true)->count(),
        'class' => 'fa fa-line-chart',
        'col' => 2
      ],
      [
        'name' => '砍价订单数量',
        'field' => '个',
        'count' => self::setEchatWhere($where, 4)->count(),
        'content' => '砍价总订单数量',
        'background_color' => 'layui-bg-cyan',
        'sum' => self::setEchatWhere($where, 4, true)->count(),
        'class' => 'fa fa-line-chart',
        'col' => 2
      ],
      [
        'name' => '秒杀订单数量',
        'field' => '个',
        'count' => self::setEchatWhere($where, 3)->count(),
        'content' => '秒杀总订单数量',
        'background_color' => 'layui-bg-cyan',
        'sum' => self::setEchatWhere($where, 3, true)->count(),
        'class' => 'fa fa-line-chart',
        'col' => 2
      ],
      [
        'name' => '普通订单数量',
        'field' => '个',
        'count' => self::setEchatWhere($where, 1)->count(),
        'content' => '普通总订单数量',
        'background_color' => 'layui-bg-cyan',
        'sum' => self::setEchatWhere($where, 1, true)->count(),
        'class' => 'fa fa-line-chart',
        'col' => 2,
      ],
      [
        'name' => '使用优惠卷金额',
        'field' => '元',
        'count' => self::setEchatWhere($where)->sum('coupon_price'),
        'content' => '普通总订单数量',
        'background_color' => 'layui-bg-cyan',
        'sum' => self::setEchatWhere($where, null, true)->sum('coupon_price'),
        'class' => 'fa fa-line-chart',
        'col' => 2
      ],
      [
        'name' => '积分消耗数',
        'field' => '个',
        'count' => self::setEchatWhere($where)->sum('use_integral'),
        'content' => '积分消耗总数',
        'background_color' => 'layui-bg-cyan',
        'sum' => self::setEchatWhere($where, null, true)->sum('use_integral'),
        'class' => 'fa fa-line-chart',
        'col' => 2
      ],
      [
        'name' => '积分抵扣金额',
        'field' => '个',
        'count' => self::setEchatWhere($where)->sum('deduction_price'),
        'content' => '积分抵扣总金额',
        'background_color' => 'layui-bg-cyan',
        'sum' => self::setEchatWhere($where, null, true)->sum('deduction_price'),
        'class' => 'fa fa-money',
        'col' => 2
      ],
      [
        'name' => '在线支付金额',
        'field' => '元',
        'count' => self::setEchatWhere($where)->where(['paid' => 1, 'refund_status' => 0])->whereIn('pay_type', ['weixin', 'alipay', 'bytedance.weixin', 'bytedance.alipay'])->sum('pay_price'),
        'content' => '在线支付总金额',
        'background_color' => 'layui-bg-cyan',
        'sum' => self::setEchatWhere($where, null, true)->where(['paid' => 1, 'refund_status' => 0])->whereIn('pay_type', ['weixin', 'alipay', 'bytedance.weixin', 'bytedance.alipay'])->sum('pay_price'),
        'class' => 'fa fa-weixin',
        'col' => 2
      ],
      [
        'name' => '余额支付金额',
        'field' => '元',
        'count' => self::setEchatWhere($where)->where('pay_type', 'yue')->where(['paid' => 1, 'refund_status' => 0])->sum('pay_price'),
        'content' => '余额支付总金额',
        'background_color' => 'layui-bg-cyan',
        'sum' => self::setEchatWhere($where, null, true)->where(['paid' => 1, 'refund_status' => 0])->where('pay_type', 'yue')->sum('pay_price'),
        'class' => 'fa  fa-balance-scale',
        'col' => 2
      ],
      [
        'name' => '赚取积分',
        'field' => '分',
        'count' => self::setEchatWhere($where)->sum('gain_integral'),
        'content' => '赚取总积分',
        'background_color' => 'layui-bg-cyan',
        'sum' => self::setEchatWhere($where, null, true)->sum('gain_integral'),
        'class' => 'fa fa-gg-circle',
        'col' => 2
      ],
      [
        'name' => '交易额',
        'field' => '元',
        'count' => self::setEchatWhere($where)->where(['paid' => 1, 'refund_status' => 0])->sum('pay_price'),
        'content' => '总交易额',
        'background_color' => 'layui-bg-cyan',
        'sum' => self::setEchatWhere($where, null, true)->where(['paid' => 1, 'refund_status' => 0])->sum('pay_price'),
        'class' => 'fa fa-jpy',
        'col' => 2
      ],
      [
        'name' => '订单商品数量',
        'field' => '元',
        'count' => self::setEchatWhere($where)->sum('total_num'),
        'content' => '订单商品总数量',
        'background_color' => 'layui-bg-cyan',
        'sum' => self::setEchatWhere($where, null, true)->sum('total_num'),
        'class' => 'fa fa-cube',
        'col' => 2
      ]
    ];
  }

  /**
   * 微信 订单发货
   * @param $oid
   * @param array $postageData
   * @throws \think\db\exception\DataNotFoundException
   * @throws \think\db\exception\ModelNotFoundException
   * @throws \think\exception\DbException
   */
  public static function orderPostageAfter($oid, $postageData = [])
  {
    $order = self::where('id', $oid)->find();
    $url = Url::buildUrl('/order/detail/' . $order['order_id'])->suffix('')->domain(true)->build();
    $group = [
      'first' => '亲,您的订单已发货,请注意查收',
      'remark' => '点击查看订单详情'
    ];
    if ($postageData['delivery_type'] == 'send') { //送货
      $goodsName = StoreOrderCartInfo::getProductNameList($order['id']);
      if ($order['is_channel'] == 1) {
        //小程序送货模版消息
        RoutineTemplate::sendOrderPostage($order);
      } else { //公众号
        $openid = WechatUser::where('uid', $order['uid'])->value('openid');
        $group = array_merge($group, [
          'keyword1' => $goodsName,
          'keyword2' => $order['pay_type'] == 'offline' ? '线下支付' : date('Y/m/d H:i', $order['pay_time']),
          'keyword3' => $order['user_address'],
          'keyword4' => $postageData['delivery_name'],
          'keyword5' => $postageData['delivery_id']
        ]);
        WechatTemplateService::sendTemplate($openid, WechatTemplateService::ORDER_DELIVER_SUCCESS, $group, $url);
      }
    } else if ($postageData['delivery_type'] == 'express') { //发货
      if ($order['is_channel'] == 1) {
        //小程序发货模版消息
        RoutineTemplate::sendOrderPostage($order, 1);
      } else { //公众号
        $openid = WechatUser::where('uid', $order['uid'])->value('openid');
        $group = array_merge($group, [
          'keyword1' => $order['order_id'],
          'keyword2' => $postageData['delivery_name'],
          'keyword3' => $postageData['delivery_id']
        ]);
        WechatTemplateService::sendTemplate($openid, WechatTemplateService::ORDER_POSTAGE_SUCCESS, $group, $url);
      }
    }
  }

  /** 收货后发送模版消息
   * @param $order
   */
  public static function orderTakeAfter($order)
  {
    $title = '';
    $cartInfo = StoreOrderCartInfo::where('oid', $order['id'])->column('cart_info', 'oid');

    if (count($cartInfo)) {
      foreach ($cartInfo as $key => &$cart) {
        $cart = json_decode($cart, true);
        $title .= $cart['productInfo']['store_name'] . ',';
      }
    }
    if (strlen(trim($title)))
      $title = substr($title, 0, bcsub(strlen($title), 1, 0));
    else {
      $cartInfo = StoreCart::alias('a')->where('a.id', 'in', implode(',', json_decode($order['cart_id'], true)))->find();
      $title = StoreProduct::where('id', $cartInfo['product_id'])->value('store_name');
    }

    if ($order['is_channel'] == 1) { //小程序
      RoutineTemplate::sendOrderTakeOver($order, $title);
    }
  }


  /** 虚拟发货发送模版消息
   * @param $order
   */
  public static function orderVirtualShipmentAfter($order)
  {
    $title = '';
    $cartInfo = StoreOrderCartInfo::where('oid', $order['id'])->column('cart_info', 'oid');

    if (count($cartInfo)) {
      foreach ($cartInfo as $key => &$cart) {
        $cart = json_decode($cart, true);
        $title .= $cart['productInfo']['store_name'] . ',';
      }
    }
    if (strlen(trim($title)))
      $title = substr($title, 0, bcsub(strlen($title), 1, 0));
    else {
      $cartInfo = StoreCart::alias('a')->where('a.id', 'in', implode(',', json_decode($order['cart_id'], true)))->find();
      $title = StoreProduct::where('id', $cartInfo['product_id'])->value('store_name');
    }

    if ($order['is_channel'] == 1) { //小程序
      RoutineTemplate::sendOrderMark($order, $title);
    }
  }


  /**
   * 不退款发送模板消息
   * @param int $id 订单id
   * @param array $data 退款详情
   * */
  public static function refundNoPrieTemplate($id, $data)
  {
    $order = self::get($id);
    if ($order) return false;
    //小程序模板消息
    $cartInfo = StoreOrderCartInfo::where('oid', $order['id'])->column('product_id', 'oid') ?: [];
    $title = '';
    foreach ($cartInfo as $k => $productId) {
      $store_name = StoreProduct::where('id', $productId)->value('store_name');
      $title .= $store_name . ',';
    }
    if ($order->is_channel == 1) {
      RoutineTemplate::sendOrderRefundFail($order, $title);
    } else {
      WechatTemplateService::sendTemplate(WechatUser::where('uid', $order->uid)->value('openid'), WechatTemplateService::ORDER_REFUND_STATUS, [
        'first' => '很抱歉您的订单退款失败，失败原因：' . $data,
        'keyword1' => $order->order_id,
        'keyword2' => $order->pay_price,
        'keyword3' => date('Y-m-d H:i:s', time()),
        'remark' => '给您带来的不便，请谅解！'
      ], Url::buildUrl('/order/detail/' . $order['order_id'])->suffix('')->domain(true)->build());
    }
  }

  /**
   * 课程订单-不退款发送模板消息
   * @param int $id 订单id
   * @param array $data 退款详情
   * */
  public static function refundSpecialNoPrieTemplate($id, $data)
  {
    $order = self::get($id);
    if ($order) return false;
    //小程序模板消息
    $title = '';
    if ($order->is_channel == 1) {
      RoutineTemplate::sendOrderRefundFail($order, $title);
    } else {
      WechatTemplateService::sendTemplate(WechatUser::where('uid', $order->uid)->value('openid'), WechatTemplateService::ORDER_REFUND_STATUS, [
        'first' => '很抱歉您的订单退款失败，失败原因：' . $data,
        'keyword1' => $order->order_id,
        'keyword2' => $order->pay_price,
        'keyword3' => date('Y-m-d H:i:s', time()),
        'remark' => '给您带来的不便，请谅解！'
      ], Url::buildUrl('/order/detail/' . $order['order_id'])->suffix('')->domain(true)->build());
    }
  }

  /**
   * 课程订单-不退款发送模板消息
   * @param int $id 订单id
   * @param array $data 退款详情
   * */
  public static function productDeliverySend($orderId, $data)
  {
    $shop = new  Shop('wechat');
    $result = $shop->delivery_send($orderId, $data);
    if ($result['errcode'] != 0) {
      return self::setErrorInfo($result['errmsg']);
    }
    return true;
  }




  /**
   * 获取订单总数
   * @param int $uid
   * @return int|string
   */
  public static function getOrderCount($uid = 0)
  {
    if (!$uid) return 0;
    return self::where('uid', $uid)->where('paid', 1)->where('refund_status', 0)->where('status', 2)->count();
  }

  /**
   * 获取已支付的订单
   * @param int $is_promoter
   * @return int|string
   */
  public static function getOrderPayCount($is_promoter = 0)
  {
    return self::where('o.paid', 1)->alias('o')->join('User u', 'u.uid=o.uid')->where('u.is_promoter', $is_promoter)->count();
  }

  /**
   * 获取最后一个月已支付的订单
   * @param int $is_promoter
   * @return int|string
   */
  public static function getOrderPayMonthCount($is_promoter = 0)
  {
    return self::where('o.paid', 1)->alias('o')->whereTime('o.pay_time', 'last month')->join('User u', 'u.uid=o.uid')->where('u.is_promoter', $is_promoter)->count();
  }

  /** 订单收货处理积分
   * @param $order
   * @return bool
   */
  public static function gainUserIntegral($order, bool $open = true)
  {
    if ($order['gain_integral'] > 0) {
      $userInfo = User::get($order['uid']);
      $open && BaseModel::beginTrans();
      $integral = bcadd($userInfo['integral'], $order['gain_integral'], 2);
      $res1 = false != User::where('uid', $userInfo['uid'])->update(['integral' => $integral]);
      $res2 = false != UserBill::income('购买商品赠送积分', $order['uid'], 'integral', 'gain', $order['gain_integral'], $order['id'], bcadd($userInfo['integral'], $order['gain_integral'], 2), '购买商品赠送' . floatval($order['gain_integral']) . '积分');
      $res = $res1 && $res2;
      $open && BaseModel::checkTrans($res);
      // RoutineTemplate::sendUserIntegral($order['uid'], $order, $order['gain_integral'], $integral);
      return $res;
    }
    return true;
  }

  public static function integralBack($id)
  {
    $order = self::get($id)->toArray();
    if (!(float)bcsub($order['use_integral'], 0, 2) && !$order['back_integral']) return true;
    if ($order['back_integral'] && !(int)$order['use_integral']) return true;
    BaseModel::beginTrans();
    $data['back_integral'] = bcsub($order['use_integral'], $order['use_integral'], 0);
    if (!$data['back_integral']) return true;
    $data['use_integral'] = 0;
    $data['deduction_price'] = 0.00;
    $data['pay_price'] = 0.00;
    $data['coupon_id'] = 0.00;
    $data['coupon_price'] = 0.00;
    $res4 = true;
    $integral = User::where('uid', $order['uid'])->value('integral');
    $res1 = User::bcInc($order['uid'], 'integral', $data['back_integral'], 'uid');
    $res2 = UserBill::income('商品退积分', $order['uid'], 'integral', 'pay_product_integral_back', $data['back_integral'], $order['id'], bcadd($integral, $data['back_integral'], 2), '订单退积分' . floatval($data['back_integral']) . '积分到用户积分');
    $res3 = self::edit($data, $id);
    if ($order['coupon_id']) $res4 = StoreCouponUser::recoverCoupon($order['coupon_id']);
    StoreOrderStatus::setStatus($id, 'integral_back', '商品退积分：' . $data['back_integral']);
    $res = $res1 && $res2 && $res3 && $res4;
    BaseModel::checkTrans($res);
    return $res;
  }

  /**
   * 订单数量 支付方式
   * @return array
   */
  public static function payTypeCount()
  {
    $where['status'] = 8;
    $where['is_del'] = 0;
    $where['real_name'] = '';
    $where['data'] = '';
    $where['type'] = '';
    $where['order'] = '';
    $where['pay_type'] = 1;
    $weixin = self::getOrderWhere($where, new self)->count();
    $where['pay_type'] = 2;
    $yue = self::getOrderWhere($where, new self)->count();
    $where['pay_type'] = 3;
    $offline = self::getOrderWhere($where, new self)->count();
    $where['pay_type'] = 4;
    $alipay = self::getOrderWhere($where, new self)->count();
    return compact('weixin', 'yue', 'offline', 'alipay');
  }


  /**
   * 订单数量 支付方式
   * @return array
   */
  public static function specialPayTypeCount()
  {
    $where['status'] = 8;
    $where['is_del'] = 0;
    $where['real_name'] = '';
    $where['data'] = '';
    $where['type'] = '';
    $where['order'] = '';
    $where['pay_type'] = 1;
    $weixin = self::getOrderWhere($where, new self)->count();
    $where['pay_type'] = 2;
    $yue = self::getOrderWhere($where, new self)->count();
    $where['pay_type'] = 3;
    $offline = self::getOrderWhere($where, new self)->count();
    $where['pay_type'] = 4;
    $alipay = self::getOrderWhere($where, new self)->count();
    return compact('weixin', 'yue', 'offline', 'alipay');
  }

  /**
   * 订单数量 支付方式
   * @return array
   */
  public static function payWxTypeCount()
  {
    $where['status'] = 8;
    $where['is_del'] = 0;
    $where['real_name'] = '';
    $where['data'] = '';
    $where['type'] = 5;
    $where['order'] = '';
    $where['pay_type'] = 1;
    $weixin = self::getOrderWhere($where, new self)->count();
    $where['pay_type'] = 2;
    $yue = self::getOrderWhere($where, new self)->count();
    $where['pay_type'] = 3;
    $offline = self::getOrderWhere($where, new self)->count();
    $where['pay_type'] = 4;
    $alipay = self::getOrderWhere($where, new self)->count();
    return compact('weixin', 'yue', 'offline', 'alipay');
  }

  /**
   * 批量发货
   * @return array
   */
  public static function bulkShipment($file_url)
  {
    $orderList =  FileClass::readOrderExcel($file_url);
    // Check if $orderList is not false and is iterable (array or object)
    if (!$orderList || !is_iterable($orderList)) {
      return self::setErrorInfo('读取文件为空');
    }
    $status = [];
    foreach (($orderList) as $key => $order) {
      $orderObj = self::where('order_id', $order['order_id'])->where('paid', 1)->where('status', 0)->where('refund_status', 0)->where('is_del', 0)->where('is_system_del', 0)->find();
      if ($orderObj) {
        $res = self::where('id', $orderObj['id'])->update(['status' => 1, 'delivery_type' => 'express', 'delivery_id' => $order['delivery_id'], 'delivery_name' => $order['delivery_name']]);
        if ($res) {
          $status = true;
          //短信发送
          event('ShortMssageSend', [self::where('id', $orderObj['id'])->value('order_id'), 'Deliver']);
          StoreOrderStatus::setStatus($orderObj['id'], 'delivery_goods', '已发货 快递公司：' . $order['delivery_name'] . ' 快递单号：' . $order['delivery_id']);
        }
      }
    }
    return true;
  }



  /**获取订单组信息
   * @param $cartInfo
   * @return array
   */
  public static function getOrderPostagePriceGroup($cartInfo, $addr)
  {
    $storeFreePostage = floatval(sys_config('store_free_postage')) ?: 0; //满额包邮
    $totalPrice = self::getOrderSumPrice($cartInfo, 'truePrice'); //获取订单总金额
    $free_shipping = 1;
    //如果满额包邮等于0
    if (!$storeFreePostage) {
      $storePostage = 0;
    } else {
      if ($addr) {
        //按照运费模板计算每个运费模板下商品的件数/重量/体积以及总金额 按照首重倒序排列
        $temp_num = [];
        foreach ($cartInfo as $cart) {

          $temp = ShippingTemplates::get($cart['productInfo']['temp_id']);
          if (!$temp) $temp = ShippingTemplates::get(1);
          if ($temp->getData('type') == 1) {
            $num = $cart['cart_num'];
          } elseif ($temp->getData('type') == 2) {
            $num = $cart['cart_num'] * $cart['productInfo']['attrInfo']['weight'];
          } else {
            $num = $cart['cart_num'] * $cart['productInfo']['attrInfo']['volume'];
          }
          $region = ShippingTemplatesRegion::where('temp_id', $cart['productInfo']['temp_id'])->order('id desc')->where('city_id', $addr['city_id'])->find(); //查询运费
          if (!$region) $region = ShippingTemplatesRegion::where('temp_id', $cart['productInfo']['temp_id'])->where('city_id', 0)->find();
          if (!$region) $region = ShippingTemplatesRegion::where('temp_id', 1)->where('city_id', 0)->find();
          if (!$region) {
            $storePostage = 0;
          }
          // 是否存在免费包邮
          if (ShippingTemplatesRegion::be((['temp_id' => $cart['productInfo']['temp_id'], 'city_id' => $addr['city_id']]))) {
            $free_shipping = 0;
          }
          if (!isset($temp_num[$cart['productInfo']['temp_id']])) {
            $temp_num[$cart['productInfo']['temp_id']]['number'] = $num;
            $temp_num[$cart['productInfo']['temp_id']]['price'] = bcmul($cart['cart_num'], $cart['truePrice'], 2);
            $temp_num[$cart['productInfo']['temp_id']]['first'] = $region['first'];
            $temp_num[$cart['productInfo']['temp_id']]['first_price'] = $region['first_price'];
            $temp_num[$cart['productInfo']['temp_id']]['continue'] = $region['continue'];
            $temp_num[$cart['productInfo']['temp_id']]['continue_price'] = $region['continue_price'];
            $temp_num[$cart['productInfo']['temp_id']]['temp_id'] = $cart['productInfo']['temp_id'];
            $temp_num[$cart['productInfo']['temp_id']]['city_id'] = $addr['city_id'];
          } else {
            $temp_num[$cart['productInfo']['temp_id']]['number'] += $num;
            $temp_num[$cart['productInfo']['temp_id']]['price'] += bcmul($cart['cart_num'], $cart['truePrice'], 2);
          }
        }
        array_multisort(array_column($temp_num, 'first_price'), SORT_DESC, $temp_num);
        $type = $storePostage = 0;
        foreach ($temp_num as $k => $v) {
          if (ShippingTemplatesFree::where('temp_id', $v['temp_id'])->where('city_id', $v['city_id'])->where('number', '<=', $v['number'])->where('price', '<=', $v['price'])->find()) {
            unset($temp_num[$k]);
          }
        }
        foreach ($temp_num as $v) {
          // 计算基础邮费
          $storePostage = bcadd($storePostage, $v['first_price'], 2);
          // 计算续件邮费（仅当 continue_price > 0 且购买数量大于 first 时）
          if ($v['number'] > $v['first'] && $v['continue_price'] > 0) {
            $extraWeight = bcsub($v['number'], $v['first'], 2);
            $extraCost = bcmul(ceil(bcdiv($extraWeight, $v['continue'], 2)), $v['continue_price'], 2);
            $storePostage = bcadd($storePostage, $extraCost, 2);
          }
          $type = 1; // 设置 type 值为 1
        }
      } else {
        $storePostage = 0;
      }
      // 判断是否配置包邮 
      if ($free_shipping) {
        if ($storeFreePostage <= $totalPrice) $storePostage = 0; //如果总价大于等于满额包邮 邮费等于0
      }
    }
    return compact('storePostage', 'storeFreePostage');
  }

  /**获取某个字段总金额
   * @param $cartInfo
   * @param $key 键名
   * @return int|string
   */
  public static function getOrderSumPrice($cartInfo, $key = 'truePrice')
  {
    $SumPrice = 0;
    foreach ($cartInfo as $cart) {
      // Check if $cart is an array and has the required keys
      if (is_array($cart) && isset($cart['cart_num']) && isset($cart[$key])) {
        $SumPrice = bcadd($SumPrice, bcmul($cart['cart_num'], $cart[$key], 2), 2);
      } else {
        // Handle the case where $cart is not valid
        // You can either skip it or log a warning, depending on your requirements
        continue;  // Skipping invalid cart item
      }
    }
    return $SumPrice;
  }
}
