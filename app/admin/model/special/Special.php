<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2021-09-27 17:06:04
 * @Last Modified time: 2022-12-21 16:41:20
 */

namespace app\admin\model\special;

use crmeb\basic\BaseModel;
use crmeb\traits\ModelTrait;
use app\admin\model\live\LiveGoods;
use app\admin\model\live\LiveStudio;
use app\admin\model\order\StoreOrder;
use app\admin\model\special\SpecialAssociated;

/**
 * Class Special
 * @package app\admin\model\special
 */
class Special extends BaseModel
{

  /**
   * 数据表主键
   * @var string
   */
  protected $pk = 'id';

  /**
   * 模型名称
   * @var string
   */
  protected $name = 'special';

  use ModelTrait;

  public static function PreWhere($alert = '')
  {
    $alert = $alert ? $alert . '.' : '';
    return self::where([$alert . 'is_show' => 1, $alert . 'is_del' => 0]);
  }

  //获取单个专题
  public static function getOne($id, $is_live = false)
  {
    $special = self::where('id', $id);
    if (!$special) return false;
    // $special->banner = self::getBannerKeyAttr($special->banner);
    $liveInfo = [];
    $special = $special->find();
    return [$special, $liveInfo];
  }

  //设置条件
  public static function setWhere($where, $alert = '', $model = null)
  {
    $model = $model === null ? new self() : $model;
    if ($alert) $model = $model->alias($alert);
    $alert = $alert ? $alert . '.' : '';
    $model = $model->order($alert . 'sort desc,' . $alert . 'id desc');
    if (isset($where['subject_id']) && $where['subject_id']) $model = $model->where($alert . 'subject_id', $where['subject_id']);
    if (isset($where['store_name']) && $where['store_name'] != '') $model = $model->where($alert . 'title|' . $alert . 'keyword|' . $alert . 'abstract|' . $alert . 'phrase|' . $alert . 'id', "LIKE", "%$where[store_name]%");
    if ($where['is_show'] !== '') $model = $model->where($alert . 'is_show', $where['is_show']);
    if (isset($where['type']) && $where['type']) $model = $model->where($alert . 'type', $where['type']);
    if (isset($where['special_type']) && $where['special_type'] !== '') {
      $model = $model->where($alert . 'type', $where['special_type']);
    }
    if (isset($where['admin_id']) && $where['admin_id']) $model = $model->where($alert . 'admin_id', $where['admin_id']);
    if (isset($where['start_time']) && $where['start_time'] && isset($where['end_time']) && $where['end_time']) $model = $model->whereTime($alert . 'add_time', 'between', [strtotime($where['start_time']), strtotime($where['end_time'])]);
    return $model->where($alert . 'is_del', 0);
  }

  /**
   * 获取连表MOdel
   * @param $model
   * @return object
   */
  public static function getModelObject($where, $alert = '', $model = null)
  {
    $model = $model === null ? new self() : $model;
    if ($alert) $model = $model->alias($alert);
    $alert = $alert ? $alert . '.' : '';
    if ($where['order'])
      $model = $model->order($alert . self::setOrder($where['order']));
    else
      $model = $model->order($alert . 'sort desc,' . $alert . 'id desc');
    if (isset($where['subject_id']) && $where['subject_id']) $model = $model->where($alert . 'subject_id', $where['subject_id']);
    if (isset($where['store_name']) && $where['store_name'] != '') $model = $model->where($alert . 'title|' . $alert . 'keyword|' . $alert . 'abstract|' . $alert . 'phrase|' . $alert . 'id', "LIKE", "%$where[store_name]%");
    if ($where['is_show'] !== '') $model = $model->where($alert . 'is_show', $where['is_show']);
    if (isset($where['is_beta_test']) && $where['is_beta_test'] !== '') $model = $model->where($alert . 'is_beta_test', $where['is_beta_test']);
    if (isset($where['type']) && $where['type']) $model = $model->where($alert . 'type', $where['type']);
    if (isset($where['special_type']) && $where['special_type'] !== '') {
      $model = $model->where($alert . 'type', $where['special_type']);
    }
    if (isset($where['admin_id']) && $where['admin_id']) $model = $model->where($alert . 'admin_id', $where['admin_id']);
    if (isset($where['start_time']) && $where['start_time'] && isset($where['end_time']) && $where['end_time']) $model = $model->whereTime($alert . 'add_time', 'between', [strtotime($where['start_time']), strtotime($where['end_time'])]);
    return $model->where($alert . 'is_del', 0);
  }

  //查找专题列表
  public static function getSpecialList($where)
  {
    $data = self::getModelObject($where, 'A')->field('A.*,T.content,S.name as subject_name')
      ->join('special_content T', 'T.special_id=A.id', 'LEFT')->join('special_subject S', 'S.id=A.subject_id', 'LEFT')
      ->page((int)$where['page'], (int)$where['limit'])->select();
    $data = count($data) ? $data->toArray() : [];
    foreach ($data as &$item) {
      $item['recommend'] = RecommendRelation::where('a.link_id', $item['id'])->where('a.type', 'in', [0, 2, 8])->alias('a')
        ->join('recommend r', 'a.recommend_id=r.id')->column('a.id,r.title');
      $item['pink_end_time'] = $item['pink_end_time'] ? strtotime($item['pink_end_time']) : 0;
      $item['sales'] = StoreOrder::where(['paid' => 1, 'cart_id' => $item['id'], 'refund_status' => 0])->count();
      $liveGoods = LiveGoods::getOne(['special_id' => $item['id'], 'is_delete' => 0]);
      $item['is_live_goods'] = 0;
      $item['live_goods_id'] = 0;
      if ($liveGoods) {
        $item['live_goods_id'] = $liveGoods->id;
        if ($liveGoods->is_show == 1) {
          $item['is_live_goods'] = 1;
        }
      }
      //查看拼团状态,如果已结束关闭拼团
      if ($item['is_pink'] && $item['pink_end_time'] < time()) {
        self::update(['is_pink' => 0], ['id' => $item['id']]);
        $item['is_pink'] = 0;
      }
      if (!$item['is_pink']) {
        $item['pink_money'] = 0;
      }
      if ($where['type'] == 4) $item['stream_name'] = LiveStudio::where('special_id', $item['id'])->value('stream_name');
      $oldTaskCount = SpecialTask::where('special_id', $item['id'])->where('is_show', 1)->count();
      $newTaskCount = SpecialSource::where('special_id', $item['id'])->count();
      $item['task_count'] = $newTaskCount + $oldTaskCount;
      $item['live_id'] = LiveStudio::where('special_id', $item['id'])->value('id');
      $item['is_play'] = 0;
      if ($item['live_id']) {
        $item['online_num'] = LiveStudio::where('id', $item['live_id'])->value('online_num');
        $item['is_play'] = LiveStudio::where('id', $item['live_id'])->value('is_play') ? 1 : 0;
      }
      $item['buy_user_num'] = StoreOrder::where(['paid' => 1, 'cart_id' => $item['id']])->count('id');
      $item['start_play_time'] = LiveStudio::where('special_id', $item['id'])->value('start_play_time');
    }
    $count = self::getModelObject($where)->count();
    return compact('data', 'count');
  }


  //查找所有专题列表
  public static function getAllSpecialList($where)
  {
    $data = self::getModelObject($where, 'A')->field('A.id,A.title,A.image,A.type as file_type,A.sort')
      ->page((int)$where['page'], (int)$where['limit'])->select();
    $data = count($data) ? $data->toArray() : [];
    if ($where['related_id'] != 0) {
      //获取已经关联
      $products = array();
      if ($where['category'] == 0) {
        $products = SpecialTaskAssociated::where('type', 1)->where('source_id', $where['related_id'])->column('is_show', 'link_id');
      } else {
        $products = SpecialAssociated::where('special_id', $where['related_id'])->where('type', 1)->column('is_show', 'related_id');
      }
      $sortCounter = 1; // 初始化 sort 计数器
      foreach ($data as $k => $v) {
        if (array_key_exists($v['id'], $products)) {
          $data[$k]['is_check'] = 1;
          $data[$k]['LAY_CHECKED'] = true;
          $data[$k]['sort'] = $sortCounter; // 使用计数器的值
        } else {
          $data[$k]['is_check'] = 0;
          $data[$k]['sort'] = $sortCounter; // 使用计数器的值
        }
        $sortCounter++; // 递增计数器
      }
    }
    $count = self::getModelObject($where)->count();
    return compact('data', 'count');
  }

  // 获取标题
  public static function getSpecialSourceTitle($specialId = 0, $sourceId = 0, $length = 50)
  {
    $text = '';
    if ($sourceId == 0) {
      $title = self::where('id', $specialId)->value('title');
      $text = ModelTrait::getSubstrUTf8($title, $length);
    } else {
      $title = SpecialTask::where('id', $sourceId)->value('title');
      $text = ModelTrait::getSubstrUTf8($title, $length);
    }
    return $text;
  }


  /**
   * 查找活动购物车里的所有课程标题
   * @param $cartId 购物车id
   * @return bool|string
   */
  public static function getSpecialTitle($id, $start = 0, $length = 10)
  {
    $title = '';
    try {
      $special = Special::field('title')->where('id', $id)->find();
      if ($special) $title = getYwSubstrUTf8($special['title'], $length);
    } catch (\Exception $e) {
    }
    return $title;
  }




  /**
   * 查找课程彩蛋素材
   * @param $cartId 购物车id
   * @return bool|string
   */
  public static function getCourseEggsLink($id)
  {
    $link = '/pages/yknowledge/course/detail?sid=' . $id; //默认跳课程页
    try {
      $special = Special::where('id', $id)->where('is_easter_eggs', 1)->find();
      if ($special) { //如果有效的话 去查找最后一个素材id
        $sourceId = SpecialSource::where('special_id', $id)->order('sort DESC,id DESC')->value('source_id');
        $link = '/pages/yknowledge/course/detail_list?sid=' . $id . '&id=' . $sourceId; //默认跳课程页
      }
    } catch (\Exception $e) {
    }
    return $link;
  }
}
