<?php

/**
 * @Author: <PERSON>
 * @Date:   2022-03-23 11:41:05
 * @Last Modified time: 2022-04-11 16:36:39
 */
namespace app\admin\model\special;

use crmeb\basic\BaseModel;
use crmeb\traits\ModelTrait;

/**
 * TODO 课程老带新活动:邀请记录Model
 * Class SpecialActivityInvitationRecord
 * @package app\models\special
 */
class SpecialActivityInvitationRecord extends BaseModel
{
    /**
     * 模型名称
     * @var string
     */
    protected $name = 'special_activity_invitation_record';

    use ModelTrait;

    public static function recordCount()
    {
        $data['reviewed'] = self::statusByWhere(1, new self())->count();
        return $data;
    }


    public static function statusByWhere($status, $model = null, $alert = '')
    {
        if ($model == null) $model = new self;
        if ('' === $status)
            return $model;
        else
            return $model;
    }

    public static function recordList($where)
    {
        $model = self::getrecordWhere($where, self::alias('a')
            ->join('user r', 'r.uid=a.uid', 'LEFT'), 'a.', 'r')
            ->field('a.*,r.nickname,r.phone,r.spread_uid,r.avatar');
        $model = $model->order('a.id desc');
        $data = ($data = $model->page((int)$where['page'], (int)$where['limit'])->select()) && count($data) ? $data->toArray() : [];
        foreach ($data as &$item) {
            $item['add_time'] = $item['add_time'] ? date('Y-m-d H:i:s', $item['add_time']) : '';
        }
        $count = self::getrecordWhere($where, self::alias('a')->join('user r', 'r.uid=a.uid', 'LEFT'), 'a.', 'r')->count();
        return compact('count', 'data');
    }

    public static function getRecordList($where, $page, $limit){
        $model = self::getrecordWhere($where, self::alias('a')
            ->join('user r', 'r.uid=a.uid', 'LEFT'), 'a.', 'r')
            ->field('a.*,r.nickname,r.phone,r.spread_uid,r.avatar');
        $model = $model->order('a.id desc');
        $data = ($data = $model->page((int)$page, (int)$limit)->select()) && count($data) ? $data->toArray() : [];
        foreach ($data as &$item) {
            $item['add_time'] = $item['add_time'] ? date('Y-m-d H:i:s', $item['add_time']) : '';
        }
        $count = self::getrecordWhere($where, self::alias('a')->join('user r', 'r.uid=a.uid', 'LEFT'), 'a.', 'r')->count();
        return compact('count', 'data');
    }

    /**
     * 处理where条件
     * @param $where
     * @param $model
     * @return mixed
     */
    public static function getRecordWhere($where, $model, $aler = '', $join = '')
    {
        if (isset($where['id']) && $where['id'] != '') {
            $model = $model->where($aler . 'activity_id', $where['id']);
        }
        if (isset($where['keywords']) && $where['keywords'] != '') {
                $model = $model->where($aler . 'uid' . ($join ? '|' . $join . '.nickname|' . $join . '.uid|' . $join . '.phone' : ''), 'LIKE', "%$where[keywords]%");
        }
        if (isset($where['data']) && $where['data'] !== '') {
            $model = self::getModelTime($where, $model, $aler . 'add_time');
        }
        return $model;
    }
}