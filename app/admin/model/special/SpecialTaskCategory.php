<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2021-09-26 11:46:17
 * @Last Modified time: 2021-11-11 10:50:04
 */
namespace app\admin\model\special;

use crmeb\traits\ModelTrait;
use crmeb\basic\BaseModel;
use crmeb\services\UtilService as Util;

/**
 * Class StoreCategory
 * @package app\admin\model\market
 */
class SpecialTaskCategory extends BaseModel
{

    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'special_task_category';

    use ModelTrait;

    /**
     * 全部素材分类
     */
    public static function taskCategoryAll($type=0){
        $model=self::where('is_del',0);
        if($type==1){
            $model=$model->where('pid',0);
        }
        $list=$model->select();
        $list=count($list) > 0 ? $list->toArray() : [];
        $list=Util::sortListTier($list);
        return $list;
    }

       /**
     * 异步获取分类列表
     * @param $where
     * @return array
     */
    public static function CategoryList($where)
    {
        $data = ($data = self::systemPage($where, true)->page((int)$where['page'], (int)$where['limit'])->select()) && count($data) ? $data->toArray() : [];
        foreach ($data as &$item) {
            if ($item['pid']) {
                $item['pid_name'] = self::where('id', $item['pid'])->value('title');
            } else {
                $item['pid_name'] = '顶级';
            }
            $cate=self::where('id',$item['id'])->find();
            if($cate){
                $cate=$cate->toArray();
                if($item>0){
                    $item['sum']=SpecialTask::where('pid', $item['id'])->count();
                }else{
                    $pids=self::categoryId($ket);
                    $item['sum']=SpecialTask::where('pid','in', $pids)->count();
                }
            }
        }
        $count = self::systemPage($where, true)->count();
        return compact('count', 'data');
    }

        /**
     * 分级排序列表
     * @param null $model
     * @param int $type
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public static function getTierList($model = null, $type = 0)
    {
        if ($model === null) $model = new self();
        if (!$type) return sort_list_tier($model->order('sort desc,id desc')->where('pid', 0)->select()->toArray());
        return sort_list_tier($model->order('sort desc,id desc')->select()->toArray());
    }

    /**
     * @param $where
     * @return array
     */
    public static function systemPage($where, $isAjax = false)
    {
        $model = new self;
        $model = $model->where('is_del', 0);
        if ($where['pid'] != '') $model = $model->where('pid', $where['pid']);
        else if ($where['pid'] == '' && $where['title'] == '') $model = $model->where('pid', 0);
        if ($where['title'] != '') $model = $model->where('title', 'LIKE', "%$where[title]%");
        if ($isAjax === true) {
            if (isset($where['order']) && $where['order'] != '') {
                $model = $model->order(self::setOrder($where['order']));
            } else {
                $model = $model->order('sort desc,id desc');
            }
            return $model;
        }
        return self::page($model, function ($item) {
            if ($item['pid']) {
                $item['pid_name'] = self::where('id', $item['pid'])->value('title');
            } else {
                $item['pid_name'] = '顶级';
            }
        }, $where);
    }

    /**获取一个分类下的所有分类ID
     * @param int $pid
     */
    public static function categoryId($pid=0){
        $data=self::where('is_del', 0)->where('pid',$pid)->column('id');
        array_push($data,$pid);
        return $data;
    }


    public static function delCategory($id)
    {
        $count = self::where('pid', $id)->count();
        if ($count)
            return self::setErrorInfo('请先删除下级子分类标签');
        else {
            return self::edit(['is_del'=>1],$id,'id');
        }
    }
}