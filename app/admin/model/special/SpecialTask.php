<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2021-09-29 15:28:15
 * @Last Modified time: 2023-02-16 13:34:43
 */

namespace app\admin\model\special;

use crmeb\traits\ModelTrait;
use crmeb\basic\BaseModel;

/**
 * Class Special
 * @package app\admin\model\special
 */
class SpecialTask extends BaseModel
{
  /**
   * 数据表主键
   * @var string
   */
  protected $pk = 'id';

  /**
   * 模型名称
   * @var string
   */
  protected $name = 'special_task';

  /**
   * 音视频处理状态常量
   */
  const PROCESSING_STATUS_PENDING = 0;    // 未处理
  const PROCESSING_STATUS_PROCESSING = 2; // 处理中
  const PROCESSING_STATUS_COMPLETED = 1;  // 处理完成
  const PROCESSING_STATUS_FAILED = 3;     // 处理失败
  const PROCESSING_STATUS_QUEUED = 4;     // 队列中

  /**
   * 获取处理状态文本
   * @param int $status
   * @return string
   */
  public static function getProcessingStatusText($status)
  {
    $statusMap = [
      self::PROCESSING_STATUS_PENDING => '未处理',
      self::PROCESSING_STATUS_PROCESSING => '处理中',
      self::PROCESSING_STATUS_COMPLETED => '处理完成',
      self::PROCESSING_STATUS_FAILED => '处理失败',
      self::PROCESSING_STATUS_QUEUED => '队列中'
    ];
    return $statusMap[$status] ?? '未知状态';
  }

  /**
   * 检查是否为音视频类型
   * @param int $type
   * @return bool
   */
  public static function isMediaType($type)
  {
    return in_array($type, [2, 3]); // 2=音频, 3=视频
  }

  /**
   * 获取需要处理的媒体文件列表
   * @return array
   */
  public static function getPendingMediaFiles()
  {
    return self::where('processing_status', self::PROCESSING_STATUS_PENDING)
               ->where('enable_preprocessing', 1)
               ->whereIn('type', [2, 3])
               ->where('is_del', 0)
               ->where('original_file_path', '<>', '')
               ->select()
               ->toArray();
  }

  /**
   * 更新处理状态
   * @param int $id
   * @param int $status
   * @param string $log
   * @return bool
   */
  public static function updateProcessingStatus($id, $status, $log = '')
  {
    $data = ['processing_status' => $status];
    if ($log) {
      $data['processing_log'] = $log;
    }
    return self::where('id', $id)->update($data);
  }

  /**
   * 获取连表MOdel
   * @param $model
   * @return object
   */
  public static function getModelObject($where = [])
  {
    $model = new self();
    if (!empty($where)) {
      if (isset($where['store_name']) && $where['store_name'] != '') {
        $model = $model->where('title|abstract|detail|id', 'LIKE', "%$where[store_name]%");
      }
      if ($where['is_show'] !== '') $model = $model->where('is_show', $where['is_show']);
      if (isset($where['order']) && $where['order'] != '') {
        $model = $model->order(self::setOrder($where['order']));
      } else {
        $model = $model->order('id desc');
      }
    }
    return $model;
  }
  public static function getTaskCount($special_id)
  {
    $ids = self::getDb('special_course')->where('special_id', $special_id)->where('is_show', 1)->column('id');
    return self::where('is_show', 1)->where('is_del', 0)->where('coures_id', 'in', $ids)->count();
  }


  //查找素材列表
  public static function getTaskList($where)
  {
    $data = self::setWhere($where)->page((int)$where['page'], (int)$where['limit'])->select();
    $data = count($data) ? $data->toArray() : [];
    $count = self::setWhere($where)->count();
    return compact('data', 'count');
  }

  public static function getTaskList2($where)
  {
    if (isset($where['special_type']) && $where['special_type'] == 4) {
      unset($where['special_type']);
      $where['store_name'] = $where['title'];
      if ($where['type'] == '') {
        $data = Special::setWhere($where)->whereIn('type', [1, 2, 3])->page((int)$where['page'], (int)$where['limit'])->select();
        $data = count($data) ? $data->toArray() : [];
        $count = Special::setWhere($where)->whereIn('type', [1, 2, 3])->count();
      } else {
        $data = Special::setWhere($where)->page((int)$where['page'], (int)$where['limit'])->select();
        $data = count($data) ? $data->toArray() : [];
        $count = Special::setWhere($where)->count();
      }
    } else {
      if ($where['pid'] > 0) {
        $where['special_type'] = 0;
      }
      $data = self::setWhere($where)->page((int)$where['page'], (int)$where['limit'])->select();
      $data = count($data) ? $data->toArray() : [];
      $count = self::setWhere($where)->count();
    }
    return compact('data', 'count');
  }


  //查找所有专题列表
  public static function getSpecialSourceList($where)
  {
    $data = self::getModelObject($where)->field('id,title,image')
      ->page((int)$where['page'], (int)$where['limit'])->select();
    $data = count($data) ? $data->toArray() : [];
    $num = 1;
    if ($where['related_id'] != 0) {
      //获取已经关联
      $products = array();
      $products = SpecialTaskAssociated::where('type', 0)->where('source_id', $where['related_id'])->where('is_show', 1)->column('sort', 'link_id');
      if (!empty($products)) {
        $sortCounter =  end($products) + 1;
      } else {
        $sortCounter = 1; // 初始化 sort 计数器
      }
      // 遍历数据数组并更新值
      foreach ($data as $k => $v) {
        if (array_key_exists($v['id'], $products)) {
          $data[$k]['is_check'] = 1;
          $data[$k]['LAY_CHECKED'] = true;
          $data[$k]['sort'] = SpecialTaskAssociated::where('type', 0)->where('source_id', $where['related_id'])->where('link_id', $v['id'])->where('is_show', 1)->value('sort');
        } else {
          $data[$k]['is_check'] = 0;
          $data[$k]['sort'] = 1;
          $data[$k]['sort'] = $sortCounter; // 使用计数器的值
          $sortCounter++; // 递增计数器
        }
      }
    }
    $count = self::getModelObject($where)->count();
    return compact('data', 'count');
  }


  //设置where条件
  public static function setWhere($where, $alirs = '', $model = null)
  {
    $model = $model === null ? new self() : $model;
    $model = $alirs === '' ? $model->alias($alirs) : $model;
    $alirs = $alirs === '' ? $alirs : $alirs . '.';
    if ($where['is_show'] !== '') $model = $model->where("{$alirs}is_show", $where['is_show']);
    if ($where['type'] !== '') $model = $model->where("{$alirs}type", $where['type']);
    if ($where['pid'] > 0) {
      $pids = SpecialTaskCategory::categoryId($where['pid']);
      array_push($pids, $where['pid']);
      if (count($pids) > 0) {
        $model = $model->where("{$alirs}pid", 'in', $pids);
      } else {
        $model = $model->where("{$alirs}pid", $where['pid']);
      }
    }
    if ($where['title']) $model = $model->where("{$alirs}title", 'LIKE', "%$where[title]%");
    if (!isset($where['all_type'])) {
      if (isset($where['special_type']) && $where['special_type']) $model = $model->where("{$alirs}type", $where['special_type']);
    }
    $model = $model->where("{$alirs}is_del", 0);
    if ($where['order'])
      $model = $model->order("{$alirs}sort desc");
    else
      $model = $model->order("{$alirs}id desc");
    return $model;
  }
}
