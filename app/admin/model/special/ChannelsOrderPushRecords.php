<?php

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date:   2023-03-21 17:47:37
 * @Last Modified time: 2023-05-16 09:48:27
 */
namespace app\admin\model\special;

use crmeb\basic\BaseModel;
use crmeb\traits\ModelTrait;
use app\admin\model\user\User;


class ChannelsOrderPushRecords extends BaseModel
{
    /**
     * 模型名称
     * @var string
     */
    protected $name = 'store_channels_order_push_records';

    use ModelTrait;


    protected function getTypeAttr($value)
    {
        $name = '';
        switch ($value) {
            case 1:
                $name = '视频号小店';
                break;
            case 2:
                $name = '抖音小店';
                break;
            case 3:
                $name = '小红书小店';
                break;
        }
        return $name;
    }

    public static function getStatusName($value)
    {
        $name = '';
        switch ($value) {
            case 0:
                $name = '';
                break;
            case 10:
                $name = '待付款';
                break;
            case 20:
                $name = '待发货';
                break;
            case 21:
                $name = '部分发货';
                break;
            case 30:
                $name = '待收货';
                break;
            case 100:
                $name = '完成';
                break;
            case 200:
                $name = '全部商品售后之后，订单取消';
                break;
            case 250:
                $name = '未付款用户主动取消或超时未付款订单自动取消';
                break;
        }
        return $name;
    }
    public static function getXhsStatusName($value)
    {
        $name = '';
        switch ($value) {

            case 1:
                $name = '已下单待付款';
                break;
            case 2:
                $name = '已支付处理中';
                break;
            case 3:
                $name = '清关中';
                break;
            case 4:
                $name = '待发货';
                break;
            case 5:
                $name = '部分发货';
                break;
            case 6:
                $name = '待收货';
                break;
            case 7:
                $name = '已完成';
                break;
            case 8:
                $name = '已关闭';
                break;
            case 9:
                $name = '已取消';
                break;
            case 10:
                $name = '换货申请中';
                break;
            default:
                $name = '';
                break;
        }
        return $name;
    }

    public static function getRecordsList($where)
    {
        $model = self::getRecordsWhere($where, self::alias('a')
            ->join('user r', 'r.uid=a.uid', 'LEFT'), 'a.', 'r')
            ->field('a.*,r.nickname,r.phone,r.spread_uid,r.avatar');
        $model = $model->order('a.id desc');
        $data = ($data = $model->page((int)$where['page'], (int)$where['limit'])->select()) && count($data) ? $data->toArray() : [];
        foreach ($data as &$item) {
            $item['title'] = Special::where('id',$item['product_id'])->value('title');
            $item['image'] = Special::where('id',$item['product_id'])->value('image');
            // 分解订单信息
            $orderInfo = is_string($item['order_detail']) ? json_decode($item['order_detail'], true) : [];
            $orderInfo = is_string($item['order_detail']) ? json_decode($item['order_detail'], true) : [];
            $deliveryInfo = is_string($item['delivery_detail']) ? json_decode($item['delivery_detail'], true) : [];
            $user_name = '';
			$user_phone = '';
			$user_mark = '';
			$mer_mark = '';
            if ($item['type'] == '视频号小店' && $orderInfo) {
            	if (isset($orderInfo['delivery_info']) && isset($orderInfo['delivery_info']['address_info'])) {
         	        $user_name =  $orderInfo['delivery_info'] ? $orderInfo['delivery_info']['address_info']['user_name'] : '';
	           		$user_phone =  $orderInfo['delivery_info']['deliver_method'] == 1  ? $orderInfo['delivery_info']['address_info']['virtual_order_tel_number'] : $orderInfo['delivery_info']['address_info']['tel_number'];
            	}
	   			// 订单备注
	   			if (isset($orderInfo['ext_info'])) {
		           	$user_mark = $orderInfo['ext_info'] ? $orderInfo['ext_info']['customer_notes'] : '';
		           	$mer_mark =  $orderInfo['ext_info'] ? $orderInfo['ext_info']['merchant_notes'] : '';
	   			}
                $item['status_name'] = self::getStatusName($item['status']);
            }elseif ($item['type'] == '小红书小店') {
                if (isset($deliveryInfo['receiverName'])) {
                    $user_name =  $deliveryInfo['receiverName'];
                }
                if (isset($deliveryInfo['receiverPhone'])) {
                    $user_phone =  $deliveryInfo['receiverPhone'];
                }
                // 订单备注
                if (isset($orderInfo['customerRemark'])) {
                    $user_mark = $orderInfo['customerRemark'] ? $orderInfo['customerRemark'] : '';
                }
                if (isset($orderInfo['sellerRemark'])) {
                    $mer_mark = $orderInfo['sellerRemark'] ? $orderInfo['sellerRemark'] : '';
                }
                $item['status_name'] = self::getXhsStatusName($item['status']);
            }
           	$item['user_name'] = $user_name;
           	$item['user_phone'] =$user_phone;
           	$item['user_mark'] = $user_mark;
           	$item['mer_mark'] = $mer_mark;
            $item['add_time'] = $item['add_time'] ? date('Y-m-d H:i:s', $item['add_time']) : '';
        }
        $count = self::getRecordsWhere($where, self::alias('a')->join('user r', 'r.uid=a.uid', 'LEFT'), 'a.', 'r')->count();
        return compact('count', 'data');
    }


    /**
     * 处理where条件
     * @param $where
     * @param $model
     * @return mixed
     */
    public static function getRecordsWhere($where, $model, $aler = '', $join = '')
    {
        if (isset($where['type']) && $where['type'] != '') {
            $model = $model->where('type',$where['type']);
        }
        if (isset($where['is_uid']) && $where['is_uid'] != '') {
            $model = $model->where($aler. 'uid',0);
        }
        if (isset($where['special_id']) && $where['special_id'] != '') {
            $model = $model->where('product_id',$where['special_id']);
        }else{
            $model = $model->where('product_id','<>',0);
        }
        if (isset($where['keywords']) && $where['keywords'] != '') {
            $model = $model->where($aler . 'order_id|order_detail|aftersale_detail', 'LIKE', "%$where[keywords]%");
        }
        if ($where['add_time'] != '') {
            list($startTime, $endTime) = explode(' - ', $where['add_time']);
            $model = $model->where($aler. 'add_time', '>', strtotime($startTime));
            $model = $model->where($aler. 'add_time', '<', strtotime($endTime) + 24 * 3600);
        }
        return $model;
    }
}