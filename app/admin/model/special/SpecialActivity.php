<?php
/**
 * @author: xaboy<<EMAIL>>
 * @day: 2017/11/11
 */

namespace app\admin\model\special;

use crmeb\traits\ModelTrait;
use crmeb\basic\BaseModel;
use crmeb\services\MiniProgramService;
use app\admin\model\ump\StoreCouponIssue;
use crmeb\services\upload\storage\OssHelper;
/**
 * Class SpecialActivity
 * @package app\admin\model\special
 */
class SpecialActivity extends BaseModel
{
    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'special_activity';

    use ModelTrait;

    /**
     * @param $where
     * @return array
     */
    public static function systemPage($where)
    {
        $model = new self;
        $model = $model->alias('s');
        if ($where['status'] != '') $model = $model->where('s.status', $where['status']);
        if ($where['store_name'] != '') $model = $model->where('s.title|s.id', 'LIKE', "%$where[store_name]%");
        $model = $model->page(bcmul($where['page'], $where['limit'], 0), $where['limit']);
        $model = $model->order('s.id desc');
        $model = $model->where('s.is_del', 0);
        return self::page($model, function ($item) {
            // 目标促销课程
            $specialInfo = Special::where('id', $item['special_id'])->find();
            $item['special_image'] = '';
            $item['special_title'] = '';
            if ($specialInfo) {
                $item['special_image'] = $specialInfo['image'];
                $item['special_title'] =  mb_substr($specialInfo['title'], 0, 10);
            }
            // 参与用户来源
            $participate_user_source_ids = $item['participate_user_source_id'] ? json_decode($item['participate_user_source_id']) : [];
            $special_list = Special::alias('a')->field('a.id,a.title')->whereIn('a.id', $participate_user_source_ids)->select()->toArray();
            $item['participate_user_source'] = $special_list;
            // 活动时间
            if ($item['status']) {
                if ($item['start_time'] > time())
                    $item['start_name'] = '活动未开始';
                else if ($item['stop_time'] < time())
                    $item['start_name'] = '活动已结束';
                else if ($item['stop_time'] > time() && $item['start_time'] < time()) {
                    $item['start_name'] = '正在进行中';
                }
            } else $item['start_name'] = '关闭';

            $item['receive_condition_name'] = '不限制';
            //限制条件
            if ($item['receive_condition'] == 1) {
                $item['receive_condition_name'] = '未购目标课客户';
            }else if ($item['receive_condition'] == 2){
                $item['receive_condition_name'] = '未购任何课客户';
            }
            // 新用户领取的优惠券
            $name  = StoreCouponIssue::alias('a')->join('store_coupon b', 'b.id=a.cid')->where('a.id', $item['new_user_coupon_id'])->value('b.title');
            $item['new_user_coupon_name'] = $name ? $name : '';
            $reward_coupon_ids = $item['reward_coupon_list'] ? json_decode($item['reward_coupon_list']) : [];
            $coupon_list = StoreCouponIssue::alias('a')->field('a.id,b.title')->join('yw_store_coupon b', 'b.id=a.cid')->whereIn('a.id', $reward_coupon_ids)->select()->toArray();
            $item['reward_coupon_list'] = $coupon_list;


        }, $where, $where['limit']);
    }


    /* 横幅隐藏显示
     * @param $id
     * @param $show
     * @return bool
     */
    public static function setSpecialActivityStatus($id, $status)
    {
        self::beginTrans();
        $res1 = true;
        $res2 = self::where('id', $id)->update(['status' => $status]);
        $res = $res1 && $res2;
        self::checkTrans($res);
        return $res;
    }



    /**
     * 获取连表MOdel
     * @param $model
     * @return object
     */
    public static function getModelObject($where, $alert = '', $model = null)
    {
        $model = $model === null ? new self() : $model;
        if ($alert) $model = $model->alias($alert);
        $alert = $alert ? $alert . '.' : '';
        $model = $model->order($alert.'sort desc,'.$alert.'id desc');
        if (isset($where['store_name']) && $where['store_name']!='') $model = $model->where($alert . 'title|' . $alert. 'id', "LIKE", "%$where[store_name]%");

        return $model->where($alert .'is_del', 0);
    }


    public static function getLiveReplayList($where)
    {
        $res = MiniProgramService::get_replay_list($where);
        $data = $res['data'];
        $count = $res['total'];
        return compact('count', 'data');
    }

    public static function getActivityList($where)
    {
        $model = self::getActivityWhere($where, self::alias('a'))
            ->field('a.*');
        $model = $model->order('a.id desc');
        $data = ($data = $model->page((int)$where['page'], (int)$where['limit'])->select()) && count($data) ? $data->toArray() : [];
        foreach ($data as &$item) {
            if ($item['status'] == 1) {
                if ($item['live_status'] == 0 && time() < $item['start_time']) {
                    $item['status_name'] = '直播未开始';
                }elseif ($item['live_status'] == 1 && time() > $item['start_time'] &&  time() < $item['stop_time']) {
                    $item['status_name'] = '直播中';
                }elseif ($item['live_status'] == 2 && time() > $item['stop_time']) {
                    $item['status_name'] = '直播已结束';
                }else{
                    $item['status_name'] = '检查直播配置';
                }
            }else{
                $item['status_name'] = '活动已关闭';
            }
            $item['add_time'] = $item['add_time'] ? date('Y-m-d H:i:s', $item['add_time']) : '';
            $item['start_time'] = $item['start_time'] ? date('Y-m-d H:i:s', $item['start_time']) : '';
            $item['stop_time'] = $item['stop_time'] ? date('Y-m-d H:i:s', $item['stop_time']) : '';
        }
        $count = self::getActivityWhere($where, self::alias('a'))->count();
        return compact('count', 'data');
    }

    /**
     * 处理where条件
     * @param $where
     * @param $model
     * @return mixed
     */
    public static function getActivityWhere($where, $model, $aler = '', $join = '')
    {
        $model = $model->where('is_del',0);
        if (isset($where['type']) && $where['type'] != '') {
            $model = $model->where('type',$where['type']);
        }
        if (isset($where['keywords']) && $where['keywords'] != '') {
            $model = $model->where('live_id',$where['keywords']);
        }
        if (isset($where['title']) && $where['title'] != '') {
            $model = $model->where($aler . 'title', 'LIKE', "%$where[title]%");
        }
        if ($where['add_time'] != '') {
            list($startTime, $endTime) = explode(' - ', $where['add_time']);
            $model = $model->where($aler. 'add_time', '>', strtotime($startTime));
            $model = $model->where($aler. 'add_time', '<', strtotime($endTime) + 24 * 3600);
        }
        return $model;
    }


    public static function getAllLiveList($where)
    {   
        $data = self::getModelObject($where, 'A')->field('A.id,A.title,A.image,A.sort')
            ->page((int)$where['page'], (int)$where['limit'])->select();
        $data = count($data) ? $data->toArray() : [];
        $count = self::getModelObject($where)->count();
        return compact('data', 'count');
    }

}