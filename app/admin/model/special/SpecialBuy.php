<?php

/**
 * @Author: <PERSON>
 * @Date:   2022-05-16 11:07:01
 * @Last Modified time: 2023-05-25 18:27:49
 */

namespace app\admin\model\special;

use crmeb\basic\BaseModel;
use crmeb\traits\ModelTrait;
use app\admin\model\user\User;
use crmeb\services\PHPExcelService;
use app\admin\model\wechat\WechatUser;
use app\admin\model\ump\StoreCustomCouponList;

class SpecialBuy extends BaseModel
{
  /**
   * 模型名称
   * @var string
   */
  protected $name = 'special_buy';

  use ModelTrait;

  // protected function getTypeAttr($value)
  // {
  //     $name = '';
  //     switch ($value) {
  //         case 0:
  //             $name = '支付获得';
  //             break;
  //         case 1:
  //             $name = '拼团获得';
  //             break;
  //         case 2:
  //             $name = '领取礼物获得';
  //             break;
  //         case 3:
  //             $name = '赠送获得';
  //             break;
  //         case 4:
  //             $name = '邀请码获得';
  //             break;
  //         case 5:
  //             $name = '视频号小店获得';
  //             break;
  //     }
  //     return $name;
  // }

  public static  function getSourceAttr($value)
  {
    $name = '';
    switch ($value) {
      case 0:
        $name = '微信小程序';
        break;
      case 1:
        $name = '微信小程序';
        break;
      case 2:
        $name = '微信小程序';
        break;
      case 3:
        $name = '微信小程序';
        break;
      case 4:
        $name = '抖音';
        break;
      case 5:
        $name = '视频号';
        break;
      case 6:
        $name = '小红书';
        break;
      case 10:
        $name = '后台';
        break;
    }
    return $name;
  }

  public static function subscribeCourseList($where)
  {
    $model = self::getSpecialWhere($where, self::alias('a')
      ->join('user r', 'r.uid=a.uid', 'LEFT'), 'a.', 'r')
      ->field('a.*,r.nickname,r.phone,r.spread_uid,r.avatar');
    $model = $model->order('a.id desc');
    $data = ($data = $model->page((int)$where['page'], (int)$where['limit'])->select()) && count($data) ? $data->toArray() : [];
    foreach ($data as &$item) {
      $item['title'] = Special::where('id', $item['special_id'])->value('title');
      $item['image'] = Special::where('id', $item['special_id'])->value('image');
      $item['source'] = self::getSourceAttr($item['type']);
      $item['code'] = '';
      $item['grant_code'] = '';
      if ($item['type'] == 6 || $item['type'] == 4) {
        $code =  StoreCustomCouponList::where('uid', $item['uid'])->where('id', $item['column_id'])->value('code');
        if ($code != '') {
          $item['code'] = $code;
          $item['type_name'] = '阿奇索自动发货';
        } else {
          $code_record = InvitationCodeRecord::where('id', $item['column_id'])->where('special_id', $item['special_id'])->find();
          if ($code_record) {
            $item['grant_code'] =  $code_record['code'];
            $item['type_name'] = '人工邀请';
          }
        }
      } elseif ($item['type'] == 10) {
        $item['type_name'] = '后台人工发放';
      } else {
        $item['type_name'] = '自动订阅';
      }
      $item['status_name'] = $item['is_del'] == 1 ? '已取消' : '已订阅';
      $item['add_time'] = $item['add_time'] ? date('Y-m-d H:i:s', $item['add_time']) : '';
    }
    $count = self::getSpecialWhere($where, self::alias('a')->join('user r', 'r.uid=a.uid', 'LEFT'), 'a.', 'r')->count();
    return compact('count', 'data');
  }

  public static function douyinCourseList($where)
  {
    $model = self::getSpecialWhere($where, self::alias('a')
      ->join('user r', 'r.uid=a.uid', 'LEFT'), 'a.', 'r')
      ->field('a.*,r.nickname,r.phone,r.spread_uid,r.avatar');
    $model = $model->order('a.id desc');
    if ((isset($where['excel']) && in_array($where['excel'], [1]))) {
      $data = ($data = $model->select()) && count($data) ? $data->toArray() : [];
    } else {
      $data = ($data = $model->page((int)$where['page'], (int)$where['limit'])->select()) && count($data) ? $data->toArray() : [];
    }

    // dd($data);

    foreach ($data as &$item) {
      if ($item['avatar'] == "") {
        $item['avatar'] = 'https://cshop.arthorize.com/attach/2021/01/9e325202101111019451973.png';
      }
      $item['title'] = Special::where('id', $item['special_id'])->value('title');
      $code_record = InvitationCodeRecord::where('id', $item['column_id'])->find();
      if ($code_record) {
        $item['grant_type'] =  $code_record['type'];
        $item['grant_code'] =  $code_record['code'];
        $item['grant_uid'] =  $code_record['uid'];
        $grant_avatar = User::where('uid', $code_record['uid'])->value('avatar');
        $item['grant_avatar'] =   $grant_avatar != "" ? $grant_avatar : 'https://cshop.arthorize.com/attach/2021/01/9e325202101111019451973.png';
        $item['grant_nickname'] =  User::where('uid', $code_record['uid'])->value('nickname');
        $item['grant_phone'] =  User::where('uid', $code_record['uid'])->value('phone');
        $item['grant_add_time'] =  $code_record['add_time'] ? date('Y-m-d H:i:s', $code_record['add_time']) : '';
      }
      $item['status_name'] = $item['is_del'] == 1 ? '管理已删除权益' : '领取成功';
      $item['add_time'] = $item['add_time'] ? date('Y-m-d H:i:s', $item['add_time']) : '';
    }
    // 导出文件
    if (isset($where['excel']) && in_array($where['excel'], [1])) {
      self::SaveExcel($data);
    }
    $count = self::getSpecialWhere($where, self::alias('a')->join('user r', 'r.uid=a.uid', 'LEFT'), 'a.', 'r')->count();
    return compact('count', 'data');
  }

  /**
   * 处理where条件
   * @param $where
   * @param $model
   * @return mixed
   */
  public static function getSpecialWhere($where, $model, $aler = '', $join = '', $joins = '')
  {
    if (isset($where['type']) && $where['type'] != '') {
      $model = self::typeByWhere($where['type'], $model, $aler);
    }
    if (isset($where['status']) && $where['status'] != '') {
      $model = self::statusByWhere($where['status'], $model, $aler);
    }
    if (isset($where['source']) && $where['source'] != '') {
      $model = self::sourceByWhere($where['source'], $model, $aler);
    }
    if (isset($where['special_id']) && $where['special_id'] != '') {
      $model = $model->where('special_id', $where['special_id']);
    }
    if (isset($where['keywords']) && $where['keywords'] != '') {
      $model = $model->where($aler . 'special_id' . ($join ? '|' . $join . '.nickname|' . $join . '.uid|' . $join . '.phone' : ''), 'LIKE', "%$where[keywords]%");
    }
    if (isset($where['code']) && $where['code'] != '') {
      $model = $model->join('store_custom_coupon_issue c', 'a.uid=c.uid');
      $model = $model->where('c.code', "$where[code]");
    }
    if ($where['add_time'] != '') {
      list($startTime, $endTime) = explode(' - ', $where['add_time']);
      $model = $model->where($aler . 'add_time', '>', strtotime($startTime));
      $model = $model->where($aler . 'add_time', '<', strtotime($endTime) + 24 * 3600);
    }
    return $model;
  }

  // 订阅状态
  public static function statusByWhere($status, $model = null, $alert = '')
  {
    if ($model == null) $model = new self;
    if ('' === $status)
      return $model;
    else if ($status == 1) //已领取成功
      return $model->where($alert . 'is_del', 0);
    else if ($status == 2) //已删除
      return $model->where($alert . 'is_del', 1);
    else
      return $model;
  }
  // 类型
  public static function typeByWhere($type, $model = null, $alert = '')
  {
    if ($model == null) $model = new self;
    if ('' === $type)
      return $model;
    else if ($type == 1) //自动订阅
      return $model->whereIn($alert . 'type', [0, 1, 2, 3, 5, 6]);
    else if ($type == 2) //人工邀请
      return $model->whereIn($alert . 'type', [4]);
    else
      return $model;
  }
  // 来源
  public static function sourceByWhere($source, $model = null, $alert = '')
  {
    if ($model == null) $model = new self;
    if ('' === $source)
      return $model;
    else if ($source == 1) //微信小程序
      return $model->whereIn($alert . 'type', [0, 1, 2, 3]);
    else if ($source == 3) //抖音
      return $model->whereIn($alert . 'type', [4]);
    else if ($source == 2) //视频号小店
      return $model->whereIn($alert . 'type', [5]);
    else if ($source == 4) //小红书
      return $model->whereIn($alert . 'type', [6]);
    else
      return $model;
  }



  /**
   * 获取单个用户购买列表
   * @param array $where
   * @return array
   */
  public static function getOneCourseList($where)
  {
    return self::where('uid', $where['uid'])
      ->order('add_time desc')
      ->page((int)$where['page'], (int)$where['limit'])
      ->field([
        'id,FROM_UNIXTIME(add_time,"%Y-%m-%d %H:%i:%s") as add_time'
      ])->select()
      ->toArray();
  }



  /*
     * 保存并下载excel
     * $list array
     * return
     */
  public static function SaveExcel($list)
  {
    $export = [];
    foreach ($list as $index => $item) {
      $WechatUserInfo = User::where('uid', $item['uid'])->value('nickname') ?: '暂无信息';
      $platform_users = $item['uid'] . '/' . $WechatUserInfo;
      // 默认导出格式
      $export[] = [
        $item['title'],
        $platform_users,
        $item['phone'],
        $item['status_name'],
        $item['add_time']
      ];
    }
    PHPExcelService::instance()->setExcelHeader(['课程信息', '用户信息', '手机号', '领取状态', '领取时间'])
      ->setExcelTile('用户课程权益领取', '订单信息' . time(), ' 生成时间：' . date('Y-m-d H:i:s', time()))
      ->setExcelContent($export)
      ->ExcelSave('用户课程权益领取');
  }
}
