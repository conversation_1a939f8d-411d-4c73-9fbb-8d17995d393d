<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2021-09-29 14:48:13
 * @Last Modified time: 2021-09-29 17:54:22
 */
namespace app\admin\model\special;

use crmeb\traits\ModelTrait;
use crmeb\basic\BaseModel;

/**
 * Class Special
 * @package app\admin\model\special
 */
class RecommendRelation extends BaseModel
{
    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'recommend_relation';

    use ModelTrait;

        public static function valiWhere($alias = '', $model = null)
    {
        if (is_null($model)) $model = new self();
        if ($alias) {
            $model = $model->alias($alias);
            $alias .= '.';
        }
        return $model->where("{$alias}is_show", 1);
    }

    public static function getRecemmodBannerList($where)
    {
        $data = self::valiWhere()->order('sort DESC,id DESC')->where('recommend_id', $where['id'])->page((int)$where['page'], (int)$where['limit'])->select();
        $data = count($data) ? $data->toArray() : [];
        foreach ($data as &$item) {
            $item['add_time'] = date('Y-m-d H:i:s', $item['add_time']);
        }
        $count = self::valiWhere()->order('sort DESC,id DESC')->where('recommend_id', $where['id'])->count();
        return compact('data', 'count');
    }
}