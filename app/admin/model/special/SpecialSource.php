<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2021-09-29 17:03:49
 * @Last Modified time: 2021-10-13 16:00:02
 */

namespace app\admin\model\special;

use crmeb\traits\ModelTrait;
use crmeb\basic\BaseModel;
use think\facade\Db;

/**
 * Class SpecialSource
 * @package app\admin\model\special
 */
class SpecialSource extends BaseModel
{
  /**
   * 数据表主键
   * @var string
   */
  protected $pk = 'id';

  /**
   * 模型名称
   * @var string
   */
  protected $name = 'special_source';

  /**获取专题素材
   * @param bool $specialId
   * @return false|\PDOStatement|string|\think\Collection
   * @throws \think\db\exception\DataNotFoundException
   * @throws \think\db\exception\ModelNotFoundException
   * @throws \think\exception\DbException
   */
  public static function getSpecialSource($specialId = false, $sourceId = false)
  {
    $where = array();
    $data = self::where($where);
    if ($specialId && is_numeric($specialId)) {
      $where['special_id'] = $specialId;
      $data->where($where);
    }
    if ($sourceId) {
      if (!is_array($sourceId)) {
        $where['source_id'] = $sourceId;
        $data->where($where);
      } else {
        $data->whereIn('source_id', $sourceId);
      }
    }
    return $data->order('sort asc,id asc')->select();
  }


  // $special=SpecialModel::where('id',$sv->id)->field('pay_type,member_pay_type')->find();
  // if($special && $data['pay_type']==1 && $data['member_pay_type']==0){
  //     if($special['pay_type']==1 && $special['member_pay_type']==1){
  //         SpecialModel::where('id',$sv->id)->update(['member_pay_type'=>0,'member_money'=>0]);
  //     }
  //     $inster['pay_status'] = 1;
  // }else if($data['pay_type']==0){
  //     if($special && $special['pay_type']==1){
  //         SpecialModel::where('id',$sv->id)->update(['member_pay_type'=>0,'member_money'=>0,'pay_type'=>0,'money'=>0]);
  //     }
  //     $inster['pay_status'] = 0;
  // }

  public static function saveSpecialSource($source_list_ids, $specialId = 0, $special_type = 1)
  {
    try {
      Db::startTrans(); //开启事物
      // 找出符合的标题 
      $title_groups = array_column($source_list_ids, 'title');
      $issetCategoryRes   =  SpecialSourceCategory::where('special_id', $specialId)->whereNotIn('title', $title_groups)->select();
      $issetCategoryRes   =  count($issetCategoryRes) ? $issetCategoryRes->toArray() : [];
      // 批量进行删除 分类和素材 
      foreach ($issetCategoryRes as $key => $Iscategory) {
        SpecialSourceCategory::where('id', $Iscategory['id'])->where('special_id', $specialId)->delete();
        self::where('category_id', $Iscategory['id'])->where('special_id', $specialId)->delete();
      }
      // 先查分类 是否有分类 
      foreach ($source_list_ids as $key => $category) {
        $categorysRes =  SpecialSourceCategory::where('special_id', $specialId)->where('title', $category['title'])->find();
        if ($categorysRes) {
          $categorysRes->sort = $category['sort'];
          $categorysRes->number = $category['number'];
          $saveCategorysRes = $categorysRes->save();
          if ($saveCategorysRes) {
            $sourceIdGroups = array_column($category['course_list'], 'id');
            self::where('special_id', $specialId)->where('category_id', $categorysRes['id'])->whereNotIn('source_id', $sourceIdGroups)->delete();
            foreach ($category['course_list'] as $kk => $course) {
              $specialSourceRes = self::where('special_id', $specialId)->where('category_id', $categorysRes['id'])->where('source_id', $course['id'])->find();

          
              if ($specialSourceRes) {
                $specialSourceRes->sort = $course['sort'];
                $specialSourceRes->is_eggs = $course['is_eggs'];
                $specialSourceRes->is_trial = $course['is_trial'];
                $specialSourceRes->pay_status = $course['pay_status'];
                $specialSourceRes->save();
              } else {
                $inster['category_id'] = $categorysRes['id'];
                $inster['special_id'] = $specialId;
                $inster['source_id'] = $course['id'];
                $inster['is_eggs'] = $course['is_eggs'];
                $inster['is_trial'] = $course['is_trial'];
                $inster['pay_status'] = $course['pay_status'];
                $inster['sort'] = $course['sort'];
                $inster['add_time'] = time();
                self::create($inster);
              }
            }
          }
        } else {
          // 如果没有 就新增 
          $categoryData['pid'] = 0;
          $categoryData['special_id'] = $specialId;
          $categoryData['title'] = $category['title'] ? $category['title'] : '';
          $categoryData['number'] = $category['number'];
          $categoryData['add_time'] = time();
          $categoryData['sort'] = $category['sort'];
          $addCategorysDataRes = SpecialSourceCategory::insertGetId($categoryData);
          if ($addCategorysDataRes) {
            foreach ($category['course_list'] as $kk => $course) {
              $inster['category_id'] = $addCategorysDataRes;
              $inster['special_id'] = $specialId;
              $inster['source_id'] = $course['id'];
              $inster['is_eggs'] = $course['is_eggs'];
              $inster['is_trial'] = $course['is_trial'];
              $inster['pay_status'] = $course['pay_status'];
              $inster['sort'] = $course['sort'];
              $inster['add_time'] = time();
              self::create($inster);
            }
          }
        }
      }

      
      Db::commit(); // 提交事务
      return true;
    } catch (\Exception $e) {
      Db::rollback(); //开始回滚
      return false;
    }
  }
}
