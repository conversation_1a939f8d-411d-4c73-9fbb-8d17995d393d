<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2021-09-24 11:29:53
 * @Last Modified time: 2023-02-15 15:31:47
 */
namespace app\admin\model\special;

use crmeb\traits\ModelTrait;
use crmeb\basic\BaseModel;

/**
 * Class StoreCategory
 * @package app\admin\model\market
 */
class SpecialSubject extends BaseModel
{

    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'special_subject';

    use ModelTrait;

    /**
     * 异步获取分类列表
     * @param $where
     * @return array
     */
    public static function CategoryList($where)
    {
        $data = ($data = self::systemPage($where, true)->page((int)$where['page'], (int)$where['limit'])->select()) && count($data) ? $data->toArray() : [];
        foreach ($data as &$item) {
            if ($item['grade_id']) {
                $item['grade_id_name'] = self::where('id', $item['grade_id'])->value('name');
            } else {
                $item['grade_id_name'] = '顶级';
            }
            $item['is_subset'] =  self::where('grade_id', $item['id'])->count() > 1 ? 1 : 0;
        }
        $count = self::systemPage($where, true)->count();
        return compact('count', 'data');
    }

    /**
     * @param $where
     * @return array
     */
    public static function systemPage($where, $isAjax = false)
    {
        $model = new self;
        if ($where['grade_id'] != '') $model = $model->where('grade_id', $where['grade_id']);
        else if ($where['grade_id'] == '' && $where['name'] == '') $model = $model->where('grade_id', 0);
        if ($where['is_show'] != '') $model = $model->where('is_show', $where['is_show']);
        if ($where['name'] != '') $model = $model->where('name', 'LIKE', "%$where[name]%");
        if ($isAjax === true) {
            if (isset($where['order']) && $where['order'] != '') {
                $model = $model->order(self::setOrder($where['order']));
            } else {
                $model = $model->order('sort desc,id desc');
            }
            return $model;
        }
        return self::page($model, function ($item) {
            if ($item['grade_id']) {
                $item['grade_id_name'] = self::where('id', $item['grade_id'])->value('name');
            } else {
                $item['grade_id_name'] = '顶级';
            }
        }, $where);
    }

    /**
     * 获取顶级分类
     * @return array
     */
    public static function getCategory()
    {
        return self::where('is_show', 1)->column('name', 'id');
    }

    /**
     * 分级排序列表
     * @param null $model
     * @param int $type
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public static function getTierList($model = null, $type = 0)
    {
        if ($model === null) $model = new self();
        if (!$type) return sort_special_list_tier($model->order('sort desc,id desc')->where('grade_id', 0)->select()->toArray());
        return sort_special_list_tier($model->order('sort desc,id desc')->select()->toArray());
    }

    public static function specialCategoryAll($type=0){
        $model=self::where(['is_del' => 0]);
        if($type==1){
            $model=$model->where('grade_id',0);
        }
        $list=$model->order('sort desc,add_time desc')->select();
        $list=count($list) > 0 ? $list->toArray() : [];
        if ($type == 2) {
            foreach ($list as $key=>$cate) {
                if ($cate['grade_id'] == 0) { //查询是否顶级，如果是顶级查询底下是否有子级
                    $childrens = self::where(['is_del' => 0,'grade_id'=>$cate['id']])->count();
                    if ($childrens > 0) {
                        unset($list[$key]);
                    }
                }
            }
            return $list;
        }else{
            return sort_special_list_tier($list);
        }
    }


    public static function delCategory($id)
    {
        $count = self::where('grade_id', $id)->count();
        if ($count)
            return self::setErrorInfo('请先删除下级子分类');
        else {
            return self::del($id);
        }
    }

    /**
     * 产品分类隐藏显示
     * @param $id
     * @param $show
     * @return bool
     */
    public static function setCategoryShow($id, $show)
    {
        $count = self::where('id', $id)->count();
        if (!$count) return self::setErrorInfo('参数错误');
        $count = self::where('id', $id)->where('is_show', $show)->count();
        if ($count) return true;
        $grade_id = self::where('id', $id)->value('grade_id');
        self::beginTrans();
        $res1 = true;
        $res2 = self::where('id', $id)->update(['is_show' => $show]);
        if (!$grade_id) {//一级分类隐藏
            $count = self::where('grade_id', $id)->count();
            if ($count) {
                $count      = self::where('grade_id', $id)->where('is_show', $show)->count();
                $countWhole = self::where('grade_id', $id)->count();
                if (!$count || $countWhole > $count) {
                    $res1 = self::where('grade_id', $id)->update(['is_show' => $show]);
                }
            }
        }
        $res = $res1 && $res2;
        self::checkTrans($res);
        return $res;
    }
}