<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2021-11-16 17:03:21
 * @Last Modified time: 2021-11-22 15:48:15
 */
namespace app\admin\model\special;

use crmeb\traits\ModelTrait;
use crmeb\basic\BaseModel;
use app\admin\model\special\Special;
use app\admin\model\system\RecommendRelation;

/**
 * Class SpecialLabel
 * @package app\admin\model\special
 */
class SpecialLabel extends BaseModel
{
    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'special_label_data';

    /**获取专题素材所有关联的标签
     * @param bool $special_id
     * @return false|\PDOStatement|string|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public static function getSpecialLabel($special_id = false)
    {
        $where = array();
        $data = self::where($where);
        if ($special_id && is_numeric($special_id)) {
            $where['special_id'] = $special_id;
            $data->where($where);
        }
        return $data->order('sort desc,id desc')->select();
    }

    public static function saveSpecialLabel($label_list,$special_id=0)
    {
        $specialLabelAll = self::getSpecialLabel($special_id)->toArray();
        if ($specialLabelAll) {
            self::where(['special_id' => $special_id])->delete();
        }
        $inster['special_id'] = $special_id;
        $inster['status'] = 1;
        foreach ($label_list as $sk => $sv) {
            $inster['name'] = $sv;
            $inster['add_time'] = time();
            self::create($inster);
        }
        return true;
    }

}