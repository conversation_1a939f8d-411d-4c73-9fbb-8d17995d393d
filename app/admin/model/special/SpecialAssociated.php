<?php

/**
 * @Author: <PERSON>
 * @Date:   2022-03-23 11:41:05
 * @Last Modified time: 2022-04-11 16:36:39
 */

namespace app\admin\model\special;

use think\facade\Db;
use crmeb\basic\BaseModel;
use crmeb\traits\ModelTrait;

/**
 * TODO 课程素材关联商品Model
 * Class SpecialAssociated
 * @package app\models\special
 */
class SpecialAssociated extends BaseModel
{
  /**
   * 模型名称
   * @var string
   */
  protected $name = 'special_associated';

  use ModelTrait;

  /**获取专题素材
   * @param bool $special_id
   */
  public static function getSourceList($relatedId = 0, $type = 0)
  {
    $where = array();
    $data = self::where($where);

    if ($relatedId) {
      $where['type'] = 0;
      $where['special_id'] = $relatedId;
      $data->where($where);
    }
    return $data->order('sort desc,id desc')->select();
  }


  /**获取专题素材
   * @param bool $special_id
   */
  public static function getSpecialList($relatedId = 0, $type = 0)
  {
    $where = array();
    $data = self::where($where);

    if ($relatedId) {
      $where['type'] = 1;
      $where['special_id'] = $relatedId;
      $data->where($where);
    }

    return $data->order('sort desc,id desc')->select();
  }

  /**获取专题素材
   * @param bool $special_id
   */
  public static function getProductList($relatedId = 0, $type = 0)
  {
    $where = array();
    $data = self::where($where);

    if ($relatedId) {
      $where['type'] = 2;
      $where['special_id'] = $relatedId;
      $data->where($where);
    }
    return $data->order('sort desc,id desc')->select();
  }



  /**获取专题工具
   * @param bool $special_id
   */
  public static function getToolList($relatedId = 0, $type = 0)
  {
    $where = array();
    $data = self::where($where);

    if ($relatedId) {
      $where['type'] = 3;
      $where['special_id'] = $relatedId;
      $data->where($where);
    }

    return $data->order('sort desc,id desc')->select();
  }


  public static function addRelatedSpecial($type = 1, $insert_type = 0,  $specialData = [], $specialId = 0)
  {
    Db::startTrans(); //开启事物
    $status = true;
    $insert_type = 1;
    if ($type == 1) { //添加
      //关联课程合集
      if (is_array($specialData)) {
        $inster = [];
        foreach ($specialData as $k => $v) {
          $inster[$k]['type'] = $insert_type;
          $inster[$k]['special_id'] = $specialId;
          $inster[$k]['related_id'] = $v['id'];
          $inster[$k]['sort'] = $v['sort'];
          $inster[$k]['add_time'] = time();
        }
        $status = self::insertAll($inster);
      }
    } elseif ($type == 2) { //编辑
      // 关联课程
      if (is_array($specialData) && count($specialData) > 0) {
        $relatedIds = array_column($specialData, 'id');
        self::where('type', $insert_type)->where('special_id', $specialId)->whereNotIn('related_id', $relatedIds)->delete();
        $inster = [];
        $update_inster = [];
        foreach ($specialData as $k => $v) {
          $object =  self::where('type', $insert_type)->where('special_id', $specialId)->where('related_id', $v['id'])->find();
          if ($object) {
            $sort = (int) $v['sort'];
            if ($sort != $object->sort) {
              $update_inster[$k]['id'] = (int) $object['id'];
              $update_inster[$k]['sort'] = (int) $v['sort'];
            }
          } else {
            $inster[$k]['type'] = $insert_type;
            $inster[$k]['special_id'] = $specialId;
            $inster[$k]['related_id'] = $v['id'];
            $inster[$k]['sort'] = (int) $v['sort'];
            $inster[$k]['add_time'] = time();
          }
        }
        $inster =  array_values($inster);
        $update_inster =  array_values($update_inster);
        if (is_array($inster) && count($inster) > 0) {
          $number = self::insertAll($inster);
          $status = $number > 0 ? true : false;
        }
        if (is_array($update_inster) && count($update_inster) > 0) {
          $object = new self();
          $number = $object->saveAll($update_inster);
          $status = $number  ? true : false;
        }
      } else {
        $count = self::where('type', $insert_type)->where('special_id', $specialId)->count();
        if ($count > 0) {
          $status = self::where('type', $insert_type)->where('special_id', $specialId)->delete();
        }
      }
      //执行事务
      if ($status) {
        Db::commit(); // 提交事务
        return true;
      } else {
        Db::rollback(); //开始回滚
        return false;
      }
    }
  }

  public static function addRelatedProducts($type = 1, $insert_type = 0, $productData = [], $specialId = 0)
  {
    Db::startTrans(); //开启事物
    $status = true;
    $insert_type = 2;
    if ($type == 1) { //添加
      //关联商品
      if (is_array($productData)) {
        $inster = [];
        foreach ($productData as $k => $v) {
          $inster[$k]['type'] = $insert_type;
          $inster[$k]['special_id'] = $specialId;
          $inster[$k]['related_id'] = $v['id'];
          $inster[$k]['sort'] = $v['sort'];
          $inster[$k]['add_time'] = time();
        }
        $status = self::insertAll($inster);
      }
    } elseif ($type == 2) { //编辑
      // 关联商品
      if (is_array($productData) && count($productData) > 0) {
        $relatedIds = array_column($productData, 'id');
        self::where('type', $insert_type)->where('special_id', $specialId)->whereNotIn('related_id', $relatedIds)->delete();
        $inster = [];
        $update_inster = [];
        foreach ($productData as $k => $v) {
          $object =  self::where('type', $insert_type)->where('special_id', $specialId)->where('related_id', $v['id'])->find();
          if ($object) {
            $sort = (int) $v['sort'];
            if ($sort != $object->sort) {
              $update_inster[$k]['id'] = (int) $object['id'];
              $update_inster[$k]['sort'] = (int) $v['sort'];
            }
          } else {
            $inster[$k]['type'] = $insert_type;
            $inster[$k]['special_id'] = $specialId;
            $inster[$k]['related_id'] = $v['id'];
            $inster[$k]['sort'] = (int) $v['sort'];
            $inster[$k]['add_time'] = time();
          }
        }
        $inster =  array_values($inster);
        $update_inster =  array_values($update_inster);
        if (is_array($inster) && count($inster) > 0) {
          $number = self::insertAll($inster);
          $status = $number > 0 ? true : false;
        }
        if (is_array($update_inster) && count($update_inster) > 0) {
          $object = new self();
          $number = $object->saveAll($update_inster);
          $status = $number  ? true : false;
        }
      } else {
        $count = self::where('type', $insert_type)->where('special_id', $specialId)->count();
        if ($count > 0) {
          $status = self::where('type', $insert_type)->where('special_id', $specialId)->delete();
        }
      }
    }
    //执行事务
    if ($status) {
      Db::commit(); // 提交事务
      return true;
    } else {
      Db::rollback(); //开始回滚
      return false;
    }
  }
}
