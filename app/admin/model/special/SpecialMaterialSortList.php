<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2021-11-22 15:44:47
 * @Last Modified time: 2022-03-21 15:58:48
 */
namespace app\admin\model\special;

use crmeb\traits\ModelTrait;
use crmeb\basic\BaseModel;
/**
 * Class SpecialMaterialSortList
 * @package app\admin\model\special
 */
class SpecialMaterialSortList extends BaseModel
{
    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'special_material_cate_sort';

    /**获取专题素材所有关联的标签
     * @param bool $special_id
     * @return false|\PDOStatement|string|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public static function getList($special_id = false)
    {
        $model = new self;
        $model =  $model->alias('a');
        $model =  $model->join('special b', 'b.id=a.special_id', 'LEFT');
        $model =  $model->join('special_task c', 'c.id=a.task_id', 'LEFT');
        $model =  $model->field('a.id,a.type as index_type,a.cate_id as subject_id,a.special_id,a.task_id,b.title,c.type as task_type,c.title as task_title,b.image,c.image as task_image');
        $model = $model->where('a.cate_id', $special_id);
        $model = $model->where('b.is_show|c.is_list_show', 1);
        $model =  $model->where('b.is_del|c.is_del', 0);
        return $model->order('a.sort desc,a.id desc')->select();
    }


    public static function deleteSpecialSort($type = 1,$subject_id,$related=0)
    {
        if ($type == 1) {
            $special_sort = self::where('cate_id',$subject_id)->where('special_id',$related)->delete();
        }elseif ($type == 2) {
            $special_sort = self::where('cate_id',$subject_id)->where('task_id',$related)->delete();
        }
        return true;
    }

    public static function addSpecialSort($type = 1,$subject_id,$related=0, $isShow = 1)
    {
        $inster['cate_id'] = $subject_id;
        if ($type == 1) {
            $inster['special_id'] = $related;
           $where = ['type' => $type, 'cate_id' => $subject_id, 'special_id'=> $related];
        }elseif ($type == 2) {
            $inster['task_id'] = $related;
            $where = ['type' => $type, 'cate_id' => $subject_id, 'task_id'=> $related];
        }
        if (!self::where($where)->find()) {
            //查询此分类下最后一个sort值
            $last_sort_value = self::where('cate_id',$subject_id)->order('sort desc')->value('sort');
            $inster['type'] = $type;
            $inster['cate_id'] = $subject_id;
            $inster['sort'] = $last_sort_value + 1;
            $inster['is_show'] = $isShow;
            $inster['add_time'] = time();
            $inster['update_time'] = time();
            self::create($inster);
        }
        return true;
    }


    public static function SaveSpecialSort($type = 1,$subject_id,$related=0, $isShow = 1)
    {
        $inster['cate_id'] = $subject_id;
        if ($type == 1) {
            $inster['special_id'] = $related;
            $where = ['type' => $type, 'special_id'=> $related];
        }elseif ($type == 2) {
            $inster['task_id'] = $related;
            $where = ['type' => $type, 'cate_id' => $subject_id, 'task_id'=> $related];
        }
        $object = self::where($where)->find();
        if ($object) {
            //查询此分类下最后一个sort值
            $last_sort_value = self::where('cate_id',$subject_id)->order('sort desc')->value('sort');
            $last_sort_id = self::where('cate_id',$subject_id)->order('sort desc')->value('id');
            $inster['type'] = $type;
            $inster['cate_id'] = $subject_id;
            if ($last_sort_id != $object->id) {
                $inster['sort'] = $last_sort_value + 1;
            }else{
                $inster['sort'] = $last_sort_value;
            }
            $inster['is_show'] = $isShow;
            $inster['update_time'] = time();
            self::where('id',$object->id)->update($inster);
        }else{
            $last_sort_value = self::where('cate_id',$subject_id)->order('sort desc')->value('sort');
            $inster['type'] = $type;
            $inster['cate_id'] = $subject_id;
            $inster['sort'] = $last_sort_value + 1;
            $inster['is_show'] = $isShow;
            $inster['add_time'] = time();
            $inster['update_time'] = time();
            self::create($inster);
        }
        return true;
    }


    public static function saveSortList($list)
    {
        self::beginTrans();
        $success_status = [];
        foreach ($list as $key => $sort) {
          $success_status[] =  self::where('id', $sort['id'])->update(['sort' => $sort['sort'],'update_time'=>time()]);
        }
        if (count($list) != count($success_status)) {
            self::rollbackTrans();
            return false;
        }
        self::commitTrans();
        return true;
    }
}