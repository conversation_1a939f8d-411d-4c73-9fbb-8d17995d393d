<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2021-09-29 17:03:49
 * @Last Modified time: 2021-10-13 16:00:02
 */
namespace app\admin\model\special;

use crmeb\traits\ModelTrait;
use crmeb\basic\BaseModel;
use app\admin\model\special\Special as SpecialModel;

/**
 * Class SpecialActivityAssociated
 * @package app\admin\model\special
 */
class SpecialActivityAssociated extends BaseModel
{
    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'special_activity_associated';

    public static function saveLive($live_list_ids,$special_id=0)
    {
        $liveAll = self::where('special_id',$special_id)->select()->toArray();
        if ($liveAll) {
            self::where(['special_id' => $special_id])->delete();
        }
        $inster['type'] = 1;
        $inster['special_id'] = $special_id;
        foreach ($live_list_ids as $sk => $sv) {
            $inster['activity_id'] = $sv['id'];
            $inster['sort'] = $sv['sort'];
            $inster['add_time'] = time();
            self::create($inster);
        }
        return true;
    }

}