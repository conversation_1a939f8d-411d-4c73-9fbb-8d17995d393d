<?php

/**
 * @Author: <PERSON>
 * @Date:   2022-03-23 11:41:05
 * @Last Modified time: 2022-04-11 16:36:39
 */

namespace app\admin\model\special;

use think\facade\Db;
use crmeb\basic\BaseModel;
use crmeb\traits\ModelTrait;

/**
 * TODO 课程素材关联商品Model
 * Class SpecialTaskTodoList
 * @package app\models\special
 */
class SpecialTaskTodoList extends BaseModel
{
  /**
   * 模型名称
   * @var string
   */
  protected $name = 'special_task_todo_list';

  use ModelTrait;


  /**获取专题素材
   * @param bool $sourceId
   */
  public static function getSpecialSourceList($sourceId = 0, $type = 0)
  {
    $where = array();
    $data = self::where($where);

    if ($sourceId) {
      $where['type'] = 0;
      $where['source_id'] = $sourceId;
      $data->where($where);
    }
    return $data->order('sort asc,id desc')->select();
  }


  public static function addRelatedResult($actionType = 1, $relatedData = [], $sourceId = 0)
  {
    // 启动事务
    Db::startTrans();
    try {
      if ($actionType === 1) { // 新增操作
        $newRecords = array_map(function ($data) use ($sourceId) {
          return [
            'source_id' => $sourceId,
            'type' => $data['type'] ?? '',
            'name' => $data['name'] ?? '',
            'info' => $data['info'] ?? '',
            'link' => $data['link'] ?? '',
            'point' => $data['point'] ?? '',
            'sort' => $data['sort'] ?? 0,
            'add_time' => time()
          ];
        }, $relatedData);

        self::insertAll($newRecords);
      } elseif ($actionType === 2) { // 编辑操作
        $existingIds = array_column($relatedData, 'id');

        // 删除不再关联的记录
        self::where('source_id', $sourceId)
          ->delete();

        $newEntries = [];
        $updateEntries = [];

        foreach ($relatedData as $data) {
          $currentRecord = self::where('source_id', $sourceId)
            ->find();

          if ($currentRecord) {
            if ($currentRecord->sort != $data['sort']) {
              $updateEntries[] = [
                'id' => $currentRecord->id,
                'sort' => $data['sort']
              ];
            }
          } else {
            $newEntries[] = [
              'source_id' => $sourceId,
              'type' => $data['type'] ?? '',
              'name' => $data['name'] ?? '',
              'info' => $data['info'] ?? '',
              'link' => $data['link'] ?? '',
              'point' => $data['point'] ?? '',
              'sort' => $data['sort'] ?? 0,
              'add_time' => time()
            ];
          }
        }

        // 插入新的记录
        if (!empty($newEntries)) {
          self::insertAll($newEntries);
        }

        // 更新已有的记录
        if (!empty($updateEntries)) {
          (new self())->saveAll($updateEntries);
        }
      }

      // 提交事务
      Db::commit();
      return true;
    } catch (\Exception $e) {
      // 异常回滚事务
      Db::rollback();
      return false;
    }
  }
}
