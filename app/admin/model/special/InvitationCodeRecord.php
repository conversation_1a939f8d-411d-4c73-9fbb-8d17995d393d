<?php

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date:   2023-02-14 17:58:03
 * @Last Modified time: 2023-02-14 17:59:29
 */

namespace app\admin\model\special;

use crmeb\traits\ModelTrait;
use crmeb\basic\BaseModel;
use app\admin\model\user\User;

/**
 * Class InvitationCodeRecord
 * @package app\admin\model\special
 */
class InvitationCodeRecord extends BaseModel
{
  /**
   * 数据表主键
   * @var string
   */
  protected $pk = 'id';

  /**
   * 模型名称
   * @var string
   */
  protected $name = 'special_invitation_code_record';

  use ModelTrait;

  public static function valiWhere($alias = '', $model = null)
  {
    if (is_null($model)) $model = new self();
    if ($alias) {
      $model = $model->alias($alias);
      $alias .= '.';
    }
    return $model->where("{$alias}is_show", 1);
  }


  public static function douyinCourseList($where)
  {
    $model = self::getCodeWhere($where, self::alias('a')
      ->join('user r', 'r.uid=a.uid', 'LEFT'), 'a.', 'r')
      ->field('a.*,r.nickname,r.phone,r.spread_uid,r.avatar');
    $model = $model->order('a.id desc');
    if ((isset($where['excel']) && in_array($where['excel'], [1]))) {
      $data = ($data = $model->select()) && count($data) ? $data->toArray() : [];
    } else {
      $data = ($data = $model->page((int)$where['page'], (int)$where['limit'])->select()) && count($data) ? $data->toArray() : [];
    }
    foreach ($data as &$item) {
      $item['title'] = Special::where('id', $item['special_id'])->value('title');
      $item['status_name'] = '待领取';
      $item['grant_avatar'] = $item['avatar'];
      // 发放人
      $item['grant_nickname'] =  User::where('uid', $item['uid'])->value('nickname');
      $item['grant_phone'] =  User::where('uid', $item['uid'])->value('phone');
      $item['grant_add_time'] =  $item['add_time'] ? date('Y-m-d H:i:s', $item['add_time']) : '';
      $item['grant_uid'] =  $item['uid'];
      $item['grant_code'] =  $item['code'];
      // 领取人
      $code_record = SpecialBuy::where('column_id', $item['id'])->where('special_id', $item['special_id'])->find();
      $item['receive_uid'] = '';
      $item['receive_avatar'] = '';
      $item['receive_nickname'] = '';
      $item['receive_phone'] = '';
      $item['receive_add_time'] = '';
      if ($code_record) {
        $avatar = User::where('uid', $code_record['uid'])->value('avatar');
        $item['receive_uid'] = $code_record['uid'];
        $item['receive_avatar'] =   $avatar != "" ? $avatar : 'https://cshop.arthorize.com/attach/2021/01/9e325202101111019451973.png';
        $item['receive_nickname'] =  User::where('uid', $code_record['uid'])->value('nickname');
        $item['receive_phone'] =  User::where('uid', $code_record['uid'])->value('phone');
        $item['receive_add_time'] =  $code_record['add_time'] ? date('Y-m-d H:i:s', $code_record['add_time']) : '';
        $item['status_name'] = $code_record['is_del'] == 1 ? '管理已删除权益' : '领取成功';
      }
    }
    $count = self::getCodeWhere($where, self::alias('a')->join('user r', 'r.uid=a.uid', 'LEFT'), 'a.', 'r')->count();
    return compact('count', 'data');
  }


  /**
   * 处理where条件
   * @param $where
   * @param $model
   * @return mixed
   */
  public static function getCodeWhere($where, $model, $aler = '', $join = '', $joins = '')
  {
    if (isset($where['special_id']) && $where['special_id'] != '') {
      $model = $model->where($aler . 'special_id', $where['special_id']);
    }

    if (isset($where['code']) && $where['code'] != '') {
      $model = $model->where($aler . 'code', $where['code']);
    }

    if (isset($where['keywords']) && $where['keywords'] != '') {
      $model = $model->where($aler . 'special_id' . ($join ? '|' . $join . '.nickname|' . $join . '.uid|' . $join . '.phone' : ''), 'LIKE', "%$where[keywords]%");
    }

    if ($where['add_time'] != '') {
      list($startTime, $endTime) = explode(' - ', $where['add_time']);
      $model = $model->where($aler . 'add_time', '>', strtotime($startTime));
      $model = $model->where($aler . 'add_time', '<', strtotime($endTime) + 24 * 3600);
    }
    return $model;
  }
}
