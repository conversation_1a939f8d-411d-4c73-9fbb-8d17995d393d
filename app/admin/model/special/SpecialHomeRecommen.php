<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2020-12-13 15:54:17
 * @Last Modified time: 2021-12-14 11:09:38
 */

namespace app\admin\model\special;

use crmeb\basic\BaseModel;
use crmeb\traits\ModelTrait;
use crmeb\services\upload\storage\OssHelper;

/**
 * 首页配置-横幅Model
 * Class StoreActivity
 * @package app\admin\model\store
 */
class SpecialHomeRecommen extends BaseModel
{
  /**
   * 数据表主键
   * @var string
   */
  protected $pk = 'id';

  /**
   * 模型名称
   * @var string
   */
  protected $name = 'special_latest_recommend';

  use ModelTrait;


  public static function List($where)
  {
    $model = self::getInquireWhere()->order('id desc');
    $data = ($data = $model->page((int)$where['page'], (int)$where['limit'])->select()) && count($data) ? $data->toArray() : [];
    foreach ($data as &$item) {
      if ($item['type'] == 1) {
        $source = SpecialTask::field('type,title,image')->where('id', $item['source_id'])->find();
        if ($source) {
          $item['title'] = $source['title'];
          if ($source['type'] == 3) {
            if (!filter_var($source['image'], FILTER_VALIDATE_URL) !== false) {
              $item['image'] =  OssHelper::normalVideo($source['image']) ?: '';
            } else {
              $item['image'] = $source['image'];
            }
          } else {
            $item['image'] = $source['image'];
          }
          $item['add_time'] = $item['add_time'] ? date('Y-m-d H:i:s', $item['add_time']) : '';
        }
      } else {
        $special = Special::field('title,image')->where('id', $item['special_id'])->find();
        if ($special) {
          $item['title'] = $special['title'];
          $item['image'] = $special['image'];
          $item['add_time'] = $item['add_time'] ? date('Y-m-d H:i:s', $item['add_time']) : '';
        }
      }
    }
    $count = self::getInquireWhere($where, self::alias('a'))->count();
    return compact('count', 'data');
  }

  /**
   * 处理where条件
   * @param $where
   * @param $model
   * @return mixed
   */
  public static function getInquireWhere()
  {
    $model = new self();
    return  $model->where('is_del', 0);
  }


  /* 横幅隐藏显示
     * @param $id
     * @param $show
     * @return bool
     */
  public static function setShow($id, $show)
  {
    self::beginTrans();
    $res1 = true;
    $res2 = self::where('id', $id)->update(['is_show' => $show]);
    $res = $res1 && $res2;
    self::checkTrans($res);
    return $res;
  }
}
