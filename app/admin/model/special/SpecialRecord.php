<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2021-11-16 17:03:21
 * @Last Modified time: 2021-11-22 15:48:15
 */

namespace app\admin\model\special;

use crmeb\traits\ModelTrait;
use crmeb\basic\BaseModel;

/**
 * Class SpecialRecord
 * @package app\admin\model\special
 */
class SpecialRecord extends BaseModel
{
  /**
   * 数据表主键
   * @var string
   */
  protected $pk = 'id';

  /**
   * 模型名称
   * @var string
   */
  protected $name = 'special_record';

  /**获取专题素材所有关联的标签
   * @param bool $special_id
   * @return false|\PDOStatement|string|\think\Collection
   * @throws \think\db\exception\DataNotFoundException
   * @throws \think\db\exception\ModelNotFoundException
   * @throws \think\exception\DbException
   */
  public static function getOneCourseList($where)
  {
    $list =  self::where('uid', $where['uid'])
      ->order('updated_time desc')
      ->page((int)$where['page'], (int)$where['limit'])
      ->field([
        'id,special_id,source_id,FROM_UNIXTIME(updated_time,"%Y-%m-%d %H:%i:%s") as updated_time'
      ])->select()
      ->toArray();

    foreach ($list as $key => $item) {
      $list[$key]['title'] =  $item['source_id'] == 0 ? Special::where('id', $item['special_id'])->where('is_del', 0)->where('is_show', 1)->value('title') :  SpecialTask::where('id', $item['source_id'])->where('is_del', 0)->where('is_show', 1)->value('title');
      $list[$key]['study_time'] = secondChanage(rand(100, 10000));
    }
    return array_values($list);
  }
}
