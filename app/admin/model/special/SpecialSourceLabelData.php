<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2021-11-16 17:03:21
 * @Last Modified time: 2021-11-22 15:48:15
 */
namespace app\admin\model\special;

use crmeb\traits\ModelTrait;
use crmeb\basic\BaseModel;

/**
 * Class SpecialSourceLabelData
 * @package app\admin\model\special
 */
class SpecialSourceLabelData extends BaseModel
{
    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'special_task_label_data';

    /**获取专题素材所有关联的标签
     * @param bool $special_id
     * @param bool $sourcesId
     * @return false|\PDOStatement|string|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public static function getSpecialSourceLabelData($specialId = false, $sourcesId = false)
    {
        $where = array();
        $data = self::where($where);
        if ($specialId && is_numeric($specialId)) {
            $where['special_id'] = $specialId;
            $data->where($where);
        }
        if ($sourcesId && is_numeric($sourcesId)) {
            $where['source_id'] = $sourcesId;
            $data->where($where);
        }
        return $data->order('sort desc,id desc')->select();
    }

    public static function saveSpecialSourceLabelData($label_list,$specialId=0,$sourcesId = 0)
    {
        $specialSourceLabelDataAll = self::getSpecialSourceLabelData($specialId,$sourcesId)->toArray();
        if ($specialSourceLabelDataAll) {
            self::where(['special_id' => $specialId, 'source_id'=> $sourcesId])->delete();
        }
        $inster['special_id'] = $specialId;
        $inster['source_id'] = $sourcesId;
        $inster['status'] = 1;
        foreach ($label_list as $sk => $sv) {
            $inster['name'] = $sv;
            $inster['add_time'] = time();
            self::create($inster);
        }
        return true;
    }

}