<?php
/**
 * @author: xaboy<<EMAIL>>
 * @day: 2017/11/11
 */
namespace app\admin\model\special;

use crmeb\basic\BaseModel;
use crmeb\traits\ModelTrait;

/**
 * 渠道课程商品配置 model
 * Class SpecialLive
 * @package app\admin\model\special
 */
class SpecialLive extends BaseModel
{
    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'special_live';

    use ModelTrait;

    public static function getLiveList($where)
    {
        $model = self::getLiveWhere($where, self::alias('a'))
            ->field('a.*');
        $model = $model->order('a.id desc');
        $data = ($data = $model->page((int)$where['page'], (int)$where['limit'])->select()) && count($data) ? $data->toArray() : [];
        foreach ($data as &$item) {
            $item['add_time'] = $item['add_time'] ? date('Y-m-d H:i:s', $item['add_time']) : '';
        }
        $count = self::getLiveWhere($where, self::alias('a'))->count();
        return compact('count', 'data');
    }


    /**
     * 处理where条件
     * @param $where
     * @param $model
     * @return mixed
     */
    public static function getLiveWhere($where, $model, $aler = '', $join = '')
    {
        $model = $model->where('is_del',0);
        if (isset($where['keywords']) && $where['keywords'] != '') {
            $model = $model->where('live_id',$where['keywords']);
        }
        if (isset($where['title']) && $where['title'] != '') {
            $model = $model->where($aler . 'title', 'LIKE', "%$where[title]%");
        }
        if ($where['add_time'] != '') {
            list($startTime, $endTime) = explode(' - ', $where['add_time']);
            $model = $model->where($aler. 'add_time', '>', strtotime($startTime));
            $model = $model->where($aler. 'add_time', '<', strtotime($endTime) + 24 * 3600);
        }
        return $model;
    }
}