<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2021-11-03 10:20:55
 * @Last Modified time: 2023-04-06 10:16:29
 */
namespace app\admin\model\special;

use crmeb\basic\BaseModel;
use crmeb\traits\ModelTrait;
use app\models\special\SpecialSurpriseRecord;

/**
 * 心愿单管理 model
 * Class StoreMessage
 * @package app\admin\model\market
 */
class SpecialMessage extends BaseModel
{

    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'special_message';

    use ModelTrait;

    public static function MessageCount()
    {
        $data['reviewed'] = self::statusByWhere(1, new self())->count();
        $data['unreviewed'] = self::statusByWhere(2, new self())->count();
        $data['rejected'] = self::statusByWhere(3, new self())->count();
        $data['deleted'] = self::statusByWhere(4, new self())->count();

        // 类型
        $data['comments'] = self::messageTypeByWhere(0, new self())->count();
        $data['works'] = self::messageTypeByWhere(1, new self())->count();

        // 留言格式类型
        $data['texts'] = self::typeByWhere(0, new self())->count();
        $data['videos'] = self::typeByWhere(1, new self())->count();
        $data['audios'] = self::typeByWhere(2, new self())->count();
        return $data;
    }


    public static function statusByWhere($status, $model = null, $alert = '')
    {
        if ($model == null) $model = new self;
        if ('' === $status)
            return $model;
        else if ($status == 1)//已审核
            return $model->where($alert . 'status', 1)->where($alert . 'is_del', 0);
        else if ($status == 2)//待审核
            return $model->where($alert . 'status', 2)->where($alert . 'is_del', 0);
        else if ($status == 3)//已删除
            return $model->where($alert . 'status', 3)->where($alert . 'is_del', 0);
        else if ($status == 4)//已删除
            return $model->where($alert . 'is_del', 1);
        else
            return $model;
    }


    public static function typeByWhere($type, $model = null, $alert = '')
    {
        if ($model == null) $model = new self;
        if ('' === $type)
            return $model;
        else if ($type == 0)//图文
            return $model->where($alert . 'type', 0)->where($alert . 'is_del', 0);
        else if ($type == 1)//视频
            return $model->where($alert . 'type', 1)->where($alert . 'is_del', 0);
        else if ($type == 2)//音频
            return $model->where($alert . 'type', 2)->where($alert . 'is_del', 0);
        else
            return $model;
    }


    public static function messageTypeByWhere($type, $model = null, $alert = '')
    {
        if ($model == null) $model = new self;
        if ('' === $type)
            return $model;
        else if ($type == 0)//普通留言
            return $model->where($alert . 'message_type', 0)->where($alert . 'is_del', 0);
        else if ($type == 1)//作业
            return $model->where($alert . 'message_type', 1)->where($alert . 'is_del', 0);
        else
            return $model;
    }


    public static function MessageList($where)
    {
        $model = self::getMessageWhere($where, self::alias('a')
            ->join('user r', 'r.uid=a.uid', 'LEFT'), 'a.', 'r')
            ->field('a.*,r.nickname,r.phone,r.spread_uid,r.avatar');
        $model = $model->order('a.id desc');
        $data = ($data = $model->page((int)$where['page'], (int)$where['limit'])->select()) && count($data) ? $data->toArray() : [];
        foreach ($data as &$item) {
            $status_name = '';
            if ($item['status'] == 2 && $item['is_del'] == 0) {
                $status_name = '待审核';
            } elseif ($item['status'] == 1 && $item['is_del'] == 0) {
                $status_name = '已审核';
            } elseif ($item['status'] == 3 && $item['is_del'] == 0) {
                $status_name = '已拒绝';
            } elseif ($item['is_del'] == 1) {
                $status_name = '已删除';
            }
            $item['is_user_get_easter_eggs'] = SpecialSurpriseRecord::isUnlock($item['uid'],$item['special_id'],0,'easter_egg') ? 1 : 0;
            $item['is_special_easter_eggs'] = $item['message_type']== 1 && $item['source_id'] == 0 ? Special::where('id',$item['special_id'])->value('is_easter_eggs') : 0;
            // 寻找封面图
            $item['message_cover'] = $item['source_id'] == 0 ? Special::where('id',$item['special_id'])->value('image') : SpecialTask::where('id',$item['source_id'])->value('image');
            $item['status_name'] = $status_name;
            $item['type_name'] = $item['message_type'] == 1 ? '作业' : '评论';
            $item['add_time'] = $item['add_time'] ? date('Y-m-d H:i:s', $item['add_time']) : '';
        }
        $count = self::getMessageWhere($where, self::alias('a')->join('user r', 'r.uid=a.uid', 'LEFT'), 'a.', 'r')->count();
        return compact('count', 'data');
    }

    public static function getMessageList($where, $page, $limit){
        $model = self::getMessageWhere($where, self::alias('a')
            ->join('user r', 'r.uid=a.uid', 'LEFT'), 'a.', 'r')
            ->field('a.*,r.nickname,r.phone,r.spread_uid,r.avatar');
        $model = $model->order('a.id desc');
        $data = ($data = $model->page((int)$page, (int)$limit)->select()) && count($data) ? $data->toArray() : [];
        foreach ($data as &$item) {
            if ($item['status'] == 2 && $item['is_del'] == 0) {
                $status_name = '待审核';
            } elseif ($item['status'] == 1 && $item['is_del'] == 0) {
                $status_name = '已审核';
            } elseif ($item['is_del'] == 1) {
                $status_name = '已删除';
            }
            $item['status_name'] = $status_name;
            $item['add_time'] = $item['add_time'] ? date('Y-m-d H:i:s', $item['add_time']) : '';
        }
        return $data;
    }

    /**
     * 处理where条件
     * @param $where
     * @param $model
     * @return mixed
     */
    public static function getMessageWhere($where, $model, $aler = '', $join = '')
    {
        // 如果不是删除状态 
        if ($where['status'] != 4) {
            $model = $model->where('is_del', 0);
        }
        if (isset($where['type']) && $where['type'] != '') {
            $model = $model->where($aler . 'type', $where['type']);
        }
        if (isset($where['special_id']) && $where['special_id'] != '') {
            $model = $model->where($aler . 'special_id', $where['special_id']);
        }
        if (isset($where['message_type']) && $where['message_type'] != '') {
            $model = $model->where($aler . 'message_type', $where['message_type']);
        }
        if (isset($where['status']) && $where['status'] != '') {
            $model = self::statusByWhere($where['status'], $model, $aler);
        }
        if (isset($where['is_del']) && $where['is_del'] != '' && $where['is_del'] != -1) $model = $model->where($aler . 'is_del', $where['is_del']);
        if (isset($where['keywords']) && $where['keywords'] != '') {
            $model = $model->where($aler . 'title|position|comment' . ($join ? '|' . $join . '.nickname|' . $join . '.uid|' . $join . '.phone' : ''), 'LIKE', "%$where[keywords]%");
        }
        if (isset($where['data']) && $where['data'] !== '') {
            $model = self::getModelTime($where, $model, $aler . 'add_time');
        }
        return $model;
    }
}