<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2021-09-26 11:46:17
 * @Last Modified time: 2021-11-11 10:50:04
 */

namespace app\admin\model\special;

use crmeb\traits\ModelTrait;
use crmeb\basic\BaseModel;
use crmeb\services\UtilService as Util;

/**
 * Class StoreCategory
 * @package app\admin\model\market
 */
class SpecialTool extends BaseModel
{

  /**
   * 数据表主键
   * @var string
   */
  protected $pk = 'id';

  /**
   * 模型名称
   * @var string
   */
  protected $name = 'special_task_tool';

  use ModelTrait;

  /**
   * 全部素材分类
   */
  public static function taskCategoryAll($type = 0)
  {
    $model = self::where('is_del', 0);
    if ($type == 1) {
      $model = $model->where('source_id', 0);
    }
    $list = $model->select();
    $list = count($list) > 0 ? $list->toArray() : [];
    $list = Util::sortListTier($list);
    return $list;
  }

  /**
   * 异步获取分类列表
   * @param $where
   * @return array
   */
  public static function List($where)
  {
    $data = ($data = self::systemPage($where, true)->page((int)$where['page'], (int)$where['limit'])->select()) && count($data) ? $data->toArray() : [];
    $count = self::systemPage($where, true)->count();
    return compact('count', 'data');
  }


  /**
   * @param $where
   * @return array
   */
  public static function systemPage($where, $isAjax = false)
  {
    $model = new self;
    $model = $model->where('is_del', 0);
    if ($where['name'] != '') $model = $model->where('title｜link', 'LIKE', "%$where[name]%");
    if ($isAjax === true) {
      if (isset($where['order']) && $where['order'] != '') {
        $model = $model->order(self::setOrder($where['order']));
      } else {
        $model = $model->order('sort desc,id desc');
      }
      return $model;
    }
    return self::page($model, function ($item) {}, $where);
  }



  /* 隐藏显示
     * @param $id
     * @param $show
     * @return bool
     */
  public static function setShow($id, $show)
  {
    self::beginTrans();
    $res = self::where('id', $id)->update(['is_show' => $show]);
    self::checkTrans($res);
    return $res;
  }


  public static function delTool($id)
  {
    return self::edit(['is_del' => 1], $id, 'id');
  }
}
