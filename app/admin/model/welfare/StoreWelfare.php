<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2020-09-02 17:45:25
 * @Last Modified time: 2020-09-03 10:32:00
 */
namespace app\admin\model\welfare;

use crmeb\traits\ModelTrait;
use crmeb\basic\BaseModel;

/**
 * Class StoreCategory
 * @package app\admin\model\store
 */
class StoreWelfare extends BaseModel
{

    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'system_welfare';

    use ModelTrait;

    /**
     * 异步获取奖品列表
     * @param $where
     * @return array
     */
    public static function WareList($where)
    {
        $data = ($data = self::systemPage($where, true)->page((int)$where['page'], (int)$where['limit'])->select()) && count($data) ? $data->toArray() : [];
        foreach ($data as &$item) {
            $item['status_name'] = '已设置活动奖品';
            $item['pending'] = rand(0,100);
            $item['expired'] = rand(0,20);
        }
        $count = self::systemPage($where, true)->count();
        return compact('count', 'data');
    }

    /**
     * @param $where
     * @return array
     */
    public static function systemPage($where, $isAjax = false)
    {
        $model = new self;
        if ($where['name'] != '') $model = $model->where('name', 'LIKE', "%$where[name]%");
        return $model->where('is_del',0)->where('is_show',1)->order('sort desc,id desc');
    }
}