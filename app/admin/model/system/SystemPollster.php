<?php
/**
 * @Author: <PERSON><PERSON>
 * @Date:   2021-10-08 14:17:36
 * @Last Modified time: 2021-10-08 15:51:21
 */
namespace app\admin\model\system;

use crmeb\basic\BaseModel;
use crmeb\traits\ModelTrait;

/**
 * Class SystemPollster
 * @package app\admin\model\system
 */
class SystemPollster extends BaseModel
{
    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'system_pollster';

    use ModelTrait;

    /**
     * @param $where
     * @return array
     */
    public static function systemPage($where)
    {
        $model = new self;
        $model = $model->alias('s');
        if ($where['status'] != '') $model = $model->where('s.status', $where['status']);
        if ($where['store_name'] != '') $model = $model->where('s.title|s.id', 'LIKE', "%$where[store_name]%");
        $model = $model->page(bcmul($where['page'], $where['limit'], 0), $where['limit']);
        $model = $model->order('s.id desc');
        $model = $model->where('s.is_del', 0);
        return self::page($model, function ($item) {

        }, $where, $where['limit']);
    }


    /* 横幅隐藏显示
     * @param $id
     * @param $show
     * @return bool
     */
    public static function setPollsterStatus($id, $status)
    {
        self::beginTrans();
        $res1 = true;
        $res2 = self::where('id', $id)->update(['status' => $status]);
        $res = $res1 && $res2;
        self::checkTrans($res);
        return $res;
    }

    /**
     * 获取连表MOdel
     * @param $model
     * @return object
     */
    public static function getModelObject($where, $alert = '', $model = null)
    {
        $model = $model === null ? new self() : $model;
        if ($alert) $model = $model->alias($alert);
        $alert = $alert ? $alert . '.' : '';
        $model = $model->order($alert.'sort desc,'.$alert.'id desc');
        if (isset($where['store_name']) && $where['store_name']!='') $model = $model->where($alert . 'title|' . $alert. 'id', "LIKE", "%$where[store_name]%");

        return $model->where($alert .'is_del', 0);
    }


    public static function getPollsterList($where)
    {
        $model = self::getPollsterWhere($where, self::alias('a'))
            ->field('a.*');
        $model = $model->order('a.id desc');
        $data = ($data = $model->page((int)$where['page'], (int)$where['limit'])->select()) && count($data) ? $data->toArray() : [];
        foreach ($data as &$item) {
            $item['add_time'] = $item['add_time'] ? date('Y-m-d H:i:s', $item['add_time']) : '';
        }
        $count = self::getPollsterWhere($where, self::alias('a'))->count();
        return compact('count', 'data');
    }

    /**
     * 处理where条件
     * @param $where
     * @param $model
     * @return mixed
     */
    public static function getPollsterWhere($where, $model, $aler = '', $join = '')
    {
        $model = $model->where('is_del',0);
        if (isset($where['type'])) {
            $model = $model->where($aler . 'type', $where['type']);
        }
        if (isset($where['keywords']) && $where['keywords'] != '') {
            $model = $model->where($aler . 'title', 'LIKE', "%$where[keywords]%");
        }
        if ($where['add_time'] != '') {
            list($startTime, $endTime) = explode(' - ', $where['add_time']);
            $model = $model->where($aler. 'add_time', '>', strtotime($startTime));
            $model = $model->where($aler. 'add_time', '<', strtotime($endTime) + 24 * 3600);
        }
        return $model;
    }
}