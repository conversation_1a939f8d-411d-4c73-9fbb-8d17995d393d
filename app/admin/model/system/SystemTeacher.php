<?php

/**
 * @Author: <PERSON>
 * @Date:   2022-11-29 17:35:56
 * @Last Modified time: 2022-12-09 10:04:34
 */
namespace app\admin\model\system;

use app\admin\model\wechat\StoreServiceLog as ServiceLogModel;
use app\admin\model\user\User;
use crmeb\traits\ModelTrait;
use crmeb\basic\BaseModel;

/**
 * 教师 model
 * Class StoreProduct
 * @package app\admin\model\system
 */
class SystemTeacher extends BaseModel
{
    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'system_teacher';

    use ModelTrait;

    /**
     * @param $mer_id
     * @return array
     */
    public static function getList($mer_id)
    {
        return self::page(self::where('is_del',0)->order('id desc'), function ($item) {
            $item['wx_name'] = User::where(['uid' => $item['uid']])->value('nickname');
            $item['wx_avatar'] = User::where(['uid' => $item['uid']])->value('avatar');
        });
    }

    /**
     * 获取连表MOdel
     * @param $model
     * @return object
     */
    public static function getModelObject($where = [])
    {
        $model = new self();
        if (!empty($where)) {
            $model = $model->alias('a')->where(['a.type' => $where['category'], 'a.is_del' => 0]);
            if (isset($where['keyword']) && $where['keyword'] != '') {
                $model = $model->join('yw_user b','a.uid = b.uid')->where('b.nickname', 'like', "%$where[keyword]%");
            }
            $model = $model->order('a.sort desc,a.id desc');
        }
        return $model;
    }

    /**
     * @param $type
     * @return array
     */
    public static function getTeacherList($where = [])
    {
        $model = self::getModelObject($where);
        $model = $model->page((int)$where['page'], (int)$where['limit']);
        $data = ($data = $model->select()) && count($data) ? $data->toArray() : [];
        foreach ($data as &$item) {
            $item['nickname'] = User::where(['uid' => $item['uid']])->value('nickname');
            $item['avatar'] = User::where(['uid' => $item['uid']])->value('avatar');
        }
        unset($item);
        $count = self::getModelObject($where)->count();
        return compact('count', 'data');
    }
}