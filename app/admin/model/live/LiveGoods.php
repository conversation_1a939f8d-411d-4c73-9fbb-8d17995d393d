<?php

/**
 * @Author: <PERSON><PERSON>
 * @Date:   2021-09-29 17:06:15
 * @Last Modified time: 2021-09-29 17:12:24
 */
namespace app\admin\model\live;

use crmeb\traits\ModelTrait;
use crmeb\basic\BaseModel;

/**
 * Class LiveGoods
 * @package app\admin\model\live
 */
class LiveGoods extends BaseModel
{
    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'live_goods';

     /**获取单个
     * @param array $where
     * @return array|bool|false|\PDOStatement|string|\think\Model
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public static function getOne(array $where)
    {
        if (!$where) return false;
        return self::where($where)->find();
    }

}