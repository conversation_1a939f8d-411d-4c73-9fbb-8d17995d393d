# @Author: <PERSON><PERSON>
# @Date:   2020-07-28 16:13:38
# @Last Modified by:   developer-<PERSON><PERSON><PERSON><PERSON>
# @Last Modified time: 2021-01-05 10:31:19
#!/bin/bash
######################################################################
#
# ab多url并发的shell脚本
#
# sh ab.sh 并发请求数(-c) 最大秒数(-t) 请求的次数(-n)
#
######################################################################
rm -rf ab.log  #清空日志
require_path='http://shop.arthorize.com';
token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJzaG9wcy5hcnRob3JpemUuY29tIiwiYXVkIjoic2hvcHMuYXJ0aG9yaXplLmNvbSIsImlhdCI6MTU5NjQ0MjAwNCwibmJmIjoxNTk2NDQyMDA0LCJleHAiOjE1OTY0NTI4MDQsImp0aSI6eyJpZCI6MiwidHlwZSI6InVzZXIifX0.H-m5YGQ6yKa_NYiMGVPmuMqCNFk3BZInuMCOhW5FZ_s

for i in $(cat url.txt)
do
    if [ "$1" == "" ]
    then 
        echo "并发请求数不能为空" 
    elif [[ "$2" == "" ]]
    then
        if [ "$3" == "" ]
        then
            echo "并发请求数 = $1,最大秒数未赋值，请求的次数未赋值"
            ab -c $1 'http://shop.arthorize.com/'.$i >> ab.log &
            continue
        else
            echo "并发请求数 = $1,最大秒数未赋值，请求的次数 = $3 "
            ab -t $2 -n $3  -T "application/json, text/plain, */*" -H "Authori-zation: Bearer $token"  $require_path.$i >> ab.log &
            continue
        fi

    elif [[ "$3" == "" ]]
    then
        echo "并发请求数 = $1,最大秒数 = $2 ,请求的次数未赋值"
        ab -c $1 -t $2 -T "application/json, text/plain, */*" -H "Authori-zation: Bearer $token" $require_path.$i >> ab.log &
        continue
    else
        echo "并发请求数 = $1,最大秒数 = $2 ,请求的次数 = $3 "
        ab  -c $1 -t $2  -n $3 -T "application/json, text/plain, */*" -H "Authori-zation: Bearer $token" $require_path.$i >> ab.log &
        continue
    fi
done